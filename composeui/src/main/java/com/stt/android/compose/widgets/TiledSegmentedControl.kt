package com.stt.android.compose.widgets

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.ui.R

@Composable
fun <T> TiledSegmentedControl(
    segment: T,
    segments: List<T>,
    moreSegments: List<T>,
    onSegmentToggled: (T) -> Unit,
    shortName: @Composable T.() -> String,
    longName: @Composable T.() -> String,
    description: @Composable T.() -> String,
    bottomSheetTitle: @Composable ColumnScope.() -> Unit,
    modifier: Modifier = Modifier,
) {
    var showPicker by remember { mutableStateOf(false) }

    Box(modifier = modifier.clip(CircleShape)) {
        Box(
            modifier = Modifier
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.dividerColor,
                    shape = CircleShape,
                )
                .matchParentSize(),
        )
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            segments.forEach { item ->
                val selected = segment == item
                Segment(
                    text = item.shortName(),
                    color = with(MaterialTheme.colorScheme) { if (selected) onPrimary else onSurface },
                    minWidth = 56.dp,
                    minHeight = 36.dp,
                    selected = selected,
                    onClick = {
                        if (!selected) {
                            onSegmentToggled(item)
                        }
                    },
                    modifier = Modifier.fillMaxHeight()
                )
            }

            if (moreSegments.isNotEmpty()) {
                val item = moreSegments.firstOrNull { it == segment }
                Segment(
                    text = item?.shortName() ?: stringResource(R.string.segmented_control_more),
                    color = with(MaterialTheme.colorScheme) { if (item != null) onPrimary else onSurface },
                    minWidth = 75.dp,
                    minHeight = 36.dp,
                    selected = item != null,
                    showIndicator = true,
                    onClick = { showPicker = true },
                    modifier = Modifier.fillMaxHeight()
                )
            }
        }
    }

    if (showPicker) {
        GenericSegmentSelectionBottomSheet(
            selectedSegment = segment,
            list = moreSegments,
            onSelected = {
                onSegmentToggled(it)
                showPicker = false
            },
            onDismiss = { showPicker = false },
            longName = longName,
            description = description,
            bottomSheetTitle = bottomSheetTitle,
        )
    }
}
