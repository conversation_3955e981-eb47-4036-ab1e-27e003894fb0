package com.stt.android.compose.util

import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.ComposeView
import androidx.navigation.NavHostController
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.M3AppTheme
import kotlinx.coroutines.flow.Flow

fun ComposeView.setContentWithTheme(content: @Composable () -> Unit) {
    setContent {
        AppTheme {
            content()
        }
    }
}

fun ComponentActivity.setContentWithTheme(content: @Composable () -> Unit) {
    setContent {
        AppTheme {
            content()
        }
    }
}

fun ComposeView.setContentWithM3Theme(content: @Composable () -> Unit) {
    setContent {
        M3AppTheme {
            content()
        }
    }
}

fun ComponentActivity.setContentWithM3Theme(content: @Composable () -> Unit) {
    setContent {
        M3AppTheme {
            content()
        }
    }
}

@Composable
fun SystemBarsColorController(
    navController: NavHostController,
    useDarkBarFlow: () -> Flow<Boolean>,
) {
    val systemUiController = rememberSystemUiController()
    val lightBgColor = MaterialTheme.colors.surface
    val darkBgColor = MaterialTheme.colors.secondary

    fun updateSystemBars(isDark: Boolean) {
        val bgColor = if (isDark) darkBgColor else lightBgColor
        systemUiController.setStatusBarColor(
            color = bgColor,
            darkIcons = !isDark
        )
        systemUiController.setNavigationBarColor(
            color = bgColor,
            darkIcons = !isDark
        )
    }

    LaunchedEffect(navController) {
        useDarkBarFlow().collect { isDark ->
            updateSystemBars(isDark)
        }
    }
}
