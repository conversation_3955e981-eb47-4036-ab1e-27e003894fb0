package com.stt.android.compose.widgets

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.darkGreyText
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.ui.R

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun TextFieldInputWithError(
    currentText: String,
    onChanged: (String) -> Unit,
    modifier: Modifier = Modifier,
    placeholderText: String = "",
    keyboardType: KeyboardType = KeyboardType.Text,
    onActionDone: (() -> Unit)? = null,
    isPassword: Boolean = false,
    errorMessage: String = "",
    tipsText: String = "",
    prefixText: String = "",
    prefixImageVector: ImageVector? = null,
    enablePlaceholderTextFloating: Boolean = true
) {
    var passwordVisible by remember { mutableStateOf(false) }
    val passwordVisualTransformation = when {
        isPassword && !passwordVisible -> PasswordVisualTransformation()
        else -> VisualTransformation.None
    }
    val keyboardController = LocalSoftwareKeyboardController.current

    Column(
        modifier = modifier,
    ) {
        TextField(
            modifier = Modifier
                .fillMaxWidth(),
            value = currentText,
            onValueChange = { newText ->
                onChanged(newText)
            },
            maxLines = 1,
            singleLine = true,
            placeholder = if (placeholderText.isNotEmpty()) {
                {
                    Text(
                        text = placeholderText,
                        color = MaterialTheme.colors.darkGreyText,
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            } else {
                null
            },
            label = if (enablePlaceholderTextFloating && currentText.isNotEmpty()) {
                {
                    Text(
                        text = placeholderText,
                        color = MaterialTheme.colors.nearBlack,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            } else {
                null
            },
            colors = TextFieldDefaults.textFieldColors(
                textColor = if (errorMessage.isNotEmpty()) MaterialTheme.colors.error else MaterialTheme.colors.primary,
                focusedIndicatorColor = when {
                    errorMessage.isNotEmpty() -> MaterialTheme.colors.error
                    currentText.isNotEmpty() -> MaterialTheme.colors.primary
                    else -> MaterialTheme.colors.dividerColor
                },
                unfocusedIndicatorColor = MaterialTheme.colors.dividerColor,
                backgroundColor = Color.Transparent,
            ),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done, keyboardType = keyboardType),
            keyboardActions = KeyboardActions(onDone = {
                onActionDone?.invoke()
                keyboardController?.hide()
            }),
            visualTransformation = passwordVisualTransformation,
            trailingIcon = if (isPassword) {
                {
                    IconButton(onClick = { passwordVisible = !passwordVisible }) {
                        Image(
                            painter = if (passwordVisible) {
                                painterResource(R.drawable.visibility_off_outline)
                            } else {
                                painterResource(R.drawable.visibility_on_outline)
                            },
                            contentDescription = if (passwordVisible) "Hide Password" else "Show Password"
                        )
                    }
                }
            } else {
                null
            },
            leadingIcon = when {
                prefixText.isNotEmpty() -> {
                    {
                        Text(
                            text = prefixText,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colors.nearBlack
                        )
                    }
                }
                prefixImageVector != null -> {
                    {
                        Icon(
                            imageVector = prefixImageVector,
                            contentDescription = null,
                            tint = MaterialTheme.colors.nearBlack
                        )
                    }
                }
                else -> {
                    null
                }
            }
        )
        if (errorMessage.isNotEmpty() || tipsText.isNotEmpty()) {
            Text(
                text = errorMessage.ifEmpty { tipsText },
                color = if (errorMessage.isNotEmpty()) MaterialTheme.colors.error else MaterialTheme.colors.darkGrey,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = MaterialTheme.spacing.medium, top = MaterialTheme.spacing.small)
            )
        }
    }
}

@Preview
@Composable
private fun EditTargetInputPreview() {
    AppTheme {
        Surface {
            TextFieldInputWithError(
                currentText = "",
                placeholderText = "email",
                tipsText = "error 123",
                prefixText = "+86",
                onChanged = {}
            )
        }
    }
}
