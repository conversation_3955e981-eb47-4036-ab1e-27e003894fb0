package com.suunto.headset.ui.ows

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.M3AppTheme
import com.suunto.extension.popBackStack
import com.suunto.headset.ai.VoiceState
import com.suunto.headset.viewmodel.AIChatViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AIChatFragment : BaseFragment() {
    private val viewModel by viewModels<AIChatViewModel>()

    private val requestAudioPermission = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            viewModel.startAI()
        } else {
            // TODO: Handle permission denial, e.g., show a message to the user
        }
    }

    @Composable
    override fun SetContentView() {
        M3AppTheme {
            val messages = viewModel.messages.collectAsState().value
            val voiceInputState = viewModel.voiceInputState.collectAsState().value
            AIChatScreen(
                messages = messages,
                onBackClick = {
                    viewModel.exitAI {
                        popBackStack()
                    }
                },
                voiceInputInfo = voiceInputState,
                onVoiceInputAction = {
                    when (voiceInputState.state) {
                        VoiceState.IDLE -> checkAndRequestAudioPermission()
                        VoiceState.STARTED,
                        VoiceState.PROGRESS -> {
                        }

                        VoiceState.STOPPED -> {}
                    }
                }
            )
        }
    }

    private fun checkAndRequestAudioPermission() {
        val context = requireContext()
        if (ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            viewModel.startAI()
        } else {
            requestAudioPermission.launch(Manifest.permission.RECORD_AUDIO)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    viewModel.exitAI {
                        popBackStack()
                    }
                }
            }
        )
    }
}
