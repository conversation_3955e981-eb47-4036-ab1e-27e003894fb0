package com.suunto.headset.repository

import com.stt.android.data.ai.HeadsetAIRemoteDatasource
import javax.inject.Inject

class HeadsetAIRepository @Inject constructor(
    private val headsetAIRemoteDatasource: HeadsetAIRemoteDatasource
) {
    suspend fun getAIAccessToken(userName: String) =
        headsetAIRemoteDatasource.getAIAccessToken(userName)

    suspend fun refreshAIAccessToken(userName: String, roomId: String) =
        headsetAIRemoteDatasource.refreshAIAccessToken(userName, roomId)

    suspend fun startVoiceChat(userName: String, roomId: String, turnDetectionMode: Int) =
        headsetAIRemoteDatasource.startVoiceChat(userName, roomId, turnDetectionMode)

    suspend fun stopVoiceChat(roomId: String) =
        headsetAIRemoteDatasource.stopVoiceChat(roomId)
}
