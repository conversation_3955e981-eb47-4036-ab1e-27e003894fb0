package com.suunto.headset.ui.ows

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SheetState
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.newdesign.widgets.PrimaryButton
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.suunto.headset.R
import com.suunto.headset.model.CustomizationKeyInfo
import com.suunto.headset.model.FunctionType
import com.suunto.headset.model.HeadphoneSide
import com.suunto.headset.model.KeyMethod
import com.suunto.headset.model.WakeUpAiKeyInfo
import com.suunto.headset.ui.components.CommonTopAppBar
import com.suunto.headset.viewmodel.ToneType
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import com.stt.android.R as BR

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AiSettingScreen(
    wakeUpSuuntoKey: WakeUpAiKeyInfo,
    onWakeUpSuuntoKeyChange: (WakeUpAiKeyInfo) -> Unit,
    toneSelection: ToneType,
    onToneSelectionChange: (ToneType) -> Unit,
    continuousDialogue: Boolean,
    onContinuousDialogueChange: (Boolean) -> Unit,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var toneExpanded by remember { mutableStateOf(false) }
    var keySheetVisible by remember { mutableStateOf(false) }
    val keyOptions = remember {
        persistentListOf(
            WakeUpAiKeyInfo(
                HeadphoneSide.LEFT,
                CustomizationKeyInfo(KeyMethod.TRIPLE_TAP, FunctionType.ACTIVATE_AI)
            ),
            WakeUpAiKeyInfo(
                HeadphoneSide.RIGHT,
                CustomizationKeyInfo(KeyMethod.TRIPLE_TAP, FunctionType.ACTIVATE_AI)
            ),
            WakeUpAiKeyInfo(
                HeadphoneSide.LEFT,
                CustomizationKeyInfo(KeyMethod.LONG_PRESS_AND_HOLD, FunctionType.ACTIVATE_AI)
            ),
            WakeUpAiKeyInfo(
                HeadphoneSide.RIGHT,
                CustomizationKeyInfo(KeyMethod.LONG_PRESS_AND_HOLD, FunctionType.ACTIVATE_AI)
            ),
        )
    }
    val keySheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    Scaffold(
        modifier = modifier,
        topBar = {
            CommonTopAppBar(
                title = stringResource(com.stt.android.R.string.settings),
                onBackClick = onBackClick
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface)
                .narrowContent()
        ) {
            Image(painter = painterResource(R.drawable.icon_su10), contentDescription = null)
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
            SettingListItem(
                title = stringResource(R.string.ai_wake_up_title),
                description = stringResource(
                    if (wakeUpSuuntoKey.headphoneSide == HeadphoneSide.LEFT) {
                        R.string.wake_up_ai_key_left_headphone
                    } else {
                        R.string.wake_up_ai_key_right_headphone
                    },
                    stringResource(wakeUpSuuntoKey.keyInfo.keyMethod.nameRes)
                ),
                rightText = stringResource(R.string.sport_switch_modify),
                modifier = Modifier.clickable { keySheetVisible = true }
            )
            HorizontalDivider(
                color = MaterialTheme.colorScheme.dividerColor,
                thickness = MaterialTheme.spacing.small
            )
            Column(modifier = Modifier.fillMaxWidth()) {
                SettingListItem(
                    title = stringResource(R.string.ai_tone_selection),
                    rightText = when (toneSelection) {
                        ToneType.FEMALE -> stringResource(BR.string.settings_general_user_settings_gender_female)
                        ToneType.MALE -> stringResource(BR.string.settings_general_user_settings_gender_male)
                    },
                    modifier = Modifier.clickable { toneExpanded = !toneExpanded }
                )
                if (toneExpanded) {
                    ToneType.entries.forEach { type ->
                        HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    onToneSelectionChange(type)
                                    toneExpanded = false
                                }
                                .heightIn(56.dp)
                                .padding(horizontal = MaterialTheme.spacing.medium),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = when (type) {
                                    ToneType.FEMALE -> stringResource(BR.string.settings_general_user_settings_gender_female)
                                    ToneType.MALE -> stringResource(BR.string.settings_general_user_settings_gender_male)
                                },
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                modifier = Modifier.weight(1f)
                            )
                            if (type == toneSelection) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                }
            }
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
            ToggleListItem(
                title = stringResource(R.string.ai_continuous_setting),
                checked = continuousDialogue,
                description = stringResource(R.string.ai_continuous_summary),
                onCheckedChange = onContinuousDialogueChange
            )
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        }
        WakeUpSuuntoKeySheet(
            visible = keySheetVisible,
            options = keyOptions,
            selected = wakeUpSuuntoKey,
            onSave = {
                onWakeUpSuuntoKeyChange(it)
                keySheetVisible = false
            },
            onDismiss = { keySheetVisible = false },
            sheetState = keySheetState
        )
    }
}

@Composable
private fun SettingListItem(
    title: String,
    modifier: Modifier = Modifier,
    description: String? = null,
    rightText: String? = null
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(56.dp)
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface
            )
            if (description != null) {
                Text(
                    modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.darkGrey
                )
            }
        }
        if (rightText != null) {
            Text(
                text = rightText,
                color = MaterialTheme.colorScheme.primary,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier
                    .padding(start = MaterialTheme.spacing.medium)
                    .align(Alignment.CenterVertically)
            )
        }
    }
}

@Composable
private fun ToggleListItem(
    title: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    description: String? = null
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface
            )
            if (description != null) {
                Text(
                    modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.darkGrey
                )
            }
        }
        Switch(
            modifier = Modifier.padding(start = MaterialTheme.spacing.medium),
            checked = checked,
            onCheckedChange = onCheckedChange,
            colors = SwitchDefaults.colors(
                uncheckedBorderColor = MaterialTheme.colorScheme.lightGrey,
                uncheckedThumbColor = MaterialTheme.colorScheme.surface,
                uncheckedTrackColor = MaterialTheme.colorScheme.lightGrey
            )
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun WakeUpSuuntoKeySheet(
    visible: Boolean,
    options: ImmutableList<WakeUpAiKeyInfo>,
    selected: WakeUpAiKeyInfo,
    onSave: (WakeUpAiKeyInfo) -> Unit,
    onDismiss: () -> Unit,
    sheetState: SheetState
) {
    if (!visible) return
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = sheetState,
        containerColor = MaterialTheme.colorScheme.surface,
        dragHandle = null
    ) {
        var selectedValue by remember { mutableStateOf(selected) }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = MaterialTheme.spacing.medium)
        ) {
            Text(
                text = stringResource(R.string.ai_wake_up_title),
                style = MaterialTheme.typography.bodyXLargeBold,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(MaterialTheme.spacing.medium)
            )
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
            options.forEach { option ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            selectedValue = option
                        }
                        .heightIn(56.dp)
                        .padding(horizontal = MaterialTheme.spacing.medium),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(
                            if (option.headphoneSide == HeadphoneSide.LEFT) {
                                R.string.wake_up_ai_key_left_headphone
                            } else {
                                R.string.wake_up_ai_key_right_headphone
                            },
                            stringResource(option.keyInfo.keyMethod.nameRes)
                        ),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier.weight(1f)
                    )
                    if (option == selectedValue) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }
                HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
            }
            PrimaryButton(
                text = stringResource(com.stt.android.R.string.save),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium),
                onClick = {
                    onSave(selectedValue)
                }
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewAiSettingScreen() {
    M3AppTheme {
        AiSettingScreen(
            wakeUpSuuntoKey = WakeUpAiKeyInfo(
                HeadphoneSide.LEFT,
                CustomizationKeyInfo(KeyMethod.TRIPLE_TAP, FunctionType.ACTIVATE_AI)
            ),
            onWakeUpSuuntoKeyChange = {},
            toneSelection = ToneType.FEMALE,
            onToneSelectionChange = {},
            continuousDialogue = false,
            onContinuousDialogueChange = {},
            onBackClick = {}
        )
    }
}
