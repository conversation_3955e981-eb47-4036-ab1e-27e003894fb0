package com.suunto.headset.viewmodel

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import com.suunto.headset.gearevent.HeadsetGearEventHelper
import com.suunto.headset.model.ConnectionPage
import com.suunto.headset.repository.SetUserHeadsetConfigDataSource
import com.suunto.soa.ble.client.ConnectFeatures
import com.suunto.soa.ble.client.ConnectStates
import com.suunto.soa.ble.device.DeviceConfigFactory
import com.suunto.soa.ble.device.DeviceType
import com.suunto.soa.ble.scanner.validName
import com.suunto.soa.ble.service.BleState
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ConnectHelper @Inject constructor(
    private val setUserHeadsetConfigDataSource: SetUserHeadsetConfigDataSource,
    private val connectFeatures: ConnectFeatures,
    private val connectStates: ConnectStates,
    private val otaHelper: OTAHelper,
    private val headsetGearEventHelper: HeadsetGearEventHelper,
) {
    private val exceptionHandler = CoroutineExceptionHandler { _, throwable -> Timber.w(throwable) }
    private val helperScope = CoroutineScope(Dispatchers.IO + exceptionHandler)
    var currentConnectionPage: ConnectionPage? = null

    fun getDeviceRunning() = connectFeatures.getDeviceRunning()

    @SuppressLint("MissingPermission")
    fun saveDeviceConfig(device: BluetoothDevice, pid: Int?) {
        val headsetConfig = setUserHeadsetConfigDataSource.getHeadsetConfig(device.address)
        headsetConfig.deviceName = device.validName()
        headsetConfig.mac = device.address
        headsetConfig.productId = pid
        setUserHeadsetConfigDataSource.saveHeadsetConfig(headsetConfig)
    }

    suspend fun handleConnectDevice(
        mac: String,
        pid: Int?,
        onConnectSuccess: (ConnectTarget) -> Unit,
        onConnectFail: (String?) -> Unit,
        retryCount: Int = 0,
    ) {
        if (connectStates.getState() == BleState.Connecting) return
        connectFeatures.disconnectDevice()
        val connectResult = connectFeatures.connectDevice(mac, retryCount)
        if (!connectResult.isConnected) {
            onConnectFail.invoke(connectResult.error)
        } else {
            val result = withTimeoutOrNull(5_000) {
                connectStates.waitForReady()
                val device = connectFeatures.getConnectedDevice()
                saveDeviceConfig(device, pid)
                setUserHeadsetConfigDataSource.saveLastDeviceMac(device.address)

                // Setup for new paired headset gear event
                headsetGearEventHelper.setupForNewPairedHeadset()

                onConnectSuccess.invoke(ConnectTarget(device, pid))
                true
            }
            if (result != true) {
                connectFeatures.disconnectDevice()
                onConnectFail.invoke("REASON_WAIT_FOR_READY_TIMEOUT")
            }
        }
    }

    suspend fun disconnect() {
        connectFeatures.disconnectDevice()
    }

    /**
     * Make sure headphone is disconnected after exit the headphone related page
     */
    fun disconnectIfNotInOTA() {
        helperScope.launch {
            if (!otaHelper.isInProgress()) {
                Timber.d("call disconnect Device")
                connectFeatures.disconnectDevice()
            }
        }
    }

    companion object {
        const val DEFAULT_RECONNECT_COUNT = 2
    }
}
