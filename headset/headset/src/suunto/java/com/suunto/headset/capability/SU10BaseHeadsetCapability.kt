package com.suunto.headset.capability

import com.suunto.headset.R
import com.suunto.headset.model.DeviceCapability
import com.suunto.headset.model.SportSupports
import com.suunto.headset.model.SportType
import com.suunto.headset.ui.HeadsetDeviceType
import com.suunto.headset.ui.onboarding.HeadsetOnBoardingPage
import com.suunto.headset.ui.onboarding.SU10OnBoardingPages
import com.suunto.soa.ble.control.attr.EqMode

open class SU10BaseHeadsetCapability : BaseHeadsetCapability() {
    override val headsetDeviceType: HeadsetDeviceType = HeadsetDeviceType.SU10

    override fun supportEqMode(): Boolean = true

    override fun supportBodySense(): Boolean = true

    override fun supportDualDeviceConnect(): Boolean = true

    override fun supportGetSerial(): Boolean = false

    override fun supportLowLatency(): Boolean = false

    override fun supportGetConnectedDevices(): Boolean = true

    override fun supportLEDLightMode(): Boolean = false

    override fun supportLanguageSetting(): Boolean = true

    override fun supportNotifyBatteryChanged(): Boolean = true

    override fun supportButtonCustomization(): Boolean = true

    override fun supportMultiBattery(): Boolean = true

    override fun getFunctionIntroduction(): List<HeadsetOnBoardingPage> {
        return SU10OnBoardingPages
    }

    override fun getUserGuideUrl(): Int {
        // TODO use actual url
        return R.string.user_guide_url_su07
    }

    override fun getMoreInformationUrl(): Int {
        // TODO use actual url
        return R.string.more_information_url_su07
    }

    override fun getSoundModeList(): List<EqMode> {
        // TODO use actual sound modes
        return emptyList()
    }

    override fun isCheckDeviceAvailableBeforeOTA(): Boolean = true

    override fun getSportTypes(deviceCapability: DeviceCapability): List<SportType> {
        val sportTypes = mutableListOf<SportType>()
        if (supportsPoolSwimSport(deviceCapability)) {
            sportTypes.add(SportType.POOL_SWIMMING)
        }
        sportTypes.add(SportType.OPEN_WATER_SWIMMING)
        if (supportJumpRopeFeature(deviceCapability)) {
            sportTypes.add(SportType.JUMP_ROPE)
        }
        if (supportRunningFeature(deviceCapability)) {
            sportTypes.add(SportType.RUNNING)
        }
        return sportTypes
    }

    override fun getDefaultSportType(): SportType {
        return SportType.RUNNING
    }

    override fun supportNeckMovementAssessment(): Boolean = true

    override fun getHeadControlInstructions(): List<HeadControlInstruction> {
        // TODO use actual image
        return listOf(
            HeadControlInstruction(
                R.string.head_control_instructions_call,
                listOf(
                    HeadControlInstructionItem(
                        R.drawable.su07_nod,
                        R.string.head_control_item_answer_call_title,
                        R.string.head_control_item_answer_call_content
                    ),
                    HeadControlInstructionItem(
                        R.drawable.su07_shake,
                        R.string.head_control_item_answer_reject_title,
                        R.string.head_control_item_shake_head
                    )
                )
            ),
            HeadControlInstruction(
                R.string.head_control_instructions_media,
                listOf(
                    HeadControlInstructionItem(
                        R.drawable.su07_shake,
                        R.string.head_control_item_skip_song_title,
                        R.string.head_control_item_shake_head
                    )
                )
            )
        )
    }

    override fun supportRunningFeature(deviceCapability: DeviceCapability): Boolean {
        return deviceCapability.runningSportSupported
    }

    override fun getSportSupports(deviceCapability: DeviceCapability): SportSupports {
        val supportSwim = supportSwimFeature()
        val supportJumpRope = supportJumpRopeFeature(deviceCapability)
        val supportRunning = supportRunningFeature(deviceCapability)
        return SportSupports(
            isSwimSupported = supportSwim,
            isJumpRopeSupported = supportJumpRope,
            isRunningSupported = supportRunning,
            poolSwimmingSupported = supportsPoolSwimSport(deviceCapability)
        )
    }

    override fun supportSportButtonModify(deviceCapability: DeviceCapability): Boolean {
        return deviceCapability.sportButtonCustomizedSupported
    }

    override fun supportSportsSwitch(deviceCapability: DeviceCapability): Boolean {
        val supportSwim = supportSwimFeature()
        val supportJumpRope = supportJumpRopeFeature(deviceCapability)
        val supportRunning = supportRunningFeature(deviceCapability)
        return supportSwim || supportJumpRope || supportRunning
    }

    override fun supportImmersiveMode(): Boolean = true

    override fun supportMetronome(): Boolean = true

    override fun supportSpatialAudio(): Boolean = true

    override fun supportFindHeadphones(): Boolean = true

    override fun supportGetColorType(): Boolean = true
}
