package com.suunto.headset.model

import androidx.annotation.StringRes
import com.suunto.headset.R
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

enum class KeyMethod(@StringRes val nameRes: Int) {
    SINGLE_TAP(R.string.single_tap),
    DOUBLE_TAP(R.string.double_tap),
    TRIPLE_TAP(R.string.triple_tap),
    LONG_PRESS_AND_HOLD(R.string.press_hold),
}

enum class FunctionType(@StringRes val nameRes: Int) {
    NONE(R.string.button_null),
    PLAY_PAUSE(R.string.button_function_music_play_pause),
    VOLUME_UP(R.string.function_volume_up),
    VOLUME_DOWN(R.string.function_volume_down),
    LAST_SONG(R.string.function_last_song),
    NEXT_SONG(R.string.function_next_song),
    ACTIVATE_VOICE_ASSISTANT(R.string.button_function_voice_assistant),
    ACTIVATE_AI(R.string.function_activate_ai),
    METRONOME(R.string.function_metronome),
    START_STOP_WORKOUT(R.string.button_function_start_end_workout),
    IMMERSIVE_MODE(R.string.function_immersive_mode),
    ANSWER_CALL(R.string.button_function_phone_call_control),
    REJECT_CALL(R.string.button_function_reject_call),
}

fun FunctionType.getDescriptionResId(): Int? {
    return when (this) {
        FunctionType.ACTIVATE_AI -> R.string.function_activate_ai_tip
        else -> null
    }
}

val keyMethodWithFunctionTypes = buildMap<KeyMethod, ImmutableList<FunctionType>> {
    put(
        KeyMethod.SINGLE_TAP,
        persistentListOf(
            FunctionType.LAST_SONG,
            FunctionType.NEXT_SONG,
            FunctionType.VOLUME_UP,
            FunctionType.VOLUME_DOWN,
            FunctionType.PLAY_PAUSE,
            FunctionType.NONE
        )
    )
    put(
        KeyMethod.DOUBLE_TAP,
        persistentListOf(
            FunctionType.LAST_SONG,
            FunctionType.NEXT_SONG,
            FunctionType.VOLUME_UP,
            FunctionType.VOLUME_DOWN,
            FunctionType.PLAY_PAUSE,
            FunctionType.NONE
        )
    )
    put(
        KeyMethod.TRIPLE_TAP,
        persistentListOf(
            FunctionType.LAST_SONG,
            FunctionType.NEXT_SONG,
            FunctionType.VOLUME_UP,
            FunctionType.VOLUME_DOWN,
            FunctionType.PLAY_PAUSE,
            FunctionType.NONE
        )
    )
    put(
        KeyMethod.LONG_PRESS_AND_HOLD,
        persistentListOf(
            FunctionType.ACTIVATE_VOICE_ASSISTANT,
            FunctionType.ACTIVATE_AI,
            FunctionType.START_STOP_WORKOUT,
            FunctionType.IMMERSIVE_MODE,
            FunctionType.METRONOME,
            FunctionType.NONE
        )
    )
}

data class CustomizationKeyInfo(val keyMethod: KeyMethod, val functionType: FunctionType)
