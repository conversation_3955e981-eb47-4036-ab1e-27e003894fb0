package com.suunto.headset.viewmodel

import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.suunto.headset.model.CustomizationKeyInfo
import com.suunto.headset.model.FunctionType
import com.suunto.headset.model.HeadphoneSide
import com.suunto.headset.model.KeyMethod
import com.suunto.headset.model.WakeUpAiKeyInfo
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

enum class ToneType {
    FEMALE,
    MALE
}

@HiltViewModel
class AiSettingViewModel @Inject constructor(
    private val dispatchers: CoroutinesDispatchers
) : CoroutineViewModel(dispatchers) {

    private val _wakeUpSuuntoKey = MutableStateFlow(
        WakeUpAiKeyInfo(
            HeadphoneSide.LEFT,
            CustomizationKeyInfo(KeyMethod.TRIPLE_TAP, FunctionType.ACTIVATE_AI)
        )
    )
    val wakeUpSuuntoKey = _wakeUpSuuntoKey.asStateFlow()

    private val _toneSelection = MutableStateFlow(ToneType.FEMALE)
    val toneSelection = _toneSelection.asStateFlow()

    private val _continuousDialogue = MutableStateFlow(false)
    val continuousDialogue = _continuousDialogue.asStateFlow()

    fun setWakeUpSuuntoKey(keyInfo: WakeUpAiKeyInfo) {
        _wakeUpSuuntoKey.value = keyInfo
    }

    fun setToneSelection(tone: ToneType) {
        _toneSelection.value = tone
    }

    fun setContinuousDialogue(enabled: Boolean) {
        _continuousDialogue.value = enabled
    }
} 
