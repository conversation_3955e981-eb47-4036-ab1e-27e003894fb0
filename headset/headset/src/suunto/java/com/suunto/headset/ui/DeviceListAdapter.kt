package com.suunto.headset.ui

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import by.kirich1409.viewbindingdelegate.viewBinding
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.STTConstants
import com.suunto.headset.R
import com.suunto.headset.databinding.HeadsetDeviceItemBinding
import com.suunto.headset.databinding.HeadsetScanFooterBinding
import com.suunto.soa.ble.scanner.Device

class DeviceListAdapter(
    val context: Context,
    private val featureTogglePreferences: SharedPreferences
) : RecyclerView.Adapter<DeviceListAdapter.DeviceListVH>() {

    var data: List<Device> = emptyList()
    private val headerViews: MutableList<View> = mutableListOf()
    private val footerViews: MutableList<View> = mutableListOf()
    private val headerLayout = LinearLayout(context)
    private val footerLayout = LinearLayout(context)
    var onClickConnect: ((Device) -> Unit)? = null
    var onClickGotoBluetoothSetting: (() -> Unit)? = null

    fun addHeaderView(view: View) {
        headerViews.add(view)
    }

    fun addFooterView(view: View) {
        footerViews.add(view)
    }

    fun getHeaderCount(): Int {
        return if (headerViews.isNotEmpty()) 1 else 0
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): DeviceListVH {
        return when (viewType) {
            TYPE_HEADER -> DeviceListVH(
                headerLayout.apply {
                    orientation = LinearLayout.VERTICAL
                    layoutParams = RecyclerView.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                    removeAllViews()
                    headerViews.forEach {
                        addView(it)
                    }
                }
            )

            TYPE_FOOTER -> DeviceListVH(
                footerLayout.apply {
                    orientation = LinearLayout.VERTICAL
                    layoutParams = RecyclerView.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                    removeAllViews()
                    footerViews.forEach {
                        addView(it)
                    }
                }
            )

            else -> DeviceListVH(
                LayoutInflater.from(context).inflate(R.layout.headset_device_item, parent, false)
            )
        }
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: DeviceListVH, position: Int) {
        when (getItemViewType(position)) {
            TYPE_FOOTER -> holder.headsetScanFooterBinding.deviceScanInstructionsSummary.setOnClickListenerThrottled {
                onClickGotoBluetoothSetting?.invoke()
            }

            TYPE_DATA -> {
                val dataPosition = position - getHeaderCount()
                if (dataPosition >= 0 && dataPosition < data.size) {
                    if (featureTogglePreferences.getBoolean(
                            STTConstants.FeatureTogglePreferences.KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG,
                            STTConstants.FeatureTogglePreferences.KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG_DEFAULT
                        )
                    ) {
                        holder.headsetDeviceItemBinding.deviceName.text =
                            "${data[dataPosition].name} (${data[dataPosition].address})"
                    } else {
                        holder.headsetDeviceItemBinding.deviceName.text = data[dataPosition].name
                    }
                    holder.headsetDeviceItemBinding.deviceConnect.setOnClickListenerThrottled {
                        onClickConnect?.invoke(data[dataPosition])
                    }
                }
            }
        }
    }

    override fun onBindViewHolder(
        holder: DeviceListVH,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty()) {
            onBindViewHolder(holder, position)
        } else {
            val payload = payloads[0] as Bundle
            val dataPosition = position - getHeaderCount()
            if (dataPosition >= 0 && dataPosition < data.size) {
                val bean: Device = data[dataPosition]
                for (key in payload.keySet()) {
                    when (key) {
                        DIFF_KEY_NAME -> holder.headsetDeviceItemBinding.deviceName.text = bean.name
                        else -> {}
                    }
                }
            }
        }
    }

    override fun getItemCount(): Int {
        return (if (headerViews.isNotEmpty()) 1 else 0) + data.size + (if (footerViews.isNotEmpty()) 1 else 0)
    }

    override fun getItemViewType(position: Int): Int {
        if (isHeaderItem(position)) {
            return TYPE_HEADER
        }
        if (isFooterItem(position)) {
            return TYPE_FOOTER
        }
        return TYPE_DATA
    }

    private fun isFooterItem(position: Int) =
        footerViews.isNotEmpty() && position == itemCount - 1

    private fun isHeaderItem(position: Int) = headerViews.isNotEmpty() && position == 0

    fun removeAllFooterView() {
        footerViews.clear()
    }

    fun removeAllHeaderView() {
        headerViews.clear()
    }

    class DeviceListVH(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val headsetDeviceItemBinding by viewBinding(HeadsetDeviceItemBinding::bind)
        val headsetScanFooterBinding by viewBinding(HeadsetScanFooterBinding::bind)
    }

    class DiffCallBack(
        private val oldData: List<Device>,
        private val newData: List<Device>
    ) :
        DiffUtil.Callback() {

        override fun getOldListSize(): Int {
            return oldData.size
        }

        override fun getNewListSize(): Int {
            return newData.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return if (oldItemPosition >= 0 && oldItemPosition < oldData.size && newItemPosition >= 0 && newItemPosition < newData.size) {
                oldData[oldItemPosition].address == newData[newItemPosition].address
            } else {
                true
            }
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return if (oldItemPosition >= 0 && oldItemPosition < oldData.size && newItemPosition >= 0 && newItemPosition < newData.size) {
                oldData[oldItemPosition] == newData[newItemPosition]
            } else {
                true
            }
        }

        override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
            if (oldItemPosition >= 0 && oldItemPosition < oldData.size && newItemPosition >= 0 && newItemPosition < newData.size) {
                val oldBean: Device = oldData[oldItemPosition]
                val newBean: Device = newData[newItemPosition]
                val payload = Bundle()
                if (oldBean.address != newBean.address) {
                    payload.putString(DIFF_KEY_MAC, newBean.address)
                }
                if (oldBean.name != newBean.name) {
                    payload.putString(DIFF_KEY_NAME, newBean.name)
                }
                return if (payload.isEmpty) null else payload
            }
            return null
        }
    }
}

private const val DIFF_KEY_NAME = "KEY_NAME"
private const val DIFF_KEY_MAC = "KEY_MAC"

const val TYPE_HEADER = 0
const val TYPE_DATA = 1
const val TYPE_FOOTER = 2
