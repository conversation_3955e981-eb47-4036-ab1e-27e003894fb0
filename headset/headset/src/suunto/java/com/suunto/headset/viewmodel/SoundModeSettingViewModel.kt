package com.suunto.headset.viewmodel

import androidx.lifecycle.SavedStateHandle
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.suunto.headset.model.EqualizerFrequencyBand
import com.suunto.headset.model.EqualizerSetting
import com.suunto.headset.model.OwsSoundMode
import com.suunto.headset.model.OwsSoundModeSetting
import com.suunto.headset.model.toEqualizerFrequencyBand
import com.suunto.headset.model.toOwsSoundMode
import com.suunto.headset.model.toSoaEqualizerFrequencyBand
import com.suunto.headset.model.toSoaOwsSoundMode
import com.suunto.headset.ui.ows.SoundModeSettingFragment.Companion.KEY_OWS_SOUND_MODE
import com.suunto.soa.ble.client.SoaRunningInfoFeatures
import com.suunto.soa.ble.control.attr.OwsSoundModeData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import com.suunto.soa.ble.control.attr.EqualizerSetting as SoaEqualizerSetting
import com.suunto.soa.ble.control.attr.OwsSoundMode as SoaOwsSoundMode

@HiltViewModel
class SoundModeSettingViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val soaRunningInfoFeatures: SoaRunningInfoFeatures,
    dispatchers: CoroutinesDispatchers
) : CoroutineViewModel(dispatchers) {

    private val _soundModeSettings =
        MutableStateFlow(getDefaultSoundModes())
    val soundModeSettings = _soundModeSettings.asStateFlow()

    private val _equalizerSettings =
        MutableStateFlow(getDefaultEqualizerSettings())
    val equalizerSettings = _equalizerSettings.asStateFlow()

    private val _loading = MutableStateFlow(false)
    val loading = _loading.asStateFlow()

    private val _applyState = MutableStateFlow(null)
    val applyState = _applyState.asStateFlow()

    init {
        getCurrentSoundMode()
    }

    private fun getDefaultEqualizerSettings(): ImmutableList<EqualizerSetting> {
        return persistentListOf(
            EqualizerSetting(EqualizerFrequencyBand.BAND_LOW, 0),
            EqualizerSetting(EqualizerFrequencyBand.BAND_LOW_MID, 0),
            EqualizerSetting(EqualizerFrequencyBand.BAND_MID, 0),
            EqualizerSetting(EqualizerFrequencyBand.BAND_HIGH_MID, 0),
            EqualizerSetting(EqualizerFrequencyBand.BAND_HIGH, 0),
        )
    }

    private fun getDefaultSoundModes(): ImmutableList<OwsSoundModeSetting> {
        return persistentListOf(
            OwsSoundModeSetting(OwsSoundMode.LEGENDARY),
            OwsSoundModeSetting(OwsSoundMode.BASS_BOOST),
            OwsSoundModeSetting(OwsSoundMode.TREBLE_BOOST),
            OwsSoundModeSetting(OwsSoundMode.VOCAL_BOOST),
            OwsSoundModeSetting(OwsSoundMode.CUSTOMIZATION)
        )
    }

    private fun getCurrentSoundMode() {
        val owsSoundMode = savedStateHandle.get<SoaOwsSoundMode>(KEY_OWS_SOUND_MODE)
        if (owsSoundMode != null && owsSoundMode != SoaOwsSoundMode.CUSTOMIZATION) {
            _soundModeSettings.update { settings ->
                settings.map {
                    it.copy(selected = it.soundMode == owsSoundMode.toOwsSoundMode())
                }.toImmutableList()
            }
        } else {
            launch(io) {
                runSuspendCatching {
                    soaRunningInfoFeatures.getOwsSoundModeData()
                }.onSuccess {
                    if (it.mode != SoaOwsSoundMode.CUSTOMIZATION) {
                        _soundModeSettings.update { settings ->
                            settings.map { setting ->
                                setting.copy(selected = setting.soundMode == it.mode.toOwsSoundMode())
                            }.toImmutableList()
                        }
                    } else {
                        _soundModeSettings.update { settings ->
                            settings.map { setting ->
                                setting.copy(selected = setting.soundMode == OwsSoundMode.CUSTOMIZATION)
                            }.toImmutableList()
                        }
                        val equalizerSettings = if (it.equalizers.isNotEmpty()) {
                            it.equalizers.map { eq ->
                                EqualizerSetting(
                                    band = eq.band.toEqualizerFrequencyBand(),
                                    gain = eq.gain
                                )
                            }.toImmutableList()
                        } else {
                            getDefaultEqualizerSettings()
                        }
                        _equalizerSettings.value = equalizerSettings
                    }
                }.onFailure {
                    Timber.w(it, "Failed to get OWS sound mode data")
                }
            }
        }
    }

    fun changeSoundMode(mode: OwsSoundMode) {
        if (mode != OwsSoundMode.CUSTOMIZATION) {
            launch(io) {
                runSuspendCatching {
                    soaRunningInfoFeatures.setOwsSoundModeData(OwsSoundModeData(mode = mode.toSoaOwsSoundMode()))
                }.onSuccess {
                    _soundModeSettings.update { settings ->
                        settings.map {
                            it.copy(selected = it.soundMode == mode)
                        }.toImmutableList()
                    }
                }.onFailure {
                    Timber.w(it, "Failed to set OWS sound mode data")
                }
            }
        } else {
            _soundModeSettings.update { settings ->
                settings.map {
                    it.copy(selected = it.soundMode == OwsSoundMode.CUSTOMIZATION)
                }.toImmutableList()
            }
        }
    }

    fun applyCustomEqualizerSettings(settings: List<EqualizerSetting>) {
        launch(io) {
            runSuspendCatching {
                soaRunningInfoFeatures.setOwsSoundModeData(
                    OwsSoundModeData(
                        mode = SoaOwsSoundMode.CUSTOMIZATION,
                        equalizers = settings.map {
                            SoaEqualizerSetting(
                                it.band.toSoaEqualizerFrequencyBand(),
                                gain = it.gain
                            )
                        }
                    )
                )
            }.onSuccess {
                _soundModeSettings.update { owsSoundModeSettings ->
                    owsSoundModeSettings.map {
                        it.copy(selected = it.soundMode == OwsSoundMode.CUSTOMIZATION)
                    }.toImmutableList()
                }
            }.onFailure {
                Timber.w(it, "Failed to set OWS sound mode data")
            }
        }
    }
}
