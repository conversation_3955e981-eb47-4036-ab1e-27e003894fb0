<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/sv_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?suuntoItemBackgroundColor"
    android:fillViewport="true"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="?suuntoItemBackgroundColor"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?suuntoItemBackgroundColor">

            <TextView
                android:id="@+id/tv_update_available_label"
                style="@style/Body.Small.Bold.AllCaps"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@color/light_grey"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/size_spacing_medium"
                android:paddingVertical="@dimen/size_spacing_smaller"
                android:text="@string/update_available_label"
                android:textColor="@color/near_black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_headset_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/padding"
                android:contentDescription="@null"
                android:src="@drawable/headphones_outline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_update_available_label" />

            <TextView
                android:id="@+id/tv_update_version"
                style="@style/Body.Larger"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/padding"
                app:layout_constraintBottom_toTopOf="@id/tv_update_package_size"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_headset_icon"
                app:layout_constraintTop_toTopOf="@id/iv_headset_icon"
                tools:text="Version: 1.1.1" />

            <TextView
                android:id="@+id/tv_update_package_size"
                style="@style/Body.Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/dark_gray"
                app:layout_constraintBottom_toBottomOf="@id/iv_headset_icon"
                app:layout_constraintStart_toStartOf="@id/tv_update_version"
                app:layout_constraintTop_toBottomOf="@id/tv_update_version"
                tools:text="13mb"/>

            <LinearLayout
                android:id="@+id/contentContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                android:orientation="vertical"
                app:layout_constraintTop_toBottomOf="@id/iv_headset_icon">

                <include
                    android:id="@+id/stateLayout"
                    layout="@layout/include_ota_update_state"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <include
                    android:id="@+id/versionLogLayout"
                    layout="@layout/include_ota_version_log"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <include
                    android:id="@+id/learnMoreLayout"
                    layout="@layout/include_ota_learn_more"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>
