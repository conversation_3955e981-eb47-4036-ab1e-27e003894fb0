<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/headset_button_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/size_spacing_medium">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/headset_button_title"
        style="@style/Body.Large"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/smaller_padding"
        android:textColor="@color/color_body_text_disabled"
        app:layout_constraintEnd_toStartOf="@id/iv_more"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Normal" />

    <TextView
        android:id="@+id/headset_button_summary"
        style="@style/Body.Medium.Gray"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@id/headset_button_title"
        app:layout_constraintStart_toStartOf="@id/headset_button_title"
        app:layout_constraintTop_toBottomOf="@id/headset_button_title"
        tools:text="Balanced frequency band, suitable for various daily scenarios" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/headset_button_summary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/headset_button_title"
        app:layout_constraintTop_toTopOf="@id/headset_button_title"
        app:srcCompat="@drawable/chevron_right" />

</androidx.constraintlayout.widget.ConstraintLayout>
