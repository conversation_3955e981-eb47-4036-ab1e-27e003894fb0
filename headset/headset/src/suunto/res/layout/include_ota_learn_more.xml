<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_old_versions"
        style="@style/Body.Larger"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding"
        android:layout_marginStart="@dimen/padding"
        android:text="@string/old_versions"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintBottom_toTopOf="@id/tv_view_in"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_view_in"
        style="@style/Body.Medium"
        android:textColor="@color/dark_gray"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding"
        android:layout_marginBottom="@dimen/padding"
        android:text="@string/view_in_suunto_com"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_old_versions" />

    <ImageView
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="@dimen/padding"
        android:importantForAccessibility="no"
        android:src="@drawable/chevron_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:tint="@color/near_black" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_divider"
        android:layout_marginTop="18dp"
        android:background="?suuntoDividerColor"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
