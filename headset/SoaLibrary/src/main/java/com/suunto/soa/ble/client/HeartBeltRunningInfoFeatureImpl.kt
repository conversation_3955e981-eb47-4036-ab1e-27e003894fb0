package com.suunto.soa.ble.client

import com.suunto.soa.ble.control.attr.BatteryMode
import com.suunto.soa.ble.control.attr.CommonSwitchState
import com.suunto.soa.ble.control.attr.HeartBeltRunningInfoAttr
import com.suunto.soa.ble.control.attr.HeartBeltSettingAttr
import com.suunto.soa.ble.control.attr.OS
import com.suunto.soa.ble.control.attr.getHeartBeltDeviceUtcAttr
import com.suunto.soa.ble.control.attr.getHeartBeltUserInfoAttr
import com.suunto.soa.ble.device.DeviceType
import com.suunto.soa.ble.exception.CmdException
import com.suunto.soa.ble.response.HeartRunningInfoResponse
import com.suunto.soa.ble.service.SoaConnectivityManager
import com.suunto.soa.data.SoaUserInfo
import kotlinx.coroutines.rx2.await
import java.nio.ByteBuffer
import javax.inject.Inject

class HeartBeltRunningInfoFeatureImpl @Inject constructor(
    manager: SoaConnectivityManager
) : HeartBeltRunningInfoFeature {
    private val connectivity = manager.getConnectivity(DeviceType.HEART_RATE_BELT)
    private suspend fun putSetting(settingsData: ByteArray): Boolean {
        return connectivity.waitForHeartBeltController().flatMap {
            it.pushSetting(settingsData)
        }.map {
            it.isSuccess()
        }.await()
    }

    override suspend fun pushSetting(vararg attrs: HeartBeltSettingAttr): Boolean {
        val requestBytes = getSettingAttrRequestBytes(*attrs)
        return putSetting(requestBytes)
    }

    override suspend fun getRunningInfo(vararg attrs: HeartBeltRunningInfoAttr): HeartRunningInfoResponse {
        return connectivity.waitForHeartBeltController().flatMap {
            it.queryRunningInfo(*attrs)
        }.await()
    }

    override suspend fun setBatteryMode(batteryMode: BatteryMode): Boolean {
        val operation = when (batteryMode) {
            BatteryMode.LOW_POWER -> HeartBeltSettingAttr.ATTR_BATTERY_MODE_LOW_POWER
            BatteryMode.NORMAL -> HeartBeltSettingAttr.ATTR_BATTERY_MODE_NORMAL
        }
        return pushSetting(operation)
    }

    override suspend fun getBatteryMode(): BatteryMode {
        val response = getRunningInfo(HeartBeltRunningInfoAttr.ATTR_DEVICE_BATTERY_MODE)
        if (response.isSuccess() && response.data.getBatteryMode() != null) {
            return response.data.getBatteryMode()!!
        } else {
            throw CmdException("get battery mode failed.")
        }
    }

    override suspend fun setDeviceUTC(timestamp: Long): Boolean {
        val userInfoSetting = getHeartBeltDeviceUtcAttr(timestamp)
        return putSetting(userInfoSetting)
    }

    override suspend fun setUserInfo(userInfo: SoaUserInfo): Boolean {
        val userInfoSetting = getHeartBeltUserInfoAttr(userInfo)
        return putSetting(userInfoSetting)
    }

    override suspend fun setRunningFormMonitoring(commonSwitchState: CommonSwitchState): Boolean {
        return pushSetting(
            if (commonSwitchState == CommonSwitchState.OPEN)
                HeartBeltSettingAttr.ATTR_RUNNING_FORM_MONITORING_ENABLE
            else HeartBeltSettingAttr.ATTR_RUNNING_FORM_MONITORING_DISABLE
        )
    }

    override suspend fun getRunningFormMonitoring(): CommonSwitchState {
        val response = getRunningInfo(HeartBeltRunningInfoAttr.ATTR_RUNNING_FORM_MONITORING)
        val runningFormMonitoring = response.data.getRunningFormMonitoring()
        if (response.isSuccess() && runningFormMonitoring != null) {
            return runningFormMonitoring
        } else {
            throw CmdException("get running form monitoring failed.")
        }
    }

    override suspend fun setOsType(): Boolean {
        return connectivity.waitForHeartBeltController().flatMap {
            it.setOSInfo(byteArrayOf(OS.ANDROID.flag))
        }.map { it.isSuccess() }.await()
    }

    override suspend fun enabledBatteryLevelNotification(onGetBatteryLevel: (Int) -> Unit): Boolean {
        return connectivity.waitForHeartBeltController().flatMap {
            it.enabledBatteryLevelNotification(onGetBatteryLevel)
        }.await()
    }

    private fun getSettingAttrRequestBytes(vararg attrs: HeartBeltSettingAttr): ByteArray {
        var countSize = 0
        attrs.forEach {
            countSize += it.mask.size + 1
        }
        val buffer = ByteBuffer.allocate(countSize)
        attrs.forEach {
            buffer.put((it.mask.size and 0xFF).toByte())
            buffer.put(it.mask)
        }
        return buffer.array()
    }
}
