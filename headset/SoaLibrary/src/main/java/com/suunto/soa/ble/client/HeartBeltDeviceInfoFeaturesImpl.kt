package com.suunto.soa.ble.client

import com.suunto.soa.ble.control.attr.HeartBeltDeviceAttr
import com.suunto.soa.ble.device.DeviceType
import com.suunto.soa.ble.exception.CmdException
import com.suunto.soa.ble.response.HeartBeltDeviceInfoResponse
import com.suunto.soa.ble.response.UpdateFileInfo
import com.suunto.soa.ble.service.SoaConnectivityManager
import kotlinx.coroutines.rx2.await
import javax.inject.Inject

class HeartBeltDeviceInfoFeaturesImpl @Inject constructor(
    connectivityManager: SoaConnectivityManager
) : HeartBeltDeviceInfoFeatures {
    private val connectivity = connectivityManager.getConnectivity(DeviceType.HEART_RATE_BELT)

    override suspend fun getDeviceInfo(vararg attrs: HeartBeltDeviceAttr): HeartBeltDeviceInfoResponse {
        return connectivity.waitForHeartBeltController().flatMap {
            it.queryDeviceInfo(*attrs)
        }.await()
    }

    override suspend fun getUpdateFileInfo(): UpdateFileInfo {
        return connectivity.waitForHeartBeltController().flatMap {
            it.queryUpdateFileInfo()
        }.map {
            it.data
        }.await()
    }

    override suspend fun getProductName(): String {
        val response = getDeviceInfo(HeartBeltDeviceAttr.SOA_ATTR_PRODUCT_NAME)
        if (response.isSuccess() && response.data.getProductName() != null) {
            return response.data.getProductName()!!
        } else {
            throw CmdException("get device product name failed.")
        }
    }

    override suspend fun getAlgorithmVersion(): String {
        val response = getDeviceInfo(HeartBeltDeviceAttr.SOA_ATTR_ALGORITHM_VER)
        if (response.isSuccess() && response.data.getAlgorithmVersion() != null) {
            return response.data.getAlgorithmVersion()!!
        } else {
            throw CmdException("get device algorithm version failed.")
        }
    }
}
