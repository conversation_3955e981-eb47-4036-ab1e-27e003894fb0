package com.suunto.soa.ble.control.attr

enum class DeviceAttr(val mask: Int) {
    SOA_ATTR_ADV_NAME(0x00000001),
    SOA_ATTR_TYPE_VERSION(0x00000002),
    SOA_ATTR_TYPE_BATTERY(0x00000004),
    SOA_ATTR_COMM_PROTOCOL_VER(0x00000008),
    SOA_ATTR_TYPE_VID_AND_PID(0x00000010),
    SOA_ATTR_DEVICE_SERIAL_NUMBER(0x00000020),
    SOA_ATTR_DEVICE_COUNTRY(0x00000040),
    SOA_ATTR_TYPE_EDR_CONNECTION_STATUS(0x00000080),
    SOA_ATTR_TYPE_MANDATORY_UPGRADE_FLAG(0x00000100),
    SOA_ATTR_TYPE_UBOOT_VERSION(0x00000200),
    SOA_ATTR_TYPE_MULTI_BATTERY(0x00000400),
    SOA_ATTR_TYPE_CODEC_TYPE(0x00000800),
    SOA_ATTR_TYPE_POWER_SAVE(0x00001000),
    SOA_ATTR_TYPE_FUNC_KEY(0x00002000),
    SOA_ATTR_TYPE_HOT_WORD(0x00004000),
    SOA_ATTR_TYPE_CHARSET_TYPE(0x00008000),
    SOA_ATTR_PRODUCT_NAME(0x00010000),
    SOA_ATTR_ALGORITHM_VER(0x00020000),
    SOA_ATTR_COLOR_TYPE(0x00040000),
}
