package com.suunto.soa.ble.request

import com.suunto.soa.DataConstants.CMD_REQUEST_RESPONSE
import com.suunto.soa.command.OpCodeSn
import com.suunto.soa.command.Request
import com.suunto.soa.to4Bytes
import com.suunto.soa.utils.toU16Long

internal class DeletePlayListRequest(playListId: String) :
    Request(CMD_REQUEST_RESPONSE, DELETE_PLAYLIST_REQUEST, OpCodeSn.nextOpCodeSn(), getRequestData(playListId)) {

    companion object {

        const val DELETE_PLAYLIST_REQUEST = 0x25.toByte()

        private fun getRequestData(playListId: String): ByteArray {
            return playListId.toU16Long().toInt().to4Bytes()
        }
    }
}
