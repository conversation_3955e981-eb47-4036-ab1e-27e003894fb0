package com.suunto.soa

internal object DataConstants {

    const val CMD_REQUEST_RESPONSE: Byte = 0xC0.toByte()
    const val CMD_REQUEST_NO_RESPONSE: Byte = 0x80.toByte()

    const val CMD_RESPONSE_RESPONSE: Byte = 0x40.toByte()
    const val CMD_RESPONSE_NO_RESPONSE: Byte = 0x00.toByte()

    val STATUS_SUCCESS = ResponseStatus.SUCCESS.code.toByte()
    val START_MASK: ByteArray = byteArrayOf(0xAA.toByte(), 0xBB.toByte(), 0xCC.toByte())
    val END_MASK: ByteArray = byteArrayOf(0xDD.toByte(), 0xEE.toByte(), 0xFF.toByte())

    const val CMD_SIZE: Int = 1
    const val OPCODE_SIZE: Int = 1
    const val CONTENT_SIZE_SIZE: Int = 2
    const val OPCODE_SN_SIZE: Int = 1
    const val STATUS_SIZE: Int = 1
    const val BLE_USE_SIZE: Int = 3

    const val AUTH_VERSION: Byte = 0x01.toByte()

    // headset will only store 26 bytes of the connected phone's name.
    const val DEVICE_NAME_MAX_BYTES = 26

    const val HEADSET_CACHE_PATH = "Headset_cache"
}
