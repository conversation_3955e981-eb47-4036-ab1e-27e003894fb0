package com.suunto.soa.ble.client

import com.suunto.soa.ble.control.attr.BodySenseOperation
import com.suunto.soa.ble.control.attr.CallStatus
import com.suunto.soa.ble.control.attr.CommonSwitchState
import com.suunto.soa.ble.control.attr.DevicesConnectSupport
import com.suunto.soa.ble.control.attr.EqMode
import com.suunto.soa.ble.control.attr.LedStatus
import com.suunto.soa.ble.control.attr.LowLatencyMode
import com.suunto.soa.ble.control.attr.MusicMode
import com.suunto.soa.ble.control.attr.OwsSoundModeData
import com.suunto.soa.ble.control.attr.RunningInfoAttr
import com.suunto.soa.ble.control.attr.SettingAttr
import com.suunto.soa.ble.control.attr.SportsMode
import com.suunto.soa.ble.response.RunningInfoResponse
import com.suunto.soa.ble.response.attr.AttrBluetoothStatus
import com.suunto.soa.ble.response.attr.OtaStatus
import com.suunto.soa.data.MetronomeData
import com.suunto.soa.data.NeckIntervalData
import com.suunto.soa.data.SoaUserInfo
import com.suunto.soa.data.SportStatus

interface RunningInfoFeatures {

    suspend fun pushSetting(vararg attrs: SettingAttr): Boolean

    suspend fun getRunningInfo(vararg attrs: RunningInfoAttr): RunningInfoResponse

    suspend fun setBodySense(bodySenseOperation: BodySenseOperation): Boolean

    suspend fun getBodySense(): BodySenseOperation

    suspend fun setEqMode(eqMode: EqMode): Boolean

    suspend fun getEqMode(): EqMode

    suspend fun setSportsMode(sportsMode: SportsMode): Boolean

    suspend fun getSportsMode(): SportsMode

    suspend fun setSupportDevicesConnect(support: DevicesConnectSupport): Boolean

    suspend fun getSupportDevicesConnect(): DevicesConnectSupport

    suspend fun getConnectedDevices(): List<AttrBluetoothStatus.ConnectedDevice>

    suspend fun setLowLatencyMode(lowLatencyMode: LowLatencyMode): Boolean

    suspend fun getLowLatencyMode(): LowLatencyMode

    suspend fun setNeckReminderInterval(interval: Int): Boolean

    suspend fun getNeckReminderInterval(): NeckIntervalData

    suspend fun getDeviceStatusBeforeOTA(): OtaStatus?

    suspend fun setDeviceMusicMode(musicMode: MusicMode): Boolean

    suspend fun getDeviceMusicMode(): MusicMode?

    suspend fun getCallingStatus(): CallStatus?

    suspend fun getSportStatus(
        isSwimSupported: Boolean,
        isJumpRopeSupported: Boolean,
        isRunningSupported: Boolean
    ): SportStatus

    suspend fun setDeviceUTC(timestamp: Long): Boolean

    suspend fun setUserInfo(userInfo: SoaUserInfo): Boolean

    suspend fun setDeviceLedStatus(isOpen: Boolean): Boolean

    suspend fun getDeviceLedStatus(): LedStatus?

    suspend fun setMetronomeData(metronomeData: MetronomeData): Boolean

    suspend fun getMetronomeData(): MetronomeData

    suspend fun setImmersiveMode(commonSwitchState: CommonSwitchState): Boolean

    suspend fun getImmersiveMode(): CommonSwitchState

    suspend fun setSpatialAudioState(commonSwitchState: CommonSwitchState): Boolean

    suspend fun getSpatialAudioState(): CommonSwitchState

    suspend fun setOwsSoundModeData(owsSoundModeData: OwsSoundModeData): Boolean

    suspend fun getOwsSoundModeData(): OwsSoundModeData
}
