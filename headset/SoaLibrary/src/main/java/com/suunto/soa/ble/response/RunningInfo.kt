package com.suunto.soa.ble.response

import com.suunto.soa.ble.control.attr.CallStatus
import com.suunto.soa.ble.control.attr.CommonSwitchState
import com.suunto.soa.ble.control.attr.JumpRopeStatus
import com.suunto.soa.ble.control.attr.LedStatus
import com.suunto.soa.ble.control.attr.MusicMode
import com.suunto.soa.ble.control.attr.RunningStatus
import com.suunto.soa.ble.control.attr.SwimStatus
import com.suunto.soa.ble.control.attr.isSwimming
import com.suunto.soa.ble.response.attr.AttOwsSoundModeData
import com.suunto.soa.ble.response.attr.AttrAncStatus
import com.suunto.soa.ble.response.attr.AttrAutoPlay
import com.suunto.soa.ble.response.attr.AttrBleMac
import com.suunto.soa.ble.response.attr.AttrBluetoothStatus
import com.suunto.soa.ble.response.attr.AttrBodySenseOperation
import com.suunto.soa.ble.response.attr.AttrCallStatus
import com.suunto.soa.ble.response.attr.AttrDeviceVirtualAddress
import com.suunto.soa.ble.response.attr.AttrDevicesConnect
import com.suunto.soa.ble.response.attr.AttrEdrMac
import com.suunto.soa.ble.response.attr.AttrEnterBtBroadcastMode
import com.suunto.soa.ble.response.attr.AttrEqMode
import com.suunto.soa.ble.response.attr.AttrJumpRopeStatus
import com.suunto.soa.ble.response.attr.AttrLedStatus
import com.suunto.soa.ble.response.attr.AttrLowLatencyMode
import com.suunto.soa.ble.response.attr.AttrMaxMtu
import com.suunto.soa.ble.response.attr.AttrMetronomeData
import com.suunto.soa.ble.response.attr.AttrMusicMode
import com.suunto.soa.ble.response.attr.AttrNeckReminderInterval
import com.suunto.soa.ble.response.attr.AttrOtaStatus
import com.suunto.soa.ble.response.attr.AttrPowerMode
import com.suunto.soa.ble.response.attr.AttrRunningStatus
import com.suunto.soa.ble.response.attr.AttrShutdownTime
import com.suunto.soa.ble.response.attr.AttrSportsModeInd
import com.suunto.soa.ble.response.attr.AttrSwimStatus
import com.suunto.soa.ble.response.attr.AttrTwsStatus
import com.suunto.soa.ble.response.attr.OtaStatus
import com.suunto.soa.data.MetronomeData
import com.suunto.soa.data.NeckIntervalData
import com.suunto.soa.data.SoaSwimType
import com.suunto.soa.toUInt
import com.suunto.soa.utils.DataUtils

class RunningInfo(data: ByteArray) {

    private var shutdownTime: AttrShutdownTime? = null
    private var devicesConnectSupport: AttrDevicesConnect? = null
    private var eqMode: AttrEqMode? = null
    private var sportsModeInd: AttrSportsModeInd? = null
    private var enterBtBroadcastMode: AttrEnterBtBroadcastMode? = null
    private var ancStatus: AttrAncStatus? = null
    private var lowLatencyMode: AttrLowLatencyMode? = null
    private var autoPlay: AttrAutoPlay? = null
    private var bodySenseOperation: AttrBodySenseOperation? = null
    private var edrMac: AttrEdrMac? = null
    private var bleMac: AttrBleMac? = null
    private var maxMtu: AttrMaxMtu? = null
    private var bluetoothStatus: AttrBluetoothStatus? = null
    private var powerMode: AttrPowerMode? = null
    private var twsStatus: AttrTwsStatus? = null
    private var deviceVirtualAddress: AttrDeviceVirtualAddress? = null
    private var neckReminderInterval: AttrNeckReminderInterval? = null
    private var deviceSwimStatus: AttrSwimStatus? = null
    private var deviceOtaStatus: AttrOtaStatus? = null
    private var deviceMusicMode: AttrMusicMode? = null
    private var deviceCallingStatus: AttrCallStatus? = null
    private var jumpRopeStatus: AttrJumpRopeStatus? = null
    private var runningStatus: AttrRunningStatus? = null
    private var ledStatus: AttrLedStatus? = null
    private var metronomeData: AttrMetronomeData? = null
    private var immersiveMode: AttrCommonSwitchState? = null
    private var spatialAudioState: AttrCommonSwitchState? = null
    private var owsSoundModeData: AttOwsSoundModeData? = null

    init {
        DataUtils.tlvDataSplit(data) { attr, value ->
            when (attr[0].toUInt()) {
                0 -> shutdownTime = AttrShutdownTime(value)
                1 -> devicesConnectSupport = AttrDevicesConnect(value)
                2 -> eqMode = AttrEqMode(value)
                3 -> sportsModeInd = AttrSportsModeInd(value)
                4 -> enterBtBroadcastMode = AttrEnterBtBroadcastMode(value)
                5 -> ancStatus = AttrAncStatus(value)
                6 -> lowLatencyMode = AttrLowLatencyMode(value)
                7 -> autoPlay = AttrAutoPlay(value)
                8 -> bodySenseOperation = AttrBodySenseOperation(value)
                9 -> edrMac = AttrEdrMac(value)
                10 -> bleMac = AttrBleMac(value)
                11 -> maxMtu = AttrMaxMtu(value)
                12 -> bluetoothStatus = AttrBluetoothStatus(value)
                13 -> powerMode = AttrPowerMode(value)
                14 -> twsStatus = AttrTwsStatus(value)
                15 -> deviceVirtualAddress = AttrDeviceVirtualAddress(value)
                16 -> neckReminderInterval = AttrNeckReminderInterval(value)
                17 -> deviceSwimStatus = AttrSwimStatus(value)
                18 -> deviceOtaStatus = AttrOtaStatus((value))
                19 -> deviceMusicMode = AttrMusicMode(value)
                20 -> deviceCallingStatus = AttrCallStatus(value)
                21 -> jumpRopeStatus = AttrJumpRopeStatus(value)
                22 -> runningStatus = AttrRunningStatus(value)
                23 -> ledStatus = AttrLedStatus(value)
                24 -> metronomeData = AttrMetronomeData(value)
                25 -> immersiveMode = AttrCommonSwitchState(value)
                26 -> spatialAudioState = AttrCommonSwitchState(value)
                27 -> owsSoundModeData = AttOwsSoundModeData(value)
            }
        }
    }

    fun getShutdownTime() = shutdownTime?.shutdownTime

    fun getDevicesConnectSupport() = devicesConnectSupport?.devicesConnectSupport

    fun getEqMode() = eqMode?.eqMode

    fun getSportsModeInd() = sportsModeInd?.sportsMode

    fun getAncStatus() = ancStatus?.ancStatus

    fun getLowLatencyMode() = lowLatencyMode?.lowLatencyMode

    fun getAutoPlay() = autoPlay?.autoPlay

    fun getBodySenseOperation() = bodySenseOperation?.bodySenseOperation

    fun getBtBroadcastMode() = enterBtBroadcastMode?.enterBtBroadcastMode

    fun getEdrMac() = edrMac?.edrMac

    fun getBleMac() = bleMac?.bleMac

    fun getMaxMtu() = maxMtu?.maxMtu

    fun getBluetoothStatus() = bluetoothStatus

    fun getPowerMode() = powerMode?.powerMode

    fun getTwsStatus() = twsStatus?.twsStatus

    fun getDeviceVirtualAddress() = deviceVirtualAddress?.deviceVirtualAddress

    fun getNeckReminderInterval(): NeckIntervalData? = neckReminderInterval?.intervalData

    fun getSwimStatus(): SwimStatus? = deviceSwimStatus?.swimStatus

    fun getSwimmingType(): SoaSwimType? = deviceSwimStatus?.swimType

    fun isSwimming(): Boolean = deviceSwimStatus?.swimStatus?.isSwimming() ?: false

    fun getOtaStatus(): OtaStatus? = deviceOtaStatus?.otaStatus

    fun getMusicMode(): MusicMode? = deviceMusicMode?.musicMode

    fun getCallingStatus(): CallStatus? = deviceCallingStatus?.callStatus

    fun getJumpRopeStatus(): JumpRopeStatus? = jumpRopeStatus?.jumpRopeStatus

    fun getRunningStatus(): RunningStatus? = runningStatus?.runningStatus

    fun getLedStatus(): LedStatus? = ledStatus?.ledStatus

    fun getMetronomeData(): MetronomeData? = metronomeData?.metronomeData

    fun getImmersiveMode(): CommonSwitchState? = immersiveMode?.commonSwitchState

    fun getSpatialAudioState(): CommonSwitchState? = spatialAudioState?.commonSwitchState

    fun getOwsSoundModeData() = owsSoundModeData?.owsSoundModeData
}
