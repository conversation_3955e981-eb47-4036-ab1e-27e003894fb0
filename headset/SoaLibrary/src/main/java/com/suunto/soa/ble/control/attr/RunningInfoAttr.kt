package com.suunto.soa.ble.control.attr

enum class RunningInfoAttr(val mask: Int) {

    ATTR_DEVICE_SHUTDOWN_TIME(0x00000001),
    ATTR_DEVICE_2DEVICES_CONNECT(0x00000002),
    ATTR_DEVICE_EQ_MODE(0x00000004),
    ATTR_SPORTS_MODE_SWITCH(0x00000008),
    ATTR_DEVICE_ENTER_BT_BROADCAST_MODE(0x00000010),
    ATTR_TYPE_GET_ANC_MODE(0x00000020),
    ATTR_DEVICE_LOW_LATENCY_MODE(0x00000040),
    ATTR_DEVICE_AUTO_PLAY(0x00000080),
    ATTR_DEVICE_BODY_SENSE_OPERATION_SWITCH(0x00000100),
    ATTR_EDR_MAC(0x00000200),
    ATTR_BLE_MAC(0x00000400),
    ATTR_COMMUNICATION_MAX_MTU(0x00000800),
    ATTR_CLASSIC_BLUETOOTH_STATUS(0x00001000),
    ATTR_GET_POWER_MODE(0x00002000),
    ATTR_TWS_STATUS(0x00004000),
    ATTR_DEVICE_VIRTUAL_ADDRESS(0x00008000),
    ATTR_NECK_REMINDER_INTERVAL(0x00010000),
    ATTR_DEVICE_SWIM_STATUS(0x00020000),
    ATTR_DEVICE_OTA_STATUS(0x00040000),
    ATTR_DEVICE_MUSIC_MODE(0x00080000),
    ATTR_DEVICE_CALL_STATUS(0x00100000),
    ATTR_DEVICE_ROPE_STATUS(0x00200000),
    ATTR_DEVICE_RUNNING_STATUS(0x00400000),
    ATTR_DEVICE_LED_STATUS(0x00800000),
    ATTR_DEVICE_METRONOME_STATE(0x01000000),
    ATTR_DEVICE_IMMERSIVE_MODE(0x02000000),
    ATTR_DEVICE_SPATIAL_AUDIO_STATE(0x04000000),
    ATTR_DEVICE_SOUND_MODE(0x08000000),
}

enum class SwimStatus(val flag: Byte) {
    IDLE(0),
    STOP(1),
    INITIALIZATION(2),
    START(3),
    PAUSE(4),
}

fun SwimStatus.isSwimming(): Boolean {
    return this == SwimStatus.START || this == SwimStatus.PAUSE
}

enum class JumpRopeStatus(val flag: Byte) {
    STOP(0),
    START(1),
    NOT_SUPPORT(0xFF.toByte()),
}

fun JumpRopeStatus.isSupported(): Boolean {
    return this != JumpRopeStatus.NOT_SUPPORT
}

fun JumpRopeStatus.isRoping(): Boolean {
    return this == JumpRopeStatus.START
}

enum class RunningStatus(val flag: Byte) {
    STOP(0),
    START(1),
    PAUSE(2),
    NOT_SUPPORT(0xFF.toByte()),
}

fun RunningStatus.isRunning(): Boolean {
    return this == RunningStatus.START || this == RunningStatus.PAUSE
}

fun RunningStatus.isSupported(): Boolean {
    return this != RunningStatus.NOT_SUPPORT
}
