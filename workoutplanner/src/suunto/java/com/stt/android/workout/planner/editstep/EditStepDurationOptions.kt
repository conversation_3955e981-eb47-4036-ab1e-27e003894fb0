package com.stt.android.workout.planner.editstep

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.darkGreyText
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.isEmpty
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.WorkoutPlannerLimits
import com.stt.android.workout.planner.common.DurationVisualTransformation
import com.stt.android.workout.planner.common.TextFieldFormattingHelper
import com.stt.android.R as BaseR

/**
 * Inputs for editing step duration. Three radio buttons control the type of the
 * duration for the step (time, distance, or lap button press). Time and distance
 * have additional text inputs for entering values.
 */
@Composable
fun EditStepDurationOptions(
    editMode: EditStepDurationMode,
    onEditModeChange: (EditStepDurationMode) -> Unit,
    targetDistance: TextFieldValue, // kilometers or miles
    onTargetDistanceChanged: (TextFieldValue) -> Unit,
    targetTime: TextFieldValue,
    onTargetTimeChanged: (TextFieldValue) -> Unit,
    unit: MeasurementUnit,
    modifier: Modifier = Modifier
) {
    val maxDistanceValue = remember(unit) {
        unit.toDistanceUnit(WorkoutPlannerLimits.maxDistance).toInt().toDouble() // Round down
    }

    Column(modifier) {
        EditTargetColumn(
            selected = editMode == EditStepDurationMode.TIME,
            onClick = { onEditModeChange(EditStepDurationMode.TIME) },
            title = stringResource(BaseR.string.workout_planner_step_duration_time),
        ) {
            EditTargetInput(
                modifier = Modifier.fillMaxWidth(),
                value = targetTime,
                onValueChange = onTargetTimeChanged,
                valueValidator = {
                    it.isEmpty() || TextFieldFormattingHelper.isValidInteger(
                        value = it,
                        range = 0..999999 // TODO: proper range
                    )
                },
                visualTransformation = DurationVisualTransformation(SpanStyle(color = MaterialTheme.colors.darkGreyText)),
                unitText = stringResource(R.string.workout_planner_step_duration_time_unit),
            )
        }

        EditTargetColumn(
            selected = editMode == EditStepDurationMode.DISTANCE,
            onClick = { onEditModeChange(EditStepDurationMode.DISTANCE) },
            title = stringResource(BaseR.string.workout_planner_step_duration_distance),
        ) {
            EditTargetInput(
                modifier = Modifier.fillMaxWidth(),
                value = targetDistance,
                onValueChange = onTargetDistanceChanged,
                valueValidator = {
                    it.isEmpty() || TextFieldFormattingHelper.isValidFloatingPointValue(
                        value = it,
                        maxDecimals = 2,
                        maximumValue = maxDistanceValue
                    )
                },
                visualTransformation = VisualTransformation.None,
                unitText = stringResource(unit.distanceUnit),
            )
        }

        EditTargetColumn(
            selected = editMode == EditStepDurationMode.LAP_BUTTON_PRESS,
            onClick = { onEditModeChange(EditStepDurationMode.LAP_BUTTON_PRESS) },
            title = stringResource(BaseR.string.workout_planner_step_duration_lap_button_press),
        ) {
            Row(
                modifier = Modifier
                    .background(color = MaterialTheme.colors.background)
                    .padding(MaterialTheme.spacing.medium)
            ) {
                Icon(
                    modifier = Modifier
                        .background(
                            color = MaterialTheme.colors.surface,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(MaterialTheme.spacing.small),
                    painter = painterResource(BaseR.drawable.ic_info_outline),
                    contentDescription = null
                )
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
                Text(stringResource(R.string.workout_planner_step_duration_lap_button_press_description))
            }
        }
    }
}

@Composable
@Preview
private fun EditStepDurationOptionsPreview() {
    AppTheme {
        Surface {
            var editMode by remember { mutableStateOf(EditStepDurationMode.TIME) }
            var targetTime by remember { mutableStateOf(TextFieldValue("0123")) }
            var targetDistance by remember { mutableStateOf(TextFieldValue("8.5")) }

            EditStepDurationOptions(
                editMode = editMode,
                onEditModeChange = { editMode = it },
                targetDistance = targetDistance,
                onTargetDistanceChanged = { targetDistance = it },
                targetTime = targetTime,
                onTargetTimeChanged = { targetTime = it },
                unit = MeasurementUnit.METRIC,
            )
        }
    }
}
