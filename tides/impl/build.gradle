plugins {
    id "stt.android.plugin.library"
    id "stt.android.plugin.compose"
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.moshi"
}

android {
    namespace = "com.stt.android.tides.impl"
    buildFeatures.buildConfig = true
}

dependencies {
    api project(Deps.tidesApi)

    implementation project(Deps.appBase)
    implementation project(Deps.core)
    implementation project(Deps.maps)
    implementation project(Deps.mapsProviderMapbox)
    implementation project(Deps.remoteBase)
    implementation project(Deps.remoteConfigApi)

    implementation libs.soy.algorithms

    implementation libs.retrofit
    implementation libs.retrofit.moshi
}
