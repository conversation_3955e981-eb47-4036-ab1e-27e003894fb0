package com.stt.android.tides.impl.screen

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMapView
import com.stt.android.maps.extensions.awaitMap
import com.stt.android.maps.mapbox.MapboxMapsProvider
import com.stt.android.tides.api.LoadTidesUseCase
import com.suunto.algorithms.geo.LatLng
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import javax.inject.Inject

@AndroidEntryPoint
internal class TidesDebugActivity : AppCompatActivity() {
    @Inject
    lateinit var loadTidesUseCase: LoadTidesUseCase

    override fun onCreate(savedInstanceState: Bundle?) {
        enableEdgeToEdge()
        super.onCreate(savedInstanceState)

        setContentWithM3Theme {
            val lifecycleOwner = LocalLifecycleOwner.current
            val localDensity = LocalDensity.current
            val mapViewHolder = remember { mutableStateOf<SuuntoMapView?>(null) }
            var tidesInfo by remember { mutableStateOf("") }

            Scaffold { contentPadding ->
                Box(
                    modifier = Modifier
                        .fillMaxSize(),
                ) {
                    AndroidView(
                        factory = { context ->
                            val options = SuuntoMapOptions().apply {
                                mapProvider(MapboxMapsProvider.NAME)
                            }
                            SuuntoMapView(context, options)
                                .also { mapView ->
                                    mapViewHolder.value = mapView
                                    mapView.getMapAsync { suuntoMap ->
                                        suuntoMap.removeScaleBar()

                                        with(localDensity) {
                                            suuntoMap.setPadding(
                                                left = (contentPadding.calculateLeftPadding(LayoutDirection.Ltr) + 16.dp).roundToPx(),
                                                right = (contentPadding.calculateRightPadding(LayoutDirection.Ltr) + 16.dp).roundToPx(),
                                                top = (contentPadding.calculateTopPadding() + 16.dp).roundToPx(),
                                                bottom = (contentPadding.calculateBottomPadding() + 64.dp).roundToPx(),
                                            )
                                        }
                                    }
                                }
                        },
                        modifier = Modifier.fillMaxSize(),
                    )

                    Text(
                        text = tidesInfo,
                        modifier = Modifier.padding(contentPadding),
                    )

                    Icon(
                        painter = SuuntoIcons.MapPin.asPainter(),
                        contentDescription = null,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .size(MaterialTheme.iconSizes.medium)
                            .offset(y = -MaterialTheme.iconSizes.medium.div(2.0F)),
                    )

                    Row(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(
                                start = MaterialTheme.spacing.medium,
                                end = MaterialTheme.spacing.medium,
                                bottom = MaterialTheme.spacing.medium + contentPadding.calculateBottomPadding(),
                            ),
                        horizontalArrangement = Arrangement.spacedBy(
                            space = MaterialTheme.spacing.medium,
                            alignment = Alignment.CenterHorizontally,
                        ),
                    ) {
                        Button(
                            onClick = {
                                lifecycleScope.launch {
                                    tidesInfo = "Loading..."

                                    tidesInfo = mapViewHolder.value
                                        ?.loadTides(LoadTidesUseCase.RequestType.AUTO)
                                        .orEmpty()
                                }
                            },
                            modifier = Modifier.weight(1.0F),
                        ) {
                            Text("Load tides (Auto)")
                        }

                        Button(
                            onClick = {
                                lifecycleScope.launch {
                                    tidesInfo = "Loading..."

                                    tidesInfo = mapViewHolder.value
                                        ?.loadTides(LoadTidesUseCase.RequestType.MANUAL)
                                        .orEmpty()
                                }
                            },
                            modifier = Modifier.weight(1.0F),
                        ) {
                            Text("Load tides (Manual)")
                        }
                    }
                }
            }

            DisposableEffect(lifecycleOwner) {
                val observer = LifecycleEventObserver { _, event ->
                    when (event) {
                        Lifecycle.Event.ON_CREATE -> mapViewHolder.value?.onCreate(null)
                        Lifecycle.Event.ON_START -> mapViewHolder.value?.onStart()
                        Lifecycle.Event.ON_RESUME -> mapViewHolder.value?.onResume()
                        Lifecycle.Event.ON_PAUSE -> mapViewHolder.value?.onPause()
                        Lifecycle.Event.ON_STOP -> mapViewHolder.value?.onStop()
                        Lifecycle.Event.ON_DESTROY -> mapViewHolder.value?.onDestroy()
                        Lifecycle.Event.ON_ANY -> Unit
                    }
                }
                lifecycleOwner.lifecycle.addObserver(observer)

                onDispose {
                    mapViewHolder.value?.onDestroy()
                    mapViewHolder.value = null
                    lifecycleOwner.lifecycle.removeObserver(observer)
                }
            }
        }
    }

    private suspend fun SuuntoMapView.loadTides(requestType: LoadTidesUseCase.RequestType): String? {
        val suuntoMap = awaitMap()
        val center = suuntoMap.getCameraPosition()?.target ?: return null
        val tides = loadTidesUseCase.invoke(
            params = LoadTidesUseCase.Params(
                requestType = requestType,
                date = LocalDate.now(),
                latLng = LatLng(
                    latitude = center.latitude,
                    longitude = center.longitude,
                ),
            ),
        ) ?: return null
        return buildString {
            appendLine("Request location: (${center.latitude}, ${center.longitude})")
            appendLine("Response location: (${tides.latLng.latitude}, ${tides.latLng.longitude})")
            appendLine("First height: ${tides.heights.first().heightInMeters} @ ${tides.heights.first().timestampInMillis.toZonedDateTime()}")
            appendLine("Last height: ${tides.heights.last().heightInMeters} @ ${tides.heights.last().timestampInMillis.toZonedDateTime()}")
        }
    }

    companion object {
        fun startIntent(context: Context): Intent = Intent(context, TidesDebugActivity::class.java)

        private fun Long.toZonedDateTime(): ZonedDateTime =
            Instant.ofEpochMilli(this).atZone(ZoneId.of("UTC"))
    }
}
