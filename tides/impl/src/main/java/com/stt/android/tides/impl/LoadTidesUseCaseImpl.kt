package com.stt.android.tides.impl

import com.stt.android.core.utils.TimeProvider
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.remoteconfig.api.RemoteConfig
import com.stt.android.tides.api.LoadTidesUseCase
import com.stt.android.tides.api.Tides
import com.stt.android.tides.impl.repository.TidesRepository
import com.suunto.algorithms.data.Length
import com.suunto.algorithms.geo.DistanceCalculator
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration

internal class LoadTidesUseCaseImpl @Inject constructor(
    private val timeProvider: TimeProvider,
    private val tidesRepository: TidesRepository,
    private val remoteConfig: RemoteConfig,
) : LoadTidesUseCase {
    override suspend fun invoke(params: LoadTidesUseCase.Params): Tides? =
        getValidCache(params) ?: fetch(params)

    private fun getValidCache(params: LoadTidesUseCase.Params): Tides? {
        val lastRequestParams = tidesRepository.lastRequestParams() ?: return null
        val lastTides = tidesRepository.lastTides() ?: return null
        val latestTideTimestamp = lastTides.heights.maxOfOrNull(Tides.Height::timestampInMillis) ?: return null
        if (latestTideTimestamp - timeProvider.currentTimeMillis() <= params.availabilityThreshold().inWholeMilliseconds) {
            return null
        }

        val distanceThreshold = params.distanceThreshold()
        if (DistanceCalculator.distanceBetween(params.latLng, lastTides.latLng) > distanceThreshold &&
            DistanceCalculator.distanceBetween(params.latLng, lastRequestParams.latLng) > distanceThreshold) {
            return null
        }

        return lastTides
    }

    private fun LoadTidesUseCase.Params.availabilityThreshold(): Duration = when (requestType) {
        LoadTidesUseCase.RequestType.AUTO -> remoteConfig.getTidesQueryParameters().autoSyncThresholds.maxAge
        LoadTidesUseCase.RequestType.MANUAL -> remoteConfig.getTidesQueryParameters().manualSyncThresholds.maxAge
    }

    private fun LoadTidesUseCase.Params.distanceThreshold(): Length = when (requestType) {
        LoadTidesUseCase.RequestType.AUTO -> remoteConfig.getTidesQueryParameters().autoSyncThresholds.maxDistance
        LoadTidesUseCase.RequestType.MANUAL -> remoteConfig.getTidesQueryParameters().manualSyncThresholds.maxDistance
    }

    private suspend fun fetch(params: LoadTidesUseCase.Params): Tides? = runSuspendCatching {
        tidesRepository.fetch(params)
    }.getOrElse { e ->
        Timber.w(e, "Failed to fetch tides")
        null
    }
}
