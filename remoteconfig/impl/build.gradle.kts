plugins {
    id("stt.android.plugin.library")
    id("stt.android.plugin.hilt")
    id("stt.android.plugin.moshi")
}

android {
    namespace = "com.stt.android.remoteconfig.impl"
    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    api(project(":remoteconfigapi"))

    implementation(project(":appbase"))
    implementation(project(":remotebase"))
    implementation(project(":utils"))

    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.config)

    implementation(libs.retrofit)
    implementation(libs.retrofit.moshi)
}
