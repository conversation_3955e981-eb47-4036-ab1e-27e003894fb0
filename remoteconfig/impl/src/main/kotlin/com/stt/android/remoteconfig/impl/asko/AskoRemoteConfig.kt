package com.stt.android.remoteconfig.impl.asko

import android.content.SharedPreferences
import android.os.Build
import androidx.annotation.VisibleForTesting
import androidx.core.content.edit
import com.stt.android.controllers.UserSettingsController
import com.stt.android.di.AskoRemoteConfigPreferences
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.di.VersionCode
import com.stt.android.remoteconfig.api.GraphhopperBaseUrl
import com.stt.android.remoteconfig.api.TidesQueryParameters
import com.stt.android.remoteconfig.impl.asko.AskoRemoteConfigResponse.Companion.KEY_AI_PLANNER_ENABLED
import com.stt.android.remoteconfig.impl.asko.AskoRemoteConfigResponse.Companion.KEY_EMARSYS_ENABLED
import com.stt.android.remoteconfig.impl.asko.AskoRemoteConfigResponse.Companion.KEY_GRAPHHOPPER_URL
import com.stt.android.remoteconfig.impl.asko.AskoRemoteConfigResponse.Companion.KEY_TIDES_PARAMETERS
import com.stt.android.utils.FlavorUtils
import com.stt.android.utils.STTConstants
import timber.log.Timber
import java.util.Locale
import java.util.Random
import javax.inject.Inject

internal class AskoRemoteConfig @Inject constructor(
    @param:AskoRemoteConfigPreferences private val preferences: SharedPreferences,
    @param:SuuntoSharedPrefs private val suuntoPreferences: SharedPreferences,
    private val askoRemoteConfigApi: AskoRemoteConfigApi,
    private val askoRemoteConfigDefaults: AskoRemoteConfigDefaults,
    private val userSettingsController: UserSettingsController,
    @param:VersionCode private val currentVersion: Int,
) {
    val isEmarsysEnabled: Boolean
        get() = requireValue(KEY_EMARSYS_ENABLED) { emarsysEnabled }

    val graphhopperBaseUrl: GraphhopperBaseUrl
        get() = requireValue(KEY_GRAPHHOPPER_URL) { graphhopperBaseUrl }.toGraphhopperBaseUrl()

    val isAiPlannerEnabled: Boolean
        get() = requireValue(KEY_AI_PLANNER_ENABLED) { aiPlannerEnabled }

    val tidesQueryParameters: TidesQueryParameters
        get() = requireValue(KEY_TIDES_PARAMETERS) { tidesQueryParameters }.toTidesQueryParameters()

    // current runtime configuration response
    @Volatile
    private var askoRemoteConfigResponse: AskoRemoteConfigResponse? = null

    suspend fun refresh() {
        askoRemoteConfigResponse = askoRemoteConfigApi.fetchRemoteConfig()
    }

    private fun <T : Any> requireValue(
        key: String,
        getConditions: AskoRemoteConfigResponse.() -> AskoRemoteConfigValueConditions<T>?,
    ): T = getValue(key, getConditions)
        ?: askoRemoteConfigDefaults.getDefault(getConditions)
        ?: throw IllegalArgumentException("Missing value for key: $key")

    private fun <T : Any> getValue(
        key: String,
        getConditions: AskoRemoteConfigResponse.() -> AskoRemoteConfigValueConditions<T>?,
    ): T? = askoRemoteConfigResponse?.getConditions()
        ?.valueConditions
        ?.let { valueConditions ->
            val matchedValueCondition = findMatchingCondition(key, valueConditions)
            when {
                matchedValueCondition != null -> matchedValueCondition.value
                valueConditions.isNotEmpty() -> valueConditions.last().value
                else -> null
            }
        }

    private fun <T> findMatchingCondition(
        key: String,
        valueConditions: List<AskoValueConditions<T>>,
    ): AskoValueConditions<T>? = valueConditions.firstOrNull { condition ->
        matchAppVersion(
            minAppVersion = condition.minAppVersion,
            maxAppVersion = condition.maxAppVersion,
        ) && matchOsVersion(
            sdkInt = Build.VERSION.SDK_INT,
            minOsVersion = condition.minOSVersion,
            maxOsVersion = condition.maxOSVersion,
        ) && matchProduct(
            product = condition.product,
        ) && matchPercentile(
            key = key,
            percentile = condition.percentile,
        ) && matchCountry(
            countries = condition.countries,
        ) && matchWatchModelVersion(
            watchModels = condition.watchModels,
        )
    }

    @VisibleForTesting
    internal fun matchWatchModelVersion(watchModels: List<String>?): Boolean {
        if (watchModels == null) {
            return true
        }
        if (watchModels.isEmpty()) {
            return false
        }
        val pairedWatchModel = suuntoPreferences
            .getString(STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL, null)
            ?.takeUnless(String::isBlank)
            ?.lowercase(Locale.US)
            ?: return false
        return watchModels.any { it.lowercase(Locale.US) == pairedWatchModel }
    }

    @VisibleForTesting
    internal fun matchCountry(countries: List<String>?): Boolean {
        if (countries == null) {
            return true
        }
        if (countries.isEmpty()) {
            return false
        }
        val country = userSettingsController.settings
            .country
            .takeUnless(String::isBlank)
            ?.lowercase(Locale.US)
            ?: return false
        return countries.any { it.lowercase(Locale.US) == country }
    }

    @VisibleForTesting
    internal fun matchPercentile(key: String, percentile: Float?): Boolean {
        // null or invalid percentile is always matching (ignored)
        if (percentile == null || percentile < 0.0F || percentile > 1.0F) {
            return true
        }

        val rollResultKey = "${STTConstants.AskoRemoteConfigPreferences.KEY_PERCENTILE_ROLL_RESULT}_$key"
        val rollResult = if (preferences.contains(rollResultKey)) {
            preferences.getFloat(rollResultKey, 1.0F)
        } else {
            rollPercentile().also {
                preferences.edit { putFloat(rollResultKey, it) }
            }
        }
        return rollResult <= percentile
    }

    @VisibleForTesting
    internal fun rollPercentile(): Float = Random().nextFloat()

    @VisibleForTesting
    internal fun matchOsVersion(
        sdkInt: Int,
        minOsVersion: String?,
        maxOsVersion: String?,
    ): Boolean = (minOsVersion == null && maxOsVersion == null) ||
        try {
            (minOsVersion == null || sdkInt >= minOsVersion.toInt()) &&
                (maxOsVersion == null || sdkInt <= maxOsVersion.toInt())
        } catch (e: Exception) {
            Timber.Forest.w(e, "Error parsing OS version number")
            false
        }

    @VisibleForTesting
    internal fun matchAppVersion(
        minAppVersion: String?,
        maxAppVersion: String?,
    ): Boolean = (minAppVersion == null && maxAppVersion == null) ||
        try {
            (minAppVersion == null || currentVersion >= minAppVersion.toInt()) &&
                (maxAppVersion == null || currentVersion <= maxAppVersion.toInt())
        } catch (e: Exception) {
            Timber.Forest.w(e, "Error parsing app version numbers")
            false
        }

    private fun matchProduct(product: String?): Boolean {
        return product == null ||
            (FlavorUtils.isSuuntoApp && product == ASKO_REMOTE_PRODUCT_SUUNTO) ||
            (FlavorUtils.isSportsTracker && product == ASKO_REMOTE_PRODUCT_SPORTSTRACKER)
        // todo add atomic support when needed
    }
}
