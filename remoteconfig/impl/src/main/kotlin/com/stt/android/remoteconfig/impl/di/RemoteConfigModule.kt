package com.stt.android.remoteconfig.impl.di

import android.app.Application
import android.content.Context
import android.net.ConnectivityManager
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import com.squareup.moshi.Moshi
import com.stt.android.BuildConfig
import com.stt.android.remote.RemoteConfigBaseUrl
import com.stt.android.remote.RemoteConfigCacheDirectory
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import com.stt.android.remoteconfig.api.RemoteConfig
import com.stt.android.remoteconfig.impl.R
import com.stt.android.remoteconfig.impl.RemoteConfigImpl
import com.stt.android.remoteconfig.impl.asko.AskoRemoteConfigRestApi
import com.stt.android.utils.STTConstants
import com.stt.android.utils.isMainProcess
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.Cache
import okhttp3.OkHttpClient
import java.io.File
import javax.inject.Singleton
import kotlin.time.Duration.Companion.hours

@Module
@InstallIn(SingletonComponent::class)
internal abstract class RemoteConfigModule {
    @Binds
    abstract fun bindRemoteConfig(impl: RemoteConfigImpl): RemoteConfig

    companion object {
        private const val CACHE_SIZE: Long = 1024 * 1024 * 2

        @Singleton
        @Provides
        fun provideFirebaseRemoteConfig(): FirebaseRemoteConfig {
            val remoteConfig = FirebaseRemoteConfig.getInstance()
            val configSettings =
                FirebaseRemoteConfigSettings.Builder()
                    .setMinimumFetchIntervalInSeconds(if (BuildConfig.DEBUG) 0 else 6.hours.inWholeSeconds)
                    .build()
            remoteConfig.setConfigSettingsAsync(configSettings)
            remoteConfig.setDefaultsAsync(R.xml.default_remote_config)

            return remoteConfig
        }

        @Provides
        @RemoteConfigCacheDirectory
        fun provideRemoteConfigCacheName(application: Application): String = if (application.isMainProcess) {
            "remoteConfigCache_"
        } else {
            "remoteConfigConnectivityCache_"
        }

        @Provides
        fun provideAskoRemoteConfigRestApi(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @RemoteConfigBaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            moshi: Moshi,
            @RemoteConfigCacheDirectory cacheDirectoryName: String,
            application: Application,
        ): AskoRemoteConfigRestApi {
            val connectivityManager = application
                .getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val okHttpConfig =
                if (STTConstants.DEBUG) {
                    BrandOkHttpConfigFactory.getResponseSourceLoggingOfflineCachingOkHttpConfig(
                        userAgent = userAgent,
                        connectivityManager = connectivityManager,
                    )
                } else {
                    BrandOkHttpConfigFactory.getOfflineCachingOkHttpConfig(
                        userAgent = userAgent,
                        connectivityManager = connectivityManager,
                    )
                }
            return RestApiFactory.buildRestApi(
                sharedClient = sharedClient,
                baseUrl = baseUrl,
                restApi = AskoRemoteConfigRestApi::class.java,
                okHttpConfig = okHttpConfig,
                moshi = moshi,
                cache = Cache(File(application.cacheDir, cacheDirectoryName), CACHE_SIZE),
            )
        }
    }
}
