package com.stt.android.remoteconfig.impl.firebase

import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import dagger.Lazy
import kotlinx.coroutines.tasks.await
import javax.inject.Inject

internal class FirebaseRemoteConfigWrapper @Inject constructor(
    private val firebaseRemoteConfigLazy: Lazy<FirebaseRemoteConfig>, // Lazy injection otherwise it might crash on watch service process.
) {
    private val firebaseRemoteConfig: FirebaseRemoteConfig get() = firebaseRemoteConfigLazy.get()

    suspend fun refresh() {
        firebaseRemoteConfig.fetch().await()
        firebaseRemoteConfig.activate().await()
    }

    fun getGraphHopperKey(): String = firebaseRemoteConfig.getString(GRAPH_HOPPER_KEY)

    private companion object {
        const val GRAPH_HOPPER_KEY: String = "graph_hopper_key"
    }
}
