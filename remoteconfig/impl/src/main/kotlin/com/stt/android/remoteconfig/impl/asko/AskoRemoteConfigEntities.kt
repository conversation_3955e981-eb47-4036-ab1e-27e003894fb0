package com.stt.android.remoteconfig.impl.asko

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.stt.android.remoteconfig.api.GraphhopperBaseUrl
import com.stt.android.remoteconfig.api.TidesQueryParameters
import com.suunto.algorithms.data.Length.Companion.meters
import kotlin.time.Duration.Companion.seconds

@JsonClass(generateAdapter = true)
internal data class AskoRemoteConfigResponse(
    @param:<PERSON><PERSON>(name = KEY_EMARSYS_ENABLED)
    val emarsysEnabled: AskoRemoteConfigValueConditions<Boolean>?,

    @param:<PERSON><PERSON>(name = KEY_GRAPHHOPPER_URL)
    val graphhopperBaseUrl: AskoRemoteConfigValueConditions<AskoGraphhopperBaseUrl>?,

    @param:<PERSON>son(name = KEY_AI_PLANNER_ENABLED)
    val aiPlannerEnabled: AskoRemoteConfigValueConditions<Boolean>?,

    @param:<PERSON><PERSON>(name = KEY_TIDES_PARAMETERS)
    val tidesQueryParameters: AskoRemoteConfigValueConditions<AskoTidesParameters>?,
) {
    companion object {
        const val KEY_EMARSYS_ENABLED = "androidEmarsysEnabled"
        const val KEY_GRAPHHOPPER_URL = "graphhopperUrl"
        const val KEY_AI_PLANNER_ENABLED = "androidAiPlannerEnabled"
        const val KEY_TIDES_PARAMETERS = "tidesParameters"
    }
}

@JsonClass(generateAdapter = true)
internal data class AskoRemoteConfigValueConditions<T>(
    @param:Json(name = "values")
    val valueConditions: List<AskoValueConditions<T>>,
)

@JsonClass(generateAdapter = true)
internal data class AskoValueConditions<T>(
    @param:Json(name = "product")
    val product: String? = null,

    @param:Json(name = "minAppVersion")
    val minAppVersion: String? = null,

    @param:Json(name = "maxAppVersion")
    val maxAppVersion: String? = null,

    @param:Json(name = "minOSVersion")
    val minOSVersion: String? = null,

    @param:Json(name = "maxOSVersion")
    val maxOSVersion: String? = null,

    @param:Json(name = "percentile")
    val percentile: Float? = null,

    @param:Json(name = "countries")
    val countries: List<String>? = null,

    @param:Json(name = "watchModels")
    val watchModels: List<String>? = null,

    @param:Json(name = "value")
    val value: T?,
)

@JsonClass(generateAdapter = true)
internal data class AskoGraphhopperBaseUrl(
    @param:Json(name = "url")
    val url: String,

    @param:Json(name = "alternatives")
    val alternatives: List<String>,
) {
    fun toGraphhopperBaseUrl(): GraphhopperBaseUrl = GraphhopperBaseUrl(
        url = url,
        alternatives = alternatives,
    )
}

@JsonClass(generateAdapter = true)
internal data class AskoTidesParameters(
    @param:Json(name = "autoSyncThresholds")
    val autoSyncThresholds: AskoTidesThresholds,

    @param:Json(name = "manualSyncThresholds")
    val manualSyncThresholds: AskoTidesThresholds,
) {
    fun toTidesQueryParameters(): TidesQueryParameters = TidesQueryParameters(
        autoSyncThresholds = autoSyncThresholds.toThresholds(),
        manualSyncThresholds = manualSyncThresholds.toThresholds(),
    )
}

@JsonClass(generateAdapter = true)
internal data class AskoTidesThresholds(
    @param:Json(name = "maxAge")
    val maxAge: Int,

    @param:Json(name = "maxDistance")
    val maxDistance: Int,
) {
    fun toThresholds(): TidesQueryParameters.Thresholds = TidesQueryParameters.Thresholds(
        maxAge = maxAge.seconds,
        maxDistance = maxDistance.meters,
    )
}

internal const val ASKO_REMOTE_PRODUCT_SUUNTO = "suunto"
internal const val ASKO_REMOTE_PRODUCT_SPORTSTRACKER = "sportstracker"
