package com.stt.android.suuntoplusstore.remote

import com.squareup.moshi.Moshi
import com.stt.android.device.domain.suuntoplusfeature.RemoveFeatureFromLibraryUseCase
import com.stt.android.device.domain.suuntoplusguide.TrainingPlanRemoteDataSource
import com.stt.android.domain.suuntoplus.SuuntoPlusStoreSingleGuideInterface
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import com.stt.android.suuntoplusstore.analytics.SuuntoPlusStoreAnalytics
import com.stt.android.suuntoplusstore.analytics.SuuntoPlusStoreAnalyticsTracker
import com.stt.android.suuntoplusstore.features.domain.usecases.RemoveFeatureFromLibraryUseCaseImpl
import com.stt.android.suuntoplusstore.guides.domain.usecases.FetchSuuntoPlusStoreSingleGuideUseCase
import com.stt.android.suuntoplusstore.remote.backend.SuuntoPlusStoreRestApi
import com.stt.android.suuntoplusstore.remote.backend.TrainingPlanRestApi
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityRetainedComponent
import okhttp3.OkHttpClient

@Module
@InstallIn(ActivityRetainedComponent::class)
abstract class SuuntoPlusStoreModule {
    @Binds
    abstract fun bindSuuntoPlusStoreRemoteDataSource(
        remoteDataSource: BackendSuuntoPlusStoreRemoteDataSource
    ): SuuntoPlusStoreRemoteDataSource

    @Binds
    abstract fun bindTrainingPlanRemoteDataSource(
        remoteDataSource: BackendSuuntoPlusStoreRemoteDataSource
    ): TrainingPlanRemoteDataSource

    @Binds
    abstract fun bindSuuntoPlusStoreAnalytics(
        tracker: SuuntoPlusStoreAnalyticsTracker
    ): SuuntoPlusStoreAnalytics

    @Binds
    abstract fun bindRemoveFeatureFromLibraryUseCase(
        removeFeatureFromLibraryUseCaseImpl: RemoveFeatureFromLibraryUseCaseImpl
    ): RemoveFeatureFromLibraryUseCase

    @Binds
    abstract fun bindSuuntoPlusStoreSingleGuide(
        suuntoPlusStoreSingleGuideUseCase: FetchSuuntoPlusStoreSingleGuideUseCase
    ): SuuntoPlusStoreSingleGuideInterface

    companion object {
        @Provides
        fun provideSuuntoStoreRestAPI(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi
        ): SuuntoPlusStoreRestApi {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                SuuntoPlusStoreRestApi::class.java,
                BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }

        @Provides
        fun provideTrainingPlanRestAPI(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi
        ): TrainingPlanRestApi {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                TrainingPlanRestApi::class.java,
                BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }
    }
}
