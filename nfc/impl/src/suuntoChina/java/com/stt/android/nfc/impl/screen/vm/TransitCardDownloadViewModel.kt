package com.stt.android.nfc.impl.screen.vm

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.nfc.impl.model.DownloadMode
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class TransitCardDownloadViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
) : ViewModel() {
    val mode = DownloadMode.entries.first { it.mode == savedStateHandle["mode"] }

    private val _uiState = MutableStateFlow<TransitCardDownloadUiState>(
        TransitCardDownloading
    )
    val uiState = _uiState.asStateFlow()

    private val _progress = MutableStateFlow(0f)
    val progress = _progress.asStateFlow()

    // For mock only
    private var retryCount: Int = 0

    fun performDownload() {
        _progress.value = 0f
        _uiState.value = TransitCardDownloading

        viewModelScope.launch {
            // mock
            while (true) {
                delay(100)
                _progress.value += 0.01f
                if (_progress.value >= 1f) break
            }
            if (retryCount >= 1) {
                _uiState.value = TransitCardDownloadSuccess
            } else {
                retryCount++
                _uiState.value = TransitCardDownloadFailure
            }
            // TODO : perform download operation
            // On success: _uiState.value = TransitCardDownloadSuccess
            // On failure: _uiState.value = TransitCardDownloadFailure
        }
    }
}
