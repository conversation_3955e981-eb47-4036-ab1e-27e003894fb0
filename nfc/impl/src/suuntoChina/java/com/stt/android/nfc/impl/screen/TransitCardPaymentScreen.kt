package com.stt.android.nfc.impl.screen

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.Suunto
import com.stt.android.compose.theme.spacing
import com.stt.android.nfc.impl.R
import com.stt.android.nfc.impl.model.PayMethod
import com.stt.android.nfc.impl.model.iconRes
import com.stt.android.nfc.impl.model.titleRes
import com.stt.android.nfc.impl.screen.components.PlaceholderActionFailed
import com.stt.android.nfc.impl.screen.components.PlaceholderTransitCardRefund
import com.stt.android.nfc.impl.screen.components.PlainPrimaryButton
import com.stt.android.nfc.impl.screen.vm.TransitCardChoosePayMethod
import com.stt.android.nfc.impl.screen.vm.TransitCardPaymentFailure
import com.stt.android.nfc.impl.screen.vm.TransitCardPaymentVerifying
import com.stt.android.nfc.impl.screen.vm.TransitCardPaymentViewModel
import com.stt.android.R as BR

@Composable
internal fun TransitCardPaymentScreen(
    viewModel: TransitCardPaymentViewModel,
    onNavigateUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val uiState by viewModel.uiState.collectAsState()
    val isVerifying = uiState == TransitCardPaymentVerifying

    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.background)
            .fillMaxSize(),
    ) {
        if (!isVerifying) {
            SuuntoTopBar(
                modifier = Modifier.fillMaxWidth(),
                title = stringResource(R.string.transit_card_payment_title),
                onNavigationClick = onNavigateUp,
            )
        }
        when (uiState) {
            TransitCardChoosePayMethod -> {
                val payMethod by viewModel.selectedPayMethod.collectAsState()
                ChoosePayMethodView(
                    modifier = Modifier
                        .weight(1f)
                        .narrowContent(),
                    selectedPayMethod = payMethod,
                    onPayMethodChange = viewModel::onPayMethodSelected,
                    onPayClick = {},
                )
            }

            TransitCardPaymentVerifying -> {
                PaymentVerifyingView(
                    modifier = Modifier
                        .weight(1f)
                        .narrowContent(),
                )
            }

            TransitCardPaymentFailure -> {
                PaymentFailureView(
                    modifier = Modifier
                        .weight(1f)
                        .narrowContent(),
                    onRetryClick = { /* TODO */ },
                )
            }
        }
    }

    BackHandler {
        if (!isVerifying) {
            onNavigateUp()
        }
    }
}

@Composable
private fun ChoosePayMethodView(
    selectedPayMethod: PayMethod,
    onPayMethodChange: (PayMethod) -> Unit,
    onPayClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface)
            .windowInsetsPadding(WindowInsets.systemBars.only(WindowInsetsSides.Bottom)),
    ) {
        LazyColumn(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
        ) {
            item {
                PaymentAmountView(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            start = MaterialTheme.spacing.medium,
                            end = MaterialTheme.spacing.medium,
                            top = MaterialTheme.spacing.xxlarge,
                            bottom = MaterialTheme.spacing.xxxlarge,
                        ),
                    label = stringResource(R.string.transit_card_beijing_tianjin_hebei_charge),
                    amount = "¥20.00", // TODO dynamic amount
                )
            }
            item {
                PayMethodSection()
            }
            items(
                listOf(
                    PayMethod.WECHAT_PAY,
                    PayMethod.ALIPAY,
                ),
                key = { "pay_method_${it.ordinal}" },
                contentType = { "pay_method" },
            ) {
                PayMethodItem(
                    method = it,
                    selected = it == selectedPayMethod,
                    onClick = { onPayMethodChange(it) },
                )
            }
        }
        PlainPrimaryButton(
            modifier = Modifier
                .padding(MaterialTheme.spacing.medium)
                .fillMaxWidth(),
            label = stringResource(R.string.transit_card_btn_pay),
            onClick = onPayClick,
        )
    }
}

@Composable
private fun PaymentAmountView(
    label: String,
    amount: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = amount,
            style = Suunto.typography.headerXxl,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Center,
        )
        Text(
            text = label,
            style = Suunto.typography.bodyM,
            color = MaterialTheme.colorScheme.secondary,
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
private fun PayMethodSection(
    modifier: Modifier = Modifier,
) {
    Text(
        modifier = modifier.padding(all = MaterialTheme.spacing.medium),
        text = stringResource(R.string.transit_card_payment_method),
        style = Suunto.typography.bodyL,
        color = MaterialTheme.colorScheme.onSurface,
    )
}

@Composable
private fun PayMethodItem(
    method: PayMethod,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .clickable(onClick = onClick)
            .fillMaxWidth()
            .padding(all = MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(method.iconRes),
            contentDescription = null,
            tint = Color.Unspecified,
        )
        Text(
            modifier = Modifier.weight(1f),
            text = stringResource(method.titleRes),
            style = Suunto.typography.headerS,
            color = MaterialTheme.colorScheme.onSurface,
        )
        RadioButton(
            selected = selected,
            onClick = null,
        )
    }
}

@Composable
private fun PaymentVerifyingView(
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .background(MaterialTheme.colorScheme.surface)
            .windowInsetsPadding(WindowInsets.systemBars.only(WindowInsetsSides.Bottom)),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
    ) {
        PlaceholderTransitCardRefund(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = 116.dp,
                    bottom = MaterialTheme.spacing.medium,
                ),
            title = stringResource(R.string.transit_card_verifying_payment),
            description = stringResource(R.string.transit_card_verifying_payment_content),
        )
        CircularProgressIndicator()
    }
}

@Composable
private fun PaymentFailureView(
    onRetryClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface)
            .windowInsetsPadding(WindowInsets.systemBars.only(WindowInsetsSides.Bottom)),
    ) {
        PlaceholderActionFailed(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .weight(1f)
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = 128.dp,
                    bottom = MaterialTheme.spacing.xxlarge,
                ),
            title = stringResource(R.string.transit_card_payment_verify_failed),
        )
        PlainPrimaryButton(
            modifier = Modifier
                .padding(MaterialTheme.spacing.medium)
                .fillMaxWidth(),
            label = stringResource(BR.string.retry),
            onClick = onRetryClick,
        )
    }
}
