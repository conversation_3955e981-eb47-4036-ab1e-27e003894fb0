package com.stt.android.nfc.impl.screen.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.heightIn
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.LocalMinimumInteractiveComponentSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.Suunto
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.spacing

internal val PLAIN_BUTTON_HEIGHT_DEFAULT = 48.dp
internal val PLAIN_BUTTON_HEIGHT_SMALL = 40.dp

@Composable
internal fun PlainPrimaryButton(
    label: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    minHeight: Dp = PLAIN_BUTTON_HEIGHT_DEFAULT,
) {
    CompositionLocalProvider(LocalMinimumInteractiveComponentSize provides Dp.Unspecified) {
        Button(
            modifier = modifier.heightIn(min = minHeight),
            enabled = enabled,
            onClick = onClick,
            elevation = null,
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.primary,
                contentColor = MaterialTheme.colorScheme.onSurface,
            ),
            shape = MaterialTheme.shapes.small,
        ) {
            Text(
                text = label.uppercase(),
                style = Suunto.typography.headerXs,
                color = MaterialTheme.colorScheme.onPrimary,
            )
        }
    }
}

@Composable
internal fun PlainSecondaryButton(
    label: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    minHeight: Dp = PLAIN_BUTTON_HEIGHT_DEFAULT,
) {
    CompositionLocalProvider(LocalMinimumInteractiveComponentSize provides Dp.Unspecified) {
        OutlinedButton(
            modifier = modifier.heightIn(min = minHeight),
            enabled = enabled,
            onClick = onClick,
            elevation = null,
            colors = ButtonDefaults.outlinedButtonColors(
                containerColor = Color.Transparent,
                contentColor = MaterialTheme.colorScheme.onSurface,
            ),
            shape = MaterialTheme.shapes.small,
            border = BorderStroke(
                width = 1.dp,
                color = MaterialTheme.colorScheme.cloudyGrey,
            )
        ) {
            Text(
                text = label.uppercase(),
                style = Suunto.typography.headerXs,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
    }
}

@Composable
internal fun PlainTextButton(
    label: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    minHeight: Dp = PLAIN_BUTTON_HEIGHT_DEFAULT,
) {
    CompositionLocalProvider(LocalMinimumInteractiveComponentSize provides Dp.Unspecified) {
        TextButton(
            modifier = modifier.heightIn(min = minHeight),
            enabled = enabled,
            onClick = onClick,
            elevation = null,
            colors = ButtonDefaults.textButtonColors(
                containerColor = Color.Transparent,
                contentColor = MaterialTheme.colorScheme.onSurface,
            ),
            shape = MaterialTheme.shapes.small,
        ) {
            Text(
                text = label.uppercase(),
                style = Suunto.typography.headerXs,
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
}

@Preview
@Composable
private fun PlainButtonsPreview() {
    M3AppTheme {
        Column(
            modifier = Modifier.background(MaterialTheme.colorScheme.surface),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        ) {
            PlainPrimaryButton(
                label = "Primary",
                onClick = {},
            )
            PlainSecondaryButton(
                label = "Secondary",
                onClick = {},
                minHeight = PLAIN_BUTTON_HEIGHT_SMALL,
            )
        }
    }
}
