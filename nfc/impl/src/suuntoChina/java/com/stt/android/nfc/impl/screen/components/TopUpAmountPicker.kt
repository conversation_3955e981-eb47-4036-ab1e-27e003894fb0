package com.stt.android.nfc.impl.screen.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.OffsetMapping
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import com.stt.android.compose.theme.Suunto
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.nfc.impl.R
import com.stt.android.nfc.impl.model.CustomTopUp
import com.stt.android.nfc.impl.model.PresetTopUp
import com.stt.android.nfc.impl.model.TopUp

@Composable
internal fun TopUpAmountPicker(
    topUp: TopUp,
    onTopUpChanged: (TopUp) -> Unit,
    errorMessage: String?,
    modifier: Modifier = Modifier,
    activationFee: String? = null,
) {
    Column(modifier = modifier) {
        TopUpHeader(
            modifier = Modifier.fillMaxWidth(),
            activationFee = activationFee,
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.small,
                ),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            TopUpOptionButton(
                modifier = Modifier.weight(1f),
                amount = PresetTopUp.THIRTY.amount,
                selected = topUp == PresetTopUp.THIRTY,
                onClick = { onTopUpChanged(PresetTopUp.THIRTY) },
            )
            TopUpOptionButton(
                modifier = Modifier.weight(1f),
                amount = PresetTopUp.FIFTY.amount,
                selected = topUp == PresetTopUp.FIFTY,
                onClick = { onTopUpChanged(PresetTopUp.FIFTY) },
            )
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.small,
                ),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            TopUpOptionButton(
                modifier = Modifier.weight(1f),
                amount = PresetTopUp.ONE_HUNDRED.amount,
                selected = topUp == PresetTopUp.ONE_HUNDRED,
                onClick = { onTopUpChanged(PresetTopUp.ONE_HUNDRED) },
            )
            TopUpCustomOptionButton(
                modifier = Modifier.weight(1f),
                amountStr = if (topUp is CustomTopUp) topUp.amountStr else null,
                selected = topUp is CustomTopUp,
                onClick = { onTopUpChanged(CustomTopUp(amountStr = null)) },
                onAmountChanged = { onTopUpChanged(CustomTopUp(amountStr = it)) },
            )
        }
        TopUpFooter(
            modifier = Modifier.fillMaxWidth(),
            message = errorMessage,
        )
    }
}

@Composable
private fun TopUpOptionButton(
    amount: Int,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .clip(MaterialTheme.shapes.large)
            .background(if (selected) MaterialTheme.colorScheme.primary.copy(alpha = 0.1f) else MaterialTheme.colorScheme.nearWhite)
            .clickable(onClick = onClick)
            .padding(
                horizontal = MaterialTheme.spacing.small,
                vertical = MaterialTheme.spacing.medium,
            ),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = "${stringResource(R.string.transit_card_currency_cny)} $amount",
            style = Suunto.typography.headerM,
            color = if (selected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface,
        )
    }
}

@Composable
private fun TopUpCustomOptionButton(
    amountStr: String?,
    selected: Boolean,
    onClick: () -> Unit,
    onAmountChanged: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    val keyboardController = LocalSoftwareKeyboardController.current

    val focusRequester = remember { FocusRequester() }
    LaunchedEffect(selected) {
        if (selected) {
            focusRequester.requestFocus()
        }
    }

    val currencyPrefix = "${stringResource(R.string.transit_card_currency_cny)} "
    val offsetMapping = remember {
        object : OffsetMapping {
            override fun originalToTransformed(offset: Int): Int {
                return offset + currencyPrefix.length
            }

            override fun transformedToOriginal(offset: Int): Int {
                return (offset - currencyPrefix.length).coerceAtLeast(0)
            }
        }
    }
    val offsetMappingEmpty = remember {
        object : OffsetMapping {
            override fun originalToTransformed(offset: Int): Int {
                return offset + 1
            }

            override fun transformedToOriginal(offset: Int): Int {
                return (offset - 1).coerceAtLeast(0)
            }
        }
    }

    Box(
        modifier = modifier
            .clip(MaterialTheme.shapes.large)
            .background(if (selected) MaterialTheme.colorScheme.primary.copy(alpha = 0.1f) else MaterialTheme.colorScheme.nearWhite)
            .clickable(onClick = onClick)
            .padding(
                horizontal = MaterialTheme.spacing.small,
                vertical = MaterialTheme.spacing.medium,
            ),
        contentAlignment = Alignment.Center,
    ) {
        if (selected) {
            BasicTextField(
                modifier = Modifier.focusRequester(focusRequester),
                value = amountStr ?: "",
                onValueChange = { value ->
                    if (value.length <= 4 && value.all { it.isDigit() }) {
                        onAmountChanged(value)
                    }
                },
                singleLine = true,
                cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                textStyle = Suunto.typography.headerM.copy(
                    color = MaterialTheme.colorScheme.primary,
                    textAlign = TextAlign.Center,
                ),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Number,
                    capitalization = KeyboardCapitalization.None,
                    autoCorrectEnabled = false,
                    imeAction = ImeAction.Done,
                    showKeyboardOnFocus = true,
                ),
                keyboardActions = KeyboardActions(onDone = {
                    keyboardController?.hide()
                    focusRequester.freeFocus()
                }),
                visualTransformation = if (!amountStr.isNullOrEmpty()) {
                    VisualTransformation { text ->
                        TransformedText(
                            buildAnnotatedString {
                                append(currencyPrefix)
                                append(text)
                            },
                            offsetMapping,
                        )
                    }
                } else VisualTransformation { text ->
                    // Fix an issue when the value is empty, the [BasicTextField] is a little higher
                    TransformedText(
                        buildAnnotatedString {
                            append(" ")
                            append(text)
                        },
                        offsetMappingEmpty,
                    )
                },
                decorationBox = { innerTextField ->
                    Box(contentAlignment = Alignment.Center) {
                        if (amountStr.isNullOrEmpty()) {
                            Text(
                                text = "${currencyPrefix}1 - 1000",
                                style = Suunto.typography.headerM,
                                color = Color(0xFFA2E1F7),
                                textAlign = TextAlign.Center,
                            )
                        }
                        innerTextField()
                    }
                }
            )
        } else {
            Text(
                text = stringResource(R.string.transit_card_custom_top_up_amount),
                style = Suunto.typography.headerM,
                color = MaterialTheme.colorScheme.onSurface,
            )
        }
    }
}

@Composable
private fun TopUpHeader(
    activationFee: String?,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier.padding(MaterialTheme.spacing.medium)) {
        Text(
            text = stringResource(R.string.transit_card_top_up_amount),
            style = Suunto.typography.headerM,
            color = MaterialTheme.colorScheme.onSurface,
        )
        activationFee?.let {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            ) {
                Text(
                    modifier = Modifier
                        .weight(1f)
                        .alignByBaseline(),
                    text = stringResource(R.string.transit_card_activation_fee),
                    style = Suunto.typography.bodyM,
                    color = MaterialTheme.colorScheme.secondary,
                )
                Text(
                    modifier = Modifier.alignByBaseline(),
                    text = it,
                    style = Suunto.typography.bodyM,
                    color = MaterialTheme.colorScheme.secondary,
                )
            }
        }
    }
}

@Composable
private fun TopUpFooter(
    message: String?,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier.padding(
            horizontal = MaterialTheme.spacing.medium,
            vertical = MaterialTheme.spacing.xsmall,
        )
    ) {
        Text(
            text = message ?: "",
            style = Suunto.typography.bodyS,
            color = MaterialTheme.colorScheme.error,
        )
    }
}
