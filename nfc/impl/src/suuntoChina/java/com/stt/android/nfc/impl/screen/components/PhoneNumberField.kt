package com.stt.android.nfc.impl.screen.components

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.Suunto
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.nfc.impl.R

private const val PHONE_NUMBER_LENGTH = 11

internal val String.isValidPhoneNumber: Boolean
    get() = length == PHONE_NUMBER_LENGTH && all { it.isDigit() }

@Composable
internal fun PhoneNumberField(
    phoneNumber: String,
    onPhoneNumberChange: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    val focusRequester = remember { FocusRequester() }
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }

    val showError = remember(phoneNumber) {
        phoneNumber.isNotEmpty() && !phoneNumber.isValidPhoneNumber
    }
    Column(modifier = modifier.animateContentSize()) {
        OutlinedTextField(
            modifier = Modifier
                .focusRequester(focusRequester)
                .fillMaxWidth(),
            value = phoneNumber,
            onValueChange = { value ->
                if (value.length <= PHONE_NUMBER_LENGTH && value.all { it.isDigit() }) {
                    onPhoneNumberChange(value)
                }
            },
            isError = showError,
            shape = MaterialTheme.shapes.small,
            singleLine = true,
            textStyle = Suunto.typography.bodyL,
            colors = OutlinedTextFieldDefaults.colors(
                focusedContainerColor = MaterialTheme.colorScheme.nearWhite,
                unfocusedContainerColor = MaterialTheme.colorScheme.nearWhite,
                errorContainerColor = MaterialTheme.colorScheme.nearWhite,
                focusedPlaceholderColor = MaterialTheme.colorScheme.mediumGrey,
                unfocusedPlaceholderColor = MaterialTheme.colorScheme.mediumGrey,
                focusedBorderColor = MaterialTheme.colorScheme.primary,
                unfocusedBorderColor = MaterialTheme.colorScheme.lightGrey,
                errorBorderColor = MaterialTheme.colorScheme.error,
                focusedTextColor = MaterialTheme.colorScheme.onSurface,
                unfocusedTextColor = MaterialTheme.colorScheme.onSurface,
                errorTextColor = MaterialTheme.colorScheme.onSurface,
                cursorColor = MaterialTheme.colorScheme.primary,
            ),
            placeholder = {
                Text(
                    text = stringResource(R.string.transit_card_phone_number),
                    style = Suunto.typography.bodyL,
                )
            },
            keyboardOptions = KeyboardOptions(
                capitalization = KeyboardCapitalization.None,
                autoCorrectEnabled = false,
                keyboardType = KeyboardType.Phone,
                imeAction = ImeAction.Done,
                showKeyboardOnFocus = true,
            ),
            trailingIcon = {
                if (phoneNumber.isNotEmpty()) {
                    IconButton(onClick = { onPhoneNumberChange("") }) {
                        Icon(
                            painter = SuuntoIcons.ActionCloseCircleFilled.asPainter(),
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.mediumGrey,
                        )
                    }
                }
            }
        )
        if (showError) {
            Text(
                modifier = Modifier.padding(vertical = MaterialTheme.spacing.xsmall),
                text = stringResource(R.string.transit_card_phone_number_error),
                style = Suunto.typography.bodyS,
                color = MaterialTheme.colorScheme.error,
            )
        }
    }
}

@Preview
@Composable
private fun PhoneNumberFieldPreview() {
    M3AppTheme {
        Surface(color = MaterialTheme.colorScheme.surface) {
            PhoneNumberField(
                phoneNumber = "1",
                onPhoneNumberChange = {},
            )
        }
    }
}
