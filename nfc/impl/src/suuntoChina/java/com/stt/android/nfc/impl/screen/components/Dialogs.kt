package com.stt.android.nfc.impl.screen.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.Suunto
import com.stt.android.compose.theme.spacing
import com.stt.android.nfc.impl.R
import com.stt.android.R as BR

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BaseDialog(
    title: String,
    positiveText: String,
    negativeText: String,
    onPositiveClick: () -> Unit,
    onNegativeClick: () -> Unit,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier,
    message: AnnotatedString? = null,
    neutralText: String? = null,
    onNeutralClick: (() -> Unit)? = null,
    horizontalButtons: Boolean = true,
    positiveButtonEnabled: Boolean = true,
    negativeButtonEnabled: Boolean = true,
    neutralButtonEnabled: Boolean = true,
    content: @Composable (ColumnScope.() -> Unit)? = null,
) {
    BasicAlertDialog(
        modifier = modifier,
        onDismissRequest = onDismissRequest,
    ) {
        Column(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface, shape = MaterialTheme.shapes.large)
                .fillMaxWidth()
                .padding(all = MaterialTheme.spacing.medium),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                text = title,
                style = Suunto.typography.headerS,
                color = MaterialTheme.colorScheme.onSurface,
                textAlign = TextAlign.Center,
            )
            if (message != null || content != null) {
                Column(
                    modifier = Modifier
                        .padding(
                            top = when {
                                message != null -> MaterialTheme.spacing.small
                                else -> MaterialTheme.spacing.medium
                            },
                        )
                        .verticalScroll(rememberScrollState())
                        .weight(1f, fill = false)
                        .fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
                ) {
                    message?.let {
                        Text(
                            text = it,
                            style = Suunto.typography.bodyM,
                            color = MaterialTheme.colorScheme.onSurface,
                            textAlign = TextAlign.Center,
                        )
                    }
                    content?.let {
                        it()
                    }
                }
            }
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
            if (horizontalButtons) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    PlainSecondaryButton(
                        modifier = Modifier.weight(1f),
                        label = negativeText,
                        enabled = negativeButtonEnabled,
                        onClick = onNegativeClick,
                        minHeight = PLAIN_BUTTON_HEIGHT_SMALL,
                    )
                    if (neutralText != null && onNeutralClick != null) {
                        PlainSecondaryButton(
                            modifier = Modifier.weight(1f),
                            label = neutralText,
                            enabled = neutralButtonEnabled,
                            onClick = onNeutralClick,
                            minHeight = PLAIN_BUTTON_HEIGHT_SMALL,
                        )
                    }
                    PlainPrimaryButton(
                        modifier = Modifier.weight(1f),
                        label = positiveText,
                        enabled = positiveButtonEnabled,
                        onClick = onPositiveClick,
                        minHeight = PLAIN_BUTTON_HEIGHT_SMALL,
                    )
                }
            } else {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
                ) {
                    PlainPrimaryButton(
                        modifier = Modifier.fillMaxWidth(),
                        label = positiveText,
                        enabled = positiveButtonEnabled,
                        onClick = onPositiveClick,
                        minHeight = PLAIN_BUTTON_HEIGHT_SMALL,
                    )
                    if (neutralText != null && onNeutralClick != null) {
                        PlainSecondaryButton(
                            modifier = Modifier.fillMaxWidth(),
                            label = neutralText,
                            enabled = neutralButtonEnabled,
                            onClick = onNeutralClick,
                            minHeight = PLAIN_BUTTON_HEIGHT_SMALL,
                        )
                    }
                    PlainSecondaryButton(
                        modifier = Modifier.fillMaxWidth(),
                        label = negativeText,
                        enabled = negativeButtonEnabled,
                        onClick = onNegativeClick,
                        minHeight = PLAIN_BUTTON_HEIGHT_SMALL,
                    )
                }
            }
        }
    }
}

@Composable
internal fun CardActivationAgreementDialog(
    onAccept: () -> Unit,
    onDecline: () -> Unit,
    onAgreementClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    BaseDialog(
        modifier = modifier,
        title = stringResource(R.string.transit_card_activation_agreement_dialog_title),
        message = buildAnnotatedString {
            val linkText = stringResource(R.string.transit_card_activation_agreement_link_text)
            val agreement =
                stringResource(R.string.transit_card_activation_agreement_popup, linkText)
            append(agreement)
            val linkAnnotation = LinkAnnotation.Clickable(
                tag = "agreement",
                styles = TextLinkStyles(
                    style = Suunto.typography.bodyM.toSpanStyle()
                        .copy(color = MaterialTheme.colorScheme.primary),
                ),
                linkInteractionListener = { onAgreementClick() },
            )
            val start = agreement.indexOf(linkText)
            addLink(linkAnnotation, start, start + linkText.length)
        },
        positiveText = stringResource(BR.string.yes),
        negativeText = stringResource(BR.string.no),
        onPositiveClick = onAccept,
        onNegativeClick = onDecline,
        onDismissRequest = onDecline,
    )
}

@Composable
internal fun AuthorizationRequestDialog(
    card: String,
    onAuthorize: (String) -> Unit,
    onDecline: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var phoneNumber by rememberSaveable { mutableStateOf("") }
    BaseDialog(
        modifier = modifier,
        title = stringResource(R.string.transit_card_authorization_request_dialog_title),
        message = AnnotatedString(
            stringResource(R.string.transit_card_authorization_request_dialog_message, card)
        ),
        positiveText = stringResource(R.string.transit_card_btn_authorize),
        negativeText = stringResource(BR.string.cancel),
        onPositiveClick = {
            onAuthorize(phoneNumber)
        },
        onNegativeClick = onDecline,
        onDismissRequest = onDecline,
        positiveButtonEnabled = phoneNumber.isValidPhoneNumber,
        content = {
            PhoneNumberField(
                phoneNumber = phoneNumber,
                onPhoneNumberChange = { phoneNumber = it },
            )
        }
    )
}

@Composable
internal fun CardRefundAdviceDialog(
    onBackUp: () -> Unit,
    onContinue: () -> Unit,
    onCancel: () -> Unit,
    modifier: Modifier = Modifier,
) {
    BaseDialog(
        modifier = modifier,
        title = stringResource(R.string.transit_card_refund_advice_dialog_title),
        positiveText = stringResource(R.string.transit_card_btn_back_up),
        negativeText = stringResource(BR.string.cancel),
        neutralText = stringResource(R.string.transit_card_btn_continue_return),
        onPositiveClick = onBackUp,
        onNegativeClick = onCancel,
        onNeutralClick = onContinue,
        onDismissRequest = onCancel,
        horizontalButtons = false,
    )
}

@Composable
internal fun CardRefundConfirmDialog(
    onConfirm: () -> Unit,
    onCancel: () -> Unit,
    modifier: Modifier = Modifier,
) {
    BaseDialog(
        modifier = modifier,
        title = stringResource(R.string.transit_card_refund_confirm_dialog_title),
        positiveText = stringResource(R.string.transit_card_btn_confirm),
        negativeText = stringResource(BR.string.cancel),
        onPositiveClick = onConfirm,
        onNegativeClick = onCancel,
        onDismissRequest = onCancel,
    )
}

@Composable
internal fun PaymentAppNotFoundDialog(
    service: String,
    onDownload: () -> Unit,
    onCancel: () -> Unit,
    modifier: Modifier = Modifier,
) {
    BaseDialog(
        modifier = modifier,
        title = stringResource(R.string.transit_card_payment_app_not_found_dialog_title, service),
        positiveText = stringResource(R.string.transit_card_btn_download),
        negativeText = stringResource(BR.string.no),
        onPositiveClick = onDownload,
        onNegativeClick = onCancel,
        onDismissRequest = onCancel,
    )
}

@Preview(widthDp = 310)
@Composable
private fun CardActivationAgreementDialogPreview() {
    M3AppTheme {
        CardActivationAgreementDialog(
            onAccept = {},
            onDecline = {},
            onAgreementClick = {},
        )
    }
}

@Preview(widthDp = 310)
@Composable
private fun AuthorizationRequestDialogPreview() {
    M3AppTheme {
        AuthorizationRequestDialog(
            card = "Beijing Transit Card",
            onAuthorize = {},
            onDecline = {},
        )
    }
}

@Preview(widthDp = 310)
@Composable
private fun CardRefundAdviceDialogPreview() {
    M3AppTheme {
        CardRefundAdviceDialog(
            onBackUp = {},
            onContinue = {},
            onCancel = {},
        )
    }
}

@Preview(widthDp = 310)
@Composable
private fun CardRefundConfirmDialogPreview() {
    M3AppTheme {
        CardRefundConfirmDialog(
            onConfirm = {},
            onCancel = {},
        )
    }
}

@Preview(widthDp = 310)
@Composable
private fun PaymentAppNotFoundDialogPreview() {
    M3AppTheme {
        PaymentAppNotFoundDialog(
            service = "Alipay",
            onDownload = {},
            onCancel = {},
        )
    }
}
