package com.stt.android.nfc.impl.screen.vm

import androidx.lifecycle.ViewModel
import com.stt.android.nfc.impl.model.PayMethod
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

@HiltViewModel
internal class TransitCardPaymentViewModel @Inject constructor() : ViewModel() {

    private val _uiState = MutableStateFlow<TransitCardPaymentUiState>(
        TransitCardChoosePayMethod
    )
    val uiState = _uiState.asStateFlow()

    private val _selectedPayMethod = MutableStateFlow<PayMethod>(PayMethod.WECHAT_PAY)
    val selectedPayMethod = _selectedPayMethod.asStateFlow()

    fun onPayMethodSelected(method: PayMethod) {
        _selectedPayMethod.value = method
    }
}
