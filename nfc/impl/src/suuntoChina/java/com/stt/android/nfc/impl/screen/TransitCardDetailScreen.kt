package com.stt.android.nfc.impl.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CornerSize
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalMinimumInteractiveComponentSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.Suunto
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.nfc.impl.R
import com.stt.android.nfc.impl.screen.components.CardRefundAdviceDialog
import com.stt.android.nfc.impl.screen.components.PlaceholderTransitCardNormal
import com.stt.android.nfc.impl.screen.components.PlainPrimaryButton
import com.stt.android.nfc.impl.screen.components.TransitCard
import com.stt.android.nfc.impl.screen.modifiers.OpenBorderType
import com.stt.android.nfc.impl.screen.modifiers.openBorder
import com.stt.android.R as BR

@Composable
internal fun TransitCardDetailScreen(
    onNavigateUp: () -> Unit,
    onAdd: () -> Unit,
    onTopUp: () -> Unit,
    onTransactions: () -> Unit,
    onMoveCard: () -> Unit,
    onReturnCard: () -> Unit,
    onCardDetail: () -> Unit,
    onDownloadCard: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.background)
            .fillMaxSize(),
    ) {
        SuuntoTopBar(
            modifier = Modifier.fillMaxWidth(),
            title = stringResource(R.string.transit_card_detail_title),
            onNavigationClick = onNavigateUp,
        )
        LocalCardView(
            modifier = Modifier
                .weight(1f)
                .narrowContent(),
            onTopUpClick = onTopUp,
            onTransactionsClick = onTransactions,
            onMoveCardClick = onMoveCard,
            onReturnCardClick = onReturnCard,
            onCardDetailClick = onCardDetail,
        )
    }
}

@Composable
private fun LocalCardView(
    onTopUpClick: () -> Unit,
    onTransactionsClick: () -> Unit,
    onMoveCardClick: () -> Unit,
    onReturnCardClick: () -> Unit,
    onCardDetailClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var showReturnCardConfirm by remember { mutableStateOf(false) }
    LazyColumn(
        modifier = modifier.background(MaterialTheme.colorScheme.surface),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        contentPadding = PaddingValues(
            top = MaterialTheme.spacing.medium,
            bottom = WindowInsets.systemBars.only(WindowInsetsSides.Bottom)
                .asPaddingValues()
                .calculateBottomPadding() + MaterialTheme.spacing.medium,
        ),
    ) {
        item {
            TransitCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.spacing.medium),
            )
        }
        item {
            TopUpSettingItem(
                amount = "¥ 25.00",
                onClick = onTopUpClick,
            )
        }
        item {
            SettingItem(
                icon = painterResource(R.drawable.ic_transit_card_transactions),
                title = stringResource(R.string.transit_card_transactions),
                onClick = onTransactionsClick,
            )
        }
        item {
            SettingItem(
                icon = painterResource(R.drawable.ic_transit_card_cloud),
                title = stringResource(R.string.transit_card_move_card),
                onClick = onMoveCardClick,
                openBorderType = OpenBorderType.TOP_SIDES,
            )
            SettingItem(
                icon = painterResource(R.drawable.ic_transit_card_return),
                title = stringResource(R.string.transit_card_return_card),
                onClick = { showReturnCardConfirm = true },
                openBorderType = OpenBorderType.BOTTOM_SIDES,
            )
        }
        item {
            SettingItem(
                icon = painterResource(R.drawable.ic_transit_card_card_detail),
                title = stringResource(R.string.transit_card_card_detail),
                onClick = onCardDetailClick,
            )
        }
    }

    if (showReturnCardConfirm) {
        CardRefundAdviceDialog(
            onBackUp = {
                showReturnCardConfirm = false
                onMoveCardClick()
            },
            onContinue = {
                showReturnCardConfirm = false
                onReturnCardClick()
            },
            onCancel = { showReturnCardConfirm = false }
        )
    }
}

@Composable
private fun RemoteOnlyCardView(
    onDownloadClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier.background(MaterialTheme.colorScheme.surface),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        contentPadding = PaddingValues(
            top = MaterialTheme.spacing.medium,
            bottom = WindowInsets.systemBars.only(WindowInsetsSides.Bottom)
                .asPaddingValues()
                .calculateBottomPadding() + MaterialTheme.spacing.medium,
        ),
    ) {
        item {
            TransitCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.spacing.medium),
                cover = painterResource(R.drawable.ic_transit_card_on_cloud),
            )
        }
        item {
            OnCloudSection()
        }
        item {
            TransitCardSettingItem(
                cover = painterResource(R.drawable.ic_transit_card_beijing_tianjin_hebei),
                name = "Beijing-Tianjin-Hebei Transit Card",
                balance = stringResource(R.string.transit_card_balance_amount, "¥ 0.00"),
                onClick = onDownloadClick,
            )
        }
    }
}

@Composable
private fun OnCloudSection(
    modifier: Modifier = Modifier,
) {
    Text(
        modifier = modifier.padding(all = MaterialTheme.spacing.medium),
        text = stringResource(R.string.transit_card_on_the_cloud),
        style = Suunto.typography.bodyL,
        color = MaterialTheme.colorScheme.onSurface,
    )
}

@Composable
private fun SettingItem(
    icon: Painter,
    title: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    openBorderType: OpenBorderType = OpenBorderType.FULL,
) {
    BasicSettingItem(
        modifier = modifier,
        leading = {
            Icon(
                painter = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface,
            )
        },
        content = {
            Text(
                modifier = it,
                text = title,
                style = Suunto.typography.bodyL,
                color = MaterialTheme.colorScheme.onSurface,
            )
        },
        trailing = {
            Icon(
                painter = SuuntoIcons.ActionRight.asPainter(),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.mediumGrey,
            )
        },
        onClick = onClick,
        openBorderType = openBorderType,
    )
}

@Composable
private fun TopUpSettingItem(
    amount: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    BasicSettingItem(
        modifier = modifier,
        leading = {
            Icon(
                painter = painterResource(R.drawable.ic_transit_card),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface,
            )
        },
        content = {
            Column(
                modifier = it,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
            ) {
                Text(
                    text = amount,
                    style = Suunto.typography.headerM,
                    color = MaterialTheme.colorScheme.onSurface,
                )
                Text(
                    text = stringResource(R.string.transit_card_balance),
                    style = Suunto.typography.bodyM,
                    color = MaterialTheme.colorScheme.secondary,
                )
            }
        },
        trailing = {
            TextButton(
                onClick = onClick,
                contentPadding = PaddingValues(
                    horizontal = MaterialTheme.spacing.small,
                    vertical = MaterialTheme.spacing.smaller,
                )
            ) {
                Text(
                    text = stringResource(R.string.transit_card_btn_top_up),
                    style = Suunto.typography.headerXs,
                    color = MaterialTheme.colorScheme.primary,
                )
            }
        },
    )
}

@Composable
private fun TransitCardSettingItem(
    cover: Painter,
    name: String,
    balance: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    BasicSettingItem(
        modifier = modifier,
        leading = {
            Image(
                modifier = Modifier.width(80.dp),
                painter = cover,
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
            )
        },
        content = {
            Column(
                modifier = it,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
            ) {
                Text(
                    text = name,
                    style = Suunto.typography.headerM,
                    color = MaterialTheme.colorScheme.onSurface,
                )
                Text(
                    text = balance,
                    style = Suunto.typography.bodyM,
                    color = MaterialTheme.colorScheme.secondary,
                )
            }
        },
        trailing = {
            TextButton(
                onClick = onClick,
                contentPadding = PaddingValues(
                    horizontal = MaterialTheme.spacing.small,
                    vertical = MaterialTheme.spacing.smaller,
                )
            ) {
                Text(
                    text = stringResource(R.string.transit_card_btn_download),
                    style = Suunto.typography.headerXs,
                    color = MaterialTheme.colorScheme.primary,
                )
            }
        },
    )
}

@Composable
private fun BasicSettingItem(
    leading: @Composable () -> Unit,
    trailing: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    openBorderType: OpenBorderType = OpenBorderType.FULL,
    onClick: (() -> Unit)? = null,
    content: @Composable (Modifier) -> Unit,
) {
    Row(
        modifier = modifier
            .padding(horizontal = MaterialTheme.spacing.medium)
            .openBorder(openBorderType, MaterialTheme.colorScheme.dividerColor, 1.dp, 16.dp)
            .then(onClick?.let {
                val shape = when (openBorderType) {
                    OpenBorderType.FULL -> MaterialTheme.shapes.large
                    OpenBorderType.TOP_SIDES -> MaterialTheme.shapes.large.copy(
                        bottomStart = CornerSize(0.dp),
                        bottomEnd = CornerSize(0.dp),
                    )

                    OpenBorderType.SIDES -> RectangleShape
                    OpenBorderType.BOTTOM_SIDES -> MaterialTheme.shapes.large.copy(
                        topStart = CornerSize(0.dp),
                        topEnd = CornerSize(0.dp),
                    )
                }
                Modifier
                    .clip(shape)
                    .clickable(onClick = it)
            } ?: Modifier)
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        leading()
        content(Modifier.weight(1f))
        CompositionLocalProvider(LocalMinimumInteractiveComponentSize provides Dp.Unspecified) {
            trailing()
        }
    }
}

@Composable
private fun EmptyView(
    onAdd: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface)
            .windowInsetsPadding(WindowInsets.systemBars.only(WindowInsetsSides.Bottom)),
    ) {
        Box(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .weight(1f)
                .fillMaxWidth()
                .padding(vertical = 60.dp),
        ) {
            PlaceholderTransitCardNormal(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium),
                description = stringResource(R.string.transit_card_no_card_yet),
                descriptionColor = MaterialTheme.colorScheme.secondary,
            )
        }
        PlainPrimaryButton(
            modifier = Modifier
                .padding(MaterialTheme.spacing.medium)
                .fillMaxWidth(),
            label = stringResource(BR.string.add),
            onClick = onAdd,
        )
    }
}
