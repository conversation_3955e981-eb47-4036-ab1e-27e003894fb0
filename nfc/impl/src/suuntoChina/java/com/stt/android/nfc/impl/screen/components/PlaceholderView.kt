package com.stt.android.nfc.impl.screen.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.Suunto
import com.stt.android.compose.theme.spacing
import com.stt.android.nfc.impl.R

@Composable
private fun PlaceholderView(
    icon: Painter,
    modifier: Modifier = Modifier,
    title: String? = null,
    titleColor: Color = MaterialTheme.colorScheme.onSurface,
    description: String? = null,
    descriptionColor: Color = MaterialTheme.colorScheme.onSurface,
    iconGap: Dp = MaterialTheme.spacing.medium,
) {
    PlaceholderView(
        modifier = modifier,
        icon = icon,
        title = title,
        titleColor = titleColor,
        iconGap = iconGap,
        descriptionContent = {
            if (description != null) {
                Text(
                    text = description,
                    style = Suunto.typography.bodyM,
                    color = descriptionColor,
                    textAlign = TextAlign.Center,
                )
            }
        },
    )
}

@Composable
private fun PlaceholderView(
    icon: Painter,
    modifier: Modifier = Modifier,
    iconGap: Dp = MaterialTheme.spacing.medium,
    title: String? = null,
    titleColor: Color = MaterialTheme.colorScheme.onSurface,
    descriptionContent: @Composable () -> Unit = {},
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Icon(
            painter = icon,
            contentDescription = null,
            tint = Color.Unspecified,
        )
        Spacer(modifier = Modifier.height(iconGap))
        Column(
            modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        ) {
            title?.let {
                Text(
                    text = it,
                    style = Suunto.typography.headerM,
                    color = titleColor,
                    textAlign = TextAlign.Center,
                )
            }
            descriptionContent()
        }
    }
}

@Composable
internal fun PlaceholderTransitCardNormal(
    modifier: Modifier = Modifier,
    title: String? = null,
    description: String? = null,
    descriptionColor: Color = MaterialTheme.colorScheme.onSurface,
) = PlaceholderView(
    modifier = modifier,
    icon = painterResource(id = R.drawable.ic_transit_card_normal),
    title = title,
    description = description,
    descriptionColor = descriptionColor,
)

@Composable
internal fun PlaceholderTransitCardBackup(
    modifier: Modifier = Modifier,
    title: String? = null,
    description: String? = null,
) = PlaceholderView(
    modifier = modifier,
    icon = painterResource(id = R.drawable.ic_transit_card_backup),
    title = title,
    description = description,
)

@Composable
internal fun PlaceholderTransitCardRefund(
    modifier: Modifier = Modifier,
    title: String? = null,
    description: String? = null,
) = PlaceholderView(
    modifier = modifier,
    icon = painterResource(id = R.drawable.ic_transit_card_refund),
    title = title,
    description = description,
)

@Composable
internal fun PlaceholderActionSuccess(
    modifier: Modifier = Modifier,
    title: String? = null,
    description: String? = null,
) = PlaceholderView(
    modifier = modifier,
    icon = painterResource(id = R.drawable.ic_transit_card_success),
    iconGap = MaterialTheme.spacing.small,
    title = title,
    description = description,
)

@Composable
internal fun PlaceholderActionSuccess(
    modifier: Modifier = Modifier,
    title: String? = null,
    descriptionContent: @Composable () -> Unit,
) = PlaceholderView(
    modifier = modifier,
    icon = painterResource(id = R.drawable.ic_transit_card_success),
    iconGap = MaterialTheme.spacing.small,
    title = title,
    descriptionContent = descriptionContent,
)

@Composable
internal fun PlaceholderActionFailed(
    modifier: Modifier = Modifier,
    title: String? = null,
    description: String? = null,
) = PlaceholderView(
    modifier = modifier,
    icon = painterResource(id = R.drawable.ic_transit_card_failed),
    iconGap = MaterialTheme.spacing.small,
    title = title,
    description = description,
)

@Preview
@Composable
private fun PlaceholderViewPreview() {
    PlaceholderTransitCardNormal(
        title = "No items",
        description = "You don't have any items yet. Start by adding a new item.",
        modifier = Modifier
            .background(MaterialTheme.colorScheme.surface)
            .padding(16.dp),
    )
}
