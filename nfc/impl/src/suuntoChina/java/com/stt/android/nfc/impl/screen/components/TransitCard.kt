package com.stt.android.nfc.impl.screen.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.dropShadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.shadow.Shadow
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.Suunto
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.stt.android.nfc.impl.R

@Composable
internal fun TransitCard(
    modifier: Modifier = Modifier,
    cover: Painter = painterResource(R.drawable.ic_transit_card_beijing_tianjin_hebei),
    state: String? = null,
    stateColor: Color = Color.White,
    stateBackgroundColor: Color = Color(0xFF0061AB),
) {
    val density = LocalDensity.current
    var cornerRadius by remember { mutableStateOf(0.dp) }
    Box(
        modifier = modifier.dropShadow(
            shape = RoundedCornerShape(cornerRadius),
            shadow = Shadow(
                radius = 6.dp,
                alpha = 0.18f,
                offset = DpOffset(0.dp, 2.dp),
            )
        ),
    ) {
        Image(
            modifier = Modifier
                .onSizeChanged {
                    cornerRadius = with(density) { it.width.toDp() } / 343f * 22f
                }
                .fillMaxWidth(),
            painter = cover,
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
        )
        state?.let {
            Text(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(start = 24.dp, bottom = 28.dp)
                    .clip(CircleShape)
                    .background(stateBackgroundColor)
                    .padding(horizontal = 8.dp, vertical = 4.dp),
                text = it,
                style = Suunto.typography.headerXs,
                color = stateColor,
            )
        }
    }
}

@Composable
internal fun TransitCardInfo(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    cover: Painter = painterResource(R.drawable.ic_transit_card_beijing_tianjin_hebei),
    name: String = stringResource(R.string.transit_card_beijing_tianjin_hebei),
    description: String = stringResource(R.string.transit_card_beijing_tianjin_hebei_description),
) {
    Column(modifier = modifier) {
        TransitCard(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth(),
            cover = cover,
        )
        Row(
            modifier = Modifier
                .clickable(onClick = onClick)
                .padding(all = MaterialTheme.spacing.medium),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = name,
                    style = Suunto.typography.bodyL,
                    color = MaterialTheme.colorScheme.onSurface,
                )
                Text(
                    text = description,
                    style = Suunto.typography.bodyM,
                    color = MaterialTheme.colorScheme.secondary,
                )
            }
            Icon(
                painter = SuuntoIcons.ActionRight.asPainter(),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface,
            )
        }
        HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
    }
}

@Preview
@Composable
private fun TransitCardPreview() {
    M3AppTheme {
        Surface {
            TransitCard(state = "已进站")
        }
    }
}

@Preview
@Composable
private fun TransitCardInfoPreview() {
    M3AppTheme {
        Surface {
            TransitCardInfo(onClick = {})
        }
    }
}
