package com.stt.android.nfc.impl.screen

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.Suunto
import com.stt.android.compose.theme.spacing
import com.stt.android.nfc.impl.R
import com.stt.android.nfc.impl.screen.components.PlaceholderActionFailed
import com.stt.android.nfc.impl.screen.components.PlaceholderActionSuccess
import com.stt.android.nfc.impl.screen.components.PlaceholderTransitCardBackup
import com.stt.android.nfc.impl.screen.components.PlainPrimaryButton
import com.stt.android.nfc.impl.screen.components.PlainTextButton
import com.stt.android.nfc.impl.screen.components.ProgressBar
import com.stt.android.nfc.impl.screen.components.TransitCard
import com.stt.android.nfc.impl.screen.vm.TransitCardBackupConfirmation
import com.stt.android.nfc.impl.screen.vm.TransitCardBackupFailure
import com.stt.android.nfc.impl.screen.vm.TransitCardBackupProcessing
import com.stt.android.nfc.impl.screen.vm.TransitCardBackupSuccess
import com.stt.android.nfc.impl.screen.vm.TransitCardBackupViewModel
import com.stt.android.R as BR

@Composable
internal fun TransitCardBackupScreen(
    viewModel: TransitCardBackupViewModel,
    onNavigateUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val uiState by viewModel.uiState.collectAsState()
    val isProcessing = uiState == TransitCardBackupProcessing

    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.background)
            .fillMaxSize(),
    ) {
        if (!isProcessing) {
            SuuntoTopBar(
                modifier = Modifier.fillMaxWidth(),
                title = stringResource(R.string.transit_card_backup_title),
                onNavigationClick = onNavigateUp,
            )
        }
        when (uiState) {
            TransitCardBackupConfirmation -> {
                BackupConfirmView(
                    modifier = Modifier
                        .weight(1f)
                        .narrowContent(),
                    onConfirmed = viewModel::onConfirmed,
                )
            }

            TransitCardBackupProcessing -> {
                val progress by viewModel.progress.collectAsState()
                ProcessingBackupView(
                    modifier = Modifier
                        .weight(1f)
                        .narrowContent(),
                    progress = progress,
                )
            }

            TransitCardBackupSuccess -> {
                BackupSuccessView(
                    modifier = Modifier
                        .weight(1f)
                        .narrowContent(),
                    onNavigateUp = onNavigateUp,
                )
            }

            TransitCardBackupFailure -> {
                BackupFailureView(
                    modifier = Modifier
                        .weight(1f)
                        .narrowContent(),
                    onNavigateUp = onNavigateUp,
                    onRetryClick = viewModel::performBackup,
                )
            }
        }
    }

    BackHandler {
        if (!isProcessing) {
            onNavigateUp()
        }
    }
}

@Composable
private fun BackupConfirmView(
    onConfirmed: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface)
            .windowInsetsPadding(WindowInsets.systemBars.only(WindowInsetsSides.Bottom)),
    ) {
        LazyColumn(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
        ) {
            item {
                TransitCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(all = MaterialTheme.spacing.medium),
                )
            }
            item {
                TipView(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(all = MaterialTheme.spacing.medium),
                    tipContent1 = stringResource(R.string.transit_card_backup_tip_1_content),
                    tipContent2 = stringResource(R.string.transit_card_backup_tip_2_content),
                )
            }
        }
        PlainPrimaryButton(
            modifier = Modifier
                .padding(MaterialTheme.spacing.medium)
                .fillMaxWidth(),
            label = stringResource(R.string.transit_card_btn_move),
            onClick = onConfirmed,
        )
    }
}

@Composable
private fun ProcessingBackupView(
    progress: Float,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .background(MaterialTheme.colorScheme.surface)
            .windowInsetsPadding(WindowInsets.systemBars.only(WindowInsetsSides.Bottom)),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
    ) {
        PlaceholderTransitCardBackup(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = 116.dp,
                    bottom = MaterialTheme.spacing.medium,
                ),
            title = stringResource(R.string.transit_card_backup_processing),
            description = stringResource(R.string.transit_card_backup_processing_content),
        )
        ProgressBar(
            modifier = Modifier.padding(
                horizontal = MaterialTheme.spacing.xxlarge,
                vertical = MaterialTheme.spacing.xsmaller,
            ),
            progress = progress,
        )
    }
}

@Composable
private fun BackupSuccessView(
    onNavigateUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface)
            .windowInsetsPadding(WindowInsets.systemBars.only(WindowInsetsSides.Bottom)),
    ) {
        PlaceholderActionSuccess(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .weight(1f)
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = 128.dp,
                    bottom = MaterialTheme.spacing.xxlarge,
                ),
            title = stringResource(R.string.transit_card_backup_success),
            descriptionContent = {
                TipView(
                    modifier = Modifier.fillMaxWidth(),
                    tipContent1 = stringResource(R.string.transit_card_backup_success_tip_1_content),
                    tipContent2 = stringResource(R.string.transit_card_backup_success_tip_2_content),
                )
            },
        )
        PlainPrimaryButton(
            modifier = Modifier
                .padding(MaterialTheme.spacing.medium)
                .fillMaxWidth(),
            label = stringResource(BR.string.got_it),
            onClick = onNavigateUp,
        )
    }
}

@Composable
private fun BackupFailureView(
    onNavigateUp: () -> Unit,
    onRetryClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface)
            .windowInsetsPadding(WindowInsets.systemBars.only(WindowInsetsSides.Bottom)),
    ) {
        PlaceholderActionFailed(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .weight(1f)
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = 128.dp,
                    bottom = MaterialTheme.spacing.xxlarge,
                ),
            title = stringResource(R.string.transit_card_backup_failed),
            description = stringResource(R.string.transit_card_backup_failed_content),
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(all = MaterialTheme.spacing.medium),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        ) {
            PlainPrimaryButton(
                modifier = Modifier.fillMaxWidth(),
                label = stringResource(BR.string.retry),
                onClick = onRetryClick,
            )
            PlainTextButton(
                modifier = Modifier.fillMaxWidth(),
                label = stringResource(BR.string.quit),
                onClick = onNavigateUp,
            )
        }
    }
}

@Composable
private fun TipView(
    tipContent1: String,
    tipContent2: String,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        var minWidthPx by remember { mutableIntStateOf(0) }
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        ) {
            Text(
                modifier = Modifier
                    .alignByBaseline()
                    .onSizeChanged { minWidthPx = maxOf(minWidthPx, it.width) }
                    .widthIn(min = with(LocalDensity.current) { minWidthPx.toDp() }),
                text = stringResource(R.string.transit_card_tip_1),
                style = Suunto.typography.bodyL,
                color = MaterialTheme.colorScheme.onSurface,
            )
            Text(
                modifier = Modifier
                    .alignByBaseline()
                    .weight(1f),
                text = tipContent1,
                style = Suunto.typography.bodyL,
                color = MaterialTheme.colorScheme.onSurface,
            )
        }
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        ) {
            Text(
                modifier = Modifier
                    .alignByBaseline()
                    .onSizeChanged { minWidthPx = maxOf(minWidthPx, it.width) }
                    .widthIn(min = with(LocalDensity.current) { minWidthPx.toDp() }),
                text = stringResource(R.string.transit_card_tip_2),
                style = Suunto.typography.bodyL,
                color = MaterialTheme.colorScheme.onSurface,
            )
            Text(
                modifier = Modifier
                    .alignByBaseline()
                    .weight(1f),
                text = tipContent2,
                style = Suunto.typography.bodyL,
                color = MaterialTheme.colorScheme.onSurface,
            )
        }
    }
}
