package com.stt.android.nfc.impl.model

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.nfc.impl.R

val PayMethod.titleRes: Int
    @StringRes
    get() = when (this) {
        PayMethod.ALIPAY -> R.string.transit_card_payment_method_alipay
        PayMethod.WECHAT_PAY -> R.string.transit_card_payment_method_wechat_pay
    }

val PayMethod.iconRes: Int
    @DrawableRes
    get() = when (this) {
        PayMethod.ALIPAY -> R.drawable.ic_payment_method_alipay
        PayMethod.WECHAT_PAY -> R.drawable.ic_payment_method_wechat
    }
