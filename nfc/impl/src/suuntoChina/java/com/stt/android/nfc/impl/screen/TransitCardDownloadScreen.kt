package com.stt.android.nfc.impl.screen

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.spacing
import com.stt.android.nfc.impl.R
import com.stt.android.nfc.impl.model.DownloadMode
import com.stt.android.nfc.impl.screen.components.PlaceholderActionFailed
import com.stt.android.nfc.impl.screen.components.PlaceholderActionSuccess
import com.stt.android.nfc.impl.screen.components.PlaceholderTransitCardNormal
import com.stt.android.nfc.impl.screen.components.PlainPrimaryButton
import com.stt.android.nfc.impl.screen.components.PlainTextButton
import com.stt.android.nfc.impl.screen.components.ProgressBar
import com.stt.android.nfc.impl.screen.vm.TransitCardDownloadFailure
import com.stt.android.nfc.impl.screen.vm.TransitCardDownloadSuccess
import com.stt.android.nfc.impl.screen.vm.TransitCardDownloadViewModel
import com.stt.android.nfc.impl.screen.vm.TransitCardDownloading
import com.stt.android.R as BR

@Composable
internal fun TransitCardDownloadScreen(
    viewModel: TransitCardDownloadViewModel,
    onNavigateUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val uiState by viewModel.uiState.collectAsState()
    val isDownloading = uiState == TransitCardDownloading

    LaunchedEffect(Unit) {
        viewModel.performDownload()
    }

    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.background)
            .fillMaxSize(),
    ) {
        if (!isDownloading) {
            SuuntoTopBar(
                modifier = Modifier.fillMaxWidth(),
                title = "", // TODO
                onNavigationClick = onNavigateUp,
            )
        }
        when (uiState) {
            TransitCardDownloading -> {
                val progress by viewModel.progress.collectAsState()
                DownloadingView(
                    modifier = Modifier
                        .weight(1f)
                        .narrowContent(),
                    mode = viewModel.mode,
                    progress = progress,
                )
            }

            TransitCardDownloadSuccess -> {
                DownloadSuccessView(
                    modifier = Modifier
                        .weight(1f)
                        .narrowContent(),
                    mode = viewModel.mode,
                    onNavigateUp = onNavigateUp,
                )
            }

            TransitCardDownloadFailure -> {
                DownloadFailureView(
                    modifier = Modifier
                        .weight(1f)
                        .narrowContent(),
                    mode = viewModel.mode,
                    onNavigateUp = onNavigateUp,
                    onRetryClick = viewModel::performDownload,
                )
            }
        }
    }

    BackHandler {
        if (!isDownloading) {
            onNavigateUp()
        }
    }
}

@Composable
private fun DownloadingView(
    mode: DownloadMode,
    progress: Float,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .background(MaterialTheme.colorScheme.surface)
            .windowInsetsPadding(WindowInsets.systemBars.only(WindowInsetsSides.Bottom)),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
    ) {
        val titleRes = when (mode) {
            DownloadMode.ADD -> R.string.transit_card_download_adding
            DownloadMode.REDOWNLOAD -> R.string.transit_card_download_redownloading
        }
        val descRes = when (mode) {
            DownloadMode.ADD -> R.string.transit_card_download_adding_content
            DownloadMode.REDOWNLOAD -> R.string.transit_card_download_redownloading_content
        }
        PlaceholderTransitCardNormal(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = 116.dp,
                    bottom = MaterialTheme.spacing.medium,
                ),
            title = stringResource(titleRes),
            description = stringResource(descRes),
        )
        ProgressBar(
            modifier = Modifier.padding(
                horizontal = MaterialTheme.spacing.xxlarge,
                vertical = MaterialTheme.spacing.xsmaller,
            ),
            progress = progress,
        )
    }
}

@Composable
private fun DownloadSuccessView(
    mode: DownloadMode,
    onNavigateUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface)
            .windowInsetsPadding(WindowInsets.systemBars.only(WindowInsetsSides.Bottom)),
    ) {
        val titleRes = when (mode) {
            DownloadMode.ADD -> R.string.transit_card_download_adding_success
            DownloadMode.REDOWNLOAD -> R.string.transit_card_download_redownload_success
        }
        PlaceholderActionSuccess(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .weight(1f)
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = 128.dp,
                    bottom = MaterialTheme.spacing.xxlarge,
                ),
            title = stringResource(titleRes),
            description = when (mode) {
                DownloadMode.ADD -> stringResource(R.string.transit_card_download_adding_success_content)
                DownloadMode.REDOWNLOAD -> null
            },
        )
        PlainPrimaryButton(
            modifier = Modifier
                .padding(MaterialTheme.spacing.medium)
                .fillMaxWidth(),
            label = stringResource(BR.string.got_it),
            onClick = onNavigateUp,
        )
    }
}

@Composable
private fun DownloadFailureView(
    mode: DownloadMode,
    onNavigateUp: () -> Unit,
    onRetryClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface)
            .windowInsetsPadding(WindowInsets.systemBars.only(WindowInsetsSides.Bottom)),
    ) {
        val titleRes = when (mode) {
            DownloadMode.ADD -> R.string.transit_card_download_adding_failed
            DownloadMode.REDOWNLOAD -> R.string.transit_card_download_redownload_failed
        }
        PlaceholderActionFailed(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .weight(1f)
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = 128.dp,
                    bottom = MaterialTheme.spacing.xxlarge,
                ),
            title = stringResource(titleRes),
            description = when (mode) {
                DownloadMode.ADD -> stringResource(R.string.transit_card_download_adding_failed_content)
                DownloadMode.REDOWNLOAD -> null
            },
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(all = MaterialTheme.spacing.medium),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        ) {
            PlainPrimaryButton(
                modifier = Modifier.fillMaxWidth(),
                label = stringResource(BR.string.retry),
                onClick = onRetryClick,
            )
            PlainTextButton(
                modifier = Modifier.fillMaxWidth(),
                label = stringResource(BR.string.quit),
                onClick = onNavigateUp,
            )
        }
    }
}
