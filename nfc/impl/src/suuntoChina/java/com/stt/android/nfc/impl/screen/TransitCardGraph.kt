package com.stt.android.nfc.impl.screen

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.stt.android.nfc.impl.model.DownloadMode
import com.stt.android.nfc.impl.screen.TransitCardRoute.TRANSIT_CARD_ACTIVATION
import com.stt.android.nfc.impl.screen.TransitCardRoute.TRANSIT_CARD_BACKUP
import com.stt.android.nfc.impl.screen.TransitCardRoute.TRANSIT_CARD_CHARGE
import com.stt.android.nfc.impl.screen.TransitCardRoute.TRANSIT_CARD_DETAIL
import com.stt.android.nfc.impl.screen.TransitCardRoute.TRANSIT_CARD_DOWNLOAD
import com.stt.android.nfc.impl.screen.TransitCardRoute.TRANSIT_CARD_PAYMENT
import com.stt.android.nfc.impl.screen.TransitCardRoute.TRANSIT_CARD_PICKER
import com.stt.android.nfc.impl.screen.TransitCardRoute.TRANSIT_CARD_REFUND

@Composable
internal fun TransitCardGraph(
    onNavigateUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val navController = rememberNavController()
    NavHost(
        modifier = modifier,
        navController = navController,
        startDestination = TRANSIT_CARD_DETAIL,
    ) {
        composable(TRANSIT_CARD_DETAIL) {
            TransitCardDetailScreen(
                onNavigateUp = onNavigateUp,
                onAdd = {
                    navController.navigate(TRANSIT_CARD_PICKER) {
                        launchSingleTop = true
                    }
                },
                onTopUp = {
                    navController.navigate(TRANSIT_CARD_CHARGE) {
                        launchSingleTop = true
                    }
                },
                onTransactions = {
                },
                onMoveCard = {
                    navController.navigate(TRANSIT_CARD_BACKUP) {
                        launchSingleTop = true
                    }
                },
                onReturnCard = {
                    navController.navigate(TRANSIT_CARD_REFUND) {
                        launchSingleTop = true
                    }
                },
                onCardDetail = {
                },
                onDownloadCard = {
                },
            )
        }
        composable(TRANSIT_CARD_PICKER) {
            TransitCardPickerScreen(
                onNavigateUp = { navController.navigateUp() },
                onCardClick = { /* TODO */ },
                onActivate = {
                    navController.navigate(TRANSIT_CARD_ACTIVATION) {
                        launchSingleTop = true
                        popUpTo(TRANSIT_CARD_DETAIL)
                    }
                },
            )
        }
        composable(TRANSIT_CARD_ACTIVATION) {
            TransitCardActivationScreen(
                viewModel = viewModel(),
                onNavigateUp = { navController.navigateUp() },
                onCardClick = { /* TODO */ },
                onFaqClick = { /* TODO */ },
                onAgreementClick = { /* TODO */ },
                onActivateClick = { /* TODO */ },
            )
        }
        composable(TRANSIT_CARD_CHARGE) {
            TransitCardChargeScreen(
                viewModel = viewModel(),
                onNavigateUp = { navController.navigateUp() },
                onFaqClick = { /* TODO */ },
                onTopUpClick = { /* TODO */ },
            )
        }
        composable(TRANSIT_CARD_PAYMENT) {
            TransitCardPaymentScreen(
                viewModel = viewModel(),
                onNavigateUp = { navController.navigateUp() },
            )
        }
        composable(TRANSIT_CARD_BACKUP) {
            TransitCardBackupScreen(
                viewModel = viewModel(),
                onNavigateUp = { navController.navigateUp() },
            )
        }
        composable(TRANSIT_CARD_REFUND) {
            TransitCardRefundScreen(
                viewModel = viewModel(),
                onNavigateUp = { navController.navigateUp() },
            )
        }
        composable(
            "$TRANSIT_CARD_DOWNLOAD/{mode}",
            arguments = listOf(
                navArgument("mode") {
                    type = NavType.StringType
                    defaultValue = DownloadMode.REDOWNLOAD.mode
                },
            ),
        ) {
            TransitCardDownloadScreen(
                viewModel = viewModel(),
                onNavigateUp = { navController.navigateUp() },
            )
        }
    }
}
