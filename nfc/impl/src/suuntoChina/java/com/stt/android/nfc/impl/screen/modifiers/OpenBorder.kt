package com.stt.android.nfc.impl.screen.modifiers

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlin.math.min

@Stable
internal enum class OpenBorderType {
    FULL,
    TOP_SIDES,
    BOTTOM_SIDES,
    SIDES,
}

internal fun Modifier.openBorder(
    type: OpenBorderType,
    color: Color,
    width: Dp,
    cornerRadius: Dp,
): Modifier = this.then(
    Modifier.drawWithCache {
        val w = size.width
        val h = size.height
        if (width <= 0.dp || w <= 0f || h <= 0f) {
            return@drawWithCache onDrawBehind { }
        }

        val strokeWidth = width.toPx()
        val half = strokeWidth / 2f
        val style = Stroke(width = strokeWidth)

        val rawR = if (type != OpenBorderType.SIDES) {
            cornerRadius.toPx().coerceAtLeast(0f)
        } else 0f
        val maxAllowed = min(w, h) / 2f
        val r = rawR.coerceAtMost(maxAllowed)

        val path: Path? = when (type) {
            OpenBorderType.FULL -> null
            OpenBorderType.SIDES -> Path().apply {
                moveTo(0f, -half)
                lineTo(0f, h + half)
                moveTo(w, -half)
                lineTo(w, h + half)
            }

            OpenBorderType.TOP_SIDES -> Path().apply {
                moveTo(0f, h + half)
                lineTo(0f, r)
                if (r > 0f) {
                    arcTo(Rect(0f, 0f, 2 * r, 2 * r), 180f, 90f, false)
                } else {
                    lineTo(0f, 0f)
                }
                lineTo(w - r, 0f)
                if (r > 0f) {
                    arcTo(Rect(w - 2 * r, 0f, w, 2 * r), 270f, 90f, false)
                } else {
                    lineTo(w, 0f)
                }
                lineTo(w, h + half)
            }

            OpenBorderType.BOTTOM_SIDES -> Path().apply {
                moveTo(0f, -half)
                lineTo(0f, h - r)
                if (r > 0f) {
                    arcTo(Rect(0f, h - 2 * r, 2 * r, h), 180f, -90f, false)
                } else {
                    lineTo(0f, h)
                }
                lineTo(w - r, h)
                if (r > 0f) {
                    arcTo(Rect(w - 2 * r, h - 2 * r, w, h), 90f, -90f, false)
                } else {
                    lineTo(w, h)
                }
                lineTo(w, -half)
            }
        }

        onDrawWithContent {
            drawContent()
            when (type) {
                OpenBorderType.FULL -> drawRoundRect(
                    color = color,
                    size = Size(w, h),
                    cornerRadius = CornerRadius(r, r),
                    style = style,
                )

                else -> path?.let {
                    drawPath(
                        it,
                        color = color,
                        style = style,
                    )
                }
            }
        }
    }
)

@Preview(showBackground = false, backgroundColor = 0xFFFFFFFF)
@Composable
private fun OpenBorderPreview() {
    val borderColor = Color(0xFF2979FF)
    val corner = 16.dp
    val stroke = 1.dp
    val width = 320.dp
    Column(modifier = Modifier.padding(8.dp)) {
        Box(
            Modifier
                .size(width, 56.dp)
                .openBorder(OpenBorderType.FULL, borderColor, stroke, corner),
        )
        Spacer(modifier = Modifier.height(8.dp))
        Box(
            Modifier
                .size(width, 56.dp)
                .openBorder(OpenBorderType.TOP_SIDES, borderColor, stroke, corner)
                .background(Color.Yellow, RoundedCornerShape(topStart = corner, topEnd = corner)),
        )
        Box(
            Modifier
                .size(width, 56.dp)
                .openBorder(OpenBorderType.SIDES, borderColor, stroke, corner)
                .background(Color.Green),
        )
        Box(
            Modifier
                .size(width, 56.dp)
                .openBorder(OpenBorderType.BOTTOM_SIDES, borderColor, stroke, corner)
                .background(
                    Color.Red,
                    RoundedCornerShape(bottomStart = corner, bottomEnd = corner)
                ),
        )
    }
}
