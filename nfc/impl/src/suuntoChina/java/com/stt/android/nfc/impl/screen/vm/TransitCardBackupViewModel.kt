package com.stt.android.nfc.impl.screen.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class TransitCardBackupViewModel @Inject constructor() : ViewModel() {

    private val _uiState = MutableStateFlow<TransitCardBackupUiState>(
        TransitCardBackupConfirmation
    )
    val uiState = _uiState.asStateFlow()

    private val _progress = MutableStateFlow(0f)
    val progress = _progress.asStateFlow()

    // For mock only
    private var retryCount: Int = 0

    fun onConfirmed() = performBackup()

    fun performBackup() {
        _progress.value = 0f
        _uiState.value = TransitCardBackupProcessing

        viewModelScope.launch {
            // mock
            while (true) {
                delay(100)
                _progress.value += 0.01f
                if (_progress.value >= 1f) break
            }
            if (retryCount >= 1) {
                _uiState.value = TransitCardBackupSuccess
            } else {
                retryCount++
                _uiState.value = TransitCardBackupFailure
            }
            // TODO : perform backup operation
            // On success: _uiState.value = TransitCardBackupSuccess
            // On failure: _uiState.value = TransitCardBackupFailure
        }
    }
}
