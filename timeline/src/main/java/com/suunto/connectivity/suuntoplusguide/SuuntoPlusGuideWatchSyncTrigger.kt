package com.suunto.connectivity.suuntoplusguide

import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities

interface SuuntoPlusGuideWatchSyncTrigger {
    // Sync SuuntoPlus™ Guides and Features to watch and update plugin status to local database
    suspend fun syncWatchPlugins(
        serial: String,
        capabilities: SuuntoWatchCapabilities,
        watchBusyStateProvider: WatchBusyStateProvider
    ): SuuntoPlusGuideSyncLogicResult
}
