package com.suunto.connectivity.capabilities

import com.suunto.connectivity.deviceid.ISuuntoDeviceCapabilityInfo.Companion.HR_INTENSITY_ZONES_PREFIX
import com.suunto.connectivity.deviceid.ISuuntoDeviceCapabilityInfo.Companion.OFFLINE_MAPS_CAPABILITY_PREFIX
import com.suunto.connectivity.deviceid.ISuuntoDeviceCapabilityInfo.Companion.USER_PROFILE_SETTING
import com.suunto.connectivity.deviceid.ISuuntoDeviceCapabilityInfo.Companion.WATCH_WIDGETS_CAPABILITY_PREFIX

class SuuntoWatchCapabilities(
    unorderedCapabilities: List<String>?
) {
    constructor(stringValue: String) : this(stringValue.split(","))

    val capabilities = unorderedCapabilities?.sorted().orEmpty()

    val stringValue = capabilities.joinToString(",")

    override fun equals(other: Any?): Boolean {
        val otherCap = (other as? SuuntoWatchCapabilities) ?: return false

        return capabilities == otherCap.capabilities
    }

    override fun hashCode() = capabilities.hashCode()

    override fun toString() = capabilities.toString()

    val areSuuntoPlusFeaturesSupported: Boolean
        get() = capabilities.any { it.startsWith(SUUNTO_PLUS_PLUGIN_CAPABILITY_PREFIX) }

    val areSuuntoPlusGuidesSupported: Boolean
        get() = areBESSuuntoPlusGuidesSupported || (areSuuntoPlusFeaturesSupported &&
            capabilities.any { it.startsWith(SUUNTO_PLUS_GUIDE_CAPABILITY_PREFIX) })

    val areSuuntoPlusWatchfaceSupported: Boolean
        get() = areSuuntoPlusFeaturesSupported &&
            capabilities.any { it.startsWith(SUUNTO_PLUS_WATCHFACE_CAPABILITY_PREFIX) }

    val areBESSuuntoPlusGuidesSupported: Boolean
        get() = capabilities.any { it.startsWith(BES_SUUNTO_PLUS_GUIDE_CAPABILITY_PREFIX) }

    // If watch is missing hardware GPS, barometer, or temperature sensor, it cannot support
    // all SuuntoPlus™ content from the store
    val limitedSuuntoPlusSupport: Boolean
        get() = capabilities.none { it.startsWith(HW_GPS_PREFIX) } ||
            capabilities.none { it.startsWith(HW_PRESSURE_PREFIX) } ||
            capabilities.none { it.startsWith(HW_TEMPERATURE_PREFIX) }

    val hasExplicitScreenSize: Boolean
        get() = capabilities.any { it.startsWith(UI_SCREEN_SIZE_PREFIX) }

    val areOfflineMapsSupported: Boolean
        get() = capabilities.any { it.startsWith(OFFLINE_MAPS_CAPABILITY_PREFIX) }

    val areSportsAppSettingsSupported: Boolean
        get() = capabilities.any { it.startsWith(SPORTS_APP_SETTINGS_PREFIX) }

    val supportsHrv: Boolean
        get() = capabilities.any { it.startsWith(HRV_PREFIX) }

    val widgetVersion: String?
        get() = capabilities.firstOrNull {
            it.startsWith(WATCH_WIDGETS_CAPABILITY_PREFIX)
        }?.removePrefix("${WATCH_WIDGETS_CAPABILITY_PREFIX}_")

    /**
     * By default watches support a maximum of 15 enabled sports apps. This can be overridden
     * by the 'feat_zappcount_' capability which allows specifying a larger value for the limit
     * such as 'feat_zappcount_100'.
     */
    val maxNumberOfEnabledFeatures: Int
        get() = capabilities.firstNotNullOfOrNull { cap ->
            ZAPP_COUNT_REGEX.matchEntire(cap)?.groups?.get(1)?.value?.toIntOrNull()
        } ?: DEFAULT_MAX_NUMBER_OF_ENABLED_FEATURES

    // eg. 'feat_zappcount_10'
    val maxNumberOfEnabledWatchface: Int
        get() = capabilities.firstNotNullOfOrNull { cap ->
            ZAPP_WATCHFACE_COUNT_REGEX.matchEntire(cap)?.groups?.get(1)?.value?.toIntOrNull()
        } ?: DEFAULT_MAX_NUMBER_OF_WATCHFACE

    val hrIntensityZonesSupported: Boolean =
        capabilities.any { it.startsWith(HR_INTENSITY_ZONES_PREFIX) }

    val supportsUserProfileSetting: Boolean
        get() = capabilities.any { it.startsWith(USER_PROFILE_SETTING) }

    companion object {
        val EMPTY = SuuntoWatchCapabilities(emptyList())

        // Support installing and uninstalling Zapp plugins, such as SuuntoPlus Features
        private const val SUUNTO_PLUS_PLUGIN_CAPABILITY_PREFIX = "feat_plugins"

        // Support SuuntoPlus Guides (requires also "feat_plugins_1")
        private const val SUUNTO_PLUS_GUIDE_CAPABILITY_PREFIX = "feat_guides"

        // Support DiLu Guides
        private const val BES_SUUNTO_PLUS_GUIDE_CAPABILITY_PREFIX = "feat_guidesforbes"

        // Support SuuntoPlus Guides (requires also "feat_plugins_1")
        private const val UI_SCREEN_SIZE_PREFIX = "ui_screensize_"

        // Support SuuntoPlus watchface (requires also "feat_zappwf_v1")
        private const val SUUNTO_PLUS_WATCHFACE_CAPABILITY_PREFIX = "feat_zappwf_"

        // Supports hardware GPS
        private const val HW_GPS_PREFIX = "hw_gps"

        // Supports barometer
        private const val HW_PRESSURE_PREFIX = "hw_pressure"

        // Supports temperature measurement
        private const val HW_TEMPERATURE_PREFIX = "hw_temperature"

        // Supports sports app settings
        private const val SPORTS_APP_SETTINGS_PREFIX = "plugin_settings"

        private const val HRV_PREFIX = "feat_hrv"

        private const val DEFAULT_MAX_NUMBER_OF_ENABLED_FEATURES = 15
        private val ZAPP_COUNT_REGEX = """feat_zappcount_(\d+)""".toRegex()

        private const val DEFAULT_MAX_NUMBER_OF_WATCHFACE = 10
        private val ZAPP_WATCHFACE_COUNT_REGEX = """feat_zappwfcount_(\d+)""".toRegex()
    }
}
