package com.suunto.connectivity.deviceid

import com.stt.android.domain.firmware.Version
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import timber.log.Timber
import kotlin.math.min

/**
 * Interface ISuuntoDeviceCapabilityInfo. All devices recognized by the application _must_ implement this
 *
 *
 * Created by <PERSON><PERSON> on 9/1/2016.
 */
interface ISuuntoDeviceCapabilityInfo {
    val suuntoDeviceType: SuuntoDeviceType

    // Can connect through Whiteboard
    val isWhiteboard: Boolean

    // Is a watch, includes devices like Eon
    val isWatch: Boolean

    fun hasGpsSensor(): Boolean
    fun supportsBarometricAltitude(): Boolean
    fun supportsNotifications(firmwareVersion: String?): Boolean
    fun supportsTrendData(mdsDeviceInfo: MdsDeviceInfo?): Boolean
    fun supportsSleepData(mdsDeviceInfo: MdsDeviceInfo?): Boolean
    fun supportsPOISync(mdsDeviceInfo: MdsDeviceInfo?): Boolean
    fun supportsRoutesSync(mdsDeviceInfo: MdsDeviceInfo?): Boolean
    fun supportsRecoveryData(mdsDeviceInfo: MdsDeviceInfo?): Boolean
    fun supportsSystemEvents(): Boolean
    fun supportsOtaUpdate(firmwareVersion: String): Boolean
    fun supportsMediaAndNotificationControls(firmwareVersion: String): Boolean
    fun supportsWidgets(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        val capabilities = mdsDeviceInfo?.capabilities
        return supportsWidgets(capabilities)
    }

    fun supportsWidgets(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(WATCH_WIDGETS_CAPABILITY_PREFIX) } == true

    fun supportsWifiSetup(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        val capabilities = mdsDeviceInfo?.capabilities
        return supportsWifiSetup(capabilities)
    }

    fun supportsWifiSetup(capabilities: List<String>?) =
        capabilities?.any { it.startsWith(WATCH_WIFI_SETUP_CAPABILITY_PREFIX) } == true

    fun supportsOfflineMaps(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        val capabilities = mdsDeviceInfo?.capabilities
        return supportsOfflineMaps(capabilities)
    }

    fun supportsOfflineMaps(capabilities: List<String>?) =
        capabilities?.any { it.startsWith(OFFLINE_MAPS_CAPABILITY_PREFIX) } == true

    fun requiresForcedUpdate(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        val capabilities = mdsDeviceInfo?.capabilities
        return requiresForcedUpdate(capabilities)
    }

    fun requiresForcedUpdate(capabilities: List<String>?): Boolean {
        return capabilities?.any { it.startsWith(WATCH_FORCED_UPDATE_CAPABILITY_PREFIX) } == true
    }

    fun supportsTurnByTurnWaypoints(mdsDeviceInfo: MdsDeviceInfo?): Boolean = runCatching {
        Version(mdsDeviceInfo?.swVersion ?: "") >= Version("2.22.34")
    }.getOrElse { e ->
        Timber.w(
            e,
            "Error in supportsTurnByTurnWaypoints, device sw version = '${mdsDeviceInfo?.swVersion}'"
        )
        false
    }

    /**
     * Currently only Suunto Orca have the plan to support weather.
     */
    fun supportsWeather(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(WATCH_WEATHER_CAPABILITY_PREFIX) } == true || supportsWeatherV2(
            capabilities
        )

    /**
     * Currently Suunto Run have the plan to support weather_v2.
     */
    fun supportsWeatherV2(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(WATCH_WEATHER_V2_CAPABILITY) } == true

    /**
     * Suunto Run have the plan to support weather_v3.
     */
    fun supportsWeatherV3(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(WATCH_WEATHER_V3_CAPABILITY) } == true

    fun supportsFindPhone(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(WATCH_FIND_PHONE_CAPABILITY_PREFIX) } == true

    fun supportsTrainingZoneSync(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        val capabilities = mdsDeviceInfo?.capabilities
        return capabilities?.any { it.startsWith(TRAINING_ZONE_SYNC_CAPABILITY_PREFIX) } == true
    }

    fun supportsZoneSenseSync(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(ZONE_SENSE_SYNC_CAPABILITY_PREFIX) } == true

    fun supportsClimbGuidance(mdsDeviceInfo: MdsDeviceInfo?): Boolean =
        mdsDeviceInfo?.capabilities
            ?.any { it.startsWith(CLIMB_GUIDANCE_PREFIX) }
            ?: false

    fun maxRoutePointsAllowed(mdsDeviceInfo: MdsDeviceInfo?): Int {
        val maxRoutePointsAllowed = mdsDeviceInfo?.capabilities
            ?.find { it.startsWith(MAX_ROUTEPOINTS_PREFIX) }
            ?.removePrefix(MAX_ROUTEPOINTS_PREFIX)
            ?.drop(1)?.toInt()
            ?: DEFAULT_MAX_ROUTE_POINTS_FOR_WATCH

        // Todo Race and Vertical will start supporting 10_000 points, however we decided to start
        //  with max 5_000 points to see how it goes and affects e.g. sync times. Can be removed
        //  when we decide to support whatever is set in the capability
        return min(5000, maxRoutePointsAllowed)
    }

    fun supportsSyncLastKnownLocation(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(LAST_KNOWN_COORDINATES_PREFIX) } == true

    fun supportBusCode(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(SOCKET_PREFIX) } == true

    fun supportsOtaUpdateCheck(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(WATCH_OTA_CHECK_PREFIX) } == true

    fun supportsScanConnect(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(LAUNCHER_STATUS_PREFIX) } == true

    fun supportsHrIntensityZones(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(HR_INTENSITY_ZONES_PREFIX) } == true

    fun supportsDeviceSetting(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        val capabilities = mdsDeviceInfo?.capabilities
        return capabilities?.any { it.startsWith(WATCH_DEVICE_SETTING_PREFIX) } == true
    }

    fun supportsOfflineMusic(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(OFFLINE_MUSIC_CAPABILITY_PREFIX) } == true

    fun supportsRunSportMode(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(BES_SPORT_MODE_PREFIX) } == true

    fun supportsCompetitionModeV2(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(BES_SPORT_MODE_COMPETITION_V2_PREFIX) } == true

    fun supportsUserProfileSetting(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        val capabilities = mdsDeviceInfo?.capabilities
        return capabilities?.any { it.startsWith(USER_PROFILE_SETTING) } == true
    }

    fun supportsBESTrainingZoneSync(mdsDeviceInfo: MdsDeviceInfo?): Boolean =
        mdsDeviceInfo?.capabilities
            ?.any { it.startsWith(BES_TRAINING_ZONE_SYNC_CAPABILITY_PREFIX) } == true

    fun supportsQuickNavigation(mdsDeviceInfo: MdsDeviceInfo?): Boolean =
        mdsDeviceInfo?.capabilities
            ?.any { it.startsWith(START_QUICK_NAVIGATION) } == true

    fun supportsDeleteRoute(mdsDeviceInfo: MdsDeviceInfo?): Boolean =
        mdsDeviceInfo?.capabilities?.any { it.startsWith(WATCH_DELETE_ROUTE_PREFIX) } == true

    fun supportsBESBuriedPoint(): Boolean = false

    fun isWidgetCountUnlimited(capabilities: List<String>?): Boolean =
        capabilities?.any { it.startsWith(WATCH_WIDGETS_UNLIMITED_CAPABILITY_PREFIX) } == true

    fun supportsOtaUpdateCheckV2(capabilities: List<String>?): Boolean =
        capabilities?.any { it == WATCH_OTA_CHECK_V2 } == true

    fun supportsTrainingZoneExerciseSync(mdsDeviceInfo: MdsDeviceInfo?): Boolean =
        mdsDeviceInfo?.capabilities?.any { it == WATCH_ACTIVITY_HISTORY_SYNC_CAPABILITY } == true

    fun supportsRunOta2(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return mdsDeviceInfo?.capabilities?.any { it == RUN_OTA_2 } == true
    }

    fun supportsInstallSelectedFirmware(mdsDeviceInfo: MdsDeviceInfo?): Boolean =
        mdsDeviceInfo?.capabilities?.any { it == INSTALL_SELECTED_FIRMWARE_CAPABILITY } == true

    fun supportsBesFindWatch(mdsDeviceInfo: MdsDeviceInfo?): Boolean =
        mdsDeviceInfo?.capabilities?.any { it == BES_FIND_WATCH } == true

    fun supportsResources(): Boolean = true

    companion object {
        private const val DEFAULT_MAX_ROUTE_POINTS_FOR_WATCH = 1_000

        internal const val WATCH_WIDGETS_CAPABILITY_PREFIX = "feat_widgets"
        private const val WATCH_WIDGETS_UNLIMITED_CAPABILITY_PREFIX = "feat_unlimited_widgets"
        private const val WATCH_WIFI_SETUP_CAPABILITY_PREFIX = "hw_wifi"
        internal const val OFFLINE_MAPS_CAPABILITY_PREFIX = "feat_offlinemaps"
        private const val WATCH_FORCED_UPDATE_CAPABILITY_PREFIX = "feat_forcedupdate"
        private const val WATCH_WEATHER_CAPABILITY_PREFIX = "feat_weather"
        private const val WATCH_WEATHER_V2_CAPABILITY = "feat_weather_v2"
        private const val WATCH_WEATHER_V3_CAPABILITY = "feat_weather_v3"
        private const val WATCH_FIND_PHONE_CAPABILITY_PREFIX = "feat_findphone"
        private const val TRAINING_ZONE_SYNC_CAPABILITY_PREFIX = "feat_trainingzone"
        private const val LAST_KNOWN_COORDINATES_PREFIX = "feat_lastknowngeocoordinates"
        private const val CLIMB_GUIDANCE_PREFIX = "feat_climbguidance"
        private const val MAX_ROUTEPOINTS_PREFIX = "feat_routepoints"
        private const val SOCKET_PREFIX = "feat_socket"
        private const val WATCH_OTA_CHECK_PREFIX = "feat_otacheck"
        private const val WATCH_OTA_CHECK_V2 = "feat_otacheck_v2"
        private const val ZONE_SENSE_SYNC_CAPABILITY_PREFIX = "feat_ddfa"
        private const val LAUNCHER_STATUS_PREFIX = "feat_devicesettings"
        internal const val HR_INTENSITY_ZONES_PREFIX = "feat_hrintensityzones"
        private const val WATCH_DEVICE_SETTING_PREFIX = "feat_devicesettings_v1"
        private const val OFFLINE_MUSIC_CAPABILITY_PREFIX = "feat_offlinemusic_v1"
        private const val BES_SPORT_MODE_PREFIX = "feat_bessportmode"
        private const val BES_SPORT_MODE_COMPETITION_V2_PREFIX = "feat_bessportmode_v2"
        internal const val USER_PROFILE_SETTING = "feat_syncuserprofile"
        private const val BES_TRAINING_ZONE_SYNC_CAPABILITY_PREFIX = "feat_bestraining"
        private const val START_QUICK_NAVIGATION = "feat_quicknavigation_1"
        private const val WATCH_DELETE_ROUTE_PREFIX = "feat_deleteroute"
        private const val WATCH_ACTIVITY_HISTORY_SYNC_CAPABILITY = "feat_activityhistoryapp_1"
        private const val INSTALL_SELECTED_FIRMWARE_CAPABILITY = "feat_installselectedfirmware_v1"
        private const val RUN_OTA_2 = "feat_run_ota_2"
        private const val BES_FIND_WATCH = "feat_bes_findwatch"
    }
}
