package com.suunto.connectivity.suuntoconnectivity.device

import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants
import timber.log.Timber
import java.util.Locale

/**
 * All devices understood by the application should be enumerated here
 * Keep in sync with the device names in Constants.java and SuuntoConnectivityConstants.java
 * Note: The device name matching is not foolproof as both BT names and Suunto product names
 * are mapped to the same device types. This should not affect operation in practice.
 * E.g. "ambit3 vertical" would match the BT scan name "Ambit3 " for Ambit3 Peak.
 *
 * Created by <PERSON><PERSON> on 9/12/2016.
 */
enum class SuuntoDeviceType(val isReleased: Boolean, val displayName: String) {
    Unrecognized(isReleased = false, displayName = "Unrecognized"),
    Suunto9(isReleased = true, displayName = "Suunto 9 Baro"),
    Suunto9Lima(isReleased = true, displayName = "Suunto 9"),
    Suunto9Peak(isReleased = true, displayName = "Suunto 9 Peak"),
    Suunto5(isReleased = true, displayName = "Suunto 5"),
    Suunto5Peak(isReleased = true, displayName = "Suunto 5 Peak"),
    SpartanUltra(isReleased = true, displayName = "Spartan Ultra"),
    SpartanSport(isReleased = true, displayName = "Spartan Sport"),
    SpartanSportWristHR(isReleased = true, displayName = "Spartan Sport WHR"),
    SpartanTrainer(isReleased = true, displayName = "Spartan Trainer"),
    Suunto3(isReleased = true, displayName = "Suunto 3"),
    Suunto3Fitness(isReleased = true, displayName = "Suunto 3 Fitness"),
    SpartanSportWristHRBaro(isReleased = true, displayName = "Spartan Sport WHR Baro"),
    Suunto7(isReleased = true, displayName = "Suunto 7"),
    Suunto9PeakPro(isReleased = true, displayName = "Suunto 9 Peak Pro"),
    SuuntoOcean(isReleased = false, displayName = "Suunto Ocean"),
    SuuntoVertical(isReleased = true, displayName = "Suunto Vertical"),
    SuuntoRace(isReleased = true, displayName = "Suunto Race"),
    SuuntoRaceS(isReleased = false, displayName = "Suunto Race S"),
    Ambit3Peak(isReleased = true, displayName = "Suunto Ambit3 Peak"),
    Ambit3Sport(isReleased = true, displayName = "Suunto Ambit3 Sport"),
    Ambit3Run(isReleased = true, displayName = "Suunto Ambit3 Run"),
    Ambit3Vertical(isReleased = true, displayName = "Suunto Ambit3 Vertical"),
    Traverse(isReleased = true, displayName = "Suunto Traverse"),
    TraverseAlpha(isReleased = true, displayName = "Suunto Traverse Alpha"),
    EonSteel(isReleased = true, displayName = "Suunto EON Steel"),
    EonSteelBlack(isReleased = true, displayName = "Suunto EON Steel Black"),
    EonCore(isReleased = true, displayName = "Suunto EON Core"),
    SuuntoD5(isReleased = true, displayName = "Suunto D5"),
    SuuntoRun(isReleased = true, displayName = "Suunto Run"),
    SuuntoRace2(isReleased = true, displayName = "Suunto Race 2"),
    SuuntoVertical2(isReleased = false, displayName = "Suunto Vertical 2"),
    SuuntoGT(isReleased = false, displayName = "Suunto GT");

    val isSpartan: Boolean
        get() = this == SpartanSport ||
            this == SpartanSportWristHR ||
            this == SpartanUltra ||
            this == SpartanTrainer ||
            this == Suunto3 ||
            this == Suunto3Fitness ||
            this == Suunto9 ||
            this == Suunto9Lima ||
            this == Suunto9Peak ||
            this == SpartanSportWristHRBaro ||
            this == Suunto7 ||
            this == Suunto5 ||
            this == Suunto5Peak ||
            this == Suunto9PeakPro ||
            this == SuuntoOcean ||
            this == SuuntoVertical ||
            this == SuuntoRace ||
            this == SuuntoRaceS ||
            this == SuuntoRun ||
            this == SuuntoRace2 ||
            this == SuuntoVertical2 ||
            this == SuuntoGT

    val isNgDevice: Boolean
        get() = this == SpartanSport ||
            this == SpartanSportWristHR ||
            this == SpartanUltra ||
            this == SpartanTrainer ||
            this == Suunto3 ||
            this == Suunto3Fitness ||
            this == Suunto9 ||
            this == Suunto9Lima ||
            this == Suunto9Peak ||
            this == SpartanSportWristHRBaro ||
            this == Suunto5 ||
            this == Suunto5Peak ||
            this == Suunto9PeakPro ||
            this == SuuntoOcean ||
            this == SuuntoVertical ||
            this == SuuntoRace ||
            this == SuuntoRaceS ||
            this == SuuntoRace2 ||
            this == SuuntoVertical2 ||
            this == SuuntoGT

    val isRunDevice: Boolean
        get() = this == SuuntoRun

    val isNgDiveDevice: Boolean
        get() = this == SuuntoOcean || this == SuuntoGT

    val isSuunto3Family: Boolean
        get() = this == Suunto3 || this == Suunto3Fitness

    val isSuuntoS5: Boolean
        get() = this == Suunto5 || this == Suunto5Peak

    val isSuunto7: Boolean
        get() = this == Suunto7

    val isSuuntoSparrow: Boolean
        get() = this == Suunto9PeakPro

    val isSuuntoVertical: Boolean
        get() = this == SuuntoVertical

    val isSuuntoVertical2: Boolean
        get() = this == SuuntoVertical2

    val isSuuntoRace: Boolean
        get() = this == SuuntoRace

    val isSuuntoRaceS: Boolean
        get() = this == SuuntoRaceS

    val isSuuntoRace2: Boolean
        get() = this == SuuntoRace2

    val isSuuntoOcean: Boolean
        get() = this == SuuntoOcean

    val isSuuntoGT: Boolean
        get() = this == SuuntoGT

    val isAmbit3: Boolean
        get() = this == Ambit3Peak ||
            this == Ambit3Sport ||
            this == Ambit3Run ||
            this == Ambit3Vertical

    val isTraverse: Boolean
        get() = this == Traverse ||
            this == TraverseAlpha

    val isAmbit: Boolean
        get() = isAmbit3 || isTraverse

    val isEonComputer: Boolean
        get() = this == EonCore || this == EonSteel || this == EonSteelBlack

    val isEon: Boolean
        get() = isEonComputer || this == SuuntoD5

    val isSuuntoD5: Boolean
        get() = this == SuuntoD5

    val isSuuntoRun: Boolean
        get() = this == SuuntoRun

    val hasGpsSensor: Boolean
        get() = SuuntoDeviceCapabilityInfoProvider[this].hasGpsSensor()

    val isDataLayerDevice: Boolean
        get() = this == Suunto7

    val isBleDevice: Boolean
        get() = !isDataLayerDevice

    fun supportsOtaUpdate(firmware: String): Boolean {
        return SuuntoDeviceCapabilityInfoProvider[this].supportsOtaUpdate(firmware)
    }

    fun supportOtaUpdateCheck(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return SuuntoDeviceCapabilityInfoProvider[this].supportsOtaUpdateCheck(mdsDeviceInfo?.capabilities)
    }

    companion object {

        /**
         * Informs whether the device type supports adaptive training guidance.
         */
        fun hasAdaptiveTrainingGuidance(deviceType: SuuntoDeviceType? = null, variant: String? = null): Boolean {
            val suuntoDeviceType = deviceType ?: fromVariantName(
                variant
            )
            return suuntoDeviceType.isSuuntoS5 || suuntoDeviceType.isSuunto3Family
        }

        @JvmStatic
        fun advertisementHasSerial(type: SuuntoDeviceType): Boolean = !type.isDataLayerDevice

        /**
         * Returns true if this device is and Eon according to its name in adv. packets.
         *
         * @param advPacketName Device name fetched from Ble Adv packet.
         * @return True, if Eon.
         */
        @JvmStatic
        fun isEon(advPacketName: String): Boolean {
            val result = fromBleAdvName(
                advPacketName
            ).isEon
            Timber.d("isEon: %s: %s", advPacketName, result.toString())
            return result
        }

        @JvmStatic
        fun isDataLayerDevice(advPacketName: String): Boolean {
            return fromBleAdvName(
                advPacketName
            ).isDataLayerDevice
        }

        /**
         * Convert a device variant name to its type
         * Will return SuuntoDeviceType.Unrecognized for all names that are not recognized.
         */
        @JvmStatic
        fun fromVariantName(name: String?): SuuntoDeviceType =
            SuuntoConnectivityConstants.variantNames.entries.firstOrNull { (constant, _) ->
                constant.equals(name, ignoreCase = true)
            }?.value ?: Unrecognized

        /**
         * Gets the variant name
         */
        @JvmStatic
        fun getVariantName(deviceType: SuuntoDeviceType): String {
            return SuuntoConnectivityConstants.variantNames.keys.first { key -> SuuntoConnectivityConstants.variantNames[key] == deviceType }
        }

        /**
         * Convert a ble advertisement name to its type
         * Will return SuuntoDeviceType.Unrecognized for all names that are not recognized.
         */
        @JvmStatic
        fun fromBleAdvName(name: String?): SuuntoDeviceType {
            name?.let {
                if (name.isNotEmpty()) {
                    // The order of the bleAdvNames defined in SuuntoConnectivityConstants
                    // is significant as forEach iterates the names in definition order.
                    SuuntoConnectivityConstants.bleAdvNames.forEach { (constant, deviceType) ->
                        if (name.lowercase(Locale.US).startsWith(constant.lowercase(Locale.US))) {
                            Timber.v("found Suunto Device[%s]: \"%s\"", deviceType.name, name)
                            return deviceType
                        }
                    }
                }
            }
            return Unrecognized
        }

        /**
         * Get ble advertisement prefix for a given device
         * @param name Full name of the device
         * @param suuntoDeviceType type of device
         * @return bluetooth advertisement prefix of the given combination
         */
        fun getBleAdvertisementPrefix(name: String, suuntoDeviceType: SuuntoDeviceType): String? {
            return SuuntoConnectivityConstants.bleAdvNames
                .filterValues { it == suuntoDeviceType }
                .filterKeys { name.contains(it) }
                .keys
                .firstOrNull()
        }
    }
}

fun SuuntoDeviceType.supportsWaypointNames(): Boolean =
    when (this) {
        SuuntoDeviceType.Suunto9,
        SuuntoDeviceType.Suunto9Lima,
        SuuntoDeviceType.Suunto9Peak,
        SuuntoDeviceType.Suunto5,
        SuuntoDeviceType.Suunto5Peak,
        SuuntoDeviceType.Suunto7,
        SuuntoDeviceType.Suunto9PeakPro,
        SuuntoDeviceType.SuuntoOcean,
        SuuntoDeviceType.SuuntoVertical,
        SuuntoDeviceType.SuuntoRace,
        SuuntoDeviceType.SuuntoRun,
        SuuntoDeviceType.SuuntoRaceS,
        SuuntoDeviceType.SuuntoRace2,
        SuuntoDeviceType.SuuntoVertical2 -> true
        else -> false
    }
