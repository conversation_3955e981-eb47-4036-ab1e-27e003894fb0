package com.suunto.connectivity.suuntoconnectivity.device

enum class ProductType {
    SPORT_WATCH,
    DIVE_WATCH,
    DIVE_COMPUTER,
    BIKE_COMPUTER, // Not yet supported
    SPORT_EARPHONE;

    companion object {
        fun fromVariantName(variantName: String?): ProductType? {
            val suuntoDeviceType = SuuntoDeviceType.fromVariantName(variantName)
            return when {
                suuntoDeviceType == SuuntoDeviceType.Unrecognized -> {
                    null
                }
                suuntoDeviceType.isEonComputer -> {
                    DIVE_COMPUTER
                }
                suuntoDeviceType.isEon -> {
                    DIVE_WATCH
                }
                else -> {
                    SPORT_WATCH
                }
            }
        }
    }
}
