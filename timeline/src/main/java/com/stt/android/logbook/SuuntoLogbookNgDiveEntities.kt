package com.stt.android.logbook

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.stt.android.utils.DiveGasNameHelper
import java.time.ZonedDateTime

/**
 * Represents the DiveHeader information of a dive recorded by Ng dive device (e.g., Suunto Seal).
 * This class includes start-dive and summary information.
 */
@JsonClass(generateAdapter = true)
data class NgDiveHeader(
    @param:<PERSON><PERSON>(name = "Algorithm") val algorithm: String? = null,
    @param:<PERSON><PERSON>(name = "StartTissue") val startTissue: NgDiveTissue? = null,
    @param:<PERSON><PERSON>(name = "SurfaceTime") val surfaceTime: Double? = null,
    @param:<PERSON><PERSON>(name = "SafetyStopTime") val safetyStopTime: Double? = null,
    @param:<PERSON><PERSON>(name = "DiveInSeries") val diveInSeries: Int? = null,
    @param:<PERSON>son(name = "Gases") val gases: List<NgDiveGas>? = null,
    @param:<PERSON><PERSON>(name = "HighGf") val highGF: Int? = null, // Percentage as a whole number
    @param:<PERSON><PERSON>(name = "LowGf") val lowGF: Int? = null, // Percentage as a whole number
) {
    fun getGasNameByIndex(gasIndex: Int): String {
        val gasesUsed = gases?.getUsedGasNames()
        return if (gasesUsed != null && gasIndex >= 0 && gasIndex < gasesUsed.size) {
            gasesUsed[gasIndex]
        } else {
            ""
        }
    }
}

/**
 * Represents the DiveFooter information of a dive recorded by Ng dive device (e.g., Suunto Seal).
 * This class includes post-dive information.
 */
@JsonClass(generateAdapter = true)
data class NgDiveFooter(
    @param:Json(name = "EndTissue") val endTissue: NgDiveTissue? = null,
    @param:Json(name = "Gases") val gases: List<NgDiveGas>? = null,
    @param:Json(name = "DiveLocation") val diveLocation: NgDiveLocation? = null,
    @param:Json(name = "DiveRouteGyroBias") val diveRouteGyroBias: NgDiveRouteGyroBias? = null,
)

@JsonClass(generateAdapter = true)
data class NgDiveRouteGyroBias(
    @param:Json(name = "FigureOfMerit") val figureOfMerig: Double? = null,
    @param:Json(name = "X") val x: Double? = null,
    @param:Json(name = "Y") val y: Double? = null,
    @param:Json(name = "Z") val z: Double? = null,
)

@JsonClass(generateAdapter = true)
data class NgDiveLocation(
    @param:Json(name = "Start") val start: NgDiveLocationPoint? = null,
    @param:Json(name = "Stop") val stop: NgDiveLocationPoint? = null
)

@JsonClass(generateAdapter = true)
data class NgDiveLocationPoint(
    @param:Json(name = "DateTime") val dateTime: ZonedDateTime?,
    @param:Json(name = "Latitude") val latitude: Double?,
    @param:Json(name = "Longitude") val longitude: Double?
)

// Parsed using custom logic, see MoshiSmlParser.parseDiveRouteOrigin
@JsonClass(generateAdapter = false)
data class NgDiveRouteOrigin(
    @param:Json(name = "Altitude") val altitude: Double,
    @param:Json(name = "Latitude") val latitude: Double,
    @param:Json(name = "Longitude") val longitude: Double,
)

@JsonClass(generateAdapter = true)
data class NgDiveTissue(
    @param:Json(name = "OTU") val otu: Double? = null,
    @param:Json(name = "CNS") val cns: Double? = null
)

// Check avgVentilation, startPressure, and endPressure from the DiveFooter gases list
@JsonClass(generateAdapter = true)
data class NgDiveGas(
    @param:Json(name = "Helium") val helium: Int?,
    @param:Json(name = "Oxygen") val oxygen: Int?,
    @param:Json(name = "State") val state: String?,
    @param:Json(name = "AvgVentilation") val avgVentilation: Double?,
    @param:Json(name = "StartPressure") val startPressure: Double?,
    @param:Json(name = "EndPressure") val endPressure: Double?,
    @param:Json(name = "TransmitterID") val transmitterId: Double?,
    @param:Json(name = "StartPressure2") val startPressure2: Double?, // For side mount
    @param:Json(name = "EndPressure2") val endPressure2: Double?, // For side mount
    @param:Json(name = "TransmitterID2") val transmitterId2: Double?, // For side mount
) {
    val startPressureKPa
        get() = startPressure?.div(1000)

    val endPressureKPa
        get() = endPressure?.div(1000)

    // For side mount
    val startPressure2KPa
        get() = startPressure2?.div(1000)

    // For side mount
    val endPressure2KPa
        get() = endPressure2?.div(1000)
}

data class NgDiveData(
    val maxDepth: Float?,
    val algorithm: String?,
    val diveNumberInSeries: Int?,
    val cns: Float?,
    val otu: Float?,
    val gasQuantities: Map<String, LogbookGasData?>?,
    val surfaceTime: Float?,
    val diveTime: Float?,
    val gasesUsed: List<String>?,
    val avgDepth: Float?,
    val minGF: Float?,
    val maxGF: Float?
)

/**
 * Note: DiveHeader has similar "Gases" list but StartPressure and EndPressure are unused and have always null values.
 */
fun List<NgDiveGas>.convertToLogbookGasData(): List<LogbookGasData> =
    mapIndexed { index, gas ->
        LogbookGasData(
            gasIndex = index,
            gasName = extractGasName(gas),
            startPressure = gas.startPressureKPa?.toFloat(),
            endPressure = gas.endPressureKPa?.toFloat(),
            avgVentilation = gas.avgVentilation?.toFloat(),
            transmitterId = gas.transmitterId?.toInt(),
            startPressure2 = gas.startPressure2KPa?.toFloat(),
            endPressure2 = gas.endPressure2KPa?.toFloat(),
            transmitterId2 = gas.transmitterId2?.toInt(),
        )
    }

fun List<NgDiveGas>.getUsedGasNames() = map { extractGasName(it) }

fun List<NgDiveGas>?.filterValidGases(): List<NgDiveGas> {
    return this?.filterNot { it.oxygen == 0 && it.helium == 0 } ?: emptyList()
}

private fun extractGasName(gas: NgDiveGas): String {
    val helium = gas.helium ?: 0
    val oxygen = gas.oxygen ?: 0
    return DiveGasNameHelper.extractNameFrom(oxygen, helium, gas.state)
}
