plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.compose"
    id "stt.android.plugin.epoxy"
    id "stt.android.plugin.moshi"
    alias libs.plugins.androidx.navigation.safeargs.kotlin
}

android {
    namespace 'com.stt.android.device'
    buildFeatures {
        dataBinding true
        buildConfig = true
    }
}

dependencies {
    implementation project(Deps.core)
    implementation project(Deps.appBase)
    implementation project(Deps.composeUi)
    implementation project(Deps.persistence)
    implementation project(Deps.domain)
    implementation project(Deps.workoutsDomain)
    implementation project(Deps.datasource)
    implementation project(Deps.remote)
    implementation project(Deps.suuntoPlusUi)

    suuntoImplementation project(Deps.mds)
    suuntoImplementation project(Deps.timeline)
    suuntoImplementation project(Deps.connectivity)

    implementation libs.androidx.corektx
    implementation libs.androidx.swiperefreshlayout

    implementation libs.retrofit
    implementation libs.coil
    implementation libs.androidx.browser
    implementation libs.androidx.work
    implementation libs.commonmark
    implementation libs.gson

    androidTestUtil libs.androidx.test.orchestrator

    androidTestImplementation libs.androidx.room.testing
    // Enables testing final classes in Android tests on Android P devices
    androidTestImplementation libs.dexmaker
    androidTestImplementation libs.timber.junit.rule
}
