package com.stt.android.device.datasource.suuntoplusguide

import com.stt.android.data.source.local.suuntoplusguide.LocalTrainingPlan
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusPluginDeviceStatusDao
import com.stt.android.data.source.local.suuntoplusguide.TrainingPlanDao
import com.stt.android.device.datasource.WatchPluginStatusDataSource
import com.stt.android.device.datasource.toDomain
import com.stt.android.device.domain.suuntoplusguide.TrainingPlanWithStatus
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.device.domain.suuntoplusguide.TrainingPlan
import com.stt.android.device.domain.suuntoplusguide.TrainingPlanId
import com.stt.android.exceptions.device.SuuntoPlusGuideNotFound
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class TrainingPlansLocalDataSource
@Inject constructor(
    private val trainingPlanDao: TrainingPlanDao,
    private val deviceStatusDao: SuuntoPlusPluginDeviceStatusDao,
    private val pluginStatusDataSource: WatchPluginStatusDataSource,
) {
    fun listAllTrainingPlans(): Flow<List<TrainingPlan>> =
        trainingPlanDao.fetchAllAsFlow()
            .map { it.map(LocalTrainingPlan::toDomain) }
            .distinctUntilChanged()
            .flowOn(Dispatchers.IO)

    fun listTrainingPlansWithStatus(watchSerial: String): Flow<List<TrainingPlanWithStatus>> =
        trainingPlanDao.fetchAllAsFlow()
            .map { list ->
                list.map { localPlan ->
                    TrainingPlanWithStatus(
                        plan = localPlan.toDomain(),
                        status = localPlan.statusForSerial(watchSerial)
                    )
                }
            }
            .distinctUntilChanged()
            .flowOn(Dispatchers.IO)

    suspend fun listTrainingPlansInGivenStates(
        watchSerial: String,
        states: Set<SuuntoPlusPluginStatus>
    ): List<TrainingPlan> = withContext(Dispatchers.IO) {
        trainingPlanDao.fetchAllAsFlow()
            .first()
            .filter { it.statusForSerial(watchSerial) in states }
            .map { it.toDomain() }
    }

    fun findTrainingPlanWithWatchStatus(watchSerial: String, planId: TrainingPlanId): Flow<Pair<TrainingPlan?, SuuntoPlusPluginStatus?>> =
        trainingPlanDao.findByIdAsFlow(planId.id)
            .map { localPlan ->
                val status = deviceStatusDao.findAllBySerial(watchSerial)
                    .firstOrNull { status -> status.watchSerial == watchSerial }
                    ?.status
                    ?.toDomain()
                localPlan?.toDomain() to status
            }
            .distinctUntilChanged()
            .flowOn(Dispatchers.IO)

    suspend fun findDeleted(): List<TrainingPlan> =
        trainingPlanDao.findDeleted()
            .map(LocalTrainingPlan::toDomain)

    suspend fun softDelete(planId: TrainingPlanId) {
        trainingPlanDao.softDelete(planId.id)
    }

    suspend fun delete(plans: List<TrainingPlan>) {
        if (plans.isNotEmpty()) {
            Timber.d("Deleting local SuuntoPlus plans: ${plans.map { it.id.id }}")
            trainingPlanDao.deleteByIds(plans.map { it.id.id })
            plans.forEach { plan ->
                plan.courses.orEmpty().forEach { (_, guideIds) ->
                    deviceStatusDao.deleteByPluginIds(guideIds.map { it })
                }
            }
        }
    }

    suspend fun countPinnedPlans(): Int =
        trainingPlanDao.countPinnedTrainingPlans()

    suspend fun upsert(plan: TrainingPlan) {
        trainingPlanDao.upsert(plan.toLocal())
    }

    suspend fun updatePinnedState(planId: TrainingPlanId, pinned: Boolean) {
        val changedRows = trainingPlanDao.updatePinnedState(planId.id, pinned)
        if (changedRows != 1) {
            throw SuuntoPlusGuideNotFound("No guide with ID $planId")
        }
    }

    suspend fun findById(planId: TrainingPlanId): TrainingPlan? =
        trainingPlanDao.findById(planId.id)?.toDomain()

    fun findByIdAsFlow(planId: TrainingPlanId): Flow<TrainingPlan?> =
        trainingPlanDao.findByIdAsFlow(planId.id)
            .map { it?.toDomain() }
            .distinctUntilChanged()
            .flowOn(Dispatchers.IO)

    suspend fun deleteAll() = trainingPlanDao.deleteAll()

    private suspend fun LocalTrainingPlan.statusForSerial(watchSerial: String): SuuntoPlusPluginStatus {
        // Fetch all statuses for guide IDs
        val statusList = coursesMap?.values?.flatten()?.map { guideId ->
            deviceStatusDao.findWatchState(watchSerial, guideId)?.status?.toDomain()
                ?: SuuntoPlusPluginStatus.UNKNOWN
        }.orEmpty()

        return when {
            statusList.any { it == SuuntoPlusPluginStatus.UNKNOWN } -> SuuntoPlusPluginStatus.UNKNOWN
            statusList.any { it == SuuntoPlusPluginStatus.WATCH_FULL } -> SuuntoPlusPluginStatus.WATCH_FULL
            statusList.any { it == SuuntoPlusPluginStatus.NOT_SUPPORTED } -> SuuntoPlusPluginStatus.NOT_SUPPORTED
            statusList.any { it == SuuntoPlusPluginStatus.DOWNLOADING } -> SuuntoPlusPluginStatus.DOWNLOADING
            statusList.any { it == SuuntoPlusPluginStatus.INSTALLING } -> SuuntoPlusPluginStatus.INSTALLING

            statusList.all { it == SuuntoPlusPluginStatus.IN_WATCH } -> SuuntoPlusPluginStatus.IN_WATCH
            statusList.all { it == SuuntoPlusPluginStatus.DOWNLOADED } -> SuuntoPlusPluginStatus.DOWNLOADED

            else -> statusList.firstOrNull() ?: SuuntoPlusPluginStatus.UNKNOWN
        }
    }

    suspend fun anyGuideFromPlanInWatch(watchSerial: String, plan: TrainingPlan) =
        plan.courses.orEmpty().any { (_, guideIds) ->
            guideIds.any { guideId ->
                getPluginWatchStatus(watchSerial, guideId) == SuuntoPlusPluginStatus.IN_WATCH
            }
        }

    private suspend fun getPluginWatchStatus(watchSerial: String, pluginId: String) =
        pluginStatusDataSource.getWatchStatus(watchSerial = watchSerial, pluginId = pluginId)
}

fun TrainingPlan.toLocal() = LocalTrainingPlan(
    id = id.id,
    catalogueId = catalogueId,
    modifiedMillis = modifiedMillis,
    name = name,
    owner = owner,
    ownerId = ownerId,
    startDate = startDate,
    endDate = endDate,
    url = url,
    iconUrl = iconUrl,
    backgroundUrl = backgroundUrl,
    description = description,
    subTitle = subTitle,
    richDescription = richDescription,
    priorityIndex = priorityIndex,
    activityIds = activityIds,
    pinned = pinned,
    deleted = false,
    remoteSyncErrorCode = remoteSyncErrorCode,
    watchSyncErrorCode = watchSyncErrorCode,
    planId = planId,
    coursesMap = courses
)

private fun LocalTrainingPlan.toDomain() = TrainingPlan(
    id = TrainingPlanId(id),
    catalogueId = catalogueId,
    modifiedMillis = modifiedMillis,
    name = name,
    owner = owner,
    ownerId = ownerId,
    startDate = startDate,
    endDate = endDate,
    url = url,
    iconUrl = iconUrl,
    backgroundUrl = backgroundUrl,
    description = description,
    subTitle = subTitle.orEmpty(),
    richDescription = richDescription,
    priorityIndex = priorityIndex,
    activityIds = activityIds,
    pinned = pinned,
    remoteSyncErrorCode = remoteSyncErrorCode,
    watchSyncErrorCode = watchSyncErrorCode,
    planId = planId,
    courses = coursesMap
)
