package com.stt.android.device.suuntoplusguide

import android.content.Context
import android.content.Intent
import com.stt.android.device.domain.GetWatchCapabilitiesUseCase
import com.stt.android.device.suuntoplusfeature.SuuntoPlusFeaturesActivity
import com.stt.android.utils.FlavorUtils
import com.stt.android.watch.SuuntoPlusNavigator
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class SuuntoPlusNavigatorImpl @Inject constructor(
    private val currentWatchCapabilitiesUseCase: GetWatchCapabilitiesUseCase,
) : SuuntoPlusNavigator {
    override fun isSuuntoPlusGuideSyncSupported(deviceType: SuuntoDeviceType?): Flow<Boolean> {
        if (deviceType?.isRunDevice == true) return flowOf(true)

        return currentWatchCapabilitiesUseCase.getCurrentCapabilitiesAsFlow()
            .map { capabilities ->
                capabilities.capabilities?.areSuuntoPlusGuidesSupported == true
            }
    }

    override fun isStructuredWorkoutPlannerSupported(deviceType: SuuntoDeviceType?): Flow<Boolean> {
        return currentWatchCapabilitiesUseCase.getCurrentCapabilitiesAsFlow()
            .map {
                it.capabilities?.areSuuntoPlusGuidesSupported ?: false
            }
    }

    override fun isSuuntoPlusFeatureSelectionSupported(): Flow<Boolean> =
        currentWatchCapabilitiesUseCase.getCurrentCapabilitiesAsFlow()
            .map {
                it.capabilities?.areSuuntoPlusFeaturesSupported ?: false
            }

    override fun isSuuntoPlusWatchfaceSupported(): Flow<Boolean> =
        currentWatchCapabilitiesUseCase.getCurrentCapabilitiesAsFlow()
            .map {
                it.capabilities?.areSuuntoPlusWatchfaceSupported ?: false
            }

    override fun isSuuntoPlusStoreSupported(deviceType: SuuntoDeviceType?): Boolean {
        return when {
            deviceType == null -> false
            deviceType.isRunDevice -> FlavorUtils.isSuuntoAppChina
            else -> true
        }
    }

    override fun newSuuntoPlusGuideIntent(context: Context) =
        Intent(context, SuuntoPlusGuideActivity::class.java)

    override fun newSuuntoPlusFeaturesIntent(context: Context) =
        SuuntoPlusFeaturesActivity.newExcludedWatchfaceStartIntent(context)

    override fun newSuuntoPlusWatchfaceIntent(context: Context): Intent =
        SuuntoPlusFeaturesActivity.newOnlyWatchfaceStartIntent(context)
}
