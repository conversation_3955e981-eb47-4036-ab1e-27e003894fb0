package com.stt.android.device.watch

import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncLogEventDao
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncStateRepository
import com.stt.android.device.datasource.suuntoplusguide.OnDeviceConnectedInitializer
import com.stt.android.device.domain.suuntoplusfeature.settings.SportsAppSettingsStateDataSource
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import javax.inject.Inject

class SuuntoPlusWatchSyncStateOnDeviceConnectInitializer
@Inject constructor(
    private val syncLogEventDao: SuuntoPlusGuideSyncLogEventDao,
    private val syncStateRepository: SuuntoPlusSyncStateRepository,
    private val sportsAppSettingsStateDataSource: SportsAppSettingsStateDataSource,
) : OnDeviceConnectedInitializer {
    override suspend fun onDeviceConnected(
        serial: String,
        model: String,
        fwVersion: String,
        hwVersion: String,
        capabilities: List<String>
    ) {
        // The connectivity process only runs the watch sync so reset the sync state to IDLE only
        // if the state is WATCH_SYNC_ONGOING. Leave it up to the UI process to handle
        // resetting REMOTE_SYNC_ONGOING.
        withContext(IO) {
            syncLogEventDao.ensureIdleIfWatchSyncOngoing("Device connected hook")
            syncStateRepository.ensureWatchSyncIdleForAll()

            sportsAppSettingsStateDataSource.resetForNewWatchConnection()
        }
    }
}
