package com.stt.android.device.domain

import androidx.compose.animation.Crossfade
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ErrorToast
import com.stt.android.compose.widgets.ProgressToast
import com.stt.android.compose.widgets.Toast

@Composable
fun WatchAndNetworkNotificationStateComposable(
    state: WatchAndNetworkNotificationState,
    modifier: Modifier = Modifier,
    onCustomToastActionClick: () -> Unit = {},
    watchSyncingMessage: String? = null,
    watchBusyMessage: String? = null,
    watchDisconnectedMessage: String? = null,
    noGuideSupportMessage: String? = null,
    noInternetConnectionMessage: String? = null,
) {
    // The ordering of this when statement is important in case of multiple messages
    // are triggered simultaneously. Most important messages are checked first.
    val targetState = when {
        state.customToastMessage != null ->
            WatchAndNetworkNotificationTargetState.ToastWithPrimaryBackgroundColor(
                message = state.customToastMessage,
                actionText = state.customToastActionText
            )

        state.watchSyncing && watchSyncingMessage != null ->
            WatchAndNetworkNotificationTargetState.ProgressToast(watchSyncingMessage)

        state.watchBusy && watchBusyMessage != null ->
            WatchAndNetworkNotificationTargetState.Toast(watchBusyMessage)

        state.watchDisconnected && watchDisconnectedMessage != null ->
            WatchAndNetworkNotificationTargetState.Toast(watchDisconnectedMessage)

        state.watchPaired && state.areSuuntoPlusGuidesSupported == false && noGuideSupportMessage != null ->
            WatchAndNetworkNotificationTargetState.ErrorToast(noGuideSupportMessage)

        !state.internetAvailable && noInternetConnectionMessage != null ->
            WatchAndNetworkNotificationTargetState.Toast(noInternetConnectionMessage)

        state.customErrorMessage != null ->
            WatchAndNetworkNotificationTargetState.ErrorToast(state.customErrorMessage)

        else ->
            WatchAndNetworkNotificationTargetState.None
    }

    Crossfade(
        targetState = targetState,
        modifier = modifier
    ) {
        val toastModifier = Modifier.fillMaxWidth()

        when (it) {
            is WatchAndNetworkNotificationTargetState.ToastWithPrimaryBackgroundColor -> Toast(
                backgroundColor = MaterialTheme.colors.primary,
                contentColor = MaterialTheme.colors.onPrimary,
                modifier = toastModifier
            ) {
                Text(
                    text = it.message,
                    modifier = Modifier
                        .padding(MaterialTheme.spacing.small)
                        .then(if (it.actionText != null) Modifier.weight(1f) else Modifier)
                )

                if (it.actionText != null) {
                    TextButton(onClick = onCustomToastActionClick) {
                        Text(
                            text = it.actionText,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(MaterialTheme.spacing.small),
                            color = MaterialTheme.colors.onPrimary
                        )
                    }
                }
            }

            is WatchAndNetworkNotificationTargetState.Toast -> Toast(
                text = it.message,
                modifier = toastModifier
            )

            is WatchAndNetworkNotificationTargetState.ProgressToast -> ProgressToast(
                text = it.message,
                modifier = toastModifier
            )

            is WatchAndNetworkNotificationTargetState.ErrorToast -> ErrorToast(
                text = it.message,
                modifier = toastModifier
            )

            is WatchAndNetworkNotificationTargetState.None -> {}
        }
    }
}

sealed class WatchAndNetworkNotificationTargetState {
    data class Toast(val message: String) : WatchAndNetworkNotificationTargetState()
    data class ProgressToast(val message: String) : WatchAndNetworkNotificationTargetState()
    data class ToastWithPrimaryBackgroundColor(
        val message: String,
        val actionText: String?
    ) : WatchAndNetworkNotificationTargetState()
    data class ErrorToast(val message: String) : WatchAndNetworkNotificationTargetState()
    object None : WatchAndNetworkNotificationTargetState()
}

@Preview
@Composable
private fun CustomToastMessagePreview() {
    AppTheme {
        WatchAndNetworkNotificationStateComposable(
            state = WatchAndNetworkNotificationState.DEFAULT.copy(
                customToastMessage = "Guide pinned for watch sync"
            )
        )
    }
}

@Preview
@Composable
private fun CustomToastMessageWithActionPreview() {
    AppTheme {
        WatchAndNetworkNotificationStateComposable(
            state = WatchAndNetworkNotificationState.DEFAULT.copy(
                customToastMessage = "Guide will installed during next sync",
                customToastActionText = "View",
            ),
            onCustomToastActionClick = {}
        )
    }
}

@Preview
@Composable
private fun WatchSyncNotificationPreview() {
    AppTheme {
        WatchAndNetworkNotificationStateComposable(
            state = WatchAndNetworkNotificationState.DEFAULT.copy(watchSyncing = true),
            watchSyncingMessage = "Watch is syncing"
        )
    }
}

@Preview
@Composable
private fun WatchBusyNotificationPreview() {
    AppTheme {
        WatchAndNetworkNotificationStateComposable(
            state = WatchAndNetworkNotificationState.DEFAULT.copy(watchBusy = true),
            watchBusyMessage = "Watch is busy. Modifications can be done when your watch is on the main screen."
        )
    }
}

@Preview
@Composable
private fun WatchDisconnectedNotificationPreview() {
    AppTheme {
        WatchAndNetworkNotificationStateComposable(
            state = WatchAndNetworkNotificationState.DEFAULT.copy(watchDisconnected = true),
            watchDisconnectedMessage = "Watch is not connected"
        )
    }
}

@Preview
@Composable
private fun WatchNotSupportedNotificationPreview() {
    AppTheme {
        WatchAndNetworkNotificationStateComposable(
            state = WatchAndNetworkNotificationState.DEFAULT,
            noGuideSupportMessage = "Watch not compatible with SuuntoPlus™ guides to sync workouts"
        )
    }
}

@Preview
@Composable
private fun NoInternetNotificationPreview() {
    AppTheme {
        WatchAndNetworkNotificationStateComposable(
            state = WatchAndNetworkNotificationState.DEFAULT.copy(internetAvailable = false),
            noInternetConnectionMessage = "No internet connection"
        )
    }
}
