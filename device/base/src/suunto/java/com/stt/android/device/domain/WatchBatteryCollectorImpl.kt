package com.stt.android.device.domain

import com.stt.android.domain.device.WatchBatteryCollector
import com.stt.android.watch.SuuntoWatchModel
import java.io.File
import javax.inject.Inject

class WatchBatteryCollectorImpl @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
) : WatchBatteryCollector {

    override suspend fun getBatteryFile(): File = suuntoWatchModel.getBatteryFile()

    override suspend fun getDrtLogsFile(): File = suuntoWatchModel.getDrtLogsFile()
}
