package com.stt.android.device.suuntoplusfeature.listitems

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.R as BaseR
import com.stt.android.core.R as CoreR

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun StoreLinkItem(
    onClick: () -> Unit,
    isWatchfaceSupported: Boolean,
    isRunDevice: Boolean,
    modifier: Modifier = Modifier,
) {
    Card(
        onClick = onClick,
        shape = RoundedCornerShape(MaterialTheme.spacing.medium),
        elevation = 2.dp,
        modifier = modifier,
    ) {
        Column {
            Image(
                painter = painterResource(id = BaseR.drawable.suunto_plus_store_link_ad),
                contentScale = ContentScale.Crop,
                contentDescription = null,
                modifier = Modifier
                    .height(110.dp)
                    .fillMaxWidth(),
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(MaterialTheme.spacing.medium)
            ) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall),
                    modifier = Modifier.weight(1f),
                ) {
                    Text(
                        text = stringResource(id = if (isRunDevice) BaseR.string.suunto_plus_store_link_title_dilu else BaseR.string.suunto_plus_store_link_title),
                        style = MaterialTheme.typography.bodyLargeBold
                    )

                    Text(
                        text = stringResource(
                            id = when {
                                isRunDevice -> BaseR.string.suunto_plus_store_link_text_dilu
                                isWatchfaceSupported -> BaseR.string.suunto_plus_store_link_text_with_watch_faces
                                else -> BaseR.string.suunto_plus_store_link_text
                            }
                        ),
                        color = MaterialTheme.colors.darkGrey
                    )
                }

                Icon(
                    painter = painterResource(id = CoreR.drawable.ic_chevron_right_fill),
                    contentDescription = null,
                )
            }
        }
    }
}

@Preview(widthDp = 320)
@Preview(widthDp = 480)
@Preview(widthDp = 760)
@Composable
private fun StoreLinkItemPreview() {
    AppTheme {
        Box(
            modifier = Modifier
                .background(MaterialTheme.colors.background)
                .padding(MaterialTheme.spacing.medium)
        ) {
            StoreLinkItem(
                onClick = {},
                isWatchfaceSupported = true,
                isRunDevice = false
            )
        }
    }
}
