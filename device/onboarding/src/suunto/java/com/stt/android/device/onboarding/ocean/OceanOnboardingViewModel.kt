package com.stt.android.device.onboarding.ocean

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.device.onboarding.BaseOnboardingActivity
import com.stt.android.device.onboarding.DeviceOnboardingAnalytics
import com.stt.android.device.onboarding.onboardingnew.IOnboardingViewModel
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.toV2Flowable
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.reactive.asFlow
import javax.inject.Inject

// Interface helps with Compose Previews
@HiltViewModel
class OceanOnboardingViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    suuntoWatchModel: SuuntoWatchModel,
    private val onboardingAnalytics: DeviceOnboardingAnalytics
) : ViewModel(), IOnboardingViewModel {
    private val suuntoDeviceType: SuuntoDeviceType =
        savedStateHandle[BaseOnboardingActivity.KEY_EXTRA_SUUNTO_DEVICE_TYPE]
            ?: SuuntoDeviceType.Unrecognized

    private var ongoingNavigationMethod: String? = null

    private var wifiSetupOpened = false

    /**
     * The boolean tells if the closing should have slight delay for animations to finish or not
     */
    val closeOnboardingEvent = SingleLiveEvent<Boolean>()

    val watchConnected: Flow<Boolean> = suuntoWatchModel
        .stateChangeObservable
        .toV2Flowable()
        .asFlow()
        .map { it.isConnected }
        .catch { emit(false) }
        .distinctUntilChanged()
        .shareIn(scope = viewModelScope, started = SharingStarted.Lazily, replay = 1)

    override fun onPageChanged(newPage: Int, oldPage: Int) {
        // Buttons should have reported that they've been pressed by calling
        // [setOngoingPageNavigationMethod]. If the value is null at this point,
        // assume that navigation happened by swiping
        val navigationMethod = ongoingNavigationMethod
            ?: AnalyticsPropertyValue.NavigationMethodProperty.SWIPE

        onboardingAnalytics.trackPageChanged(
            suuntoDeviceType,
            newPage,
            oldPage,
            navigationMethod
        )

        ongoingNavigationMethod = null
    }

    override fun onCloseOnboardingClicked(pageName: String?, delay: Boolean) {
        trackOpenWifiSetup()
        pageName?.let {
            onboardingAnalytics.trackOnboardingFinished(
                suuntoDeviceType,
                it
            )
        }
        closeOnboardingEvent.postValue(delay)
    }

    private fun trackOpenWifiSetup() {
        if (suuntoDeviceType.isSuuntoOcean) {
            onboardingAnalytics.trackOpenWifiSetup(suuntoDeviceType, !wifiSetupOpened)
        }
    }

    override fun onStartClicked() {
        onboardingAnalytics.trackStartClick(suuntoDeviceType)
    }

    override fun onWatchWidgetCustomizationOpened() {
        onboardingAnalytics.trackOpenWatchWidgetCustomization(suuntoDeviceType)
    }

    override fun onVideoEndReached(pageName: String) {
        onboardingAnalytics.trackVideoCompleted(suuntoDeviceType, pageName)
    }

    override fun setOngoingPageNavigationMethod(method: String) {
        ongoingNavigationMethod = method
    }

    override fun onWifiSetupOpened() {
        wifiSetupOpened = true
    }

    override fun onSleepTrackingEnabled(enabled: Boolean) {}

    override fun on247HrEnabled() {}
}
