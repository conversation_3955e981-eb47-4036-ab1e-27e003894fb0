@file:OptIn(ExperimentalComposeUiApi::class)

package com.stt.android.device.onboarding.onboardingnew

import android.app.Activity
import android.content.Intent
import android.content.res.Configuration
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.SnackbarHost
import androidx.compose.material.SnackbarHostState
import androidx.compose.material.Switch
import androidx.compose.material.SwitchDefaults
import androidx.compose.material.Text
import androidx.compose.material.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInteropFilter
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalResources
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.core.app.ActivityOptionsCompat
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue.NavigationMethodProperty
import com.stt.android.compose.component.HorizontalPagerIndicator
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.darkGreyText
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.theme.veryLightGray
import com.stt.android.compose.widgets.PrimaryButton
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import kotlinx.collections.immutable.ImmutableList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.scan
import kotlinx.coroutines.launch
import java.util.Locale

@Composable
fun OnboardingScreen(
    pages: ImmutableList<OnboardingPage>,
    videoBaseUrl: String,
    userAgent: String,
    watchWidgetCustomizationIntent: Intent,
    setupNetworkIntent: Intent,
    offlineMapsIntent: Intent,
    viewModel: IOnboardingViewModel,
    deviceType: SuuntoDeviceType,
    modifier: Modifier = Modifier,
    sportsModeIntent: Intent? = null,
    watchFacesIntent: Intent? = null,
) {
    val pagerState = rememberPagerState { pages.size }
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()
    val nextPageOnResultOkActivityLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        // Animate to next page if the user answered the survey
        if (result.resultCode == Activity.RESULT_OK) {
            animateToNextPage(coroutineScope, pagerState, delayMs = 500)
        }
    }
    val nextPageIgnoreResultActivityLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        animateToNextPage(coroutineScope, pagerState, delayMs = 500)
    }
    val launchedActivityOptions = ActivityOptionsCompat.makeCustomAnimation(
        LocalContext.current,
        R.anim.slide_in_up,
        R.anim.fade_out
    )

    val onCloseOnboardingWithPageName = {
        val currentPage = pages[pagerState.currentPage]
        viewModel.onCloseOnboardingClicked(currentPage.analyticsName, true)
    }

    val onReachVideoEndWithPageName: () -> Unit = {
        val currentPage = pages[pagerState.currentPage]
        currentPage.analyticsName?.let { viewModel.onVideoEndReached(it) }
    }

    val onChangePageButtonClick = {
        viewModel.setOngoingPageNavigationMethod(NavigationMethodProperty.ARROWS)
    }

    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }
            .scan(pagerState.currentPage to pagerState.currentPage) { acc, value ->
                value to acc.first
            }
            .filter { it.first != it.second }
            .distinctUntilChanged()
            .collect { (newPage, oldPage) ->
                viewModel.onPageChanged(newPage, oldPage)
            }
    }

    fun handlePageButtonClick(action: OnboardingPageButtonAction?) {
        action?.let {
            when (it) {
                OnboardingPageButtonAction.NEXT_PAGE -> {
                    viewModel.setOngoingPageNavigationMethod(NavigationMethodProperty.START_BUTTON)
                    viewModel.onStartClicked()
                    animateToNextPage(coroutineScope, pagerState)
                }
                OnboardingPageButtonAction.CUSTOMIZE_WATCH_WIDGETS -> {
                    nextPageOnResultOkActivityLauncher.launch(
                        watchWidgetCustomizationIntent,
                        launchedActivityOptions
                    )
                    viewModel.onWatchWidgetCustomizationOpened()
                }
                OnboardingPageButtonAction.CLOSE_ONBOARDING -> onCloseOnboardingWithPageName()
                OnboardingPageButtonAction.DOWNLOAD_MAPS -> {
                    nextPageOnResultOkActivityLauncher.launch(
                        offlineMapsIntent,
                        launchedActivityOptions
                    )
                }
                OnboardingPageButtonAction.SETUP_NETWORK -> {
                    nextPageOnResultOkActivityLauncher.launch(
                        setupNetworkIntent,
                        launchedActivityOptions
                    )
                    viewModel.onWifiSetupOpened()
                }

                OnboardingPageButtonAction.TURN_ON_247_HR -> {
                    viewModel.on247HrEnabled()
                    animateToNextPage(coroutineScope, pagerState)
                }

                OnboardingPageButtonAction.SKIP_SLEEP_TRACKING,
                OnboardingPageButtonAction.SKIP_247_HR,
                OnboardingPageButtonAction.SKIP_SETUP_NETWORK,
                OnboardingPageButtonAction.SKIP_DOWNLOAD_MAPS,
                OnboardingPageButtonAction.SKIP_CUSTOMIZE_WATCH_WIDGETS -> {
                    animateToNextPage(coroutineScope, pagerState)
                }

                OnboardingPageButtonAction.CUSTOMIZE_SPORTS_MODE -> {
                    sportsModeIntent?.let {
                        nextPageIgnoreResultActivityLauncher.launch(
                            sportsModeIntent,
                            launchedActivityOptions
                        )
                    } ?: run {
                        animateToNextPage(coroutineScope, pagerState)
                    }
                }

                OnboardingPageButtonAction.DISCOVER_WATCH_FACES -> {
                    watchFacesIntent?.let {
                        nextPageIgnoreResultActivityLauncher.launch(
                            watchFacesIntent,
                            launchedActivityOptions
                        )
                    } ?: run {
                        animateToNextPage(coroutineScope, pagerState)
                    }
                }
            }
        }
    }

    fun handlePageSwitchAction(action: OnboardingPageSwitchAction) {
        when (action) {
            is OnboardingPageSwitchAction.TrackSleep -> {
                viewModel.onSleepTrackingEnabled(!action.checked)
            }
        }
    }

    BackHandler {
        val currentPage = pages[pagerState.currentPage]
        viewModel.onCloseOnboardingClicked(currentPage.analyticsName, false)
    }

    AppTheme {
        Scaffold(
            modifier = modifier,
            snackbarHost = {
                SnackbarHost(hostState = snackbarHostState)
            }
        ) { paddingValues ->
            if (LocalConfiguration.current.orientation == Configuration.ORIENTATION_PORTRAIT) {
                PortraitOnboardingScreen(
                    pages = pages,
                    videoBaseUrl = videoBaseUrl,
                    userAgent = userAgent,
                    deviceType = deviceType,
                    onCloseOnboarding = onCloseOnboardingWithPageName,
                    pagerState = pagerState,
                    onPageButtonClick = ::handlePageButtonClick,
                    onChangePageButtonClick = onChangePageButtonClick,
                    onReachVideoEnd = onReachVideoEndWithPageName,
                    coroutineScope = coroutineScope,
                    modifier = Modifier.padding(paddingValues),
                    onPageSecondButtonClick = ::handlePageButtonClick,
                    onPageSwitchCheckChanged = ::handlePageSwitchAction,
                )
            } else {
                LandscapeOnBoardingScreen(
                    pages = pages,
                    videoBaseUrl = videoBaseUrl,
                    userAgent = userAgent,
                    deviceType = deviceType,
                    onCloseOnboarding = onCloseOnboardingWithPageName,
                    pagerState = pagerState,
                    handlePageButtonClick = ::handlePageButtonClick,
                    onChangePageButtonClick = onChangePageButtonClick,
                    onReachVideoEnd = onReachVideoEndWithPageName,
                    coroutineScope = coroutineScope,
                    handlePageSecondButtonClick = ::handlePageButtonClick,
                    onPageSwitchCheckChanged = ::handlePageSwitchAction,
                    modifier = Modifier.padding(paddingValues),
                )
            }
        }
    }
}

private fun animateToNextPage(
    coroutineScope: CoroutineScope,
    pagerState: PagerState,
    delayMs: Long? = null
) {
    coroutineScope.launch {
        if (delayMs != null) delay(delayMs)
        if (pagerState.currentPage < pagerState.lastPageIndex) {
            pagerState.animateScrollToPage(pagerState.currentPage + 1)
        }
    }
}

@Composable
private fun PortraitOnboardingScreen(
    pages: ImmutableList<OnboardingPage>,
    videoBaseUrl: String,
    userAgent: String,
    deviceType: SuuntoDeviceType,
    onCloseOnboarding: () -> Unit,
    pagerState: PagerState,
    onPageButtonClick: (OnboardingPageButtonAction) -> Unit,
    onPageSecondButtonClick: (OnboardingPageButtonAction?) -> Unit,
    onPageSwitchCheckChanged: (OnboardingPageSwitchAction) -> Unit,
    onChangePageButtonClick: () -> Unit,
    onReachVideoEnd: () -> Unit,
    coroutineScope: CoroutineScope,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .narrowContent()
    ) {
        BoxWithConstraints(
            modifier = Modifier.background(MaterialTheme.colors.surface)
        ) {
            val minPagerHeight = 320.dp
            val maxHeaderSize = <EMAIL> - minPagerHeight
            val headerSize = minOf(maxHeaderSize, <EMAIL>)

            OnboardingHeader(
                deviceType = deviceType,
                pages = pages,
                userAgent = userAgent,
                videoBaseUrl = videoBaseUrl,
                pagerState = pagerState,
                onReachVideoEnd = onReachVideoEnd,
                modifier = Modifier
                    .width(headerSize)
                    .align(Alignment.TopCenter),
            )

            Column {
                HorizontalPager(
                    state = pagerState,
                    contentPadding = PaddingValues(top = headerSize),
                    modifier = Modifier.weight(1f)
                ) { pageIndex ->
                    OnboardingPageContent(
                        page = pages[pageIndex],
                        onButtonClick = onPageButtonClick,
                        onSecondButtonClick = onPageSecondButtonClick,
                        onPageSwitchCheckChanged = onPageSwitchCheckChanged,
                    )
                }

                PageIndicator(
                    pagerState = pagerState,
                    coroutineScope = coroutineScope,
                    onChangePageButtonClick = onChangePageButtonClick
                )
            }

            CloseOnboardingButton(
                onClick = onCloseOnboarding
            )
        }
    }
}

@Composable
private fun LandscapeOnBoardingScreen(
    pages: ImmutableList<OnboardingPage>,
    videoBaseUrl: String,
    userAgent: String,
    deviceType: SuuntoDeviceType,
    onCloseOnboarding: () -> Unit,
    pagerState: PagerState,
    handlePageButtonClick: (OnboardingPageButtonAction) -> Unit,
    onChangePageButtonClick: () -> Unit,
    onReachVideoEnd: () -> Unit,
    coroutineScope: CoroutineScope,
    handlePageSecondButtonClick: (OnboardingPageButtonAction?) -> Unit,
    onPageSwitchCheckChanged: (OnboardingPageSwitchAction) -> Unit,
    modifier: Modifier = Modifier,
) {
    BoxWithConstraints(
        modifier = modifier.background(MaterialTheme.colors.surface)
    ) {
        // The header is on the left and uses parent height as its width,
        // the pager screen contents and page indicator are adjusted to only
        // use the remaining space's worth of horizontal space on the right.
        // As the pager itself expands behind the header (which passes clicks through),
        // the pages can be swiped from anywhere on the screen.

        val minPagerWidth = 320.dp
        val maxHeaderSize = <EMAIL> - minPagerWidth
        val headerSize = minOf(maxHeaderSize, <EMAIL>)

        HorizontalPager(
            state = pagerState,
            contentPadding = PaddingValues(
                start = headerSize,
                top = MaterialTheme.spacing.medium,
                bottom = PAGE_INDICATOR_HEIGHT
            ),
            modifier = Modifier.fillMaxHeight()
        ) { pageIndex ->
            OnboardingPageContent(
                page = pages[pageIndex],
                onButtonClick = handlePageButtonClick,
                onSecondButtonClick = handlePageSecondButtonClick,
                onPageSwitchCheckChanged = onPageSwitchCheckChanged
            )
        }

        PageIndicator(
            pagerState = pagerState,
            coroutineScope = coroutineScope,
            onChangePageButtonClick = onChangePageButtonClick,
            modifier = Modifier
                .padding(start = headerSize)
                .align(Alignment.BottomEnd)
        )

        // Fill area above & below the header with white background
        // so the scrolling pager content doesn't peek behind the header
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .width(headerSize)
                .align(Alignment.CenterStart)
                .background(Color.White)
        )

        OnboardingHeader(
            deviceType = deviceType,
            pages = pages,
            userAgent = userAgent,
            videoBaseUrl = videoBaseUrl,
            pagerState = pagerState,
            onReachVideoEnd = onReachVideoEnd,
            modifier = Modifier
                .width(headerSize)
                .align(Alignment.CenterStart)
                .pointerInteropFilter {
                    // Let all touch events pass trough
                    false
                },
        )

        CloseOnboardingButton(
            onClick = onCloseOnboarding
        )
    }
}

@Composable
private fun OnboardingPageContent(
    page: OnboardingPage,
    onButtonClick: (OnboardingPageButtonAction) -> Unit,
    onSecondButtonClick: (OnboardingPageButtonAction?) -> Unit,
    onPageSwitchCheckChanged: (OnboardingPageSwitchAction) -> Unit,
    modifier: Modifier = Modifier,
) {
    val scrollState = rememberScrollState()
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .fillMaxSize()
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .verticalScroll(scrollState)
                .fillMaxWidth()
                .weight(1f)
        ) {
            Text(
                text = page.title.uppercase(Locale.getDefault()),
                style = MaterialTheme.typography.bodyXLargeBold,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(
                    start = MaterialTheme.spacing.medium,
                    top = MaterialTheme.spacing.large,
                    end = MaterialTheme.spacing.medium,
                    bottom = MaterialTheme.spacing.medium,
                )
            )

            Text(
                text = page.body,
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.spacing.large)
            )

            if (page.switchAction != null) {
                PageSwitch(
                    switchAction = page.switchAction,
                    onPageSwitchCheckChanged = onPageSwitchCheckChanged,
                    modifier = Modifier.padding(top = MaterialTheme.spacing.large)
                )
            }
        }

        if (page.buttonAction != null && page.buttonText.isNotEmpty()) {
            PrimaryButton(
                text = page.buttonText.uppercase(Locale.getDefault()),
                onClick = { onButtonClick(page.buttonAction) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = MaterialTheme.spacing.large,
                        end = MaterialTheme.spacing.large,
                        bottom = MaterialTheme.spacing.small,
                    )
            )
        }

        if (page.secondButtonAction != null && !page.secondButtonText.isNullOrEmpty()) {
            Text(
                page.secondButtonText.uppercase(Locale.getDefault()),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colors.primary,
                modifier = Modifier
                    .padding(vertical = MaterialTheme.spacing.large)
                    .clickableThrottleFirst {
                        onSecondButtonClick(page.secondButtonAction)
                    }
            )
        }
    }
}

@Composable
private fun PageSwitch(
    switchAction: OnboardingPageSwitchAction,
    onPageSwitchCheckChanged: (OnboardingPageSwitchAction) -> Unit,
    modifier: Modifier = Modifier,
) {
    val showDescription = switchAction.checked
    val margin = MaterialTheme.spacing.medium
    ConstraintLayout(modifier = modifier) {
        val (title, description, switch, divider) = createRefs()
        Text(
            text = stringResource(switchAction.title),
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.constrainAs(title) {
                width = Dimension.fillToConstraints
                start.linkTo(parent.start, margin)
                top.linkTo(parent.top, margin)
                end.linkTo(switch.start, margin)
                if (showDescription) {
                    bottom.linkTo(description.top)
                } else {
                    bottom.linkTo(parent.bottom, margin)
                }
            }
        )
        if (showDescription) {
            Text(
                text = stringResource(switchAction.description),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colors.darkGreyText,
                modifier = Modifier.constrainAs(description) {
                    width = Dimension.fillToConstraints
                    start.linkTo(title.start)
                    top.linkTo(title.bottom, margin / 2)
                    end.linkTo(switch.start)
                    bottom.linkTo(parent.bottom, margin)
                }
            )
        }
        Switch(
            checked = switchAction.checked,
            onCheckedChange = {
                onPageSwitchCheckChanged(switchAction)
            },
            colors = SwitchDefaults.colors(
                checkedThumbColor = MaterialTheme.colors.primary,
                uncheckedThumbColor = MaterialTheme.colors.veryLightGray,
            ),
            modifier = Modifier.constrainAs(switch) {
                top.linkTo(parent.top)
                end.linkTo(parent.end, margin)
                bottom.linkTo(parent.bottom)
            }
        )
        Divider(
            modifier = Modifier.constrainAs(divider) {
                bottom.linkTo(parent.bottom)
            }
        )
    }
}

private val PAGE_INDICATOR_HEIGHT = 48.dp

@Composable
private fun PageIndicator(
    pagerState: PagerState,
    coroutineScope: CoroutineScope,
    onChangePageButtonClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.spacing.medium)
            .height(PAGE_INDICATOR_HEIGHT)
    ) {
        if (pagerState.targetPage > 0 || pagerState.currentPage > 0) {
            SuuntoIconButton(
                icon = SuuntoIcons.ActionBack,
                onClick = {
                    if (pagerState.targetPage <= 0) return@SuuntoIconButton
                    onChangePageButtonClick()
                    coroutineScope.launch {
                        pagerState.animateScrollToPage(pagerState.targetPage - 1)
                    }
                },
                modifier = Modifier.align(Alignment.CenterStart),
                contentDescription = stringResource(R.string.previous_page),
            )
        }

        HorizontalPagerIndicator(
            pagerState = pagerState,
            modifier = Modifier
                .padding(all = MaterialTheme.spacing.medium)
                .align(Alignment.Center)
        )

        if (
            pagerState.targetPage < pagerState.lastPageIndex ||
            pagerState.currentPage < pagerState.lastPageIndex
        ) {
            IconButton(
                onClick = {
                    if (pagerState.targetPage >= pagerState.lastPageIndex) return@IconButton
                    onChangePageButtonClick()
                    coroutineScope.launch {
                        pagerState.animateScrollToPage(pagerState.targetPage + 1)
                    }
                },
                modifier = Modifier
                    .align(Alignment.CenterEnd)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.all_ic_arrow_right),
                    contentDescription = stringResource(R.string.next_page),
                )
            }
        }
    }
}

@Composable
private fun BoxScope.CloseOnboardingButton(
    onClick: () -> Unit
) {
    Icon(
        painter = painterResource(id = R.drawable.ic_close_dialog_icon),
        contentDescription = stringResource(R.string.close),
        modifier = Modifier
            .align(Alignment.TopEnd)
            .padding(all = MaterialTheme.spacing.small)
            .clickable(
                onClick = onClick,
                interactionSource = remember { MutableInteractionSource() },
                indication = ripple(bounded = false),
                role = Role.Button
            )
            .padding(all = MaterialTheme.spacing.small) // expands touchable area
            .size(24.dp),
        tint = Color.Unspecified
    )
}

private val PagerState.lastPageIndex: Int get() = pageCount - 1

@Preview(name = "Portrait", widthDp = 720, heightDp = 1280)
@Preview(name = "Landscape", widthDp = 1280, heightDp = 720)
@Composable
private fun OnboardingPreview() {
    val resources = LocalResources.current
    OnboardingScreen(
        pages =
        buildSuuntoRunOnboardingPages(
            resources = resources,
            username = "Preview User",
            sleepTrackEnabled = true,
            diluOnboardingWatchFaceEnabled = false,
        ),
        videoBaseUrl = "",
        userAgent = "",
        watchWidgetCustomizationIntent = Intent(),
        offlineMapsIntent = Intent(),
        setupNetworkIntent = Intent(),
        deviceType = SuuntoDeviceType.SuuntoRace,
        viewModel = object : IOnboardingViewModel {
            override fun onPageChanged(newPage: Int, oldPage: Int) { }

            override fun onCloseOnboardingClicked(pageName: String?, delay: Boolean) { }

            override fun onStartClicked() { }

            override fun onWatchWidgetCustomizationOpened() { }

            override fun onVideoEndReached(pageName: String) { }

            override fun setOngoingPageNavigationMethod(method: String) { }

            override fun onWifiSetupOpened() { }

            override fun onSleepTrackingEnabled(enabled: Boolean) { }

            override fun on247HrEnabled() { }
        }
    )
}
