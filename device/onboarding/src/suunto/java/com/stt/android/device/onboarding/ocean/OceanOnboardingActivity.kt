package com.stt.android.device.onboarding.ocean

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.content.IntentCompat
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.ui.observeNotNull
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.launchOnLifecycle
import com.stt.android.device.onboarding.BaseOnboardingActivity
import com.stt.android.device.onboarding.onboardingnew.OnboardingScreen
import com.stt.android.offlinemaps.OfflineMapsNavigator
import com.stt.android.remote.AssetsBaseUrl
import com.stt.android.remote.UserAgent
import com.stt.android.watch.WatchWidgetCustomizationNavigator
import com.stt.android.watch.wifi.WifiNetworksActivity
import com.stt.android.window.setFlagsAndColors
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import javax.inject.Inject
import com.stt.android.R as BaseR

@AndroidEntryPoint
class OceanOnboardingActivity : AppCompatActivity() {
    @Inject
    @UserAgent
    lateinit var userAgent: String

    @Inject
    @AssetsBaseUrl
    lateinit var videoBaseUrl: String

    @Inject
    lateinit var watchWidgetCustomizationNavigator: WatchWidgetCustomizationNavigator

    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var offlineMapsNavigator: OfflineMapsNavigator

    private val viewModel: OceanOnboardingViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window?.setFlagsAndColors()
        val deviceType = requireNotNull(
            IntentCompat.getSerializableExtra(
                intent,
                BaseOnboardingActivity.KEY_EXTRA_SUUNTO_DEVICE_TYPE,
                SuuntoDeviceType::class.java,
            )
        )

        setContent {
            val watchConnected by viewModel.watchConnected.collectAsState(initial = false)

            val pages = buildOnboardingPages(
                resources = resources,
                username = currentUserController.realNameOrUsername,
                showWatchWidgetCustomization = watchConnected,
            )

            OnboardingScreen(
                pages = pages,
                videoBaseUrl = videoBaseUrl,
                userAgent = userAgent,
                watchWidgetCustomizationIntent = createWatchWidgetCustomizationIntent(),
                setupNetworkIntent = createWifiNetworksIntent(),
                offlineMapsIntent = createOfflineMapsIntent(),
                viewModel = viewModel,
                deviceType = deviceType,
            )
        }
    }

    override fun onResume() {
        super.onResume()

        viewModel.closeOnboardingEvent.observeNotNull(this) { delayForAnimations ->
            launchOnLifecycle {
                if (delayForAnimations) {
                    delay(resources.getInteger(android.R.integer.config_shortAnimTime).toLong())
                }
                finish()
            }
        }
    }

    private fun createWatchWidgetCustomizationIntent() = watchWidgetCustomizationNavigator
        .newWatchWidgetCustomizationIntent(
            this,
            AnalyticsPropertyValue.WatchEditWidgetScreenSource.ONBOARDING_FLOW,
            android.R.anim.fade_in to BaseR.anim.slide_out_down,
            fromOnboarding = true
        )

    private fun createWifiNetworksIntent() = WifiNetworksActivity.newStartIntent(this)

    private fun createOfflineMapsIntent() = offlineMapsNavigator.newOfflineMapSelectionStartIntent(
        context = this,
        analyticsSource = AnalyticsPropertyValue.DownloadMapsScreenSource.ONBOARDING_FLOW,
    )

    companion object {
        @JvmStatic
        fun newStartIntent(context: Context, deviceType: SuuntoDeviceType): Intent {
            return Intent(context, OceanOnboardingActivity::class.java)
                .putExtra(BaseOnboardingActivity.KEY_EXTRA_SUUNTO_DEVICE_TYPE, deviceType)
        }
    }
}
