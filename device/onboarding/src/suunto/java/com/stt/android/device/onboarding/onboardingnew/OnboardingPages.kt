package com.stt.android.device.onboarding.onboardingnew

import android.content.res.Resources
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.device.onboarding.R
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.widget.WidgetVersion
import com.suunto.connectivity.widget.isMiniStyle
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList

data class OnboardingPage(
    val headerContent: OnboardingHeaderContent,
    val title: String,
    val body: String,
    val buttonText: String,
    val buttonAction: OnboardingPageButtonAction? = null,
    val secondButtonText: String? = null,
    val secondButtonAction: OnboardingPageButtonAction? = null,
    val switchAction: OnboardingPageSwitchAction? = null,
    val analyticsName: String? = null
)

sealed class OnboardingHeaderContent
class OnboardingHeaderVideo(
    val videoUrl: String,
    @DrawableRes val placeholderImageResId: Int
) : OnboardingHeaderContent()

class OnboardingHeaderImageResource(@DrawableRes val imageRes: Int) : OnboardingHeaderContent()
class OnboardingHeaderAnimationResource(
    val assetFileName: String,
    val imageAssetsFolderName: String
) : OnboardingHeaderContent()

enum class OnboardingPageButtonAction {
    NEXT_PAGE,
    CUSTOMIZE_WATCH_WIDGETS,
    SKIP_CUSTOMIZE_WATCH_WIDGETS,
    CLOSE_ONBOARDING,
    SETUP_NETWORK,
    SKIP_SETUP_NETWORK,
    DOWNLOAD_MAPS,
    SKIP_DOWNLOAD_MAPS,
    SKIP_SLEEP_TRACKING,
    TURN_ON_247_HR,
    SKIP_247_HR,
    CUSTOMIZE_SPORTS_MODE,
    DISCOVER_WATCH_FACES,
}

sealed interface OnboardingPageSwitchAction {
    @get:StringRes
    val title: Int

    @get:StringRes
    val description: Int
    val checked: Boolean

    data class TrackSleep(
        override val checked: Boolean,
    ) : OnboardingPageSwitchAction {
        override val title: Int = R.string.onboarding_run_enable_sleep_tracking_title
        override val description: Int = R.string.onboarding_run_enable_sleep_tracking_description
    }
}

fun buildOnboardingPages(
    resources: Resources,
    deviceType: SuuntoDeviceType,
    username: String,
    showWatchWidgetCustomization: Boolean,
    widgetVersion: WidgetVersion,
    show247HrPage: Boolean,
    sleepTrackEnabled: Boolean,
    diluOnboardingWatchFaceEnabled: Boolean,
): ImmutableList<OnboardingPage> {
    return when (deviceType) {
        SuuntoDeviceType.SuuntoRace -> {
            buildRaceOnboardingPages(
                resources = resources,
                username = username
            )
        }

        SuuntoDeviceType.SuuntoVertical -> {
            buildSuuntoVerticalOnboardingPages(
                resources = resources,
                widgetVersion = widgetVersion
            )
        }

        SuuntoDeviceType.Suunto9PeakPro -> {
            buildSuunto9PeakProOnboardingPages(
                resources = resources,
                deviceType = deviceType,
                username = username,
                showWatchWidgetCustomization = showWatchWidgetCustomization,
                widgetVersion = widgetVersion
            )
        }

        SuuntoDeviceType.SuuntoRaceS -> {
            buildSuuntoRaceSOnboardingPages(
                resources = resources,
                username = username,
                show247HrPage = show247HrPage,
                sleepTrackEnabled = sleepTrackEnabled,
            )
        }

        SuuntoDeviceType.SuuntoRun -> {
            buildSuuntoRunOnboardingPages(
                resources = resources,
                username = username,
                sleepTrackEnabled = sleepTrackEnabled,
                diluOnboardingWatchFaceEnabled = diluOnboardingWatchFaceEnabled
            )
        }

        SuuntoDeviceType.SuuntoVertical2 -> {
            buildSuuntoVertical2OnboardingPages(
                resources = resources,
                username = username,
            )
        }

        SuuntoDeviceType.SuuntoRace2 -> {
            buildRace2OnboardingPages(
                resources = resources,
                username = username,
                show247HrPage = show247HrPage,
                sleepTrackEnabled = sleepTrackEnabled,
            )
        }

        else -> persistentListOf()
    }
}

fun buildSuunto9PeakProOnboardingPages(
    resources: Resources,
    deviceType: SuuntoDeviceType,
    username: String,
    showWatchWidgetCustomization: Boolean,
    widgetVersion: WidgetVersion
): ImmutableList<OnboardingPage> = buildList {
    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_sparrow_intro),
            title = resources.getString(
                R.string.onboarding_sparrow_welcome_title,
                username,
                deviceType.displayName
            ),
            body = resources.getString(R.string.onboarding_sparrow_welcome_detail),
            buttonText = resources.getString(R.string.onboarding_sparrow_action_start),
            buttonAction = OnboardingPageButtonAction.NEXT_PAGE,
            analyticsName = AnalyticsPropertyValue.OnboardingNG3PageNameProperty.START_SCREEN
        )
    )

    val startingExerciseVideoUrl = when {
        widgetVersion.isMiniStyle() -> "videos/onboarding_starting_sport02.mov"
        else -> "videos/onboarding_starting_sport.mov"
    }
    add(
        OnboardingPage(
            headerContent = OnboardingHeaderVideo(
                videoUrl = startingExerciseVideoUrl,
                placeholderImageResId = R.drawable.onboarding_sparrow_video_placeholder_starting_sport
            ),
            title = resources.getString(R.string.onboarding_sparrow_starting_sport_title),
            body = resources.getString(R.string.onboarding_sparrow_starting_sport_detail_inbox),
            buttonText = resources.getString(R.string.onboarding_sparrow_action_set_sport_list),
            buttonAction = null,
            analyticsName = AnalyticsPropertyValue.OnboardingNG3PageNameProperty.STARTING_EXERCISE
        )
    )

    val widgetHeaderContent = when {
        widgetVersion.isMiniStyle() -> OnboardingHeaderVideo(
            videoUrl = "videos/onboarding_widgets02.mov",
            placeholderImageResId = R.drawable.onboarding_sparrow_video_placeholder_widgets_mini_style
        )

        else -> OnboardingHeaderVideo(
            videoUrl = "videos/onboarding_widgets.mov",
            placeholderImageResId = R.drawable.onboarding_sparrow_video_placeholder_widgets
        )
    }

    @StringRes
    val widgetBodyResId = when {
        !showWatchWidgetCustomization -> R.string.onboarding_sparrow_widgets_detail_inbox
        widgetVersion.isMiniStyle() -> R.string.onboarding_sparrow_widgets_detail_rid_mini_style
        else -> R.string.onboarding_sparrow_widgets_detail_rid
    }
    add(
        OnboardingPage(
            headerContent = widgetHeaderContent,
            title = resources.getString(R.string.onboarding_sparrow_widgets_title),
            body = resources.getString(widgetBodyResId),
            buttonText = resources.getString(R.string.onboarding_sparrow_action_select_widgets),
            buttonAction = OnboardingPageButtonAction.CUSTOMIZE_WATCH_WIDGETS
                .takeIf { showWatchWidgetCustomization },
            analyticsName = AnalyticsPropertyValue.OnboardingNG3PageNameProperty.WIDGETS
        )
    )

    @StringRes
    val navigationBodyResId = when {
        widgetVersion.isMiniStyle() -> R.string.onboarding_sparrow_navigation_detail_mini_style
        else -> R.string.onboarding_sparrow_navigation_detail
    }
    val navigationVideoUrl = when {
        widgetVersion.isMiniStyle() -> "videos/onboarding_navigation02.mov"
        else -> "videos/onboarding_navigation.mov"
    }
    add(
        OnboardingPage(
            headerContent = OnboardingHeaderVideo(
                videoUrl = navigationVideoUrl,
                placeholderImageResId = R.drawable.onboarding_sparrow_video_placeholder_navigation
            ),
            title = resources.getString(R.string.onboarding_sparrow_navigation_title),
            body = resources.getString(navigationBodyResId),
            buttonText = "",
            buttonAction = null,
            analyticsName = AnalyticsPropertyValue.OnboardingNG3PageNameProperty.NAVIGATION
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_sparrow_final),
            title = resources.getString(R.string.onboarding_sparrow_final_title),
            body = resources.getString(R.string.onboarding_sparrow_final_detail),
            buttonText = resources.getString(R.string.onboarding_sparrow_action_close),
            buttonAction = OnboardingPageButtonAction.CLOSE_ONBOARDING,
            analyticsName = AnalyticsPropertyValue.OnboardingNG3PageNameProperty.END
        )
    )
}.toImmutableList()

fun buildRaceOnboardingPages(
    resources: Resources,
    username: String,
): ImmutableList<OnboardingPage> = buildList {
    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_race_welcome),
            title = resources.getString(
                R.string.onboarding_race_welcome_title,
                username,
                SuuntoDeviceType.SuuntoRace.displayName
            ),
            body = resources.getString(R.string.onboarding_race_welcome_detail),
            buttonText = resources.getString(R.string.onboarding_race_welcome_button_title),
            buttonAction = OnboardingPageButtonAction.NEXT_PAGE,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderAnimationResource(
                resources.getString(R.string.onboarding_anim_race_starting_exercise),
                resources.getString(R.string.onboarding_anim_race_starting_exercise_folder)
            ),
            title = resources.getString(R.string.onboarding_race_start_exercise_title),
            body = resources.getString(R.string.onboarding_race_start_exercise_detail),
            buttonText = "",
            buttonAction = null,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderAnimationResource(
                resources.getString(R.string.onboarding_anim_race_widgets),
                resources.getString(R.string.onboarding_anim_race_widgets_folder)
            ),
            title = resources.getString(R.string.onboarding_race_widgets_title),
            body = resources.getString(R.string.onboarding_race_widgets_detail),
            buttonText = "",
            buttonAction = null,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_race_wifi),
            title = resources.getString(R.string.onboarding_race_networks_title),
            body = resources.getString(R.string.onboarding_race_networks_detail),
            buttonText = resources.getString(R.string.onboarding_race_networks_button_title),
            buttonAction = OnboardingPageButtonAction.SETUP_NETWORK,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_race_offline_maps),
            title = resources.getString(R.string.onboarding_orca_download_maps_title),
            body = resources.getString(R.string.onboarding_orca_download_maps_detail),
            buttonText = resources.getString(R.string.onboarding_orca_download_maps_button_title),
            buttonAction = OnboardingPageButtonAction.DOWNLOAD_MAPS,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_race_customize),
            title = resources.getString(R.string.onboarding_race_customize_title),
            body = resources.getString(R.string.onboarding_race_customize_detail),
            buttonText = resources.getString(R.string.onboarding_race_customize_button_title),
            buttonAction = OnboardingPageButtonAction.CUSTOMIZE_WATCH_WIDGETS,
        )
    )
}.toImmutableList()

fun buildRace2OnboardingPages(
    resources: Resources,
    username: String,
    show247HrPage: Boolean,
    sleepTrackEnabled: Boolean,
): ImmutableList<OnboardingPage> = buildList {
    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_race2_welcome),
            title = resources.getString(
                R.string.onboarding_race_welcome_title,
                username,
                SuuntoDeviceType.SuuntoRace2.displayName
            ),
            body = resources.getString(R.string.onboarding_race_welcome_detail),
            buttonText = resources.getString(R.string.onboarding_race_welcome_button_title),
            buttonAction = OnboardingPageButtonAction.NEXT_PAGE,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderVideo(
                videoUrl = "videos/watch/race2/starting_exercise.mp4",
                placeholderImageResId = R.drawable.race2_starting_exercise_placeholder
            ),
            title = resources.getString(R.string.onboarding_race_start_exercise_title),
            body = resources.getString(R.string.onboarding_race_start_exercise_detail),
            buttonText = "",
            buttonAction = null,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderVideo(
                videoUrl = "videos/watch/race2/widgets.mp4",
                placeholderImageResId = R.drawable.race2_widgets_placeholder
            ),
            title = resources.getString(R.string.onboarding_race_widgets_title),
            body = resources.getString(R.string.onboarding_race_widgets_detail),
            buttonText = "",
            buttonAction = null,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderVideo(
                videoUrl = "videos/watch/race2/track_your_sleep.mp4",
                placeholderImageResId = R.drawable.race2_track_your_sleep_placeholder
            ),
            title = resources.getString(R.string.onboarding_races_track_your_sleep_title),
            body = resources.getString(R.string.onboarding_races_track_your_sleep_body),
            buttonText = "",
            buttonAction = null,
            switchAction = OnboardingPageSwitchAction.TrackSleep(
                checked = sleepTrackEnabled,
            ),
            secondButtonText = resources.getString(com.stt.android.R.string.later),
            secondButtonAction = OnboardingPageButtonAction.SKIP_SLEEP_TRACKING,
        )
    )

    if (show247HrPage) {
        add(
            OnboardingPage(
                headerContent = OnboardingHeaderVideo(
                    videoUrl = "videos/watch/race2/onboarding_247_hr.mp4",
                    placeholderImageResId = R.drawable.race2_onboarding_247_hr_placeholder
                ),
                title = resources.getString(R.string.onboarding_races_track_your_daily_health_title),
                body = resources.getString(R.string.onboarding_races_track_your_daily_health_body),
                buttonText = resources.getString(R.string.onboarding_races_track_your_daily_health_button),
                buttonAction = OnboardingPageButtonAction.TURN_ON_247_HR,
                secondButtonText = resources.getString(com.stt.android.R.string.later),
                secondButtonAction = OnboardingPageButtonAction.SKIP_247_HR
            )
        )
    }

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_race2_wifi),
            title = resources.getString(R.string.onboarding_race_networks_title),
            body = resources.getString(R.string.onboarding_race_networks_detail),
            buttonText = resources.getString(R.string.onboarding_race_networks_button_title),
            buttonAction = OnboardingPageButtonAction.SETUP_NETWORK,
            secondButtonText = resources.getString(com.stt.android.R.string.later),
            secondButtonAction = OnboardingPageButtonAction.SKIP_SETUP_NETWORK,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_race2_offline_maps),
            title = resources.getString(R.string.onboarding_orca_download_maps_title),
            body = resources.getString(R.string.onboarding_orca_download_maps_detail),
            buttonText = resources.getString(R.string.onboarding_orca_download_maps_button_title),
            buttonAction = OnboardingPageButtonAction.DOWNLOAD_MAPS,
            secondButtonText = resources.getString(com.stt.android.R.string.later),
            secondButtonAction = OnboardingPageButtonAction.SKIP_DOWNLOAD_MAPS,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_race2_customize),
            title = resources.getString(R.string.onboarding_race_customize_title),
            body = resources.getString(R.string.onboarding_race_customize_detail),
            buttonText = resources.getString(R.string.onboarding_race_customize_button_title),
            buttonAction = OnboardingPageButtonAction.CUSTOMIZE_WATCH_WIDGETS,
            secondButtonText = resources.getString(com.stt.android.R.string.later),
            secondButtonAction = OnboardingPageButtonAction.SKIP_CUSTOMIZE_WATCH_WIDGETS,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_race2_background),
            title = resources.getString(R.string.onboarding_race_end_title),
            body = resources.getString(R.string.onboarding_race_end_detail),
            buttonText = resources.getString(R.string.onboarding_seal_lets_go_button_title),
            buttonAction = OnboardingPageButtonAction.CLOSE_ONBOARDING,
        )
    )
}.toImmutableList()

fun buildSuuntoVerticalOnboardingPages(
    resources: Resources,
    widgetVersion: WidgetVersion
): ImmutableList<OnboardingPage> = buildList {
    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_sv_wifi_setup),
            title = resources.getString(R.string.onboarding_orca_setup_network_title),
            body = resources.getString(R.string.onboarding_orca_setup_network_detail),
            buttonText = resources.getString(R.string.onboarding_orca_setup_network_button_title),
            buttonAction = OnboardingPageButtonAction.SETUP_NETWORK,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_sv_offline_maps),
            title = resources.getString(R.string.onboarding_orca_download_maps_title),
            body = resources.getString(R.string.onboarding_orca_download_maps_detail),
            buttonText = resources.getString(R.string.onboarding_orca_download_maps_button_title),
            buttonAction = OnboardingPageButtonAction.DOWNLOAD_MAPS,
        )
    )

    @StringRes
    val widgetBodyResId = when {
        widgetVersion.isMiniStyle() -> R.string.onboarding_orca_widgets_detail_mini_style
        else -> R.string.onboarding_orca_widgets_detail
    }
    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_sv_widgets),
            title = resources.getString(R.string.onboarding_orca_widgets_title),
            body = resources.getString(widgetBodyResId),
            buttonText = resources.getString(R.string.onboarding_orca_widgets_button_title),
            buttonAction = OnboardingPageButtonAction.CUSTOMIZE_WATCH_WIDGETS,
        )
    )
}.toImmutableList()

fun buildSuuntoVertical2OnboardingPages(
    resources: Resources,
    username: String,
): ImmutableList<OnboardingPage> = buildList {
    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_vertical2_welcome),
            title = resources.getString(
                R.string.onboarding_race_welcome_title,
                username,
                SuuntoDeviceType.SuuntoVertical2.displayName
            ),
            body = resources.getString(R.string.onboarding_race_welcome_detail),
            buttonText = resources.getString(R.string.onboarding_race_welcome_button_title),
            buttonAction = OnboardingPageButtonAction.NEXT_PAGE,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_sv2_wifi_setup),
            title = resources.getString(R.string.onboarding_orca_setup_network_title),
            body = resources.getString(R.string.onboarding_orca_setup_network_detail),
            buttonText = resources.getString(R.string.onboarding_orca_setup_network_button_title),
            buttonAction = OnboardingPageButtonAction.SETUP_NETWORK,
            secondButtonText = resources.getString(com.stt.android.R.string.later),
            secondButtonAction = OnboardingPageButtonAction.SKIP_SETUP_NETWORK,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_sv2_offline_maps),
            title = resources.getString(R.string.onboarding_orca_download_maps_title),
            body = resources.getString(R.string.onboarding_orca_download_maps_detail),
            buttonText = resources.getString(R.string.onboarding_orca_download_maps_button_title),
            buttonAction = OnboardingPageButtonAction.DOWNLOAD_MAPS,
            secondButtonText = resources.getString(com.stt.android.R.string.later),
            secondButtonAction = OnboardingPageButtonAction.SKIP_DOWNLOAD_MAPS,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_sv2_widgets),
            title = resources.getString(R.string.onboarding_race_customize_title),
            body = resources.getString(R.string.onboarding_race_customize_detail),
            buttonText = resources.getString(R.string.onboarding_race_customize_button_title),
            buttonAction = OnboardingPageButtonAction.CUSTOMIZE_WATCH_WIDGETS,
            secondButtonText = resources.getString(com.stt.android.R.string.later),
            secondButtonAction = OnboardingPageButtonAction.SKIP_CUSTOMIZE_WATCH_WIDGETS,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_vertical2_background),
            title = resources.getString(R.string.onboarding_race_end_title),
            body = resources.getString(R.string.onboarding_race_end_detail),
            buttonText = resources.getString(R.string.onboarding_seal_lets_go_button_title),
            buttonAction = OnboardingPageButtonAction.CLOSE_ONBOARDING,
        )
    )
}.toImmutableList()

fun buildSuuntoRaceSOnboardingPages(
    resources: Resources,
    username: String,
    show247HrPage: Boolean,
    sleepTrackEnabled: Boolean,
): ImmutableList<OnboardingPage> = buildList {
    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_races_welcome),
            title = resources.getString(
                R.string.onboarding_race_welcome_title,
                username,
                SuuntoDeviceType.SuuntoRaceS.displayName
            ),
            body = resources.getString(R.string.onboarding_race_welcome_detail),
            buttonText = resources.getString(R.string.onboarding_race_welcome_button_title),
            buttonAction = OnboardingPageButtonAction.NEXT_PAGE,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderAnimationResource(
                resources.getString(R.string.onboarding_anim_races_starting_exercise),
                resources.getString(R.string.onboarding_anim_races_starting_exercise_folder)
            ),
            title = resources.getString(R.string.onboarding_race_start_exercise_title),
            body = resources.getString(R.string.onboarding_race_start_exercise_detail),
            buttonText = "",
            buttonAction = null,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderAnimationResource(
                resources.getString(R.string.onboarding_anim_races_widgets),
                resources.getString(R.string.onboarding_anim_races_widgets_folder)
            ),
            title = resources.getString(R.string.onboarding_race_widgets_title),
            body = resources.getString(R.string.onboarding_race_widgets_detail),
            buttonText = "",
            buttonAction = null,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderVideo(
                videoUrl = "videos/onboarding_sleep.mov",
                placeholderImageResId = R.drawable.onboarding_races_sleep_placeholder
            ),
            title = resources.getString(R.string.onboarding_races_track_your_sleep_title),
            body = resources.getString(R.string.onboarding_races_track_your_sleep_body),
            buttonText = "",
            buttonAction = null,
            switchAction = OnboardingPageSwitchAction.TrackSleep(
                checked = sleepTrackEnabled,
            ),
            secondButtonText = resources.getString(com.stt.android.R.string.later),
            secondButtonAction = OnboardingPageButtonAction.SKIP_SLEEP_TRACKING
        )
    )

    if (show247HrPage) {
        add(
            OnboardingPage(
                headerContent = OnboardingHeaderVideo(
                    videoUrl = "videos/onboarding_247_hr.mov",
                    placeholderImageResId = R.drawable.onboarding_races_247_hr_placeholder
                ),
                title = resources.getString(R.string.onboarding_races_track_your_daily_health_title),
                body = resources.getString(R.string.onboarding_races_track_your_daily_health_body),
                buttonText = resources.getString(R.string.onboarding_races_track_your_daily_health_button),
                buttonAction = OnboardingPageButtonAction.TURN_ON_247_HR,
                secondButtonText = resources.getString(com.stt.android.R.string.later),
                secondButtonAction = OnboardingPageButtonAction.SKIP_247_HR
            )
        )
    }

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_races_wifi),
            title = resources.getString(R.string.onboarding_race_networks_title),
            body = resources.getString(R.string.onboarding_race_networks_detail),
            buttonText = resources.getString(R.string.onboarding_race_networks_button_title),
            buttonAction = OnboardingPageButtonAction.SETUP_NETWORK,
            secondButtonText = resources.getString(R.string.onboarding_seal_offline_maps_later),
            secondButtonAction = OnboardingPageButtonAction.NEXT_PAGE
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_races_offline_maps),
            title = resources.getString(R.string.onboarding_orca_download_maps_title),
            body = resources.getString(R.string.onboarding_orca_download_maps_detail),
            buttonText = resources.getString(R.string.onboarding_orca_download_maps_button_title),
            buttonAction = OnboardingPageButtonAction.DOWNLOAD_MAPS,
            secondButtonText = resources.getString(R.string.onboarding_seal_offline_maps_later),
            secondButtonAction = OnboardingPageButtonAction.NEXT_PAGE
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_races_customize),
            title = resources.getString(R.string.onboarding_race_customize_title),
            body = resources.getString(R.string.onboarding_race_customize_detail),
            buttonText = resources.getString(R.string.onboarding_race_customize_button_title),
            buttonAction = OnboardingPageButtonAction.CUSTOMIZE_WATCH_WIDGETS,
            secondButtonText = resources.getString(R.string.onboarding_seal_offline_maps_later),
            secondButtonAction = OnboardingPageButtonAction.NEXT_PAGE
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_races_end),
            title = resources.getString(R.string.onboarding_race_end_title),
            body = resources.getString(R.string.onboarding_race_end_detail),
            buttonText = resources.getString(R.string.onboarding_race_end_button_title),
            buttonAction = OnboardingPageButtonAction.CLOSE_ONBOARDING,
        )
    )
}.toImmutableList()

fun buildSuuntoRunOnboardingPages(
    resources: Resources,
    username: String,
    sleepTrackEnabled: Boolean,
    diluOnboardingWatchFaceEnabled: Boolean,
): ImmutableList<OnboardingPage> = buildList {
    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_run_welcome),
            title = resources.getString(
                R.string.onboarding_race_welcome_title,
                username,
                SuuntoDeviceType.SuuntoRun.displayName
            ),
            body = resources.getString(R.string.onboarding_race_welcome_detail),
            buttonText = resources.getString(R.string.onboarding_race_welcome_button_title),
            buttonAction = OnboardingPageButtonAction.NEXT_PAGE,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderVideo(
                videoUrl = "videos/watch/run/onboarding_run_exercise.mov",
                placeholderImageResId = R.drawable.onboarding_run_placeholder
            ),
            title = resources.getString(R.string.onboarding_run_start_exercise_title),
            body = resources.getString(R.string.onboarding_run_start_exercise_body),
            buttonText = "",
            buttonAction = null,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderVideo(
                videoUrl = "videos/watch/run/onboarding_run_widgets.mov",
                placeholderImageResId = R.drawable.onboarding_run_placeholder
            ),
            title = resources.getString(R.string.onboarding_race_widgets_title),
            body = resources.getString(R.string.onboarding_run_widgets_detail),
            buttonText = "",
            buttonAction = null,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_run_widgets),
            title = resources.getString(R.string.onboarding_race_customize_title),
            body = resources.getString(R.string.onboarding_run_customize_detail),
            buttonText = resources.getString(R.string.onboarding_race_customize_button_title),
            buttonAction = OnboardingPageButtonAction.CUSTOMIZE_WATCH_WIDGETS,
            secondButtonText = resources.getString(R.string.onboarding_seal_offline_maps_later),
            secondButtonAction = OnboardingPageButtonAction.NEXT_PAGE
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_run_track_sleep),
            title = resources.getString(R.string.onboarding_races_track_your_sleep_title),
            body = resources.getString(R.string.onboarding_run_track_your_sleep_body),
            buttonText = "",
            buttonAction = null,
            switchAction = OnboardingPageSwitchAction.TrackSleep(
                checked = sleepTrackEnabled,
            ),
            secondButtonText = if (!sleepTrackEnabled) resources.getString(com.stt.android.R.string.later) else null,
            secondButtonAction = if (!sleepTrackEnabled) OnboardingPageButtonAction.SKIP_SLEEP_TRACKING else null,
        )
    )

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_run_sport_mode),
            title = resources.getString(R.string.onboarding_run_sport_mode_title),
            body = resources.getString(R.string.onboarding_run_sport_mode_body),
            buttonText = resources.getString(R.string.onboarding_run_sport_mode_button),
            buttonAction = OnboardingPageButtonAction.CUSTOMIZE_SPORTS_MODE,
            secondButtonText = resources.getString(R.string.onboarding_seal_offline_maps_later),
            secondButtonAction = OnboardingPageButtonAction.NEXT_PAGE
        )
    )

    if (diluOnboardingWatchFaceEnabled) {
        add(
            OnboardingPage(
                headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_run_watch_faces),
                title = resources.getString(R.string.onboarding_run_online_watch_faces_title),
                body = resources.getString(R.string.onboarding_run_online_watch_faces_body),
                buttonText = resources.getString(R.string.onboarding_run_online_watch_faces_button),
                buttonAction = OnboardingPageButtonAction.DISCOVER_WATCH_FACES,
                secondButtonText = resources.getString(R.string.onboarding_seal_offline_maps_later),
                secondButtonAction = OnboardingPageButtonAction.NEXT_PAGE
            )
        )
    }

    add(
        OnboardingPage(
            headerContent = OnboardingHeaderImageResource(R.drawable.onboarding_run_background),
            title = resources.getString(R.string.onboarding_race_end_title),
            body = resources.getString(R.string.onboarding_race_end_detail),
            buttonText = resources.getString(R.string.onboarding_race_welcome_button_title),
            buttonAction = OnboardingPageButtonAction.CLOSE_ONBOARDING,
        )
    )
}.toImmutableList()
