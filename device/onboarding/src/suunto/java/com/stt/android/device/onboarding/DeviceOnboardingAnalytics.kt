package com.stt.android.device.onboarding

import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.core.utils.TimeProvider
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.scopes.ActivityRetainedScoped
import javax.inject.Inject
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@ActivityRetainedScoped
class DeviceOnboardingAnalytics @Inject constructor(
    private val timeProvider: TimeProvider,
    private val datahubAnalyticsTracker: DatahubAnalyticsTracker,
) {
    private val onboardingStartTime = timeProvider.elapsedRealtime()

    fun trackOnboardingFinished(
        suuntoDeviceType: SuuntoDeviceType,
        pageName: String
    ) {
        val elapsedTimeSinceStartSeconds = (timeProvider.elapsedRealtime() - onboardingStartTime)
            .toDuration(DurationUnit.MILLISECONDS)
            .inWholeSeconds

        val watchModelName =
            AnalyticsDevicePropertyHelper.getWatchModelNameForSuuntoDeviceType(suuntoDeviceType)

        AnalyticsProperties().apply {
            put(AnalyticsEventProperty.DURATION_IN_SECONDS, elapsedTimeSinceStartSeconds)
            put(AnalyticsEventProperty.LAST_SCREEN, pageName)
            put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModelName)

            datahubAnalyticsTracker.trackEvent(
                AnalyticsEvent.SUUNTO_VIEW_EVENT_ONBOARDING_FLOW_FINISHED,
                this
            )
        }
    }

    fun trackPageChanged(
        suuntoDeviceType: SuuntoDeviceType,
        newPage: Int,
        oldPage: Int,
        navigationMethod: String
    ) {
        // navigation type events are sent only when moving from first page
        if (oldPage != 0) return

        val watchModelName =
            AnalyticsDevicePropertyHelper.getWatchModelNameForSuuntoDeviceType(suuntoDeviceType)

        AnalyticsProperties().apply {
            put(AnalyticsEventProperty.NAVIGATION_METHOD, navigationMethod)
            put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModelName)

            datahubAnalyticsTracker.trackEvent(
                AnalyticsEvent.SUUNTO_VIEW_EVENT_ONBOARDING_FLOW_NAVIGATION,
                this
            )
        }
    }

    fun trackStartClick(suuntoDeviceType: SuuntoDeviceType) = trackOnboardingFlowEvent(
        suuntoDeviceType,
        AnalyticsPropertyValue.OnboardingCallToActionProperty.START,
        AnalyticsPropertyValue.OnboardingReactionProperty.START
    )

    fun trackOpenWatchWidgetCustomization(suuntoDeviceType: SuuntoDeviceType) = trackOnboardingFlowEvent(
        suuntoDeviceType,
        AnalyticsPropertyValue.OnboardingCallToActionProperty.SELECT_WATCH_WIDGETS,
        AnalyticsPropertyValue.OnboardingReactionProperty.SELECT_WATCH_WIDGETS
    )

    fun trackOpenWifiSetup(suuntoDeviceType: SuuntoDeviceType, skipped: Boolean) =
        trackOnboardingFlowEvent(
            suuntoDeviceType,
            AnalyticsPropertyValue.OnboardingCallToActionProperty.SETUP_WIFI,
            if (skipped) {
                AnalyticsPropertyValue.OnboardingReactionProperty.SKIP
            } else {
                AnalyticsPropertyValue.OnboardingReactionProperty.SETUP_WIFI
            }
        )

    private fun trackOnboardingFlowEvent(
        suuntoDeviceType: SuuntoDeviceType,
        callToAction: String,
        reaction: String
    ) {
        val watchModelName =
            AnalyticsDevicePropertyHelper.getWatchModelNameForSuuntoDeviceType(suuntoDeviceType)

        AnalyticsProperties().apply {
            put(AnalyticsEventProperty.CALL_TO_ACTION, callToAction)
            put(AnalyticsEventProperty.REACTION, reaction)
            put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModelName)

            datahubAnalyticsTracker.trackEvent(
                AnalyticsEvent.SUUNTO_VIEW_EVENT_ONBOARDING_FLOW,
                this
            )
        }
    }

    fun trackVideoCompleted(suuntoDeviceType: SuuntoDeviceType, pageName: String) {
        val watchModelName =
            AnalyticsDevicePropertyHelper.getWatchModelNameForSuuntoDeviceType(suuntoDeviceType)

        AnalyticsProperties().apply {
            put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModelName)
            put(AnalyticsEventProperty.VIDEO, pageName)

            datahubAnalyticsTracker.trackEvent(
                AnalyticsEvent.SUUNTO_WATCH_ONBOARDING_FLOW_VIDEO_COMPLETED,
                this
            )
        }
    }
}
