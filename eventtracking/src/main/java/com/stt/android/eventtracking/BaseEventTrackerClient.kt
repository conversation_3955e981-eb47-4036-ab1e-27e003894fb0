package com.stt.android.eventtracking

import android.content.Context
import android.database.sqlite.SQLiteDatabaseLockedException
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.sqlite.db.SupportSQLiteDatabase
import com.stt.android.eventtracking.database.TrackerDatabase
import com.stt.android.eventtracking.database.entity.LocalEvent
import com.stt.android.eventtracking.database.entity.LocalUserProperty
import com.stt.android.eventtracking.extensions.toJSONObject
import com.stt.android.eventtracking.extensions.toMap
import com.stt.android.eventtracking.extensions.toStringList
import com.stt.android.eventtracking.preference.TrackerPreferences
import com.stt.android.eventtracking.utils.currentTimestamp
import com.stt.android.eventtracking.utils.gatherDeviceInfo
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONObject
import timber.log.Timber

internal abstract class BaseEventTrackerClient(
    context: Context,
    databaseName: String,
    preferencesName: String,
    coroutineDispatcher: CoroutineDispatcher,
) : EventTrackerClient {

    private val appContext = context.applicationContext

    protected val database = Room.databaseBuilder(
        appContext,
        TrackerDatabase::class.java,
        databaseName,
    ).enableMultiInstanceInvalidation().addCallback(object : RoomDatabase.Callback() {
        override fun onOpen(db: SupportSQLiteDatabase) {
            super.onOpen(db)
            // Work Around: https://issuetracker.google.com/issues/280124659
            // Issue caused by enableMultiInstanceInvalidation, there isn't a fix for this issue yet.
            // According to the page above,
            // the occurrence rate is ~1% before the work around, approximately 0.04% after the work around.
            db.query("PRAGMA busy_timeout = 10000;")
        }
    }).build()

    protected val preferences = TrackerPreferences(appContext, preferencesName)

    protected val coroutineScope =
        CoroutineScope(CoroutineName(COROUTINE_NAME) + coroutineDispatcher + SupervisorJob())

    override var userId: String?
        get() = preferences.userId
        set(value) {
            preferences.userId = value
        }

    private val eventsConfigFlow = MutableStateFlow<Set<String>>(emptySet())

    private val isServiceDisabled: Boolean
        get() = eventsConfigFlow.value.isEmpty()

    override fun initEventsConfig() {
        coroutineScope.launch {
            database.keyValueDao().keyValueUpdates(KEY_EVENTS_CONFIG).collectLatest { keyValue ->
                runCatching {
                    keyValue?.value?.let {
                        eventsConfigFlow.tryEmit(JSONArray(it).toStringList().toSet())
                    }
                }.onFailure {
                    Timber.w(it, "Read events config failed")
                }
            }
        }
    }

    override fun trackWatchEvents(eventsJson: String) {
        if (isServiceDisabled) {
            return
        }

        // Currently we don't record events without user
        val userId = userId
        if (userId.isNullOrEmpty()) {
            return
        }

        Timber.d("Watch Events: $eventsJson")

        coroutineScope.launch {
            val startMillis = currentTimestamp()
            runCatching {
                val jsonObject = JSONObject(eventsJson)
                val events = jsonObject.getJSONArray("events").let { events ->
                    val device = jsonObject.getJSONObject("device").toString()
                    (0 until events.length()).map { index ->
                        val event = events.getJSONObject(index)
                        LocalEvent(
                            0L,
                            userId,
                            event.getString("eventType"),
                            event.getJSONObject("eventProperties").toMap(),
                            device,
                            event.getLong("timestamp"),
                            false,
                            null,
                            LocalEvent.SOURCE_WATCH,
                        )
                    }
                }
                database.eventDao().insert(events)
            }.onFailure {
                val traceTimeInMillis = currentTimestamp() - startMillis
                if (it is SQLiteDatabaseLockedException) {
                    Timber.w(it, "Insert failed: database locked. Timeout: $traceTimeInMillis")
                } else {
                    Timber.w(it, "Insert failed. TimeInMillis: $traceTimeInMillis")
                }
            }
        }
    }

    override fun trackEvent(eventType: String, eventProperties: Map<String, Any>?) {
        if (isServiceDisabled) {
            return
        }

        // Currently we don't record events without user
        val userId = userId
        if (userId.isNullOrEmpty()) {
            return
        }

        Timber.d("Event: $eventType")

        coroutineScope.launch {
            val startMillis = currentTimestamp()
            runCatching {
                val event = LocalEvent(
                    0L,
                    userId,
                    eventType,
                    eventProperties,
                    gatherDeviceInfo(appContext).toJSONObject().toString(),
                    currentTimestamp(),
                    false,
                    null,
                    null,
                )
                database.eventDao().insert(event)
            }.onFailure {
                val traceTimeInMillis = currentTimestamp() - startMillis
                if (it is SQLiteDatabaseLockedException) {
                    Timber.w(it, "Insert failed: database locked. Timeout: $traceTimeInMillis")
                } else {
                    Timber.w(it, "Insert failed. TimeInMillis: $traceTimeInMillis")
                }
            }
        }
    }

    override fun trackUserProperties(userProperties: Map<String, Any>) {
        if (isServiceDisabled || userProperties.isEmpty()) {
            return
        }

        // Currently we don't record events without user
        val userId = userId
        if (userId.isNullOrEmpty()) {
            return
        }

        Timber.d("User Properties: ${userProperties.keys.joinToString(", ")}")

        coroutineScope.launch {
            val startMillis = currentTimestamp()
            runCatching {
                val userProperty = LocalUserProperty(
                    0L,
                    userId,
                    userProperties,
                    null,
                    currentTimestamp(),
                    false,
                    null,
                )
                database.userPropertyDao().insert(userProperty)
            }.onFailure {
                val traceTimeInMillis = currentTimestamp() - startMillis
                if (it is SQLiteDatabaseLockedException) {
                    Timber.w(it, "Insert failed: database locked. Timeout: $traceTimeInMillis")
                } else {
                    Timber.w(it, "Insert failed. TimeInMillis: $traceTimeInMillis")
                }
            }
        }
    }

    companion object {
        private const val COROUTINE_NAME = "event_tracker"

        const val KEY_EVENTS_CONFIG = "events"
    }
}
