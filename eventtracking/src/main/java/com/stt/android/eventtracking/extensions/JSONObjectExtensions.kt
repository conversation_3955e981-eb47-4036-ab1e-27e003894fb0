package com.stt.android.eventtracking.extensions

import org.json.JSONArray
import org.json.JSONObject

@Suppress("UNCHECKED_CAST")
internal fun Map<String, Any>.toJSONObject(): JSONObject {
    return JSONObject().apply {
        forEach { (key, value) ->
            when (value) {
                is Map<*, *> -> {
                    put(key, (value as Map<String, Any>).toJSONObject())
                }

                is Iterable<*> -> {
                    put(key, JSONArray().apply { value.forEach { put(it) } })
                }

                else -> {
                    put(key, value)
                }
            }
        }
    }
}

internal fun JSONObject.toMap(): Map<String, Any> {
    return buildMap {
        <EMAIL>().forEach { key ->
            when (val value = <EMAIL>(key)) {
                is JSONObject -> {
                    put(key, value.toMap())
                }

                is JSONArray -> {
                    val list = buildList {
                        (0 until value.length()).forEach { add(value.get(it)) }
                    }
                    put(key, list)
                }

                else -> {
                    put(key, value)
                }
            }
        }
    }
}

internal fun List<String>.toJSONArray(): JSONArray {
    return JSONArray().apply {
        forEach {
            put(it)
        }
    }
}

internal fun JSONArray.toStringList(): List<String> {
    return (0..<length()).mapNotNull { index ->
        optString(index, null)
    }
}
