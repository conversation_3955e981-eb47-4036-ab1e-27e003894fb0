package com.stt.android.eventtracking

import android.content.Context
import android.database.sqlite.SQLiteDatabaseLockedException
import com.stt.android.eventtracking.database.TrackerDatabase
import com.stt.android.eventtracking.database.entity.LocalEvent
import com.stt.android.eventtracking.database.entity.LocalKeyValue
import com.stt.android.eventtracking.database.entity.LocalUserProperty
import com.stt.android.eventtracking.extensions.toJSONArray
import com.stt.android.eventtracking.extensions.toJSONObject
import com.stt.android.eventtracking.extensions.toMap
import com.stt.android.eventtracking.network.Env
import com.stt.android.eventtracking.network.EventRepository
import com.stt.android.eventtracking.network.EventRepository.ResponseStrategy
import com.stt.android.eventtracking.utils.currentTimestamp
import com.stt.android.eventtracking.utils.gatherDeviceInfo
import com.stt.android.eventtracking.utils.uuidString
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import org.json.JSONArray
import org.json.JSONObject
import timber.log.Timber
import java.util.Collections
import java.util.concurrent.TimeUnit
import kotlin.time.Duration.Companion.minutes

internal class PrimaryEventTrackerClient(
    context: Context,
    databaseName: String,
    preferencesName: String,
    coroutineDispatcher: CoroutineDispatcher,
    private val apiKey: String,
    private val watchApiKey: String,
    httpClient: OkHttpClient,
    env: Env,
) : BaseEventTrackerClient(context, databaseName, preferencesName, coroutineDispatcher) {

    private val appContext = context.applicationContext

    override val deviceId: String by lazy {
        preferences.ensureDeviceId()
    }

    // TODO should be removed in a later version for compatibility
    private val deviceInfo: Map<String, Any> by lazy {
        Collections.unmodifiableMap(gatherDeviceInfo(appContext))
    }

    private val repository: EventRepository by lazy {
        EventRepository(httpClient, env, deviceId)
    }

    private var syncJob: Job? = null

    override val isRunning: Boolean
        get() = syncJob?.isActive ?: false

    override fun initEventsConfig() {
        super.initEventsConfig()

        fun timeSinceLastUpdate(): Long {
            return currentTimestamp() - preferences.updatedTimestamp
        }

        coroutineScope.launch {
            if (timeSinceLastUpdate() > TimeUnit.DAYS.toMillis(1)) {
                runCatching {
                    Timber.d("Fetch Events Config")

                    fetchEventsConfig()
                }.onFailure {
                    Timber.w(it, "Fetch events config failed")
                }
            }
        }
    }

    /**
     * Start uploading event logs with a fixed delay schedule.
     * This method usually does not need to be called manually.
     * Called by [EventTracker.initialize].
     */
    override fun startSynchronization() {
        if (isRunning) return

        Timber.d("Synchronization Start")

        fun timeSinceLastUpload(): Long {
            return currentTimestamp() - preferences.uploadedTimestamp
        }

        syncJob = coroutineScope.launch {
            Timber.d("Synchronization: Cleanup Database")

            runCatching {
                database.cleanup(userId)
            }.onFailure {
                Timber.w(it, "Cleanup database failed")
            }

            if (timeSinceLastUpload() > TimeUnit.DAYS.toMillis(2)) {
                Timber.d("Synchronization: Upload - More Than 2 Days")

                runCatching {
                    uploadEventsIfNeeded(1)
                }.onFailure {
                    Timber.w(it, "Upload events failed")
                }

                runCatching {
                    uploadWatchEventsIfNeeded(1)
                }.onFailure {
                    Timber.w(it, "Upload watch events failed")
                }
            }

            val timeSinceLastUpload = timeSinceLastUpload()
            val timeRemainingUntilOneMinute = TimeUnit.MINUTES.toMillis(1) - timeSinceLastUpload
            if (timeSinceLastUpload > 0L && timeRemainingUntilOneMinute > 0L) {
                Timber.d("Synchronization: Delay Until One Minute")

                delay(timeRemainingUntilOneMinute)
            }

            while (true) {
                Timber.d("Synchronization: Upload - Schedule")

                runCatching {
                    uploadEventsIfNeeded(BATCH_EVENT_COUNT)
                }.onFailure {
                    Timber.w(it, "Upload events failed")
                }

                runCatching {
                    uploadWatchEventsIfNeeded(BATCH_EVENT_COUNT)
                }.onFailure {
                    Timber.w(it, "Upload watch events failed")
                }

                delay(1.minutes)
            }
        }
    }

    private suspend fun fetchEventsConfig() {
        val config = repository.getEventsConfig()
        val configJson = config.toJSONArray().toString()
        Timber.d("Events: $configJson")

        val startMillis = currentTimestamp()
        runCatching {
            val keyValue = LocalKeyValue(KEY_EVENTS_CONFIG, configJson)
            database.keyValueDao().insert(keyValue)
            preferences.updatedTimestamp = startMillis
        }.onFailure {
            val traceTimeInMillis = currentTimestamp() - startMillis
            if (it is SQLiteDatabaseLockedException) {
                Timber.w(it, "Insert failed: database locked. Timeout: $traceTimeInMillis")
            } else {
                Timber.w(it, "Insert failed. TimeInMillis: $traceTimeInMillis")
            }
        }
    }

    private suspend fun TrackerDatabase.cleanup(userId: String?) {
        // Currently we don't record events without user
        if (userId.isNullOrEmpty()) {
            return
        }

        eventDao().deleteAllUploaded(userId)
        userPropertyDao().deleteAllUploaded(userId)
    }

    private suspend fun uploadEventsIfNeeded(limitation: Int) {
        // Currently we don't record events without user
        val userId = userId
        if (userId.isNullOrEmpty()) {
            return
        }

        val eventDao = database.eventDao()
        val userPropertyDao = database.userPropertyDao()

        val count = eventDao.count(userId, 0, null)
        if (count < limitation) {
            Timber.d("Synchronization: Condition Not Matched")
            return
        }

        Timber.d("Synchronization Begin")

        var userPropertiesUploaded = false

        while (true) {
            val chunk = eventDao.getLimited(userId, 0, null, BATCH_EVENT_COUNT)
            if (chunk.isEmpty()) {
                break
            }

            var hasError = false

            for ((deviceInfoJson, subChunk) in chunk.groupBy { it.deviceInfo }) {
                val userPropertyChunk = if (!userPropertiesUploaded) {
                    userPropertiesUploaded = true
                    userPropertyDao.getSorted(userId, 0)
                } else emptyList()

                val deviceInfo = deviceInfoJson?.let { JSONObject(it).toMap() } ?: this.deviceInfo
                if (!submitEvent(
                        apiKey,
                        userId,
                        deviceId,
                        deviceInfo,
                        subChunk,
                        userPropertyChunk
                    )
                ) {
                    hasError = true
                    break
                }
            }

            if (hasError) {
                break
            }

            if (chunk.size < BATCH_EVENT_COUNT) {
                break
            }
        }

        Timber.d("Synchronization End")
    }

    private suspend fun uploadWatchEventsIfNeeded(limitation: Int) {
        // Currently we don't record events without user
        val userId = userId
        if (userId.isNullOrEmpty()) {
            return
        }

        val eventDao = database.eventDao()

        val count = eventDao.count(userId, 0, LocalEvent.SOURCE_WATCH)
        if (count < limitation) {
            Timber.d("Synchronization Watch: Condition Not Matched")
            return
        }

        Timber.d("Synchronization Watch Begin")

        while (true) {
            val chunk = eventDao.getLimited(userId, 0, LocalEvent.SOURCE_WATCH, BATCH_EVENT_COUNT)
            if (chunk.isEmpty()) {
                break
            }

            var hasError = false

            // Typically, the watch device info remains the same, except in cases such as firmware updates or other changes.
            for ((deviceInfoJson, subChunk) in chunk.groupBy { it.deviceInfo }) {
                val deviceInfo = deviceInfoJson?.let { JSONObject(it).toMap() } ?: emptyMap()
                if (!submitEvent(watchApiKey, userId, null, deviceInfo, subChunk, emptyList())) {
                    hasError = true
                    break
                }
            }

            if (hasError) {
                break
            }

            if (chunk.size < BATCH_EVENT_COUNT) {
                break
            }
        }

        Timber.d("Synchronization Watch End")
    }

    private suspend fun submitEvent(
        apiKey: String,
        userId: String,
        deviceId: String?,
        deviceInfo: Map<String, Any>,
        chunk: List<LocalEvent>,
        userPropertyChunk: List<LocalUserProperty>,
    ): Boolean {
        val currentTimestamp = currentTimestamp()

        val events = JSONArray().also { array ->
            val userProperties = userPropertyChunk.mergeUserProperties()
            if (userProperties.isNotEmpty()) {
                createEventJSONObject(
                    "\$identify",
                    null,
                    userProperties,
                    currentTimestamp,
                    null
                ).let { array.put(it) }
            }
            chunk.forEach { event ->
                createEventJSONObject(
                    event.eventType,
                    event.eventProperties,
                    null,
                    event.createdTimestamp,
                    event.id
                ).let { array.put(it) }
            }
        }.toString()
        val device = buildMap {
            putAll(deviceInfo)
            put("uuid", uuidString())
            put("library", mapOf("name" to LIBRARY_NAME, "version" to LIBRARY_VERSION))
            deviceId?.let { put("deviceId", it) }
        }.toJSONObject().toString()
        val strategy = repository.submitEvent(
            apiKey,
            userId,
            currentTimestamp,
            device,
            events,
            preferences.correctionMillis,
        )
        when (strategy) {
            ResponseStrategy.Success -> {
                Timber.d("Synchronization Response Strategy: Success")
            }

            ResponseStrategy.Retry -> {
                Timber.d("Synchronization Response Strategy: Retry")
                return false
            }

            ResponseStrategy.ClientError -> {
                Timber.d("Synchronization Response Strategy: Client Error")
                // Currently we treat it just like ResponseStrategy.Retry.
                // Maybe we should terminate the service, since client error can't be recovered.
                return false
            }

            is ResponseStrategy.ClientTimeError -> {
                Timber.d("Synchronization Response Strategy: Client Time Error")
                strategy.serverTimeInMillis.takeIf { it > 0L }?.let {
                    preferences.correctionMillis = it - currentTimestamp()
                }
                return false
            }
        }

        val updatedChunk = chunk.map {
            it.copy(
                uploaded = true,
                uploadedTimestamp = currentTimestamp,
            )
        }
        database.eventDao().update(updatedChunk)

        if (userPropertyChunk.isNotEmpty()) {
            val updatedUserPropertyChunk = userPropertyChunk.map {
                it.copy(
                    uploaded = true,
                    uploadedTimestamp = currentTimestamp,
                )
            }
            database.userPropertyDao().update(updatedUserPropertyChunk)
        }

        preferences.uploadedTimestamp = currentTimestamp

        return true
    }

    private fun createEventJSONObject(
        eventType: String,
        eventProperties: Map<String, Any>?,
        userProperties: Map<String, Any>?,
        timestamp: Long,
        eventId: Long?,
    ) = JSONObject().apply {
        // required
        put("eventType", eventType)
        // required
        put("eventProperties", eventProperties?.toJSONObject() ?: JSONObject())
        // optional
        userProperties?.let { put("userProperties", it.toJSONObject()) }
        // required
        put("timestamp", timestamp)
        // optional, may be useful for detecting duplicate event
        eventId?.let { put("eventId", it) }
    }

    private fun List<LocalUserProperty>.mergeUserProperties(): Map<String, Any> {
        if (isEmpty()) {
            return emptyMap()
        }

        return buildMap {
            <EMAIL> {
                putAll(it.userProperties)
            }
        }
    }

    /**
     * Stop uploading event logs with a fixed delay schedule.
     * This method usually does not need to be called unless the app allows the user to toggle the service.
     */
    override fun stopSynchronization() {
        Timber.d("Synchronization Stop")

        syncJob?.cancel()
        syncJob = null
    }

    companion object {
        private const val LIBRARY_NAME = "EventTracker-Android"
        private const val LIBRARY_VERSION = "1.1.0"
        private const val BATCH_EVENT_COUNT = 30
    }
}
