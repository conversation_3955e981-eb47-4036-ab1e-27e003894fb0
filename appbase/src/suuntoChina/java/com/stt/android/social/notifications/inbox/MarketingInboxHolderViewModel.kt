package com.stt.android.social.notifications.inbox

import androidx.lifecycle.viewModelScope
import com.emarsys.Emarsys.messageInbox
import com.emarsys.core.api.result.CompletionListener
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalyticsImpl
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.FeedController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.marketing.MarketingInboxRemoteDataSource
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.eventtracking.EventTracker
import com.stt.android.social.notifications.list.EmarsysInboxItem
import com.stt.android.social.notifications.list.EmarsysInboxSource
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class MarketingInboxHolderViewModel
@Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    feedController: FeedController,
    datahubAnalyticsTracker: DatahubAnalyticsTracker,
    emarsysAnalyticsImpl: EmarsysAnalyticsImpl,
    private val marketingInboxRemoteDataSource: MarketingInboxRemoteDataSource,
    private val dispatchers: CoroutinesDispatchers,
    eventTracker: EventTracker,
) : BaseMarketingInboxHolderViewModel(
    ioThread,
    mainThread,
    feedController,
    datahubAnalyticsTracker,
    emarsysAnalyticsImpl,
    eventTracker,
) {
    override fun addTag(item: EmarsysInboxItem, tag: String, listener: CompletionListener) {
        val messageId = item.id
        when (item.source) {
            EmarsysInboxSource.EMARSYS -> {
                messageInbox.addTag(tag, messageId, listener)
            }

            EmarsysInboxSource.SUUNTO -> {
                viewModelScope.launch {
                    val result = withContext(dispatchers.io + NonCancellable) {
                        runSuspendCatching {
                            if (!marketingInboxRemoteDataSource.updateTag(messageId, tag)) {
                                throw Exception("Set tag failed")
                            }
                        }
                    }
                    listener.onCompleted(result.exceptionOrNull())
                }
            }
        }
    }
}
