package com.stt.android.login.newsletter

import androidx.core.app.NavUtils
import androidx.lifecycle.coroutineScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@AndroidEntryPoint
class NewsletterSubscriptionChinaFragment : NewsletterSubscriptionFragment() {
    val args: NewsletterSubscriptionChinaFragmentArgs by navArgs()
    override fun navigateForward() {
        if (viewModel.viewState.value?.data?.subscribed == true) {
            if (userSettingsDataSource.getUserSettings().email.isNullOrEmpty()) {
                findNavController().navigate(
                    NewsletterSubscriptionChinaFragmentDirections.actionBindEmailWhenSubscribe()
                )
            } else {
                lifecycle.coroutineScope.launch {
                    delay(500)
                    navigate()
                }
            }
        } else {
            navigate()
        }
    }

    override fun navigate() {
        NavUtils.navigateUpFromSameTask(requireActivity())
    }
}
