package com.stt.android.home.dashboard;

import com.stt.android.newfeed.FeedTopBannerNavigator;
import com.stt.android.newfeed.FeedTopBannerNavigatorImpl;
import com.stt.android.watch.WorkoutPlannerNavigator;
import com.stt.android.workout.planner.WorkoutPlannerNavigatorImpl;
import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.components.SingletonComponent;

@Module
@InstallIn(SingletonComponent.class)
public abstract class DashboardModule {
    @Provides
    static WorkoutPlannerNavigator provideWorkoutPlannerNavigator() {
        return new WorkoutPlannerNavigatorImpl();
    }

    @Binds
    public abstract FeedTopBannerNavigator bindFeedTopBannerNavigator(
        FeedTopBannerNavigatorImpl feedTopBannerNavigatorImpl
    );
}
