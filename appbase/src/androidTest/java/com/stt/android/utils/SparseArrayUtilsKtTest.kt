package com.stt.android.utils

import androidx.test.ext.junit.runners.AndroidJUnit4
import com.google.common.truth.Truth.assertThat
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class SparseArrayUtilsKtTest {

    @Test
    fun sparseArrayOf() {
        val sa = sparseArrayOf(1 to "foo", 4 to "bar")
        assertThat(sa[1]).isEqualTo("foo")
        assertThat(sa[4]).isEqualTo("bar")
    }

    @Test
    fun sparseArrayForEach() {
        class Mutable(var value: Int)
        val sa = sparseArrayOf(1 to Mutable(1), 4 to Mutable(2))
        sa.forEach { _, mutable -> mutable.value++ }
        assertThat(sa[1].value).isEqualTo(2)
        assertThat(sa[4].value).isEqualTo(3)
    }

    @Test
    fun sparseArrayMap() {
        val sa = sparseArrayOf(1 to 1, 4 to 2)
            .map { _, value -> value.toString() }
        assertThat(sa[1]).isEqualTo("1")
        assertThat(sa[4]).isEqualTo("2")
    }

    @Test
    fun sparseArrayPlus() {
        val sa = sparseArrayOf(1 to 1, 4 to 2) + sparseArrayOf(2 to 3)
        assertThat(sa[1]).isEqualTo(1)
        assertThat(sa[2]).isEqualTo(3)
        assertThat(sa[4]).isEqualTo(2)
    }

    @Test
    fun sparseArrayFirstOrNull() {
        assertThat(sparseArrayOf(1 to 1, 2 to 3).firstOrNull()).isEqualTo(1)
        assertThat(sparseArrayOf<Int>().firstOrNull()).isNull()
    }
}
