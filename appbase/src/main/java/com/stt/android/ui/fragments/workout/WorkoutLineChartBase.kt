package com.stt.android.ui.fragments.workout

import android.content.Context
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Shader
import android.graphics.Typeface
import android.graphics.drawable.ShapeDrawable
import android.graphics.drawable.shapes.RectShape
import android.util.AttributeSet
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.amersports.formatter.SourceUnit
import com.amersports.formatter.Success
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.Legend
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.DataSet.Rounding
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.IFillFormatter
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import com.github.mikephil.charting.utils.EntryXComparator
import com.stt.android.FontRefs
import com.stt.android.R
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.ErrorEvent
import com.stt.android.domain.sml.ErrorMarkType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlExtensionStreamPoint
import com.stt.android.domain.sml.SmlTimedExtensionStreamPoint
import com.stt.android.domain.sml.StateEvent
import com.stt.android.domain.sml.StateMarkType
import com.stt.android.domain.sml.TimedValueDataPoint
import com.stt.android.domain.sml.WarningEvent
import com.stt.android.domain.sml.WarningMarkType
import com.stt.android.domain.sml.dataPointsWithoutPauses
import com.stt.android.domain.sml.doStreamDataHaveDistance
import com.stt.android.domain.sml.filterStreamPoint
import com.stt.android.domain.sml.reader.SmlFactory
import com.stt.android.domain.sml.toTimedStreamPoints
import com.stt.android.domain.sml.withModifiedElapsed
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.infomodel.SummaryItem
import com.stt.android.logbook.NgDiveHeader
import com.stt.android.logbook.SuuntoLogbookWindow
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.components.charts.EnlargedGraphMarkerView
import com.stt.android.ui.extensions.smallIcon
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.ui.utils.averageHrEvents
import io.reactivex.Completable
import io.reactivex.Observable
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import sanitisePaceGeoPoints
import sanitisePaceStreamPoints
import sanitiseStokeRateStreamPoints
import timber.log.Timber
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale
import java.util.concurrent.TimeUnit
import kotlin.math.ceil
import kotlin.math.min
import kotlin.math.roundToInt

/**
 * Base class for charts to be shown in workout detail view and in the overlay of a photo to be shared
 *
 * TODO: Remove this class and start using GenerateAnalysisGraphDataUseCase and WorkoutLineChart.
 */
abstract class WorkoutLineChartBase @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : LineChart(context, attrs, defStyle) {

    private var infoModelFormatter: InfoModelFormatter? = null

    private val durationExtractor: (TimedValueDataPoint) -> Float = { point ->
        TimeUnit.MILLISECONDS.toSeconds(
            point.time
        ).toFloat()
    }

    private val distanceExtractor: (TimedValueDataPoint) -> Float =
        { point -> point.cumulativeDistance }

    protected val xAxisDurationFormatter = object : ValueFormatter() {
        override fun getFormattedValue(value: Float): String = when {
            value == 0.0f -> "0"
            value.isSafeValue -> infoModelFormatter?.formatValue(
                SummaryItem.DURATION,
                value
            )?.value.orEmpty()

            else -> ""
        }
    }

    private val xAxisDistanceFormatter = object : ValueFormatter() {
        override fun getFormattedValue(value: Float): String = if (value.isSafeValue) {
            try {
                infoModelFormatter?.let { formatter ->
                    val workoutValue =
                        formatter.formatValue(SummaryItem.DISTANCE, value.toDouble())
                    val unitLabel = workoutValue.getUnitLabel(context)
                    "${workoutValue.value} $unitLabel".trim()
                }
            } catch (t: Throwable) {
                Timber.w(t, "Distance formatting failed")
                null
            } ?: ""
        } else {
            ""
        }
    }

    private val paceFormatter = object : ValueFormatter() {
        override fun getFormattedValue(value: Float): String = if (value.isSafeValue) {
            // Pace values are min/km or min/mi for running and min/100m or min/100yd for swimming
            try {
                infoModelFormatter?.formatDurationAsPace(value * 60.0)
            } catch (t: Throwable) {
                Timber.w(t, "Pace formatting failed")
                null
            } ?: infoModelFormatter?.formatValue(
                SummaryItem.DURATION,
                value * 60.0
            )?.value.orEmpty()
        } else {
            ""
        }
    }

    private val speedFormatter = object : ValueFormatter() {
        override fun getFormattedValue(value: Float): String = if (value.isSafeValue) {
            TextFormatter.formatSpeed(value.toDouble())
        } else {
            ""
        }
    }

    private val verticalOscillationFormatter = object : ValueFormatter() {
        override fun getFormattedValue(value: Float): String = if (value.isSafeValue) {
            try {
                infoModelFormatter?.run {
                    formatValue(SummaryItem.AVGVERTICALOSCILLATION, value).value ?: ""
                }
            } catch (t: Throwable) {
                Timber.w(t, "vertical oscillation formatting failed")
                null
            } ?: value.toString()
        } else {
            ""
        }
    }

    // For whatever reason this is used for many other things, not just distance.
    private val distanceWithoutLeadingZeroFormatter = object : ValueFormatter() {
        override fun getFormattedValue(value: Float): String = if (value.isSafeValue) {
            TextFormatter.formatDistanceWithoutZeros(value.toDouble())
        } else {
            ""
        }
    }

    private val altitudeFormatter = object : ValueFormatter() {
        override fun getFormattedValue(value: Float): String = if (value.isSafeValue) {
            try {
                infoModelFormatter?.run {
                    val meters = unit.fromAltitudeUnit(value.toDouble())
                    formatValue(SummaryItem.LOWALTITUDE, meters).value ?: ""
                }
            } catch (t: Throwable) {
                Timber.w(t, "Altitude formatting failed")
                null
            } ?: value.roundToInt().toString()
        } else {
            ""
        }
    }

    // Pace graph is inverted, but normally filled.
    private val paceFillFormatter = IFillFormatter { _, dataProvider ->
        dataProvider.xChartMax
    }

    val disposables = CompositeDisposable()

    @ColorInt
    protected var lineColor: Int = 0

    @ColorInt
    protected var fillColorStart: Int = 0

    @ColorInt
    protected var fillColorEnd: Int = 0

    @ColorInt
    protected var labelColor: Int = 0
    protected var chartTypeface: Typeface = Typeface.DEFAULT
    var lineWidth = 1.0f

    init {
        description.text = ""
        setNoDataText("")
        isHighlightPerDragEnabled = false
        isHighlightPerTapEnabled = false
        setDrawGridBackground(false)
        setDrawBorders(false)
        setScaleEnabled(false)
        isDoubleTapToZoomEnabled = false
        setTouchEnabled(false)
        minOffset = 0.0f
        extraTopOffset = 8.0f
        extraBottomOffset = 2.0f
        setHardwareAccelerationEnabled(true)

        axisLeft.isEnabled = false

        with(legend) {
            textColor = labelColor
            typeface = chartTypeface
            isWordWrapEnabled = true
        }
    }

    private fun setupHighlighter(graphType: GraphType) {
        if (isScaleXEnabled) {
            isHighlightPerTapEnabled = true
            isHighlightPerDragEnabled = true
            infoModelFormatter?.run {
                setDrawMarkers(true)
                marker = EnlargedGraphMarkerView(
                    context,
                    R.layout.enlarged_graph_markerview,
                    graphType,
                    this
                )
            }
        }
    }

    fun setInfoModelFormatter(infoModelFormatter: InfoModelFormatter) {
        this.infoModelFormatter = infoModelFormatter
    }

    open fun drawDiveGraph(
        graphType: GraphType,
        diveExtension: DiveExtension?,
        sml: Sml,
        measurementUnit: MeasurementUnit,
        drawEvents: Boolean = false
    ): Completable {
        setupHighlighter(graphType)

        legend.apply {
            verticalAlignment = Legend.LegendVerticalAlignment.BOTTOM
            horizontalAlignment = Legend.LegendHorizontalAlignment.LEFT
            orientation = Legend.LegendOrientation.HORIZONTAL
        }

        return when (graphType) {
            is GraphType.Summary -> {
                when (graphType.summaryGraph) {
                    SummaryGraph.TEMPERATURE -> drawStreamPointChart(
                        measurementUnit,
                        { sml.streamData.temperature.toTimedStreamPoints() },
                        MeasurementUnit::toTemperatureUnit,
                        yValueFormatter = distanceWithoutLeadingZeroFormatter
                    )

                    SummaryGraph.DEPTH -> {
                        val eventsIfEnabled =
                            if (drawEvents) {
                                Completable.fromCallable { drawDiveEvents(sml) }
                            } else {
                                Completable.complete()
                            }

                        drawStreamPointChart(
                            measurementUnit,
                            { sml.streamData.depth.toTimedStreamPoints() },
                            MeasurementUnit::toAltitudeUnit,
                            isFilled = true,
                            isInverted = true,
                            yMinValue = 0f,
                            yValueFormatter = altitudeFormatter
                        ).andThen(eventsIfEnabled)
                    }

                    SummaryGraph.GASCONSUMPTION -> {
                        if (sml.streamData.ventilation.isNotEmpty()) {
                            // Legacy dive devices provide a single, combined ventilation data entry within Dive samples.
                            drawStreamPointChart(
                                measurementUnit,
                                { sml.streamData.ventilation.toTimedStreamPoints() },
                                MeasurementUnit::toGasConsumptionUnit,
                                showAverage = true,
                                avgValueExtractor = { diveExtension?.gasConsumption },
                                yValueFormatter = distanceWithoutLeadingZeroFormatter
                            )
                        } else {
                            // Ng devices provide separate ventilation data for each gas.
                            drawDiveCylinderDataGraph(
                                streamData = sml.streamData.gasConsumptions,
                                valueUnitConversion = MeasurementUnit::toGasConsumptionUnit,
                                measurementUnit = measurementUnit,
                                diveExtension = diveExtension,
                                diveHeader = sml.summary.diveHeader
                            )
                        }
                    }

                    SummaryGraph.TANKPRESSURE -> {
                        drawDiveCylinderDataGraph(
                            streamData = sml.streamData.tankPressures,
                            streamData2 = sml.streamData.tankPressures2,
                            valueUnitConversion = MeasurementUnit::toPressureUnit,
                            measurementUnit = measurementUnit,
                            diveExtension = diveExtension,
                            diveHeader = sml.summary.diveHeader
                        )
                    }

                    else -> Completable.error(IllegalArgumentException("Unsupported graphType: $graphType"))
                }
            }

            is GraphType.SuuntoPlus -> Completable.error(IllegalArgumentException("Unsupported graphType: $graphType"))
        }
    }

    private fun drawDiveCylinderDataGraph(
        streamData: Map<Int, List<SmlExtensionStreamPoint>>,
        streamData2: Map<Int, List<SmlExtensionStreamPoint>> = emptyMap(),
        valueUnitConversion: MeasurementUnit.(Double) -> Double,
        measurementUnit: MeasurementUnit,
        diveExtension: DiveExtension?,
        diveHeader: NgDiveHeader?
    ): Completable {
        // Build combined series list across both maps, grouped by gas index
        data class Series(val gasIndex: Int, val name: String, val values: List<SmlExtensionStreamPoint>)

        val gasKeys = streamData.keys.union(streamData2.keys)
        if (gasKeys.isEmpty()) return Completable.complete()

        // Sort gases by earliest timestamp across both series
        val gasesSorted = gasKeys.toList().sortedBy { gasIndex ->
            val t1 = streamData[gasIndex]?.firstOrNull()?.timestamp ?: Long.MAX_VALUE
            val t2 = streamData2[gasIndex]?.firstOrNull()?.timestamp ?: Long.MAX_VALUE
            minOf(t1, t2)
        }

        val seriesList = mutableListOf<Series>()
        for (gasIndex in gasesSorted) {
            val primary = streamData[gasIndex].orEmpty()
            val secondary = streamData2[gasIndex].orEmpty()
            val gasName = diveHeader?.getGasNameByIndex(gasIndex) ?: diveExtension?.getGasNameByIndex(gasIndex) ?: ""
            if (primary.isNotEmpty()) seriesList.add(Series(gasIndex, gasName, primary))
            if (secondary.isNotEmpty()) seriesList.add(Series(gasIndex, "", secondary)) // empty label to keep single legend per gas
        }

        // Decide single vs multi series
        val totalSeries = seriesList.size
        if (totalSeries == 0) return Completable.complete()
        if (totalSeries == 1) {
            val only = seriesList.first()
            return drawStreamPointChart(
                measurementUnit,
                { only.values.toTimedStreamPoints() },
                valueUnitConversion,
                yValueFormatter = distanceWithoutLeadingZeroFormatter
            )
        }

        // Determine a common start timestamp for relative time conversion
        val startTimestamp = seriesList.flatMap { it.values }.minByOrNull { it.timestamp }?.timestamp

        // Prepare extractor lambdas
        val nameExtractor: (Int) -> String = { i -> seriesList[i].name }
        val valuesExtractor: (Int) -> List<SmlTimedExtensionStreamPoint> = { i ->
            seriesList[i].values.toTimedStreamPoints(startTimestamp)
        }
        val uniqueGasesOrder = seriesList.map { it.gasIndex }.distinct()
        val colorExtractor: (Int) -> Int = { i ->
            val gasIdx = seriesList[i].gasIndex
            val colorIdx = uniqueGasesOrder.indexOf(gasIdx).coerceAtLeast(0)
            resources.getIntArray(R.array.chart_color_sequence)[colorIdx.rem(resources.getIntArray(R.array.chart_color_sequence).size)]
        }

        return drawMultipleSeriesDivingChart(
            measurementUnit,
            totalSeries,
            nameExtractor,
            valuesExtractor,
            valueUnitConversion,
            valueFormatter = distanceWithoutLeadingZeroFormatter,
            seriesColorExtractor = colorExtractor
        )
    }

    private fun drawDiveEvents(sml: Sml) {
        // drawStreamPointChart needs to be called and finished first to have the data entries
        if (data.dataSetCount == 0) return

        val dataSet = data.getDataSetByIndex(0)
        val entries = sml.streamData.getDiveEvents(sml.summary.isDiveAfterTissueReset)
            .mapIndexedNotNull { index, event ->
                when {
                    event.data.elapsed != null -> event
                    // Seal may log dive active-event before start-event
                    index == 0 && event is StateEvent && event.type == StateMarkType.DIVE_ACTIVE ->
                        event.copy(data = event.data.withModifiedElapsed(elapsed = 0L))

                    else -> null // skip
                }
            }
            .map { event ->
                val xValue = TimeUnit.MILLISECONDS.toSeconds(event.data.elapsed!!).toFloat()
                val yValue = dataSet.getEntryForXValue(xValue, 0f).y
                Entry(xValue, yValue, ContextCompat.getDrawable(context, event.smallIcon), event)
            }

        val eventDataSet = LineDataSet(entries, "").apply {
            enableDashedLine(0f, 1f, 0f) // disables the line connecting the entries
            axisDependency = YAxis.AxisDependency.RIGHT
            // draw only the icons
            setDrawValues(false)
            setDrawCircles(false)
            setDrawIcons(true)
            highLightColor = Color.BLACK
            highlightLineWidth = 1.0f
            setDrawHighlightIndicators(true)
            setDrawHorizontalHighlightIndicator(false)
        }
        data.addDataSet(eventDataSet)

        drawAlgorithmLockedIfNeeded(sml)

        // The renderer will not draw values or icons if the number of entries exceed this.
        setMaxVisibleValueCount(Int.MAX_VALUE)
    }

    private fun drawAlgorithmLockedIfNeeded(sml: Sml) {
        sml.streamData.events
            .firstOrNull {
                (it is ErrorEvent && it.type == ErrorMarkType.CEILING_BROKEN) ||
                    (it is WarningEvent && it.type == WarningMarkType.MINI_LOCK)
            }?.data?.elapsed?.run {
                drawAlgorithmLocked(TimeUnit.MILLISECONDS.toSeconds(this).toFloat())
            }
    }

    private fun drawAlgorithmLocked(xValue: Float) {
        if (data.dataSetCount == 0) return

        val dataSet = data.getDataSetByIndex(0)
        val entryIndex = dataSet.getEntryIndex(xValue, 0f, Rounding.CLOSEST)
        if (entryIndex < 0) return

        val lockedEntries = mutableListOf<Entry>()
        for (i in entryIndex until dataSet.entryCount) {
            lockedEntries.add(dataSet.getEntryForIndex(i).copy())
        }
        LineDataSet(lockedEntries, "").apply {
            setDrawValues(false)
            setDrawCircles(true)
            setDrawIcons(false)
            circleColors = listOf(Color.RED)
            circleHoleColor = Color.RED
            circleRadius = 1f
            axisDependency = YAxis.AxisDependency.RIGHT
            color = Color.RED
            lineWidth = 2f
            data.addDataSet(this)
            setDrawFilled(true)
            fillDrawable = ShapeDrawable(RectShape()).apply {
                shaderFactory = object : ShapeDrawable.ShaderFactory() {
                    override fun resize(width: Int, height: Int): Shader {
                        return LinearGradient(
                            0f,
                            0f,
                            0f,
                            height.toFloat(),
                            ContextCompat.getColor(
                                context,
                                R.color.algorithm_locked_fill_color_end
                            ),
                            ContextCompat.getColor(
                                context,
                                R.color.algorithm_locked_fill_color_start
                            ),
                            Shader.TileMode.REPEAT
                        )
                    }
                }
            }
        }
    }

    open fun drawGraph(
        isCadenceProcessingRequired: Boolean,
        graphType: GraphType,
        geoPoints: List<WorkoutGeoPoint>,
        workoutHeader: WorkoutHeader,
        sml: Sml?,
        measurementUnit: MeasurementUnit
    ): Completable = drawGraph(
        isCadenceProcessingRequired = isCadenceProcessingRequired,
        graphType = graphType,
        geoPoints = geoPoints,
        workoutHeader = workoutHeader,
        sml = sml,
        measurementUnit = measurementUnit,
        activityWindow = sml?.getActivityWindow(multisportPartActivity = null)
    )

    fun drawGraph(
        isCadenceProcessingRequired: Boolean,
        graphType: GraphType,
        geoPoints: List<WorkoutGeoPoint>,
        workoutHeader: WorkoutHeader,
        sml: Sml?,
        measurementUnit: MeasurementUnit,
        activityWindow: SuuntoLogbookWindow?,
        multisportPartActivity: MultisportPartActivity? = null
    ): Completable {
        val streamData = if (sml != null && sml != SmlFactory.EMPTY) sml.streamData else null
        val activityType = multisportPartActivity?.activityType?.run { ActivityType.valueOf(this) }
            ?: workoutHeader.activityType
        setupHighlighter(graphType)
        return when (graphType) {
            is GraphType.Summary -> when (graphType.summaryGraph) {
                SummaryGraph.PACE -> {
                    when {
                        streamData != null -> {
                            drawStreamPointChart(
                                measurementUnit,
                                {
                                    streamData.dataPointsWithoutPauses(
                                        streamData.speed.filterStreamPoint(multisportPartActivity),
                                        startTimestamp = multisportPartActivity?.startTime
                                    ).sanitisePaceStreamPoints(activityType)
                                },
                                when (workoutHeader.activityType.isSwimming) {
                                    true -> MeasurementUnit::toSwimPaceUnit
                                    false -> MeasurementUnit::toPaceUnit
                                },
                                isInverted = true,
                                isFillInverted = true,
                                yValueFormatter = paceFormatter,
                                avgValueExtractor = {
                                    activityWindow?.speed?.firstOrNull()?.avg
                                },
                                showAverage = true
                            )
                        }

                        geoPoints.isNotEmpty() -> {
                            val geoPointsForPaceFiltered =
                                geoPoints.sanitisePaceGeoPoints(activityType)
                            if (geoPointsForPaceFiltered.isNotEmpty()) {
                                drawSpeedPaceChart(
                                    geoPointsForPaceFiltered,
                                    graphType,
                                    workoutHeader,
                                    measurementUnit
                                )
                            } else {
                                todoCompletable()
                            }
                        }

                        else -> {
                            todoCompletable()
                        }
                    }
                }

                SummaryGraph.SPEEDKNOTS,
                SummaryGraph.SPEED -> {
                    when {
                        streamData != null -> {
                            drawStreamPointChart(
                                measurementUnit,
                                {
                                    streamData.dataPointsWithoutPauses(
                                        streamData.speed.filterStreamPoint(multisportPartActivity),
                                        startTimestamp = multisportPartActivity?.startTime
                                    )
                                },
                                when (graphType) {
                                    GraphType.Summary(SummaryGraph.SPEEDKNOTS) -> MeasurementUnit::toKnots
                                    else -> MeasurementUnit::toSpeedUnit
                                },
                                yValueFormatter = distanceWithoutLeadingZeroFormatter,
                                avgValueExtractor = {
                                    activityWindow?.speed?.firstOrNull()?.avg
                                },
                                showAverage = true
                            )
                        }

                        geoPoints.isNotEmpty() -> {
                            drawSpeedPaceChart(
                                geoPoints,
                                graphType,
                                workoutHeader,
                                measurementUnit
                            )
                        }

                        else -> {
                            todoCompletable()
                        }
                    }
                }

                SummaryGraph.VERTICALOSCILLATION -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            streamData.dataPointsWithoutPauses(
                                streamData.verticalOscillations.filterStreamPoint(
                                    multisportPartActivity
                                ),
                                startTimestamp = multisportPartActivity?.startTime
                            )
                        },
                        MeasurementUnit::fromMeterToCentimeter,
                        yValueFormatter = verticalOscillationFormatter,
                        avgValueExtractor = { activityWindow?.verticalOscillation?.firstOrNull()?.avg },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.GROUNDCONTACTTIME -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            streamData.dataPointsWithoutPauses(
                                streamData.groundContactTimes.filterStreamPoint(
                                    multisportPartActivity
                                ),
                                startTimestamp = multisportPartActivity?.startTime
                            )
                        },
                        MeasurementUnit::fromSecondsToMilliseconds,
                        yValueFormatter = distanceWithoutLeadingZeroFormatter,
                        avgValueExtractor = { activityWindow?.groundContactTime?.firstOrNull()?.avg },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.ALTITUDE -> {
                    when {
                        streamData != null -> {
                            val altitudeData =
                                streamData.altitude.filterStreamPoint(multisportPartActivity)
                            if (streamData.doStreamDataHaveDistance(
                                    altitudeData,
                                    multisportPartActivity
                                )
                            ) {
                                // If we have distance use distance formatters
                                val xValueDistanceCorrection = if (multisportPartActivity != null) {
                                    altitudeData.firstOrNull { it.cumulativeDistance != null }?.cumulativeDistance
                                } else {
                                    null
                                }
                                drawStreamPointChart(
                                    measurementUnit,
                                    {
                                        streamData.dataPointsWithoutPauses(
                                            altitudeData,
                                            startDistance = xValueDistanceCorrection
                                        )
                                    },
                                    MeasurementUnit::toAltitudeUnit,
                                    yValueFormatter = altitudeFormatter,
                                    yMinRange = ALTITUDE_Y_MIN_RANGE,
                                    xValueFormatter = xAxisDistanceFormatter,
                                    xValueExtractor = distanceExtractor
                                )
                            } else {
                                // Else fallback to duration formatters
                                drawStreamPointChart(
                                    measurementUnit,
                                    {
                                        streamData.dataPointsWithoutPauses(
                                            altitudeData,
                                            startTimestamp = multisportPartActivity?.startTime
                                        )
                                    },
                                    valueUnitConversion = MeasurementUnit::toAltitudeUnit,
                                    yMinRange = ALTITUDE_Y_MIN_RANGE,
                                    xValueFormatter = xAxisDurationFormatter,
                                    xValueExtractor = durationExtractor
                                )
                            }
                        }

                        geoPoints.isNotEmpty() -> {
                            drawAltitudeChart(geoPoints, measurementUnit)
                        }

                        else -> {
                            todoCompletable()
                        }
                    }
                }

                SummaryGraph.AVGSKIPSRATE,
                SummaryGraph.CADENCE -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            streamData.dataPointsWithoutPauses(
                                streamData.cadence.filterStreamPoint(multisportPartActivity),
                                startTimestamp = multisportPartActivity?.startTime
                            ).let { points ->
                                if (isCadenceProcessingRequired) {
                                    points.map { it.copy(streamPoint = it.streamPoint.copy(value = it.value / 2)) }
                                } else {
                                    points
                                }
                            }
                        },
                        MeasurementUnit::fromHzToRpm,
                        yValueFormatter = distanceWithoutLeadingZeroFormatter,
                        avgValueExtractor = {
                            val cadence = activityWindow?.cadence?.firstOrNull()?.avg
                            if (isCadenceProcessingRequired) cadence?.div(2) else cadence
                        },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.AVGSKIPSPERROUND -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            streamData.dataPointsWithoutPauses(
                                streamData.skipsPerRound.filterStreamPoint(multisportPartActivity),
                                startTimestamp = multisportPartActivity?.startTime
                            )
                        },
                        { value -> value },
                        yValueFormatter = distanceWithoutLeadingZeroFormatter,
                        avgValueExtractor = { activityWindow?.avgSkipsPerRound?.toFloat() },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.TEMPERATURE -> if (streamData != null) {
                    val temperatureDataPointsWithoutPauses = streamData.dataPointsWithoutPauses(
                        streamData.temperature.filterStreamPoint(multisportPartActivity),
                        startTimestamp = multisportPartActivity?.startTime
                    )
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            temperatureDataPointsWithoutPauses
                        },
                        MeasurementUnit::toTemperatureUnit,
                        yValueFormatter = distanceWithoutLeadingZeroFormatter,
                        avgValueExtractor = {
                            temperatureDataPointsWithoutPauses.map { it.value }.average().toFloat()
                        },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.POWER -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            streamData.dataPointsWithoutPauses(
                                streamData.power.filterStreamPoint(multisportPartActivity),
                                startTimestamp = multisportPartActivity?.startTime
                            )
                        },
                        { power -> power },
                        yValueFormatter = distanceWithoutLeadingZeroFormatter,
                        avgValueExtractor = { activityWindow?.power?.firstOrNull()?.avg },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.SEALEVELPRESSURE -> todoCompletable()
                SummaryGraph.SWIMSTROKERATE -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            streamData.dataPointsWithoutPauses(
                                streamData.strokeRate.filterStreamPoint(multisportPartActivity),
                                startTimestamp = multisportPartActivity?.startTime
                            ).sanitiseStokeRateStreamPoints(activityType)
                        },
                        { strokeRate -> strokeRate },
                        yValueFormatter = distanceWithoutLeadingZeroFormatter,
                        avgValueExtractor = {
                            // Hz -> strokes/min
                            activityWindow?.strokeRate?.firstOrNull()?.avg?.times(60)
                        },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.SWOLF -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            streamData.dataPointsWithoutPauses(
                                streamData.swolf.filterStreamPoint(multisportPartActivity),
                                startTimestamp = multisportPartActivity?.startTime
                            )
                        },
                        { swolf -> swolf },
                        yValueFormatter = distanceWithoutLeadingZeroFormatter,
                        avgValueExtractor = { activityWindow?.swolf?.firstOrNull()?.avg },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.VERTICALSPEED -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit = measurementUnit,
                        valueExtractor = {
                            streamData.dataPointsWithoutPauses(
                                streamData.verticalSpeed.filterStreamPoint(multisportPartActivity),
                                startTimestamp = multisportPartActivity?.startTime
                            )
                        },
                        valueUnitConversion = MeasurementUnit::toVerticalSpeedUnit,
                        yValueFormatter = speedFormatter,
                        avgValueExtractor = {
                            activityWindow?.verticalSpeed?.firstOrNull()?.avg
                        },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.BREASTSTROKEGLIDETIME -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            streamData.dataPointsWithoutPauses(
                                streamData.breaststrokeGlideTime.filterStreamPoint(
                                    multisportPartActivity
                                ),
                                startTimestamp = multisportPartActivity?.startTime
                            )
                        },
                        { value -> value },
                        yValueFormatter = distanceWithoutLeadingZeroFormatter,
                        avgValueExtractor = { activityWindow?.breaststrokeGlideTime?.toFloat() },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.AVGBREASTSTROKEBREATHANGLE -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            streamData.dataPointsWithoutPauses(
                                streamData.breaststrokeAvgBreathAngle.filterStreamPoint(
                                    multisportPartActivity
                                ),
                                startTimestamp = multisportPartActivity?.startTime
                            )
                        },
                        { value -> value },
                        yValueFormatter = distanceWithoutLeadingZeroFormatter,
                        avgValueExtractor = { activityWindow?.breaststrokeAvgBreathAngle?.toFloat() },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.AVGFREESTYLEBREATHANGLE -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            streamData.dataPointsWithoutPauses(
                                streamData.freestyleAvgBreathAngle.filterStreamPoint(
                                    multisportPartActivity
                                ),
                                startTimestamp = multisportPartActivity?.startTime
                            )
                        },
                        { value -> value },
                        yValueFormatter = distanceWithoutLeadingZeroFormatter,
                        avgValueExtractor = { activityWindow?.freestyleAvgBreathAngle?.toFloat() },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.BREASTSTROKEHEADANGLE -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            streamData.dataPointsWithoutPauses(
                                streamData.breaststrokeHeadAngle.filterStreamPoint(
                                    multisportPartActivity
                                ),
                                startTimestamp = multisportPartActivity?.startTime
                            )
                        },
                        { value -> value },
                        yValueFormatter = distanceWithoutLeadingZeroFormatter,
                        avgValueExtractor = { activityWindow?.breaststrokeHeadAngle?.toFloat() },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                SummaryGraph.FREESTYLEPITCHANGLE -> if (streamData != null) {
                    drawStreamPointChart(
                        measurementUnit,
                        {
                            streamData.dataPointsWithoutPauses(
                                streamData.freestyleHeadAngle.filterStreamPoint(
                                    multisportPartActivity
                                ),
                                startTimestamp = multisportPartActivity?.startTime
                            )
                        },
                        { value -> value },
                        yValueFormatter = distanceWithoutLeadingZeroFormatter,
                        avgValueExtractor = { activityWindow?.freestyleHeadAngle?.toFloat() },
                        showAverage = true
                    )
                } else {
                    todoCompletable()
                }

                else -> throw IllegalArgumentException("Unsupported graphType: $graphType")
            }

            is GraphType.SuuntoPlus -> {
                if (streamData != null) {
                    val points = streamData.suuntoPlusSamplePoints[graphType.suuntoPlusChannel]
                    val formatStyle = graphType.suuntoPlusChannel.formatStyleForSIM
                    if (points != null && points.isNotEmpty()) {
                        drawStreamPointChart(
                            measurementUnit = measurementUnit,
                            valueExtractor = {
                                streamData.dataPointsWithoutPauses(
                                    points.filterStreamPoint(multisportPartActivity),
                                    startTimestamp = multisportPartActivity?.startTime
                                )
                            },
                            valueUnitConversion = { value ->
                                if (formatStyle != null) {
                                    infoModelFormatter?.convertFromSItoRangeUnit(formatStyle, value)
                                } else {
                                    value
                                }
                            },
                            isInverted = graphType.suuntoPlusChannel.inverted == true,
                            yValueFormatter = object : ValueFormatter() {
                                override fun getFormattedValue(value: Float): String =
                                    if (value.isSafeValue) {
                                        if (formatStyle != null) {
                                            (
                                                infoModelFormatter?.formatValue(
                                                    formatStyle,
                                                    value,
                                                    withStyle = true,
                                                    sourceUnit = SourceUnit.RANGE_UNIT,
                                                    forceRangeUnitOutput = true
                                                ) as? Success
                                                )?.value ?: ""
                                        } else {
                                            DecimalFormat(
                                                "#.##",
                                                DecimalFormatSymbols.getInstance(Locale.US)
                                            ).format(value)
                                        }
                                    } else {
                                        ""
                                    }
                            },
                        )
                    } else {
                        todoCompletable()
                    }
                } else {
                    todoCompletable()
                }
            }
        }
    }

    open fun drawHeartRateGraph(
        hrEvents: List<WorkoutHrEvent>,
        maxHeartRateValues: Int = MAX_HR_VALUES_DEFAULT,
        multisportPartActivity: MultisportPartActivity? = null
    ): Completable {
        setupHighlighter(GraphType.Summary(SummaryGraph.HEARTRATE))
        val hrEventsObs = if (hrEvents.size < maxHeartRateValues) {
            Single.just(hrEvents)
        } else {
            Single.just(hrEvents)
                .map { averageHrEvents(it, maxHeartRateValues.toLong()) }
        }
        return hrEventsObs
            .map { events ->
                val entries = events.map { event ->
                    Entry(
                        TimeUnit.MILLISECONDS
                            .toSeconds(event.millisecondsInWorkout)
                            .toFloat(),
                        event.heartRate.toFloat()
                    )
                }
                val hrDataSet = createLineDataSet(entries, lineColor)
                LineData(hrDataSet)
            }
            .subscribeOn(Schedulers.computation())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnError { throwable -> handleDataError(throwable, "hr chart") }
            .doOnSuccess { lineData: LineData ->
                prepareXYAxis(
                    lineData,
                    null,
                    lineData.getDataSetByIndex(0).yMax,
                    null,
                    false,
                    distanceWithoutLeadingZeroFormatter
                )
                data = lineData
                legend.isEnabled = false
                invalidate()
            }
            .ignoreElement()
    }

    /**
     * Attention: the only assumption that this method does is that the seriesValuesExtractor
     * will extract time series that are sequential in time (with their respective start timestamp)
     */
    private fun drawMultipleSeriesDivingChart(
        measurementUnit: MeasurementUnit,
        nSeries: Int,
        seriesNameExtractor: (Int) -> String,
        seriesValuesExtractor: (Int) -> List<SmlTimedExtensionStreamPoint>,
        valueUnitConversion: MeasurementUnit.(Double) -> Double,
        isFilled: Boolean = false,
        isInverted: Boolean = false,
        yMinValue: Float? = null,
        valueFormatter: ValueFormatter,
        seriesColorExtractor: ((Int) -> Int)? = null
    ): Completable {
        require(nSeries > 1) { "Not a multiple series" }

        val chartColors = resources.getIntArray(R.array.chart_color_sequence)

        return Observable.range(0, nSeries)
            .map { seriesValuesExtractor(it) }
            .filter { it.isNotEmpty() }
            .flatMapIterable { it }
            .map { it.time }
            .distinct()
            .toSortedList()
            .flatMap { timestampsRelative ->
                Observable.range(0, nSeries)
                    // converting from absolute timestamp to relative
                    .map {
                        it to seriesValuesExtractor(it)
                    }
                    // filtering out empty series
                    .filter { it.second.isNotEmpty() }
                    .map { (index, values) ->
                        val name = seriesNameExtractor(index)
                        val color = seriesColorExtractor?.invoke(index) ?: chartColors[index.rem(chartColors.size)]

                        val valuesByTimestamp: Map<Long, SmlTimedExtensionStreamPoint> =
                            values.associateBy { it.time }

                        val entries = timestampsRelative
                            .map { timestamp -> valuesByTimestamp[timestamp] }
                            .mapNotNull { point ->
                                point?.streamPoint?.value?.let {
                                    val convertedValue =
                                        measurementUnit.valueUnitConversion(it.toDouble()).toFloat()
                                    Entry(
                                        TimeUnit.MILLISECONDS.toSeconds(point.time).toFloat(),
                                        convertedValue
                                    )
                                }
                            }

                        createLineDataSet(
                            entries,
                            color,
                            isFilled = isFilled,
                            label = name
                        )
                    }
                    .collectInto(mutableListOf<LineDataSet>()) { coll, dataSet ->
                        coll.add(dataSet)
                    }
            }
            // creating line data
            .map { dataSets ->
                LineData(dataSets as List<ILineDataSet>?)
            }
            .subscribeOn(Schedulers.computation())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnError {
                handleDataError(it, "diving chart")
            }
            .doOnSuccess { lineData ->
                prepareXYAxis(lineData, yMinValue, lineData.yMax, null, isInverted, valueFormatter)
                legend.textColor = lineColor
                ResourcesCompat.getFont(context, FontRefs.CHART_FONT_REF)?.let {
                    legend.typeface = it
                }

                legend.isEnabled = true
                data = lineData
                invalidate()
            }
            .ignoreElement()
    }

    private fun drawStreamPointChart(
        measurementUnit: MeasurementUnit,
        valueExtractor: () -> List<TimedValueDataPoint>,
        valueUnitConversion: MeasurementUnit.(Double) -> Double?,
        isFilled: Boolean = false,
        isInverted: Boolean = false,
        isFillInverted: Boolean = false,
        yMinValue: Float? = null,
        yMinRange: Float? = null,
        showAverage: Boolean = false,
        avgValueExtractor: () -> Float? = { null },
        yValueFormatter: ValueFormatter = speedFormatter,
        xValueFormatter: ValueFormatter = xAxisDurationFormatter,
        xValueExtractor: (TimedValueDataPoint) -> Float = durationExtractor
    ): Completable {
        return Observable.fromCallable(valueExtractor)
            .flatMapIterable { it }
            // ensure time order, not sure if necessary
            .toSortedList { o1, o2 -> (o1.time - o2.time).toInt() }
            .filter { it.isNotEmpty() }
            .map { points ->
                val entries = points
                    .mapNotNull { point ->
                        val convertedValue =
                            measurementUnit.valueUnitConversion(point.value.toDouble())?.toFloat()
                        convertedValue?.let {
                            Entry(xValueExtractor(point), convertedValue)
                        }
                    }
                val lineDataSet = createLineDataSet(
                    entries,
                    lineColor,
                    isFilled = isFilled,
                    invertFillColors = isFilled
                )
                if (isFillInverted) lineDataSet.fillFormatter = paceFillFormatter

                LineData(lineDataSet)
            }
            .subscribeOn(Schedulers.computation())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnError { handleDataError(it, "diving chart") }
            .doOnSuccess { lineData ->
                prepareXYAxis(
                    lineData,
                    yMinValue,
                    lineData.yMax,
                    yMinRange,
                    isInverted,
                    yValueFormatter,
                    xValueFormatter
                )
                if (showAverage) {
                    avgValueExtractor()?.let {
                        val avgConverted =
                            measurementUnit.valueUnitConversion(it.toDouble())?.toFloat()
                        if (avgConverted != null) addAvgLine(avgConverted)
                    }
                }
                data = lineData
                legend.isEnabled = false
                invalidate()
            }
            .ignoreElement()
    }

    fun drawDepth(data: LineData) {
        if (data.dataSets.isEmpty()) return
        prepareXYAxis(
            lineData = data,
            minValue = 0f,
            maxValue = data.dataSets.first().yMax,
            minRange = null,
            inverted = true,
            yValueFormatter = distanceWithoutLeadingZeroFormatter
        )
        this.data = data
        legend.isEnabled = false
        setMaxVisibleValueCount(Int.MAX_VALUE)
        invalidate()
    }

    private fun drawSpeedPaceChart(
        workoutGeoPoints: List<WorkoutGeoPoint>,
        graphType: GraphType,
        workoutHeader: WorkoutHeader,
        measurementUnit: MeasurementUnit
    ): Completable {
        var speedEntryObservable =
            Observable.fromIterable(workoutGeoPoints) // Extract the speed and time data from the workout geo point
                .map { workoutGeoPoint ->
                    SpeedPaceEntry(
                        TimeUnit.MILLISECONDS.toSeconds(workoutGeoPoint.millisecondsInWorkout.toLong()),
                        workoutGeoPoint.speedMetersPerSecond
                    )
                }
        val pointsCount = workoutGeoPoints.size.toLong()
        val hourWorthOfData = TimeUnit.HOURS.toSeconds(1)
        if (pointsCount > hourWorthOfData) {
            // Average the values if there's more than an hour worth of data
            val windowSize = (pointsCount / hourWorthOfData).toFloat().roundToInt()
            Timber.d("Speed analysis moving-window average over %d entries", windowSize)
            speedEntryObservable = speedEntryObservable.buffer(windowSize)
                .map { speedEntries ->
                    var speed = 0.0f
                    var secondsInWorkout: Long = 0
                    for (speedEntry in speedEntries) {
                        speed += speedEntry.speedInMetersPerSecond
                        secondsInWorkout += speedEntry.secondsInWorkout
                    }
                    val count = speedEntries.size
                    SpeedPaceEntry(secondsInWorkout / count, speed / count)
                }
        }
        return speedEntryObservable.toList()
            .map { speedEntries ->
                val workoutGeoPointsCount = speedEntries.size
                val entries = ArrayList<Entry>(workoutGeoPointsCount)

                for (i in 0 until workoutGeoPointsCount) {
                    val speedPaceEntry = speedEntries[i]
                    val speedMetersPerSecond = speedPaceEntry.speedInMetersPerSecond.toDouble()
                    when (graphType) {
                        GraphType.Summary(SummaryGraph.SPEED) -> {
                            entries.add(
                                Entry(
                                    speedPaceEntry.secondsInWorkout.toFloat(),
                                    measurementUnit.toSpeedUnit(speedMetersPerSecond).toFloat()
                                )
                            )
                        }

                        GraphType.Summary(SummaryGraph.SPEEDKNOTS) -> {
                            entries.add(
                                Entry(
                                    speedPaceEntry.secondsInWorkout.toFloat(),
                                    measurementUnit.toKnots(speedMetersPerSecond)
                                        .toFloat()
                                )
                            )
                        }

                        GraphType.Summary(SummaryGraph.PACE) -> {
                            entries.add(
                                Entry(
                                    speedPaceEntry.secondsInWorkout.toFloat(),
                                    when (workoutHeader.activityType.isSwimming) {
                                        true -> measurementUnit.toSwimPaceUnit(
                                            speedMetersPerSecond
                                        ).toFloat()

                                        false -> measurementUnit.toPaceUnit(speedMetersPerSecond)
                                            .toFloat()
                                    }
                                )
                            )
                        }

                        else -> { /* Ignore geo-point */
                        }
                    }
                }
                when (graphType) {
                    GraphType.Summary(SummaryGraph.SPEED) -> {
                        addAvgLine(measurementUnit.toSpeedUnit(workoutHeader.avgSpeed).toFloat())
                    }

                    GraphType.Summary(SummaryGraph.SPEEDKNOTS) -> {
                        addAvgLine(measurementUnit.toKnots(workoutHeader.avgSpeed).toFloat())
                    }

                    GraphType.Summary(SummaryGraph.PACE) -> {
                        addAvgLine(
                            when (workoutHeader.activityType.isSwimming) {
                                true -> measurementUnit.toSwimPaceUnit(workoutHeader.avgSpeed)
                                    .toFloat()

                                false -> measurementUnit.toPaceUnit(workoutHeader.avgSpeed)
                                    .toFloat()
                            }
                        )
                    }

                    else -> { /* No average line */
                    }
                }
                val speedDataSet = createLineDataSet(
                    entries,
                    lineColor
                )
                val lineData = LineData(speedDataSet)
                lineData
            }
            .subscribeOn(Schedulers.computation())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnError { handleDataError(it, "speed chart") }
            .doOnSuccess { lineData ->
                val isInverted = graphType == GraphType.Summary(SummaryGraph.PACE)
                val formatter = if (graphType == GraphType.Summary(SummaryGraph.PACE)) {
                    (lineData.getDataSetByIndex(0) as LineDataSet).fillFormatter = paceFillFormatter
                    paceFormatter
                } else {
                    speedFormatter
                }

                prepareXYAxis(
                    lineData,
                    lineData.getDataSetByIndex(0).yMin,
                    lineData.getDataSetByIndex(0).yMax,
                    null,
                    isInverted,
                    formatter
                )
                data = lineData
                legend.isEnabled = false
                invalidate()
            }
            .ignoreElement()
    }

    private fun drawAltitudeChart(
        workoutGeoPoints: List<WorkoutGeoPoint>,
        measurementUnit: MeasurementUnit
    ): Completable {
        return Single.fromCallable {
            /*
             * sometimes the first points have exactly altitude=0.0 for a bug, we exclude those
             * we exclude also points for which [WorkoutGeoPoint.hasAltitude] is false. Empty values returned instead,
             * to preserve the index information.
             */
            workoutGeoPoints.indexOfFirst { it.hasAltitude() && it.altitude != 0.0 }
        }.flatMap { firstValidIndex ->
            Observable.fromIterable(workoutGeoPoints.withIndex())
                .map { (index, point) ->
                    val totalDistance = point.totalDistance.toLong()
                    if (firstValidIndex in 0..index && point.hasAltitude()) {
                        AltitudeEntry(totalDistance, point.altitude)
                    } else {
                        AltitudeEntry(totalDistance, null)
                    }
                }.toList()
                .map { altitudeEntries ->
                    val workoutGeoPointsCount = altitudeEntries.size
                    val entries = ArrayList<Entry>(workoutGeoPointsCount)

                    for (i in 0 until workoutGeoPointsCount) {
                        val altitudeEntry = altitudeEntries[i]
                        altitudeEntry.altitude?.also {
                            entries.add(
                                Entry(
                                    altitudeEntry.totalDistance.toFloat(),
                                    measurementUnit.toAltitudeUnit(altitudeEntry.altitude).toFloat()
                                )
                            )
                        }
                    }
                    val altitudeDataSet =
                        createLineDataSet(
                            entries,
                            lineColor
                        )
                    LineData(altitudeDataSet)
                }
        }.subscribeOn(Schedulers.computation())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnError { throwable -> handleDataError(throwable, "altitude chart") }
            .doOnSuccess { lineData ->
                prepareXYAxis(
                    lineData,
                    null,
                    lineData.getDataSetByIndex(0).yMax,
                    ALTITUDE_Y_MIN_RANGE,
                    false,
                    // x axis values for altitude are distances
                    xValueFormatter = xAxisDistanceFormatter
                )
                data = lineData
                legend.isEnabled = false
                invalidate()
            }
            .ignoreElement()
    }

    protected abstract fun addAvgLine(avgValue: Float)

    override fun onDetachedFromWindow() {
        disposables.clear()
        super.onDetachedFromWindow()
    }

    protected open fun prepareXYAxis(
        lineData: LineData,
        minValue: Float?,
        maxValue: Float?,
        minRange: Float?,
        inverted: Boolean,
        yValueFormatter: ValueFormatter = speedFormatter,
        xValueFormatter: ValueFormatter = xAxisDurationFormatter
    ) {
        with(xAxis) {
            // Force 5 labels on x-axis when x-axis scaling (zooming) is not enabled and entry count is over 4
            if (lineData.entryCount > 0 && !isScaleXEnabled) {
                setLabelCount(min(lineData.entryCount, 5), true)
            }
            valueFormatter = xValueFormatter
        }
        with(axisRight) {
            setLabelCount(4, true)
            if (minValue != null) {
                axisMinimum = minValue
            } else {
                val rawGranularity = ceil((lineData.yMax - lineData.yMin) / 4)
                // granularities such as 6 or 7 are bumped to 10 by the library which can make
                // charts not have enough axis lines. The problem remains for other magnitude orders
                // but it is less prominent.
                granularity =
                    if (rawGranularity > 5 && rawGranularity < 10) 5f else rawGranularity
                // If the yMin == yMax we need to set the min a tenth lower
                axisMinimum = if (lineData.isMinMaxTheSame()) {
                    lineData.yMin.lowerByATenth()
                } else {
                    lineData.yMin
                }
            }

            isInverted = inverted
            this.valueFormatter = yValueFormatter

            if (minRange == null) {
                maxValue?.run {
                    axisMaximum = when {
                        isInverted -> lineData.yMax
                        lineData.isMinMaxTheSame() -> lineData.yMax.increaseByATenth()
                        else -> lineData.yMax.roundUpToEven()
                    }
                }
            } else {
                axisMaximum = getMaxFromMinRange(minValue, lineData, inverted, maxValue, minRange)
                spaceMin = 0.5f
            }
        }
    }

    protected fun getMaxFromMinRange(
        minValue: Float?,
        lineData: LineData,
        inverted: Boolean,
        maxValue: Float?,
        minRange: Float
    ): Float {
        val min = minValue ?: lineData.yMin
        val max = if (inverted) {
            maxValue ?: lineData.yMax
        } else {
            (10 * (((maxValue ?: lineData.yMax) / 10.0f).toInt() + 1)).toFloat()
        }
        return if (max - min < minRange) min + minRange else max
    }

    protected open fun createLineDataSet(
        entries: List<Entry?>,
        @ColorInt color: Int,
        isFilled: Boolean = false,
        label: String = "",
        invertFillColors: Boolean = false
    ): LineDataSet {
        val sortedEntries = entries.sortedWith(EntryXComparator())
        val (colorStart, colorEnd) = if (invertFillColors) {
            fillColorEnd to fillColorStart
        } else {
            fillColorStart to fillColorEnd
        }
        return LineDataSet(sortedEntries, label.takeIf { it.isNotBlank() }).apply {
            this.color = color
            setDrawValues(false)
            setDrawCircles(false)
            setDrawFilled(isFilled)
            if (isFilled) {
                fillDrawable = ShapeDrawable(RectShape()).apply {
                    shaderFactory = object : ShapeDrawable.ShaderFactory() {
                        override fun resize(width: Int, height: Int): Shader {
                            return LinearGradient(
                                0f,
                                0f,
                                0f,
                                height.toFloat(),
                                colorStart,
                                colorEnd,
                                Shader.TileMode.REPEAT
                            )
                        }
                    }
                }
            }
            mode = LineDataSet.Mode.LINEAR
            setDrawHighlightIndicators(true)
            highLightColor = Color.BLACK
            highlightLineWidth = 1.0f
            setDrawHorizontalHighlightIndicator(false)
            axisDependency = YAxis.AxisDependency.RIGHT
            lineWidth = <EMAIL>
            if (label.isBlank()) { // Sidemount case: hide the duplicate legend identified by empty label.
                form = Legend.LegendForm.NONE
            }
        }
    }

    private fun todoCompletable() = Completable.error(NotImplementedError())

    private fun handleDataError(throwable: Throwable, chartType: String) {
        Timber.w(throwable, "Unable to prepare chart %s data", chartType)
    }

    protected class SpeedPaceEntry internal constructor(
        internal val secondsInWorkout: Long,
        internal val speedInMetersPerSecond: Float
    )

    protected class AltitudeEntry internal constructor(
        internal val totalDistance: Long,
        internal val altitude: Double?
    )

    private val Float.isSafeValue: Boolean
        get() = isFinite() && this != Float.MAX_VALUE && this != -Float.MAX_VALUE

    companion object {
        private const val MAX_HR_VALUES_DEFAULT: Int = 5000
        private const val ALTITUDE_Y_MIN_RANGE: Float = 50f
    }
}

private fun Float.roundUpToEven() = (ceil(this.toDouble() / 2) * 2).toFloat()

private fun Float.ceil() = ceil(this.toDouble()).toFloat()

private fun Float.floor() = kotlin.math.floor(this.toDouble()).toFloat()

private fun LineData.isMinMaxTheSame(): Boolean = this.yMin == this.yMax

private fun Float.lowerByATenth() = this - (this / 10).floor()

private fun Float.increaseByATenth() = this + (this / 10).ceil()
