package com.stt.android.ui.components

import android.content.Context
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.TextAppearanceSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.stt.android.R
import com.stt.android.databinding.SummaryDataViewBinding
import com.stt.android.domain.summaries.SummaryData
import com.stt.android.domain.summaries.TripleSummaryData

/**
 * View that shows speed, distance and durationValue values of a particular route
 */
class SummaryDataView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val binding = SummaryDataViewBinding.inflate(LayoutInflater.from(context), this)

    fun setSummaryData(tripleSummaryData: TripleSummaryData) = with(binding) {
        firstData.text = setSummaryDataText(tripleSummaryData.firstData)
        secondData.text = setSummaryDataText(tripleSummaryData.secondData)
        thirdData.text = setSummaryDataText(tripleSummaryData.thirdData)
    }

    private fun setSummaryDataText(summaryData: SummaryData): SpannableStringBuilder {
        val summaryDataUnit = if (summaryData.unit != null) context.getString(summaryData.unit) else ""
        val summaryDataText = summaryData.value + " " + summaryDataUnit + "\n" +
            summaryData.label
        return SpannableStringBuilder(summaryDataText).apply {
            // Set value style
            setSpan(
                TextAppearanceSpan(context, R.style.Datalabel_Mini),
                0,
                summaryData.value.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            // Set label style
            setSpan(
                TextAppearanceSpan(context, R.style.Body_Medium),
                indexOf(summaryDataUnit),
                indexOf(summaryDataUnit) + summaryDataUnit.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            // Set label style
            setSpan(
                TextAppearanceSpan(context, R.style.Body_Small),
                length - summaryData.label.length,
                length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
    }
}
