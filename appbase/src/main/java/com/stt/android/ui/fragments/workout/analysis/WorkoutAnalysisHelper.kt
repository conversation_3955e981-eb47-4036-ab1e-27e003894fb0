package com.stt.android.ui.fragments.workout.analysis

import android.content.Context
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.amersports.formatter.Failure
import com.amersports.formatter.SourceUnit
import com.amersports.formatter.Success
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineDataSet
import com.stt.android.R
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlExtensionStreamPoint
import com.stt.android.domain.sml.SmlStreamData
import com.stt.android.domain.sml.averageOrNull
import com.stt.android.domain.sml.dataPointsWithoutPauses
import com.stt.android.domain.sml.filterStreamPoint
import com.stt.android.domain.sml.reader.SmlFactory
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.DomainWindow
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.infomodel.SummaryItem
import com.stt.android.infomodel.getStIdForMcId
import com.stt.android.mapping.ActivitySummariesMapping
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.mapping.getActivitySummaryForActivityId
import com.stt.android.ui.utils.TextFormatter
import sanitisePaceGeoPoints
import sanitisePaceStreamPoints
import timber.log.Timber
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.roundToInt
import kotlin.math.roundToLong
import com.stt.android.core.R as CR

@Singleton
class WorkoutAnalysisHelper @Inject constructor(
    private val infoModelFormatter: InfoModelFormatter
) {
    fun getGraphNameSubtitle(
        context: Context,
        graphType: GraphType,
        workoutHeader: WorkoutHeader,
        diveExtension: DiveExtension?,
        sml: Sml?,
        activityWindow: DomainWindow? = null
    ): String? {
        val unit = infoModelFormatter.unit
        val avgSpeed = activityWindow?.speed?.avg
            ?: workoutHeader.avgSpeed
        val maxAltitude = activityWindow?.altitudeRange?.max
            ?: workoutHeader.maxAltitude
        val avgHr = activityWindow?.hr?.avg?.times(60f)
            ?: workoutHeader.heartRateAverage
        val activityTypeFromWindow = activityWindow?.activityId?.run {
            val stId = getStIdForMcId(this)
            ActivityType.valueOf(stId)
        }
        val avgGroundContactTime = activityWindow?.groundContactTime?.avg
        val avgVerticalOscillation = activityWindow?.verticalOscillation?.avg
        val swimming = activityTypeFromWindow?.isSwimming ?: workoutHeader.activityType.isSwimming
        val diving = activityTypeFromWindow?.isDiving ?: workoutHeader.activityType.isDiving
        return when (graphType) {
            is GraphType.Summary -> when (graphType.summaryGraph) {
                SummaryGraph.PACE -> String.format(
                    Locale.US,
                    "%s %s",
                    context.getString(CR.string.average),
                    if (swimming) {
                        infoModelFormatter.formatValueAsString(SummaryItem.AVGSWIMPACE, avgSpeed)
                    } else {
                        infoModelFormatter.formatValueAsString(SummaryItem.AVGPACE, avgSpeed)
                    }
                )

                SummaryGraph.SPEED -> {
                    String.format(
                        Locale.US,
                        "%s %.2f %s",
                        context.getString(CR.string.average),
                        unit.toSpeedUnit(avgSpeed),
                        context.getString(unit.speedUnit)
                    )
                }

                SummaryGraph.SPEEDKNOTS -> {
                    String.format(
                        Locale.US,
                        "%s %.2f %s",
                        context.getString(CR.string.average),
                        unit.toKnots(avgSpeed),
                        context.getString(CR.string.knots)
                    )
                }

                SummaryGraph.ALTITUDE -> {
                    val highestPoint = if (sml == null || sml == SmlFactory.EMPTY) {
                        maxAltitude?.let { unit.fromDecimetersToMeters(it) }
                    } else {
                        activityWindow?.altitudeRange?.max
                    }
                    if (highestPoint != null) {
                        String.format(
                            Locale.US,
                            "%s %s %s",
                            context.getString(CR.string.workout_values_headline_high_altitude),
                            TextFormatter.formatAltitude(
                                unit.toAltitudeUnit(highestPoint)
                            ),
                            context.getString(unit.altitudeUnit)
                        )
                    } else {
                        "-"
                    }
                }

                SummaryGraph.CADENCE -> {
                    val avgCadence = activityWindow?.cadence?.avg
                    avgCadence?.let {
                        String.format(
                            Locale.US,
                            context.getString(CR.string.average_with_value_and_unit),
                            unit.fromHzToRpm(it).roundToInt(),
                            context.getString(R.string.rpm)
                        )
                    }
                }

                SummaryGraph.EPOC -> null
                SummaryGraph.TEMPERATURE -> {
                    val streamData = sml?.streamData?.takeIf {
                        sml != SmlFactory.EMPTY
                    }
                    val temperature =
                        when {
                            diving && diveExtension != null -> diveExtension.maxDepthTemperature
                            streamData != null -> streamData.temperature.averageOrNull()
                            else -> null
                        }

                    temperature?.let {
                        String.format(
                            Locale.US,
                            context.getString(
                                if (diving && diveExtension != null) {
                                    CR.string.max_bottom_temperature_with_value_and_unit
                                } else {
                                    CR.string.average_with_value_and_unit
                                }
                            ),

                            infoModelFormatter.formatValue(
                                if (diving) {
                                    SummaryItem.DIVEMAXDEPTHTEMPERATURE
                                } else {
                                    SummaryItem.AVGTEMPERATURE
                                },
                                temperature
                            ).value?.toInt(),
                            context.getString(unit.temperatureUnit)
                        )
                    }
                }

                SummaryGraph.POWER -> {
                    val avgPower = activityWindow?.power?.avg
                    avgPower?.let {
                        String.format(
                            Locale.US,
                            context.getString(CR.string.average_with_value_and_unit),
                            it.roundToInt(),
                            context.getString(CR.string.watt)
                        )
                    }
                }

                SummaryGraph.SEALEVELPRESSURE -> null
                SummaryGraph.BIKECADENCE -> null
                SummaryGraph.SWIMSTROKERATE -> {
                    val avgStrokeRate = activityWindow?.strokeRate?.avg
                    avgStrokeRate?.let {
                        String.format(
                            Locale.US,
                            context.getString(CR.string.average_with_value_and_unit),
                            it.times(60).roundToInt(), // Hz -> strokes/min
                            context.getString(CR.string.per_minute)
                        )
                    }
                }

                SummaryGraph.SWIMPACE -> null
                SummaryGraph.SWOLF -> {
                    val avgSwolf = activityWindow?.swolf?.avg
                    avgSwolf?.let {
                        String.format(
                            Locale.US,
                            context.getString(CR.string.average_with_value_and_unit),
                            it.roundToInt(),
                            context.getString(R.string.empty) // swolf does not have a unit
                        )
                    }
                }

                SummaryGraph.DEPTH -> {
                    if (diving && diveExtension != null) {
                        val maxDepth = diveExtension.maxDepth
                        if (maxDepth != null) {
                            val workoutValue =
                                infoModelFormatter.formatValue(SummaryItem.MAXDEPTH, maxDepth)
                            context.getString(
                                CR.string.max_depth_with_value_and_unit,
                                workoutValue.value ?: "",
                                workoutValue.getUnitLabel(context)
                            )
                        } else {
                            null
                        }
                    } else {
                        null
                    }
                }

                SummaryGraph.HEARTRATE,
                SummaryGraph.RECOVERYHRINTHREEMINS -> {
                    val hrUnit = context.getString(CR.string.bpm)
                    context.getString(
                        CR.string.average_with_value_and_unit,
                        avgHr.roundToLong(),
                        hrUnit
                    )
                }

                SummaryGraph.VERTICALSPEED -> {
                    val avgVerticalSpeed = activityWindow?.verticalSpeed?.avg
                    avgVerticalSpeed?.let {
                        String.format(
                            Locale.US,
                            context.getString(CR.string.average_with_decimal_value_and_unit),
                            unit.toVerticalSpeedUnit(it),
                            context.getString(unit.verticalSpeedUnit)
                        )
                    }
                }

                SummaryGraph.GASCONSUMPTION ->
                    if (diving && diveExtension != null) {
                        val gasConsumption = diveExtension.gasConsumption
                        if (gasConsumption != null) {
                            String.format(
                                Locale.US,
                                "%s %.1f %s",
                                context.getString(CR.string.average),
                                unit.toGasConsumptionUnit(gasConsumption.toDouble()),
                                context.getString(unit.gasConsumptionUnit)
                            )
                        } else {
                            null
                        }
                    } else {
                        null
                    }

                SummaryGraph.TANKPRESSURE -> {
                    if (diving &&
                        diveExtension != null &&
                        sml?.streamData != null
                    ) {
                        val ngDiveHeader = sml.summary.diveHeader
                        val getGasNameByIndex: (Int) -> String = if (ngDiveHeader != null) {
                            ngDiveHeader::getGasNameByIndex
                        } else {
                            diveExtension::getGasNameByIndex
                        }

                        val pressureDeltas = sml.streamData.run {
                            // Combine primary and secondary pressures per gas
                            val gasKeys = tankPressures.keys.union(tankPressures2.keys)
                            gasKeys.map { gasIndex ->
                                val gasName = getGasNameByIndex(gasIndex)
                                val values =
                                    (tankPressures[gasIndex].orEmpty() + tankPressures2[gasIndex].orEmpty())
                                        .map { it.value }
                                val pressureDelta =
                                    (values.maxOrNull() ?: 0f) - (values.minOrNull() ?: 0f)
                                val pressureValueConverted =
                                    unit.toPressureUnit(pressureDelta.toDouble()).roundToInt()
                                val pressureUnit = context.getString(unit.pressureUnit)
                                context.getString(
                                    CR.string.gas_pressure_with_value_unit_and_gas_name,
                                    pressureValueConverted,
                                    pressureUnit,
                                    gasName
                                )
                            }
                        }
                        if (pressureDeltas.isEmpty()) {
                            ""
                        } else {
                            context.resources.getQuantityString(
                                CR.plurals.tank_pressure_delta_with_gas_values,
                                pressureDeltas.size,
                                pressureDeltas.joinToString(", ")
                            )
                        }
                    } else {
                        null
                    }
                }

                SummaryGraph.VERTICALOSCILLATION -> {
                    avgVerticalOscillation?.let { value ->
                        val meters = TextFormatter.formatJumpHeight(value.toFloat(), unit)
                        String.format(
                            Locale.US,
                            context.getString(CR.string.average_with_decimal_value_and_unit),
                            infoModelFormatter.formatValue(
                                SummaryItem.AVGVERTICALOSCILLATION,
                                meters
                            ).value?.toDouble() ?: 0.0,
                            context.getString(unit.shorterDistanceUnitResId)
                        )
                    }
                }

                SummaryGraph.GROUNDCONTACTTIME -> {
                    avgGroundContactTime?.let { value ->
                        String.format(
                            Locale.US,
                            context.getString(CR.string.average_with_decimal_value_and_unit),
                            infoModelFormatter.formatValue(
                                SummaryItem.AVGGROUNDCONTACTTIME,
                                value
                            ).value?.toInt(),
                            context.getString(CR.string.ms)
                        )
                    }
                }

                SummaryGraph.AEROBICZONE,
                SummaryGraph.AEROBICHRTHRESHOLDS,
                SummaryGraph.AEROBICPOWERTHRESHOLDS,
                SummaryGraph.BREATHINGRATE,
                SummaryGraph.AVGBREASTSTROKEBREATHANGLE,
                SummaryGraph.AVGFREESTYLEBREATHANGLE,
                SummaryGraph.DURATION,
                SummaryGraph.BREASTSTROKEHEADANGLE,
                SummaryGraph.FREESTYLEPITCHANGLE,
                SummaryGraph.BREASTSTROKEGLIDETIME,
                SummaryGraph.AVGSKIPSPERROUND,
                SummaryGraph.AVGSKIPSRATE,
                SummaryGraph.NONE -> null
            }

            is GraphType.SuuntoPlus -> {
                val avg =
                    sml?.summary?.suuntoPlusSamplesStatistics?.get(graphType.suuntoPlusChannel)?.avg
                if (avg != null) {
                    val formatStyle = graphType.suuntoPlusChannel.formatStyleForSIM
                    if (formatStyle != null) {
                        when (
                            val formatResult = infoModelFormatter.formatValue(
                                formatName = formatStyle,
                                value = avg,
                                withStyle = true,
                                sourceUnit = SourceUnit.SI,
                                forceRangeUnitOutput = true
                            )
                        ) {
                            is Success -> {
                                val unitString =
                                    InfoModelFormatter.getUnitResId(formatResult.unit)?.let {
                                        context.getString(it)
                                    } ?: ""
                                return String.format(
                                    Locale.US,
                                    "%s %s %s",
                                    context.getString(CR.string.average),
                                    formatResult.value,
                                    unitString
                                )
                            }

                            is Failure -> {
                                Timber.w("Failed formatting value $avg with $formatStyle for reason: ${formatResult.reason}")
                                null
                            }
                        }
                    } else {
                        String.format(
                            Locale.US,
                            "%s %s",
                            context.getString(CR.string.average),
                            DecimalFormat(
                                "#.##",
                                DecimalFormatSymbols.getInstance(Locale.US)
                            ).format(avg)
                        )
                    }
                } else {
                    null
                }
            }
        }
    }

    companion object {

        @JvmStatic
        fun getGraphNameTitle(context: Context, graphType: GraphType): String = when (graphType) {
            is GraphType.Summary -> {
                when (graphType.summaryGraph) {
                    SummaryGraph.PACE -> R.string.pace
                    SummaryGraph.SPEED -> CR.string.speed
                    SummaryGraph.ALTITUDE -> CR.string.all_altitude
                    SummaryGraph.CADENCE -> CR.string.all_cadence
                    SummaryGraph.EPOC -> CR.string.all_epoc
                    SummaryGraph.TEMPERATURE -> CR.string.all_temperature
                    SummaryGraph.POWER -> CR.string.all_power
                    SummaryGraph.SEALEVELPRESSURE -> CR.string.all_sea_level_pressure
                    SummaryGraph.BIKECADENCE -> CR.string.all_bike_cadence
                    SummaryGraph.SWIMSTROKERATE -> CR.string.swim_stroke_rate
                    SummaryGraph.SWIMPACE -> CR.string.all_swim_pace
                    SummaryGraph.SWOLF -> CR.string.all_swolf
                    SummaryGraph.SPEEDKNOTS -> CR.string.all_speed_knots
                    SummaryGraph.DEPTH -> CR.string.all_depth
                    SummaryGraph.HEARTRATE -> CR.string.all_heart_rate
                    SummaryGraph.VERTICALSPEED -> CR.string.all_vertical_speed
                    SummaryGraph.GASCONSUMPTION -> CR.string.all_gas_consumption
                    SummaryGraph.TANKPRESSURE -> CR.string.all_tank_pressure
                    SummaryGraph.AEROBICZONE -> CR.string.zone_sense
                    SummaryGraph.AEROBICHRTHRESHOLDS -> CR.string.aerobic_hr_thresholds
                    SummaryGraph.AEROBICPOWERTHRESHOLDS -> CR.string.aerobic_power_thresholds
                    SummaryGraph.RECOVERYHRINTHREEMINS -> CR.string.hr_list_in_three_mins
                    SummaryGraph.VERTICALOSCILLATION -> CR.string.all_vertical_oscillation
                    SummaryGraph.GROUNDCONTACTTIME -> CR.string.all_ground_contact_time
                    SummaryGraph.BREATHINGRATE -> CR.string.workout_values_headline_breathing_rate
                    SummaryGraph.AVGBREASTSTROKEBREATHANGLE -> CR.string.workout_values_headline_breaststroke_avg_breath_angle
                    SummaryGraph.AVGFREESTYLEBREATHANGLE -> CR.string.workout_values_headline_freestyle_avg_breath_angle
                    SummaryGraph.DURATION -> CR.string.duration_lap
                    SummaryGraph.BREASTSTROKEHEADANGLE -> CR.string.workout_values_headline_breaststroke_head_angel
                    SummaryGraph.FREESTYLEPITCHANGLE -> CR.string.workout_values_headline_freestyle_head_angel
                    SummaryGraph.BREASTSTROKEGLIDETIME -> CR.string.workout_values_headline_breaststroke_glide_time
                    SummaryGraph.AVGSKIPSRATE -> CR.string.workout_values_headline_skips_per_min
                    SummaryGraph.AVGSKIPSPERROUND -> CR.string.workout_values_headline_skip_per_round
                    SummaryGraph.NONE -> CR.string.all_none
                    else -> throw IllegalArgumentException("Unsupported graphType: $graphType")
                }.let { context.getString(it) }
            }

            is GraphType.SuuntoPlus -> graphType.suuntoPlusChannel.name
        }

        @StringRes
        fun getGraphUnitStringRes(
            simFormatter: InfoModelFormatter,
            graphType: GraphType,
            unit: MeasurementUnit,
            isSwimming: Boolean
        ): Int? =
            when (graphType) {
                is GraphType.Summary -> {
                    when (graphType.summaryGraph) {
                        SummaryGraph.PACE -> {
                            when (isSwimming) {
                                true -> unit.swimPaceUnit
                                false -> unit.paceUnit
                            }
                        }

                        SummaryGraph.SPEED -> unit.speedUnit
                        SummaryGraph.ALTITUDE -> unit.altitudeUnit
                        SummaryGraph.CADENCE -> R.string.rpm
                        SummaryGraph.POWER -> CR.string.watt
                        SummaryGraph.TEMPERATURE -> unit.temperatureUnit
                        SummaryGraph.SEALEVELPRESSURE -> unit.pressureUnit
                        SummaryGraph.DEPTH -> unit.altitudeUnit
                        SummaryGraph.VERTICALSPEED -> unit.verticalSpeedUnit
                        SummaryGraph.GASCONSUMPTION -> unit.gasConsumptionUnit
                        SummaryGraph.TANKPRESSURE -> unit.pressureUnit
                        SummaryGraph.HEARTRATE, SummaryGraph.RECOVERYHRINTHREEMINS -> CR.string.bpm
                        SummaryGraph.SWIMSTROKERATE -> CR.string.per_minute
                        SummaryGraph.SPEEDKNOTS -> CR.string.knots
                        SummaryGraph.SWOLF -> R.string.empty // swolf does not have a unit

                        SummaryGraph.AEROBICZONE -> CR.string.ddfa_index
                        SummaryGraph.AEROBICHRTHRESHOLDS,
                        SummaryGraph.AEROBICPOWERTHRESHOLDS -> CR.string.bpm

                        SummaryGraph.VERTICALOSCILLATION -> unit.shorterDistanceUnitResId
                        SummaryGraph.GROUNDCONTACTTIME -> CR.string.ms
                        SummaryGraph.BREATHINGRATE -> CR.string.per_minute
                        SummaryGraph.AVGBREASTSTROKEBREATHANGLE -> CR.string.degree
                        SummaryGraph.AVGFREESTYLEBREATHANGLE -> CR.string.degree
                        SummaryGraph.DURATION -> CR.string.seconds
                        SummaryGraph.BREASTSTROKEHEADANGLE -> CR.string.degree
                        SummaryGraph.FREESTYLEPITCHANGLE -> CR.string.degree
                        SummaryGraph.BREASTSTROKEGLIDETIME -> CR.string.min_sec
                        SummaryGraph.AVGSKIPSPERROUND -> CR.string.round
                        SummaryGraph.AVGSKIPSRATE -> CR.string.per_minute
                        SummaryGraph.NONE -> null
                        else -> throw IllegalArgumentException("Unsupported graphType: $graphType")
                    }
                }

                is GraphType.SuuntoPlus -> {
                    val formatWithStyle = graphType.suuntoPlusChannel.formatStyleForSIM
                    if (formatWithStyle != null) {
                        // it's not ideal that we need to format an arbitrary value (1.0) which
                        // happens to work for all our format styles, to get rangeUnit information.
                        // Alternatives would need a better formatter API.
                        val result =
                            simFormatter.formatValue(formatWithStyle, 1.0, withStyle = true)
                        (result as? Success)?.rangeUnit?.let { InfoModelFormatter.getUnitResId(it) }
                    } else {
                        null
                    }
                }
            }

        @DrawableRes
        fun getGraphIconDrawableRes(graphType: GraphType): Int =
            when (graphType) {
                is GraphType.Summary -> when (graphType.summaryGraph) {
                    SummaryGraph.HEARTRATE, SummaryGraph.RECOVERYHRINTHREEMINS -> R.drawable.ic_summary_item_heart_rate
                    SummaryGraph.PACE -> R.drawable.ic_summary_item_pace
                    SummaryGraph.SPEED -> R.drawable.ic_summary_item_pace
                    SummaryGraph.ALTITUDE -> R.drawable.ic_altitude_outline
                    SummaryGraph.CADENCE -> R.drawable.ic_summary_item_cadence
                    SummaryGraph.EPOC -> throw IllegalArgumentException("Unsupported graphType: $graphType")
                    SummaryGraph.TEMPERATURE -> R.drawable.ic_summary_item_temperature
                    SummaryGraph.POWER -> R.drawable.ic_summary_item_power
                    SummaryGraph.SEALEVELPRESSURE -> R.drawable.ic_summary_item_sea_level_pressure
                    SummaryGraph.BIKECADENCE -> throw IllegalArgumentException("Unsupported graphType: $graphType")
                    SummaryGraph.SWIMSTROKERATE -> R.drawable.ic_summary_item_stroke_rate
                    SummaryGraph.SWIMPACE -> throw IllegalArgumentException("Unsupported graphType: $graphType")
                    SummaryGraph.SWOLF -> R.drawable.ic_summary_item_swolf
                    SummaryGraph.SPEEDKNOTS -> R.drawable.ic_summary_item_pace
                    SummaryGraph.DEPTH -> R.drawable.ic_summary_item_depth
                    SummaryGraph.VERTICALSPEED -> R.drawable.ic_summary_item_vertical_speed
                    SummaryGraph.NONE -> R.drawable.visibility_off_outline
                    SummaryGraph.GASCONSUMPTION -> throw IllegalArgumentException("Unsupported graphType: $graphType")
                    SummaryGraph.TANKPRESSURE -> throw IllegalArgumentException("Unsupported graphType: $graphType")
                    SummaryGraph.AEROBICZONE -> R.drawable.intensity_outline
                    SummaryGraph.AEROBICHRTHRESHOLDS -> throw IllegalArgumentException("Unsupported graphType: $graphType")
                    SummaryGraph.AEROBICPOWERTHRESHOLDS -> throw IllegalArgumentException("Unsupported graphType: $graphType")
                    SummaryGraph.VERTICALOSCILLATION -> R.drawable.ic_summary_item_vertical_oscillation
                    SummaryGraph.GROUNDCONTACTTIME -> R.drawable.ic_summary_item_ground_contact
                    SummaryGraph.BREATHINGRATE -> R.drawable.icon_breathing_rate
                    SummaryGraph.AVGBREASTSTROKEBREATHANGLE -> R.drawable.icon_avg_breaststroke_breath_angle
                    SummaryGraph.AVGFREESTYLEBREATHANGLE -> R.drawable.icon_avg_freestyle_breath_angle
                    SummaryGraph.DURATION -> R.drawable.ic_summary_item_duration
                    SummaryGraph.BREASTSTROKEHEADANGLE -> R.drawable.icon_swimming_head_angle
                    SummaryGraph.FREESTYLEPITCHANGLE -> R.drawable.icon_swimming_head_angle
                    SummaryGraph.BREASTSTROKEGLIDETIME -> R.drawable.icon_breaststorke_glide_time
                    SummaryGraph.AVGSKIPSRATE -> R.drawable.icon_summary_item_times
                    SummaryGraph.AVGSKIPSPERROUND -> R.drawable.ic_summary_item_rounds
                }

                is GraphType.SuuntoPlus -> R.drawable.ic_suuntoplus
            }

        @JvmStatic
        fun LineDataSet.getEntryForXIndexExact(xIndex: Int): Entry? =
            values.firstOrNull { it.data == xIndex }

        @JvmStatic
        fun createGraphList(
            activityType: ActivityType,
            geoPoints: List<WorkoutGeoPoint>?,
            sml: Sml?,
            multisportPartActivity: MultisportPartActivity? = null
        ): List<GraphType> {
            val graphTypes = getActivitySummaryForActivityId(activityType.id).graphs
            val startTime = multisportPartActivity?.startTime
            val stopTime = multisportPartActivity?.stopTime
            return createGraphList(
                activityType,
                ActivitySummariesMapping.filterUnimplementedGraphs(
                    activityType,
                    graphTypes
                ),
                geoPoints?.filter {
                    startTime == null || stopTime == null || it.timestamp in startTime..stopTime
                },
                sml,
                multisportPartActivity
            )
        }

        @JvmStatic
        private fun createGraphList(
            activityType: ActivityType,
            summaryGraphs: List<SummaryGraph>,
            geoPoints: List<WorkoutGeoPoint>?,
            sml: Sml?,
            multisportPartActivity: MultisportPartActivity? = null
        ): List<GraphType> {
            val graphs = summaryGraphs.toMutableList()

            val hasGeoPoints = geoPoints != null && geoPoints.isNotEmpty()
            val hasPacePoints =
                geoPoints != null && geoPoints.sanitisePaceGeoPoints(activityType).isNotEmpty()

            val suuntoPlusGraphs = mutableListOf<GraphType.SuuntoPlus>()

            // removing graphs without data
            if (sml == null || sml == SmlFactory.EMPTY) {
                graphs.removeAll {
                    it == SummaryGraph.POWER ||
                        it == SummaryGraph.TEMPERATURE ||
                        it == SummaryGraph.CADENCE ||
                        it == SummaryGraph.VERTICALSPEED ||
                        it == SummaryGraph.SWIMSTROKERATE ||
                        it == SummaryGraph.SWOLF ||
                        it == SummaryGraph.GASCONSUMPTION ||
                        it == SummaryGraph.TANKPRESSURE ||
                        it == SummaryGraph.DEPTH ||
                        it == SummaryGraph.VERTICALOSCILLATION ||
                        it == SummaryGraph.GROUNDCONTACTTIME ||
                        it == SummaryGraph.DURATION ||
                        it == SummaryGraph.AVGBREASTSTROKEBREATHANGLE ||
                        it == SummaryGraph.AVGFREESTYLEBREATHANGLE ||
                        it == SummaryGraph.BREASTSTROKEGLIDETIME ||
                        it == SummaryGraph.FREESTYLEPITCHANGLE ||
                        it == SummaryGraph.BREASTSTROKEHEADANGLE ||
                        it == SummaryGraph.BREATHINGRATE ||
                        it == SummaryGraph.AVGSKIPSPERROUND ||
                        it == SummaryGraph.AVGSKIPSRATE
                }
                if (!hasPacePoints) {
                    graphs.removeAll {
                        it == SummaryGraph.PACE
                    }
                }
                if (!hasGeoPoints) {
                    // Don't show PACE, SPEED, SPEEDKNOTS and ALTITUDE graphs without SML and geo points
                    graphs.removeAll {
                        it == SummaryGraph.PACE ||
                            it == SummaryGraph.SPEED ||
                            it == SummaryGraph.ALTITUDE ||
                            it == SummaryGraph.SPEEDKNOTS
                    }
                } else if (!hasNonZeroAltitudes(geoPoints)) {
                    graphs.removeAll {
                        it == SummaryGraph.ALTITUDE
                    }
                }
            } else {
                with(sml.streamData) {
                    // Legacy ventilation data
                    val isVentilationDataEmpty =
                        isStreamDataEmpty(ventilation, multisportPartActivity)
                    // Ng dive ventilation data
                    val isGasConsumptionsEmpty = gasConsumptions.values.flatten().isEmpty()
                    if (isVentilationDataEmpty && isGasConsumptionsEmpty) {
                        graphs.removeAll { it == SummaryGraph.GASCONSUMPTION }
                    }
                    if (isStreamDataEmpty(temperature, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.TEMPERATURE }
                    }
                    run {
                        val hasPrimary = tankPressures.values.flatten().isNotEmpty()
                        val hasSecondary = tankPressures2.values.flatten().isNotEmpty()
                        val hasAny = hasPrimary || hasSecondary
                        if (!hasAny) {
                            graphs.removeAll { it == SummaryGraph.TANKPRESSURE }
                        }
                    }
                    if (isStreamDataEmpty(power, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.POWER }
                    }
                    if (isStreamDataEmpty(cadence, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.CADENCE }
                    }
                    if (isStreamDataEmpty(verticalSpeed, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.VERTICALSPEED }
                    }
                    if (isStreamDataEmpty(strokeRate, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.SWIMSTROKERATE }
                    }
                    if (isStreamDataEmpty(swolf, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.SWOLF }
                    }

                    if (!hasNonZeroPaceValues(speed, multisportPartActivity, activityType) &&
                        !hasPacePoints
                    ) {
                        graphs.removeAll {
                            it == SummaryGraph.PACE
                        }
                    }
                    if (isStreamDataEmpty(speed, multisportPartActivity) && !hasGeoPoints) {
                        graphs.removeAll {
                            it == SummaryGraph.SPEED ||
                                it == SummaryGraph.SPEEDKNOTS ||
                                it == SummaryGraph.PACE
                        }
                    }
                    if (isStreamDataEmpty(altitude, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.ALTITUDE }
                    }
                    if (isStreamDataEmpty(depth, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.DEPTH }
                    }
                    if (isStreamDataEmpty(verticalOscillations, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.VERTICALOSCILLATION }
                    }
                    if (isStreamDataEmpty(groundContactTimes, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.GROUNDCONTACTTIME }
                    }
                    if (isStreamDataEmpty(duration, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.DURATION }
                    }
                    if (isStreamDataEmpty(breathingRate, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.BREATHINGRATE }
                    }
                    if (isStreamDataEmpty(breaststrokeGlideTime, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.BREASTSTROKEGLIDETIME }
                    }
                    if (isStreamDataEmpty(freestyleAvgBreathAngle, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.AVGFREESTYLEBREATHANGLE }
                    }
                    if (isStreamDataEmpty(breaststrokeAvgBreathAngle, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.AVGBREASTSTROKEBREATHANGLE }
                    }
                    if (isStreamDataEmpty(freestyleHeadAngle, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.FREESTYLEPITCHANGLE }
                    }
                    if (isStreamDataEmpty(breaststrokeHeadAngle, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.BREASTSTROKEHEADANGLE }
                    }
                    if (isStreamDataEmpty(cadence, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.AVGSKIPSRATE }
                    }
                    if (isStreamDataEmpty(skipsPerRound, multisportPartActivity)) {
                        graphs.removeAll { it == SummaryGraph.AVGSKIPSPERROUND }
                    }
                    suuntoPlusGraphs += suuntoPlusSamplePoints
                        .filterNot { (_, values) ->
                            isStreamDataEmpty(values, multisportPartActivity)
                        }
                        .map { GraphType.SuuntoPlus(it.key) }
                }
            }

            return graphs.map { GraphType.Summary(it) } + suuntoPlusGraphs
        }

        private fun hasNonZeroAltitudes(geoPoints: List<WorkoutGeoPoint>?): Boolean {
            if (geoPoints == null) {
                return false
            }
            return geoPoints.any { it.altitude != 0.0 }
        }

        /**
         * FIXME
         *
         * Helper methods below need to follow the same logic used by WorkoutLineChartBase.kt
         * when deciding if we have data or not. This is extremely error-prone and a likely
         * cause of regression if either side is changed. Should be rewritten, at latest when
         * we decide to extract business logic from the WorkoutLineChartBase and related UI
         * views.
         */

        // TODO: This function is very expensive to call on long workouts as the duplicates the
        // stream point list when converting to TimestampedDataPoint. There should be no need to
        // iterate through all the samples just in order to check if any of them has a value.
        private fun SmlStreamData.isStreamDataEmpty(
            streamPoints: List<SmlExtensionStreamPoint>,
            multisportPartActivity: MultisportPartActivity?
        ): Boolean = dataPointsWithoutPauses(
            streamPoints.filterStreamPoint(multisportPartActivity),
            startTimestamp = multisportPartActivity?.startTime
        )
            .none { it.value != 0f }

        // TODO: This function is very expensive to call on long workouts as the duplicates the
        // stream point list when converting to TimestampedDataPoint. There should be no need to
        // iterate through all the samples just in order to check if any of them has a valid pace
        // value.
        private fun SmlStreamData.hasNonZeroPaceValues(
            streamPoints: List<SmlExtensionStreamPoint>,
            multisportPartActivity: MultisportPartActivity?,
            activityType: ActivityType
        ): Boolean = dataPointsWithoutPauses(
            streamPoints.filterStreamPoint(multisportPartActivity),
            startTimestamp = multisportPartActivity?.startTime
        )
            .sanitisePaceStreamPoints(activityType)
            .any { it.value != 0f }
    }
}
