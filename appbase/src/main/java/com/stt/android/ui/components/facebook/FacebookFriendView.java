package com.stt.android.ui.components.facebook;

import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.domain.STTErrorCodes;
import com.stt.android.follow.UserFollowStatus;
import com.stt.android.home.people.FollowActionViewHelper;
import com.stt.android.home.people.FollowStatusView;
import com.stt.android.home.people.widgets.FollowStatusWidget;
import com.stt.android.social.userprofileV2.BaseUserProfileActivity;
import com.stt.android.ui.utils.DialogHelper;
import dagger.hilt.android.AndroidEntryPoint;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;

@AndroidEntryPoint
public class FacebookFriendView extends LinearLayout
    implements FollowStatusView, FollowStatusWidget.Listener {
    public static final int MAX_FRIENDS_TO_SHOW = 3;

    @Inject
    FeedFbFriendPresenter feedFbFriendPresenter;

    private final List<UserFollowStatus> fbFriends = new ArrayList<>();
    private final List<FollowStatusWidget> views = new ArrayList<>(MAX_FRIENDS_TO_SHOW);

    public FacebookFriendView(Context context) {
        super(context);
        init();
    }

    public FacebookFriendView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public FacebookFriendView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public FacebookFriendView(Context context, AttributeSet attrs, int defStyleAttr,
        int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    private void init() {
        setOrientation(VERTICAL);
        // We need to get the background from the Android framework and it's a bit cumbersome
        // since it might require some indirections
        int[] attrs = new int[]{android.R.attr.selectableItemBackground};
        Context context = getContext();
        TypedArray typedArray = context.obtainStyledAttributes(attrs);
        int backgroundResource = typedArray.getResourceId(0, 0);
        setBackgroundResource(backgroundResource);
        typedArray.recycle();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();

        if (isInEditMode()) {
            return;
        }

        feedFbFriendPresenter.takeView(this);

        //we need to refresh statuses from db if it was changed from somewhere else
        if (fbFriends.size() > 0) {
            feedFbFriendPresenter.updateFaceBookFriendStatuses(fbFriends);
        }
    }

    public void showFbFriends(List<UserFollowStatus> friends) {
        // Create a deep copy of the list in order not to mutate the list instance stored in the
        // Epoxy model
        fbFriends.clear();
        for (UserFollowStatus status : friends) {
            fbFriends.add(status.toBuilder().build());
        }

        removeAllViews();
        views.clear();

        Context context = getContext();
        Resources resources = context.getResources();
        int horizontalPadding = resources.getDimensionPixelSize(com.stt.android.core.R.dimen.padding);
        int verticalPadding = resources.getDimensionPixelSize(com.stt.android.core.R.dimen.smaller_padding);
        for (int i = 0; i < fbFriends.size(); ++i) {
            FollowStatusWidget followStatusWidget = new FollowStatusWidget(context);
            followStatusWidget.setListener(this);
            followStatusWidget.setUserFollowStatus(fbFriends.get(i), false, false);
            followStatusWidget.setPadding(horizontalPadding, verticalPadding, horizontalPadding,
                verticalPadding);
            addView(followStatusWidget);
            views.add(followStatusWidget);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        feedFbFriendPresenter.dropView();
        super.onDetachedFromWindow();
    }

    @Override
    public void updateStatus(UserFollowStatus userFollowStatus) {
        for (int i = 0; i < fbFriends.size(); ++i) {
            if (fbFriends.get(i).getUsername().equals(userFollowStatus.getUsername())) {
                fbFriends.set(i, userFollowStatus);
                views.get(i).setUserFollowStatus(userFollowStatus, false, false);
                break;
            }
        }
    }

    @Override
    public void showUserProfile(@NonNull String username) {
        Context context = getContext();
        context.startActivity(BaseUserProfileActivity.newStartIntent(context, username, false));
    }

    @Override
    public void showUnfollowDialog(final UserFollowStatus userFollowStatus) {
        DialogHelper.showDialog(getContext(), true, 0, R.string.unfollow_dialog_message,
            R.string.unfollow_dialog_confirm, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    feedFbFriendPresenter.unfollow(userFollowStatus);
                }
            }, R.string.cancel, null);
    }

    @Override
    public void showActionError(UserFollowStatus userFollowStatus, OnClickListener tryAgainAction) {
        FollowActionViewHelper.showActionError(this, getRootView(), userFollowStatus,
            tryAgainAction);
    }

    @Override
    public void showError(@NonNull STTErrorCodes errorCode) {
        FollowActionViewHelper.showError(getRootView(), errorCode);
    }

    @Override
    public void showFollowActionSpinner(UserFollowStatus userFollowStatus) {
        for (int i = 0; i < fbFriends.size(); ++i) {
            if (fbFriends.get(i).getUsername().equals(userFollowStatus.getUsername())) {
                views.get(i).showActionSpinner();
                break;
            }
        }
    }

    @Override
    public void showEmptyView() {
        //we never show empty here
    }

    @Override
    public void onUserInfoClicked(@NonNull UserFollowStatus userFollowStatus) {
        Context context = getContext();
        context.startActivity(
            BaseUserProfileActivity.newStartIntent(context, userFollowStatus.getUsername(), false));
    }

    @Override
    public void onAcceptPendingRequestClicked(@NonNull UserFollowStatus userFollowStatus) {
        // won't reach here
    }

    @Override
    public void onRejectPendingRequestClicked(@NonNull UserFollowStatus userFollowStatus) {
        // won't reach here
    }

    @Override
    public void onFollowUserClicked(@NonNull UserFollowStatus userFollowStatus) {
        feedFbFriendPresenter.follow(userFollowStatus,
            AnalyticsPropertyValue.FollowSourceProperty.FOLLOW_SUGGESTIONS_FEED);
    }

    @Override
    public void onAskToUnfollowUserClicked(@NonNull UserFollowStatus userFollowStatus) {
        feedFbFriendPresenter.askToUnfollow(userFollowStatus);
    }

    @Override
    public void onUnfollowUserClicked(@NonNull UserFollowStatus userFollowStatus) {
        feedFbFriendPresenter.unfollow(userFollowStatus);
    }

    @Override
    public void onUserLongTapped(@NonNull UserFollowStatus userFollowStatus) {
        // pass
    }
}
