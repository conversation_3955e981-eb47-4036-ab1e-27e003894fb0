package com.stt.android.ui.fragments.settings

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.common.ui.SimpleProgressDialogFragment
import com.stt.android.databinding.FragmentEditPastActivityPrivacyBinding
import com.stt.android.remote.workouts.update.SharingFlags
import com.stt.android.ui.fragments.BaseCurrentUserControllerFragment
import com.stt.android.viewmodel.EditPastActivityPrivacyViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Locale

@AndroidEntryPoint
class EditPastActivityPrivacyFragment : BaseCurrentUserControllerFragment() {
    private var binding: FragmentEditPastActivityPrivacyBinding? = null
    val viewModel: EditPastActivityPrivacyViewModel by activityViewModels()
    val sharingFlags get() = viewModel.settingSharingFlags

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentEditPastActivityPrivacyBinding.inflate(inflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        updateShareFlagsView()
    }

    override fun onStart() {
        super.onStart()
        if (viewModel.hasSubmitSharingFlags) {
            view?.post {
                activity?.finish()
            }
        }
    }

    private fun showConfirmDialog() {
        val titleKey = when (sharingFlags) {
            SharingFlags.PUBLIC -> getString(R.string.sharing_public)
            SharingFlags.FOLLOWERS -> getString(R.string.followers)
            SharingFlags.PRIVATE -> getString(R.string.sharing_private)
            else -> ""
        }
        val builder: AlertDialog.Builder = AlertDialog.Builder(
            requireActivity(),
            R.style.SimpleAlertDialog
        )
            .setTitle(getString(R.string.privacy_edit_change_all_to, "\"${titleKey}\""))
            .setMessage(
                getString(
                    R.string.privacy_edit_past_activity_tips,
                    titleKey
                )
            )
            .setCancelable(false)
            .setNegativeButton(
                R.string.cancel
            ) { dialog, _ -> dialog?.dismiss() }
            .setPositiveButton(R.string.yes) { dialog, _ ->
                dialog?.dismiss()
                // Submit to the server for processing
                submit()
            }
        builder.create().show()
    }

    private fun submit() {
        showLoading()
        viewModel.submitSharingFlags(sharingFlags) { succeeded ->
            viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                delay(300) // minimum waiting time
                hideLoading()
                if (succeeded) {
                    delay(300)
                    requireActivity().finish()
                } else {
                    binding?.root?.let {
                        Snackbar.make(it, R.string.error_generic_try_again, Snackbar.LENGTH_LONG)
                            .show()
                    }
                }
            }
        }
    }

    private fun showLoading() {
        val dialog =
            SimpleProgressDialogFragment.newInstance(getString(R.string.please_wait))
        dialog.show(childFragmentManager, SimpleProgressDialogFragment.FRAGMENT_TAG)
    }

    private fun hideLoading() {
        val fragment =
            childFragmentManager.findFragmentByTag(SimpleProgressDialogFragment.FRAGMENT_TAG) as? DialogFragment
        fragment?.dismiss()
    }

    private fun updateShareFlagsView() {
        binding?.let {
            setRadioButton(
                it.sharePublic.root,
                getString(
                    R.string.privacy_edit_past_activity_to,
                    getString(R.string.sharing_public).lowercase(Locale.getDefault())
                ),
                sharingFlags == SharingFlags.PUBLIC
            )
            setRadioButton(
                it.shareFollowers.root,
                getString(
                    R.string.privacy_edit_past_activity_to,
                    getString(R.string.followers).lowercase(Locale.getDefault())
                ),
                sharingFlags == SharingFlags.FOLLOWERS
            )
            setRadioButton(
                it.sharePrivate.root,
                getString(
                    R.string.privacy_edit_past_activity_to,
                    getString(R.string.sharing_private).lowercase(Locale.getDefault())
                ),
                sharingFlags == SharingFlags.PRIVATE
            )
            it.btnConfirm.setOnClickListener { showConfirmDialog() }
            it.btnConfirm.isEnabled = sharingFlags != SharingFlags.UNKNOWN
        }
    }

    private fun onSelectedView(selectedView: View) {
        binding?.let {
            if (selectedView == it.sharePublic.root) {
                viewModel.settingSharingFlags = SharingFlags.PUBLIC
            }
            if (selectedView == it.shareFollowers.root) {
                viewModel.settingSharingFlags = SharingFlags.FOLLOWERS
            }
            if (selectedView == it.sharePrivate.root) {
                viewModel.settingSharingFlags = SharingFlags.PRIVATE
            }
            updateShareFlagsView()
        }
    }

    private fun setRadioButton(root: View, title: String, isChecked: Boolean) {
        setTitleAndSummary(root, title)
        val button = root.findViewById<RadioButton>(R.id.radioButton)
        button.visibility = View.VISIBLE
        button.isChecked = isChecked
        root.setOnClickListener {
            onSelectedView(it)
        }
    }

    private fun setTitleAndSummary(root: View, title: String) {
        (root.findViewById<View>(android.R.id.title) as TextView).text = title
        val summaryView = root.findViewById<TextView>(android.R.id.summary)
        summaryView.visibility = View.GONE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }
}
