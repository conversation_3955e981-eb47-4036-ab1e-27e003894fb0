package com.stt.android.ui.activities;

import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory;
import com.stt.android.backgroundwork.WorkerKey;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.components.SingletonComponent;
import dagger.multibindings.IntoMap;

@Module
@InstallIn(SingletonComponent.class)
public abstract class SaveWorkoutAnalyticsJobModule {
    @Binds
    @IntoMap
    @WorkerKey(SaveWorkoutAnalyticsJob.class)
    public abstract CoroutineWorkerAssistedFactory bindFactory(
        SaveWorkoutAnalyticsJob.Factory factory);
}
