package com.stt.android.ui.adapters;

import android.content.Context;
import android.content.res.Resources;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.compose.ui.platform.ComposeView;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.stt.android.R;
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.ui.components.workout.WorkoutCardViewModel;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import rx.Observable;
import rx.subjects.PublishSubject;
import timber.log.Timber;

/**
 * Adapter for {@link android.widget.ExpandableListView} that groups workouts per month and each
 * row contains a summary with the most important workout information.
 */
public class ExpandableWorkoutListAdapter extends FilterableExpandableListAdapter<WorkoutHeader> {
    private final SimpleDateFormat df;
    private final LayoutInflater inflater;
    private final Resources resources;
    private final boolean hideGroupHeaders;

    @NonNull
    private final Context context;

    @NonNull
    private final WorkoutCardViewModel workoutCardViewModel;

    @NonNull
    private final WorkoutDetailsRewriteNavigator navigator;

    /**
     * Contains workout headers grouped by month. We can use the start time of
     * the first workout in the group to fetch the month.
     */
    private final TreeMap<Month, WorkoutHeaderTreeSet> monthlyWorkouts = new TreeMap<>();
    /**
     * We need to track which groups are open because every time we call
     * {@link #notifyDataSetChanged()} the groups are collapsed again.
     */
    private Set<Integer> expandedGroups = new HashSet<>();
    /**
     * Contains the workout headers after filtering.
     */
    private List<WorkoutHeader> filteredWorkouts;

    private final PublishSubject<WorkoutHeader> addPhotosClicks = PublishSubject.create();

    /**
     * @param hideGroupHeaders true if you want to hide the grouping headers. That means that user
     * can't
     * collapse/expand groups.
     */
    public ExpandableWorkoutListAdapter(Context context, boolean hideGroupHeaders,
        @NonNull WorkoutCardViewModel workoutCardViewModel, @NonNull WorkoutDetailsRewriteNavigator navigator) {
        super(context);
        df = new SimpleDateFormat("MMMM yyyy",
            new Locale(context.getString(R.string.language_code)));
        inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        resources = context.getResources();
        this.hideGroupHeaders = hideGroupHeaders;
        this.context = context;
        this.workoutCardViewModel = workoutCardViewModel;
        this.navigator = navigator;
        setWorkouts(Collections.<WorkoutHeader>emptyList());
    }

    /**
     * Always return false so the header in the list view where this adapter is used can be
     * displayed.
     *
     * @return always false
     */
    @Override
    public boolean isEmpty() {
        return false;
    }

    @Override
    public int getGroupCount() {
        return monthlyWorkouts.size();
    }

    @Override
    public int getChildrenCount(int groupPosition) {
        return getGroup(groupPosition).size();
    }

    @Override
    public List<WorkoutHeader> getGroup(int groupPosition) {
        ArrayList<WorkoutHeaderTreeSet> workoutHeaderTreeSet =
            new ArrayList<>(monthlyWorkouts.values());
        return new ArrayList<>(workoutHeaderTreeSet.get(groupPosition));
    }

    @Override
    public WorkoutHeader getChild(int groupPosition, int childPosition) {
        return getGroup(groupPosition).get(childPosition);
    }

    public void deleteChild(int groupPosition, int childPosition) {
        // Find the Month at the given group position
        ArrayList<Month> keySet = new ArrayList<>(monthlyWorkouts.keySet());
        Month month = keySet.get(groupPosition);

        WorkoutHeaderTreeSet workouts = monthlyWorkouts.get(month);

        // Find the workout at the given child position
        ArrayList<WorkoutHeader> workoutsList = new ArrayList<>(workouts);
        WorkoutHeader workoutToDelete = workoutsList.get(childPosition);
        workouts.remove(workoutToDelete);
        filteredWorkouts.remove(workoutToDelete);

        if (workouts.size() == 0) {
            monthlyWorkouts.remove(month);
            expandedGroups.remove(groupPosition);
            shiftOpenGroupsAfterRemove(groupPosition);
        }

        notifyDataSetChanged();
    }

    @Override
    public long getGroupId(int groupPosition) {
        return groupPosition;
    }

    @Override
    public long getChildId(int groupPosition, int childPosition) {
        return childPosition;
    }

    @Override
    public boolean hasStableIds() {
        return true;
    }

    @Override
    public View getGroupView(int groupPosition, boolean isExpanded, View convertView,
        ViewGroup parent) {
        ConstraintLayout groupView =
            (ConstraintLayout) (convertView == null ? inflater.inflate(R.layout.expandable_list_group_view,
                parent, false) : convertView);
        if (hideGroupHeaders) {
            // Hack to "hide" the group header, actually we just set its height to 1 (0 makes it
            // fill parent)
            ViewGroup.LayoutParams layoutParams = groupView.getLayoutParams();
            layoutParams.height = 1;
            layoutParams.width = 0;
            groupView.setLayoutParams(layoutParams);
        } else {
            List<WorkoutHeader> workouts = getGroup(groupPosition);
            int amount = workouts.size();
            if (amount > 0) {
                WorkoutHeader workout = workouts.get(0);
                TextView monthYear = groupView.findViewById(R.id.date);
                TextView activitiesSum = groupView.findViewById(R.id.activitiesSum);
                ImageView openIndicator = groupView.findViewById(R.id.openIndicator);
                View bottomDivider = groupView.findViewById(R.id.bottomDivider);

                monthYear.setText(df.format(new Date(workout.getStartTime())));
                String activitiesSumString = String.format(Locale.getDefault(), "(%s)",
                    resources.getQuantityString(R.plurals.workouts_plural, amount, amount));
                activitiesSum.setText(activitiesSumString);

                float scaleY = 1f;
                float elevation = 0f;
                int bottomDividerVisibility = View.GONE;

                if (isExpanded) {
                    scaleY = -1f;
                    bottomDividerVisibility = View.VISIBLE;
                } else {
                    if (groupPosition == getGroupCount() - 1) {
                        elevation = resources.getDimension(R.dimen.size_spacing_xxsmall);
                    }
                }

                openIndicator.setScaleY(scaleY);
                groupView.setElevation(elevation);
                bottomDivider.setVisibility(bottomDividerVisibility);
            }
        }
        return groupView;
    }

    @Override
    public View getChildView(int groupPosition, int childPosition, boolean isLastChild,
        View convertView, ViewGroup parent) {
        ComposeView composeView = convertView != null
            ? (ComposeView) convertView
            : new ComposeView(context);
        ExpandableWorkoutListAdapterHelper.INSTANCE.bind(
            composeView, getChild(groupPosition, childPosition), workoutCardViewModel, navigator);
        return composeView;
    }

    @Override
    public boolean isChildSelectable(int groupPosition, int childPosition) {
        return true;
    }

    /**
     * @return a list of all the workouts currently matching the filter (order is not guaranteed
     * in any way)
     */
    public List<WorkoutHeader> getFilteredWorkouts() {
        return filteredWorkouts;
    }

    public void setWorkouts(List<WorkoutHeader> workouts) {
        setOriginalValues(new ArrayList<>(workouts));
        replaceCurrentWorkouts(workouts);
    }

    public Observable<WorkoutHeader> getAddPhotosClicks() {
        return addPhotosClicks.asObservable();
    }

    private void replaceCurrentWorkouts(List<WorkoutHeader> workouts) {
        filteredWorkouts = new ArrayList<>(workouts);
        monthlyWorkouts.clear();
        expandedGroups.clear();
        for (WorkoutHeader workout : workouts) {
            addWorkoutInOrder(workout);
        }
        Timber.d("Total number of months %d", monthlyWorkouts.size());
        notifyDataSetChanged();
    }

    private int addWorkoutInOrder(WorkoutHeader workout) {
        LocalDateTime workoutStartTime =
            LocalDateTime.ofInstant(Instant.ofEpochMilli(workout.getStartTime()),
                ZoneId.systemDefault());
        Month key = new Month(workoutStartTime.getMonthValue(), workoutStartTime.getYear());
        WorkoutHeaderTreeSet group = monthlyWorkouts.get(key);
        if (group == null) {
            WorkoutHeaderTreeSet newMonth = new WorkoutHeaderTreeSet();
            newMonth.add(workout);
            monthlyWorkouts.put(key, newMonth);
            int groupPos = monthlyWorkouts.headMap(key).size();
            shiftOpenGroupsAfterAdd(groupPos);
            return groupPos;
        } else {
            group.add(workout);
            return monthlyWorkouts.headMap(key).size();
        }
    }

    /**
     * @return the open groups updated with the one where the workout was added
     */
    public Set<Integer> updateWorkout(int oldWorkoutId, WorkoutHeader updatedWorkout) {
        removeWorkoutFromGroups(oldWorkoutId);
        // We also have to replace it from the original list for filtering purposes
        updateWorkoutFromOriginalValues(oldWorkoutId, updatedWorkout);

        ExpandableListAdapterFilter filter = (ExpandableListAdapterFilter) getFilter();
        Set<Integer> oldOpenGroups = new HashSet<>(expandedGroups);
        // Only add it to the current list of workouts being shown if it matches the filter
        boolean matches = filter.matchesCurrentConstraint(updatedWorkout);
        if (matches) {
            int groupToOpen = addWorkoutInOrder(updatedWorkout);
            expandedGroups.add(groupToOpen);
        }
        updateFilteredWorkouts(oldWorkoutId, updatedWorkout, matches);
        // When we call this notify all the groups are collapsed
        notifyDataSetChanged();
        return oldOpenGroups;
    }

    private void updateFilteredWorkouts(int oldWorkoutId, WorkoutHeader updatedWorkout,
        boolean matches) {
        filteredWorkouts.removeIf(next -> next.getId() == oldWorkoutId);
        if (matches) {
            filteredWorkouts.add(updatedWorkout);
        }
    }

    /**
     * Removes the workout with the given id from the list.
     */
    private void removeWorkoutFromGroups(int workoutId) {
        Iterator<Month> monthIt = monthlyWorkouts.keySet().iterator();
        // Iterate through all the months keeping track of the position
        for (int monthPos = 0; monthIt.hasNext(); monthPos++) {
            Month month = monthIt.next();
            WorkoutHeaderTreeSet workoutsInMonth = monthlyWorkouts.get(month);
            Iterator<WorkoutHeader> workoutsIt = workoutsInMonth.iterator();
            // Iterate through all the workouts in the month until we find the
            // one we're looking for
            while (workoutsIt.hasNext()) {
                WorkoutHeader oldWorkout = workoutsIt.next();
                if (oldWorkout.getId() == workoutId) {
                    // Remove the workout from the list
                    workoutsIt.remove();
                    if (workoutsInMonth.size() == 0) {
                        // Remove the month if there are no more workouts in it
                        monthIt.remove();
                        // Update the list of open groups
                        expandedGroups.remove(monthPos);
                        shiftOpenGroupsAfterRemove(monthPos);
                    }
                    return;
                }
            }
        }
    }

    private void updateWorkoutFromOriginalValues(int workoutId, WorkoutHeader updatedWorkout) {
        boolean removed = removeWorkoutFromOriginalValues(workoutId);
        if (removed) {
            getOriginalValues().add(updatedWorkout);
        }
    }

    public void removeWorkout(int workoutId) {
        removeWorkoutFromGroups(workoutId);
        removeWorkoutFromOriginalValues(workoutId);
        updateFilteredWorkouts(workoutId, null, false);
        notifyDataSetChanged();
    }

    private boolean removeWorkoutFromOriginalValues(int workoutId) {
        List<WorkoutHeader> originalValues = getOriginalValues();
        Iterator<WorkoutHeader> iterator = originalValues.iterator();
        while (iterator.hasNext()) {
            WorkoutHeader workoutHeader = iterator.next();
            if (workoutHeader.getId() == workoutId) {
                iterator.remove();
                return true;
            }
        }
        return false;
    }

    /**
     * @param monthPos the group position removed from the workouts list
     */
    private void shiftOpenGroupsAfterRemove(int monthPos) {
        // We removed one group so we need to shift the opened
        // groups values
        HashSet<Integer> newOpenGroups = new HashSet<>(expandedGroups.size());
        for (Integer openGroup : expandedGroups) {
            if (openGroup > monthPos) {
                /*
                 * Let's say that initially we've groups 1,2,3,4 and we remove
                 * 2. Then we need to "shift" 3 and 4 to be 2 and 3 in the
                 * newOpenGroups
                 */
                newOpenGroups.add(openGroup - 1);
            } else {
                newOpenGroups.add(openGroup);
            }
        }
        expandedGroups = newOpenGroups;
    }

    /**
     * Call this method before adding the monthPos to the expandedGroups
     *
     * @param monthPos the group position added to the workouts list
     */
    private void shiftOpenGroupsAfterAdd(int monthPos) {
        // We added one group so we need to shift the opened
        // groups values
        HashSet<Integer> newOpenGroups = new HashSet<>(expandedGroups.size());
        for (Integer openGroup : expandedGroups) {
            if (openGroup >= monthPos) {
                /*
                 * Let's say that initially we've groups 1,2,3,4 and we add 2.
                 * Then we need to "shift" 2, 3 and 4 to be 3, 4 and 5 in the
                 * newOpenGroups
                 */
                newOpenGroups.add(openGroup + 1);
            } else {
                newOpenGroups.add(openGroup);
            }
        }
        expandedGroups = newOpenGroups;
    }

    @Override
    public void onGroupCollapsed(int groupPosition) {
        super.onGroupCollapsed(groupPosition);
        expandedGroups.remove(groupPosition);
    }

    @Override
    public void onGroupExpanded(int groupPosition) {
        super.onGroupExpanded(groupPosition);
        expandedGroups.add(groupPosition);
    }

    public Set<Integer> getExpandedGroups() {
        return expandedGroups;
    }

    @Override
    protected void publishResults(List<WorkoutHeader> filteredObjects) {
        replaceCurrentWorkouts(filteredObjects);
    }

    private static class Month implements Comparable<Month> {
        private final int month;
        private final int year;

        Month(int month, int year) {
            this.month = month;
            this.year = year;
        }

        @Override
        public int compareTo(@NonNull Month another) {
            if (year == another.year) {
                return another.month - this.month;
            } else {
                return another.year - this.year;
            }
        }

        @Override
        public String toString() {
            return month + "-" + year;
        }
    }

    private static class WorkoutHeaderTreeSet extends TreeSet<WorkoutHeader> {
        private static final long serialVersionUID = 1L;

        WorkoutHeaderTreeSet() {
            super((lhs, rhs) -> {
                if (lhs.equals(rhs)) {
                    return 0;
                } else {
                    int result = Long.compare(rhs.getStartTime(), lhs.getStartTime());
                    if (result == 0) {
                        Timber.d("lhs %d and rhs %d have the same start time %d", lhs.getId(),
                            rhs.getId(), rhs.getStartTime());
                        // Arbitrarily we decide the order by id
                        return lhs.getId() - rhs.getId();
                    }
                    return result;
                }
            });
        }
    }
}
