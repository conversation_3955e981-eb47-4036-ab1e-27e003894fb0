package com.stt.android.ui.components;

import android.content.Context;
import android.graphics.drawable.Drawable;
import androidx.appcompat.widget.AppCompatEditText;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import timber.log.Timber;

public class RightDrawableClickableEditText extends AppCompatEditText implements View.OnTouchListener {
    private DrawableTouchListener drawableTouchListener;
    /**
     * We need to hijack the {@link android.view.View} touch listener to dispatch the touch event to the right
     * listener ({@link #touchListener} or {@link #drawableTouchListener}
     */
    private OnTouchListener touchListener;

    public RightDrawableClickableEditText(Context context) {
        super(context);
        init();
    }

    public RightDrawableClickableEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public RightDrawableClickableEditText(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    private void init() {
        // This class will handle the touch events itself to dispatch the events to the right listener
        super.setOnTouchListener(this);
    }

    public void setDrawableTouchListener(DrawableTouchListener listener) {
        this.drawableTouchListener = listener;
    }

    public void removeDrawableTouchListener() {
        this.drawableTouchListener = null;
    }

    @Override
    public void setOnTouchListener(OnTouchListener l) {
        this.touchListener = l;
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        // In case we have a right drawable being shown
        Drawable rightDrawable = getCompoundDrawables()[2];
        if (rightDrawable != null) {
            // Check if the user tapped the right drawable
            boolean tappedX = event.getX() > (getWidth() - getPaddingRight() - rightDrawable.getIntrinsicWidth());
            if (tappedX) {
                // Inform the listener that the drawable was tapped when the user releases the tap
                if (event.getAction() == MotionEvent.ACTION_UP) {
                    if (drawableTouchListener != null) {
                        drawableTouchListener.onDrawableTouch();
                    }
                }
                // Otherwise just swallow the event
                return true;
            }
        }
        // If there's no right drawable or the user didn't tap the drawable then propagate the event to the generic
        // listener
        if (touchListener != null) {
            return touchListener.onTouch(v, event);
        }
        return false;
    }

    @Override
    public void setCompoundDrawables(Drawable left, Drawable top, Drawable right, Drawable bottom) {
        Timber.d("com.stt.android.ui.components.RightDrawableClickableEditText.setCompoundDrawables: left: %s, top: %s, right: %s, bottom: %s", left, top, right, bottom);
        super.setCompoundDrawables(left, top, right, bottom);
    }

    public static interface DrawableTouchListener {
        public void onDrawableTouch();
    }
}
