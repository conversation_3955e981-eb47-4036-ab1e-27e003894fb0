package com.stt.android.ui.workout.widgets;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.stt.android.R;
import com.stt.android.workouts.RecordWorkoutService;
import javax.inject.Inject;

public class EnergyWidget extends SimpleUiUpdateWorkoutWidget {
    public static class BigEnergyWidget extends EnergyWidget {
        @Inject
        public BigEnergyWidget(LocalBroadcastManager localBM) {
            super(localBM);
        }

        @Override
        protected int getLayoutId() {
            return R.layout.big_tracking_widget_with_unit;
        }
    }

    public static class SmallEnergyWidget extends EnergyWidget {
        @Inject
        public SmallEnergyWidget(LocalBroadcastManager localBM) {
            super(localBM);
        }

        @Override
        protected int getLayoutId() {
            return R.layout.small_tracking_widget_with_unit;
        }
    }

    @Inject
    public EnergyWidget(LocalBroadcastManager localBM) {
        super(localBM);
    }

    @Override
    protected void onViewInflated() {
        super.onViewInflated();
        label.setText(com.stt.android.core.R.string.energy_capital);
        unit.setText(com.stt.android.core.R.string.kcal);
        updateValue();
    }

    @Override
    protected void updateValue() {
        super.updateValue();
        RecordWorkoutService rws = serviceConnection.getRecordWorkoutService();
        double energy = rws != null ? rws.getTotalEnergyKCal() : 0.0;
        value.setText(Integer.toString((int) Math.floor(energy)));
        int textColor = getTextColor();
        value.setTextColor(textColor);
        unit.setTextColor(textColor);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.tracking_widget_with_unit;
    }
}
