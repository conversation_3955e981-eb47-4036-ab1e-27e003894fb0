package com.stt.android.ui.utils

import androidx.annotation.MainThread

/**
 * Taken from https://medium.com/androiddevelopers/livedata-with-snackbar-navigation-and-other-events-the-singleliveevent-case-ac2622673150
 *
 * SingleLiveEvent can be used for events, but it does not support multiple observers.
 *
 * LiveEvent class can be used as a wrapper for the live data to provide event-like behaviour as well as
 * support for multiple observers where only a SINGLE OBSERVER is supposed to handle the event.
 */
class LiveEvent<out T>(private val content: T) {

    var hasBeenHandled = false
        private set

    /**
     * Returns the content and marks the event handled.
     */
    @MainThread
    fun getContentIfNotHandled(): T? {
        return if (hasBeenHandled) {
            null
        } else {
            hasBeenHandled = true
            content
        }
    }

    /**
     * Returns the content, even if it's already been handled.
     */
    fun peekContent(): T = content
}
