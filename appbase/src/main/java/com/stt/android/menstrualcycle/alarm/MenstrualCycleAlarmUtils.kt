package com.stt.android.menstrualcycle.alarm

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

object MenstrualCycleAlarmUtils {

    private fun getPredictPendingIntent(
        context: Context,
        menstrualCycleStartDate: LocalDate?
    ): PendingIntent {
        val intent = Intent(context, AlarmNotificationReceiver::class.java).apply {
            action = AlarmNotificationReceiver.ALARM_ACTION_PREDICT
        }
        menstrualCycleStartDate?.let {
            intent.putExtra(
                AlarmNotificationReceiver.EXTRA_MENSTRUAL_CYCLE_START_DATE,
                it.format(DateTimeFormatter.ISO_LOCAL_DATE)
            )
        }
        return PendingIntent.getBroadcast(
            context,
            1,
            intent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )
    }

    fun setPredictAlarm(context: Context, menstrualCycleStartDate: LocalDate) {
        if (!menstrualCycleStartDate.isAfter(LocalDate.now())) return
        // Remind users 2 days before the predicted period start day, at 12:00.
        var alarmDateTime = LocalDateTime.of(menstrualCycleStartDate.plusDays(-2), LocalTime.NOON)
        while (alarmDateTime.isBefore(LocalDateTime.now())) {
            alarmDateTime = alarmDateTime.plusDays(1)
        }
        if (alarmDateTime.isAfter(menstrualCycleStartDate.atStartOfDay())) return

        val pendingIntent = getPredictPendingIntent(context, menstrualCycleStartDate)
        context.alarmManager?.setAndAllowWhileIdle(
            AlarmManager.RTC_WAKEUP,
            alarmDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            pendingIntent
        )
    }

    fun cancelPredictAlarm(context: Context) {
        val pendingIntent = getPredictPendingIntent(context, null)
        context.alarmManager?.cancel(pendingIntent)
    }

    private fun getLogPendingIntent(context: Context): PendingIntent {
        val intent = Intent(context, AlarmNotificationReceiver::class.java).apply {
            action = AlarmNotificationReceiver.ALARM_ACTION_LOG
        }
        return PendingIntent.getBroadcast(
            context,
            2,
            intent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )
    }

    fun setLogAlarm(context: Context, menstrualCycleStartDate: LocalDate) {
        // Reminder to log period 7 days after the predicted period start day, at 12:00.
        val alarmDateTime = LocalDateTime.of(menstrualCycleStartDate.plusDays(7), LocalTime.NOON)

        val pendingIntent = getLogPendingIntent(context)
        context.alarmManager?.setAndAllowWhileIdle(
            AlarmManager.RTC_WAKEUP,
            alarmDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            pendingIntent
        )
    }

    fun cancelLogAlarm(context: Context) {
        val pendingIntent = getLogPendingIntent(context)
        context.alarmManager?.cancel(pendingIntent)
    }
}

private val Context.alarmManager
    get() = getSystemService(Context.ALARM_SERVICE) as? AlarmManager
