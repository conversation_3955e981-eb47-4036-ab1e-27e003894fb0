package com.stt.android.social.badges.friendBadgesList

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.viewModels
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.social.badges.badgesDetail.BadgeDetailActivity
import dagger.hilt.android.AndroidEntryPoint
import kotlin.getValue

@AndroidEntryPoint
class FriendBadgesListActivity : ComponentActivity() {

    private val viewModel: FriendBadgesListViewModel by viewModels()

    private fun handleMyBadgesListEvent(event: FriendBadgesListViewEvent) {
        when (event) {
            is FriendBadgesListViewEvent.Close -> {
                finish()
            }

            is FriendBadgesListViewEvent.OnBadgesClick -> startActivity(
                BadgeDetailActivity.newIntent(this, event.badgesId)
            )
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val userName = intent.getStringExtra(BADGE_FRIEND_USER_NAME)
        if (userName == null) {
            finish()
            return
        }
        viewModel.loadFriendBadgesList(userName)
        setContentWithM3Theme {
            val viewData by viewModel.uiState.collectAsState()

            FriendBadgesListScreen(
                viewData = viewData,
                onEvent = ::handleMyBadgesListEvent
            )
        }
    }

    companion object {
        private const val BADGE_FRIEND_USER_NAME = "badge_friend_user_name"

        fun newIntent(context: Context, userName: String): Intent {
            return Intent(context, FriendBadgesListActivity::class.java)
                .apply {
                    putExtra(BADGE_FRIEND_USER_NAME, userName)
                }
        }
    }
}
