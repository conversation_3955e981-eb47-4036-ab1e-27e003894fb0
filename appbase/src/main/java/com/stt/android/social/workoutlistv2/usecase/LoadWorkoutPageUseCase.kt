package com.stt.android.social.workoutlistv2.usecase

import com.stt.android.controllers.CurrentUserController
import com.stt.android.data.workout.WorkoutRepository
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.user.User
import com.stt.android.home.dashboard.card.WorkoutCardLoader
import com.stt.android.social.workoutlistv2.WorkoutPage
import javax.inject.Inject

class LoadWorkoutPageUseCase @Inject constructor(
    private val workoutRepository: WorkoutRepository,
    private val workoutCardLoader: WorkoutCardLoader,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
    private val currentUserController: CurrentUserController,
) {
    suspend operator fun invoke(
        username: String,
        user: User?,
        page: Int,
        pageSize: Int,
        withMediaOnly: <PERSON>olean,
    ): WorkoutPage {
        val user = user ?: getUserByUsernameUseCase.getUserByUsername(username, true)
        val domainWorkouts =
            workoutRepository.fetchWorkoutsPage(user.username, page, pageSize, withMediaOnly)
        val isOwnWorkout = user.username == currentUserController.username
        val userWorkoutPairs = domainWorkouts.map {
            user to it
        }
        val workoutCardInfos = workoutCardLoader.buildWorkoutCardsByDomainWorkouts(
            userWorkoutPairs = userWorkoutPairs,
            isOwnWorkout = isOwnWorkout,
            includeCover = true,
        )
        return WorkoutPage(
            username = user.username,
            page = page,
            workouts = workoutCardInfos,
        )
    }
}
