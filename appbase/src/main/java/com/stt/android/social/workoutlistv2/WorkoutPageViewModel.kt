package com.stt.android.social.workoutlistv2

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.flatMap
import androidx.paging.insertSeparators
import androidx.paging.map
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.othersWorkoutUpdated
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.remote.UserAgent
import com.stt.android.social.userprofileV2.BaseUserProfileActivity.Companion.KEY_USER
import com.stt.android.social.userprofileV2.BaseUserProfileActivity.Companion.KEY_USER_NAME
import com.stt.android.social.workoutlistv2.data.WorkoutsPagingSource
import com.stt.android.social.workoutlistv2.usecase.LoadWorkoutPageUseCase
import com.stt.android.social.workoutlistv2.usecase.UpdateSingleWorkoutUseCase
import com.stt.android.ui.components.workout.WorkoutCardViewData
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.STTConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@OptIn(ExperimentalCoroutinesApi::class)
@HiltViewModel
class WorkoutPageViewModel @Inject constructor(
    @param:ApplicationContext private val appContext: Context,
    savedStateHandle: SavedStateHandle,
    private val currentUserController: CurrentUserController,
    @param:FeatureTogglePreferences private val featureTogglePrefs: SharedPreferences,
    @param:UserAgent private val userAgent: String,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
    private val loadWorkoutPageUseCase: LoadWorkoutPageUseCase,
    private val updateSingleWorkoutUseCase: UpdateSingleWorkoutUseCase,
    val workoutCardActionsHandler: WorkoutCardActionsHandler,
    private val workoutHeaderController: WorkoutHeaderController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    private val withDataHeader: Boolean = false
    private val username: String = savedStateHandle.get<String>(KEY_USER_NAME)
        ?: savedStateHandle.get<User>(KEY_USER)?.username
        ?: currentUserController.username

    val enabled: Boolean = featureTogglePrefs.getBoolean(
        STTConstants.FeatureTogglePreferences.KEY_ENABLE_WORKOUT_PAGING,
        STTConstants.FeatureTogglePreferences.KEY_ENABLE_WORKOUT_PAGING_DEFAULT
    )

    private val _userFlow = MutableStateFlow<User?>(null)
    private val userFlow: StateFlow<User?> = _userFlow.asStateFlow()

    val workoutPagingDataFlow: Flow<PagingData<WorkoutCardItem>> =
        userFlow.flatMapLatest { user ->
            getWorkoutPager(user)
                .flow
                .map { page -> page.mapToListItem() }
        }.cachedIn(viewModelScope)

    val mediaPagingDataFlow: Flow<PagingData<WorkoutMediaItem>> =
        userFlow.flatMapLatest { user ->
            getWorkoutPager(user, withMediaOnly = true)
                .flow
                .map { page -> page.mapToMediaItem() }
        }.cachedIn(viewModelScope)

    private val _updatedCardFlow = MutableStateFlow<List<WorkoutCardInfo>>(emptyList())
    val updatedCardFlow = _updatedCardFlow.asStateFlow()

    init {
        viewModelScope.launch {
            workoutHeaderController.othersWorkoutUpdated
                .distinctUntilChanged()
                .collect { updateWorkoutByKey(it) }
        }

        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                if (currentUserController.username == username) {
                    currentUserController.currentUser
                } else {
                    getUserByUsernameUseCase.getUserByUsername(username, true)
                }
            }.onSuccess { user ->
                _userFlow.value = user
            }.onFailure { e ->
                Timber.w(e, "Failed to load user")
            }
        }
    }

    private fun PagingData<WorkoutCardInfo>.mapToListItem(): PagingData<WorkoutCardItem> {
        if (!withDataHeader) return map { WorkoutCardItem.Workout(it) }
        return map { WorkoutCardItem.Workout(it) }
            .insertSeparators { before, after ->
                val beforeTime = before?.data?.workoutHeader?.startTime
                val afterTime = after?.data?.workoutHeader?.startTime

                if (afterTime == null) return@insertSeparators null

                val afterYearMonth = TextFormatter.formatYearMonth(appContext, afterTime)
                val beforeYearMonth =
                    beforeTime?.let { TextFormatter.formatYearMonth(appContext, it) }

                if (afterYearMonth != beforeYearMonth) {
                    WorkoutCardItem.Header(afterYearMonth)
                } else null
            }
    }

    private fun PagingData<WorkoutCardInfo>.mapToMediaItem(): PagingData<WorkoutMediaItem> {
        return flatMap { workoutCardInfo ->
            val workoutHeader = workoutCardInfo.workoutHeader
            workoutCardInfo.workoutCardViewData.coverInfo.mapNotNull {
                when (it) {
                    is WorkoutCardViewData.CoverInfo.Map -> null
                    is WorkoutCardViewData.CoverInfo.Image -> WorkoutMediaItem.Photo(
                        it.uri,
                        workoutHeader
                    )

                    is WorkoutCardViewData.CoverInfo.Video -> WorkoutMediaItem.Video(
                        it.uri,
                        workoutHeader,
                        userAgent
                    )
                }
            }
        }
    }

    private fun getWorkoutPager(
        user: User?,
        withMediaOnly: Boolean = false
    ): Pager<Int, WorkoutCardInfo> {
        return Pager(
            config = PagingConfig(
                pageSize = PAGE_SIZE,
                enablePlaceholders = false,
                prefetchDistance = 1,
                initialLoadSize = PAGE_SIZE,
            ),
            pagingSourceFactory = {
                WorkoutsPagingSource(
                    username,
                    user,
                    loadWorkoutPageUseCase,
                    PAGE_SIZE,
                    withMediaOnly,
                )
            }
        )
    }

    private fun updateWorkoutByKey(workoutHeader: WorkoutHeader) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                val updated =
                    updateSingleWorkoutUseCase(_userFlow.filterNotNull().first(), workoutHeader)
                _updatedCardFlow.update { currentList ->
                    currentList.filter { it.workoutHeader.key != workoutHeader.key } + updated
                }
            }
        }
    }

    private companion object {
        const val PAGE_SIZE = 20
    }
}
