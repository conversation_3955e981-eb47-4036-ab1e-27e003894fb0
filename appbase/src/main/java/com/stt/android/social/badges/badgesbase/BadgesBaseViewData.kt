package com.stt.android.social.badges.badgesbase

internal sealed interface BadgesViewData {
    data object Initial : BadgesViewData

    data class Loaded(
        val activityBadgesList: BadgeListByModule,
        val myBadgesList: MyBadgesItem,
    ) : BadgesViewData
}

data class BadgesDisplayItem(
    val isAcquired: Boolean,
    val moduleName: String,
    val badgeName: String?,
    val badgeConfigId: String,
    val badgeIconUrl: String?,
    val acquiredBadgeIconUrl: String?,
    val badgeBackgroundImageUrl: String?,
    val acquisitionTime: Long? = null,
)

typealias BadgeListByModule = Map<String, List<BadgesDisplayItem>>

data class MyBadgesItem(
    val numberOfBadges: Int,
    val recentBadgeImage: String?,
    val hasNewBadges: Boolean = false,
    val recentBadgeId: String?,
    val redPointState: Boolean,
)
