package com.stt.android.social.userprofile

import com.stt.android.social.userprofile.followlist.FollowListType

interface NavigationClickListener {
    fun onSupportClicked()
    fun onSettingsClicked()
    fun onFollowersClicked()
    fun onPartnerServicesClicked()
    fun onFollowCountClicked(followListType: FollowListType, followCount: Int)
    fun onPremiumSubscriptionClicked(isSubscribed: Boolean)
    fun onMyHeadsetClicked()
    fun onContactCustomerServiceClicked()
    fun onChatBotClicked()
    fun onFeedbackClicked()
    fun onPersonalRecord()
    fun onAfterSalesServiceClicked()
    fun onBadgesRecord()
    fun onHeartBeltClicked()
}
