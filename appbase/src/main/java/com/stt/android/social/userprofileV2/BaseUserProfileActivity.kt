package com.stt.android.social.userprofileV2

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import androidx.activity.SystemBarStyle
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScaffoldDefaults
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.helpshift.support.Support
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.core.utils.EventThrottler
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.databinding.ComposeMapSnapshotterBinding
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.user.User
import com.stt.android.featuretoggle.OpenFeatureToggleHandlerV2
import com.stt.android.help.LoadSupportMetadataUseCase
import com.stt.android.home.explore.WorkoutMapNavigator
import com.stt.android.home.settings.SettingsActivity
import com.stt.android.maps.MapSnapshotter
import com.stt.android.social.badges.badgesbase.BadgesActivity
import com.stt.android.social.following.PeopleActivity
import com.stt.android.social.friends.others.OthersFriendsActivity
import com.stt.android.social.personalrecord.PersonalRecordsActivity
import com.stt.android.social.userprofile.NavigationClickListener
import com.stt.android.social.userprofile.UserProfileNavigator
import com.stt.android.social.userprofile.followlist.FollowListType
import com.stt.android.social.userprofileV2.ui.FeatureToggle
import com.stt.android.social.userprofileV2.ui.UserProfileScreen
import com.stt.android.social.workoutlist.AllWorkoutActivity
import com.stt.android.social.workoutlist.AllWorkoutViewModel
import com.stt.android.social.workoutlist.search.SearchWorkoutActivity
import com.stt.android.social.workoutlistv2.WorkoutPageViewModel
import com.stt.android.ui.components.workout.actions.rememberWorkoutCardActionsHandler
import com.stt.android.utils.getLocalizedErrorMessage
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.social.workoutlistv2.search.SearchWorkoutActivity as SearchWorkoutActivityV2

abstract class BaseUserProfileActivity : AppCompatActivity(), NavigationClickListener {
    class Navigator
    @Inject constructor() : UserProfileNavigator {
        override fun openUserProfile(context: Context, username: String) {
            context.startActivity(newStartIntent(context, username, false))
        }
    }

    private val viewModel: UserProfileViewModel by viewModels()
    private val allWorkoutViewModel: AllWorkoutViewModel by viewModels()
    private val workoutPageViewModel: WorkoutPageViewModel by viewModels()

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    protected val clickEventThrottler = EventThrottler()

    @Inject
    lateinit var loadSupportMetadataUseCase: LoadSupportMetadataUseCase

    @Inject
    lateinit var workoutMapNavigator: WorkoutMapNavigator

    private val binding: ComposeMapSnapshotterBinding by lazy {
        ComposeMapSnapshotterBinding.inflate(layoutInflater)
    }
    private val openFeatureToggleHandlerV2: OpenFeatureToggleHandlerV2 by lazy { OpenFeatureToggleHandlerV2() }

    override fun onCreate(savedInstanceState: Bundle?) {
        val isPortrait = resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT
        if (isPortrait) {
            enableEdgeToEdge(statusBarStyle = SystemBarStyle.dark(Color.TRANSPARENT))
        }
        super.onCreate(savedInstanceState)
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                mapSnapshotter.runSnapshotterEngine(this@BaseUserProfileActivity)
            }
        }
        setContentView(binding.root)
        binding.composeView.setContentWithM3Theme {
            Surface {
                val snackbarHostState = remember { SnackbarHostState() }

                val removedMessage = stringResource(R.string.user_removed_tips)
                val reportedMessage = stringResource(R.string.user_reported_tips)
                val duplicatedReportedMessage =
                    stringResource(R.string.user_duplicate_reported_tips)

                val context = LocalContext.current

                var confirmDialogEvent by remember {
                    mutableStateOf<ConfirmDialogEvent?>(null)
                }

                LaunchedEffect(Unit) {
                    viewModel.eventFlow.collect { event ->
                        val message = when (event) {
                            FollowerRemoved -> removedMessage
                            UserReported -> reportedMessage
                            UserDuplicateReported -> duplicatedReportedMessage
                            is UserProfileError -> event.throwable.getLocalizedErrorMessage(context)
                            is ConfirmDialogEvent -> {
                                confirmDialogEvent = event
                                return@collect
                            }
                        }
                        snackbarHostState.showSnackbar(
                            message = message,
                            duration = SnackbarDuration.Short,
                        )
                    }
                }

                FeatureToggle(
                    onFeatureToggleClick = {
                        openFeatureToggleHandlerV2.onClick(this)
                    },
                )

                Scaffold(
                    snackbarHost = { SnackbarHost(hostState = snackbarHostState) },
                    contentWindowInsets = if (isPortrait) WindowInsets.navigationBars else ScaffoldDefaults.contentWindowInsets
                ) { paddingValues ->
                    val workoutCardActionsHandler = rememberWorkoutCardActionsHandler(
                        workoutCardActionsHandler = if (workoutPageViewModel.enabled) {
                            workoutPageViewModel.workoutCardActionsHandler
                        } else {
                            allWorkoutViewModel.workoutCardActionsHandler
                        },
                        fragmentManager = supportFragmentManager,
                        snackbarHostState = snackbarHostState,
                        analyticsSource = AnalyticsPropertyValue.WorkoutDetailsSourceProperty.OTHERS_PROFILE_SCREEN,
                    )

                    UserProfileScreen(
                        viewModel = viewModel,
                        allWorkoutViewModel = allWorkoutViewModel,
                        workoutPageViewModel = workoutPageViewModel,
                        menuList = getSettingMenuList(),
                        onBackClick = ::finish,
                        onEditClick = {
                            startActivity(
                                SettingsActivity.newScreenStartIntent(
                                    this,
                                    R.string.user_settings_category
                                )
                            )
                        },
                        onAllActivityClick = {
                            startActivity(
                                AllWorkoutActivity.newStartIntent(
                                    this,
                                    it.username,
                                    TRACK_PAGE_NAME
                                )
                            )
                        },
                        onPersonalRecordsClick = ::onPersonalRecord,
                        onFollowersClicked = { count ->
                            onFollowCountClicked(FollowListType.FOLLOWERS, count)
                        },
                        onFollowingClicked = { count ->
                            onFollowCountClicked(FollowListType.FOLLOWING, count)
                        },
                        onFollowButtonClickedV2 = {
                            viewModel.onFollowButtonClicked()
                        },
                        onMenuClick = ::onMenuClick,
                        onPremiumSubscriptionClick = ::onPremiumSubscriptionClicked,
                        openWorkout = { workoutHeader, analyticsSource ->
                            rewriteNavigator.navigate(
                                context = this,
                                username = workoutHeader.username,
                                workoutKey = workoutHeader.key,
                                workoutId = null,
                                analyticsSource = analyticsSource,
                            )
                        },
                        onMoreMenuItemClick = ::onMoreMenuClick,
                        onUpdateAvatarClicked = if (viewModel.isCurrentUser) {
                            { intent ->
                                viewModel.updateProfilePicture(intent)
                            }
                        } else null,
                        tempProfilePictureFile = if (viewModel.isCurrentUser) {
                            viewModel.tempProfilePictureFile
                        } else null,
                        onSearchClick = ::onSearchClick,
                        workoutCardActionsHandler = workoutCardActionsHandler,
                        modifier = Modifier
                            .padding(paddingValues),
                        onBadgesClick = ::onBadgesRecord,
                    )
                }

                confirmDialogEvent?.run {
                    ConfirmationDialog(
                        title = stringResource(title),
                        text = stringResource(message),
                        cancelButtonText = stringResource(R.string.cancel),
                        confirmButtonText = stringResource(confirmText),
                        onDismissRequest = { confirmDialogEvent = null },
                        onConfirm = {
                            confirmDialogEvent = null
                            onConfirm()
                        }
                    )
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.reloadUser()
    }

    fun onMenuClick(menu: SettingMenuInfo) {
        when (menu.type) {
            SettingMenuType.HEADPHONES -> onMyHeadsetClicked()
            SettingMenuType.SETTING -> onSettingsClicked()
            SettingMenuType.FIND_PEOPLE -> onFollowersClicked()
            SettingMenuType.FEEDBACK -> onFeedbackClicked()
            SettingMenuType.REPAIR_SERVICE -> onAfterSalesServiceClicked()
            SettingMenuType.PARTNER_SERVICE -> onPartnerServicesClicked()
            SettingMenuType.SUPPORT -> onSupportClicked()
            SettingMenuType.CHAT_BOT -> onChatBotClicked()
            SettingMenuType.CONTACT_CUSTOMER_SERVICE -> onContactCustomerServiceClicked()
            SettingMenuType.HEART_BELT ->  onHeartBeltClicked()
        }
    }

    fun onMoreMenuClick(menu: MoreMenu) {
        viewModel.onMoreMenuClick(menu)
    }

    abstract fun getSettingMenuList(): List<SettingMenuInfo>

    override fun onSupportClicked() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        lifecycleScope.launch {
            runSuspendCatching {
                val apiConfig = loadSupportMetadataUseCase.run()
                Support.showFAQs(this@BaseUserProfileActivity, apiConfig)
            }.onFailure { e ->
                Timber.w(e, "Failed to load valid subscription")
            }
        }
    }

    override fun onSettingsClicked() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        startActivity(SettingsActivity.newStartIntent(this))
    }

    override fun onFollowersClicked() {
        // Find people
        if (!clickEventThrottler.checkAcceptEvent()) return
        startActivity(PeopleActivity.newIntent(this))
    }

    override fun onBadgesRecord() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        viewModel.deleteBadgeRedPoint()
        startActivity(BadgesActivity.newIntent(this, ANALYTICS_USER_PROFILE))
    }

    override fun onPartnerServicesClicked() {
    }

    override fun onFollowCountClicked(
        followListType: FollowListType,
        followCount: Int
    ) {
        if (!clickEventThrottler.checkAcceptEvent()) return
        val username = viewModel.user.username
        if (viewModel.isCurrentUser) {
            when (followListType) {
                FollowListType.FOLLOWING -> startActivity(
                    PeopleActivity.newIntent(
                        context = this,
                        showFollowingTab = true
                    )
                )

                FollowListType.FOLLOWERS -> startActivity(
                    PeopleActivity.newIntent(
                        context = this,
                        showPendingRequests = true
                    )
                )
            }
        } else {
            startActivity(
                OthersFriendsActivity.newStartIntent(
                    context = this,
                    username = username,
                    showFollower = followListType == FollowListType.FOLLOWERS,
                )
            )
        }
    }

    private fun onSearchClick() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        if (workoutPageViewModel.enabled) {
            startActivity(
                SearchWorkoutActivityV2.newStartIntent(
                    this,
                    viewModel.user.username,
                    TRACK_PAGE_NAME,
                )
            )
        } else {
            startActivity(
                SearchWorkoutActivity.newStartIntent(
                    this,
                    viewModel.user.username,
                    TRACK_PAGE_NAME,
                )
            )
        }
    }

    override fun onPremiumSubscriptionClicked(isSubscribed: Boolean) {
    }

    override fun onMyHeadsetClicked() {
    }

    override fun onContactCustomerServiceClicked() {
    }

    override fun onChatBotClicked() {
    }

    protected open fun trackAnalytics(event: String) {
    }

    override fun onFeedbackClicked() {
    }

    override fun onPersonalRecord() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        startActivity(PersonalRecordsActivity.newIntent(this, ANALYTICS_USER_PROFILE))
    }

    override fun onAfterSalesServiceClicked() {
    }

    override fun onHeartBeltClicked() {
    }

    companion object {
        const val KEY_USER = "com.stt.android.KEY_USER"
        const val KEY_USER_NAME = "com.stt.android.KEY_USER_NAME"
        const val KEY_FROM_NOTIFICATION = "com.stt.android.KEY_FROM_NOTIFICATION"
        private const val ANALYTICS_USER_PROFILE = "UserProfile"
        private const val CONFIRM_REVOKE_DIALOG_TAG = "confirm_revoke_dlg"
        private const val CONFIRM_BLOCK_USER_DIALOG_TAG = "confirm_block_user_dlg"
        private const val CONFIRM_REPORT_USER_DIALOG_TAG = "confirm_report_user_dlg"
        private const val CONFIRM_UNBLOCK_USER_DIALOG_TAG = "confirm_unblock_user_dlg"
        private const val USER_REPORTED_DIALOG_TAG = "user_reported_dlg"
        private const val USER_BLOCKED_DIALOG_TAG = "user_blocked_dlg"

        private const val TRACK_PAGE_NAME = "ProfileActivity"

        @JvmStatic
        fun newStartIntent(context: Context): Intent {
            return Intent(context, UserProfileActivity::class.java)
        }

        @JvmStatic
        fun newStartIntent(
            context: Context,
            userName: String,
            fromNotification: Boolean
        ): Intent {
            return Intent(
                context,
                UserProfileActivity::class.java
            )
                .putExtra(KEY_USER_NAME, userName)
                .putExtra(KEY_FROM_NOTIFICATION, fromNotification)
        }

        @JvmStatic
        fun newStartIntent(context: Context, user: User): Intent {
            return Intent(context, UserProfileActivity::class.java).putExtra(KEY_USER, user)
        }
    }
}
