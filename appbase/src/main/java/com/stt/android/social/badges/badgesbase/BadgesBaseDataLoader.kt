package com.stt.android.social.badges.badgesbase

import com.stt.android.social.badges.badgesUseCase.BadgesBaseUseCase
import com.stt.android.social.badges.badgesUseCase.MyBadgesListUseCase
import javax.inject.Inject

class BadgesBaseDataLoader @Inject constructor(
    private val badgesBaseUseCase: BadgesBaseUseCase,
    private val myBadgesListUseCase: MyBadgesListUseCase,
) {
    suspend fun loadBadgeListByModule(): BadgeListByModule {
        val allModules = badgesBaseUseCase.getBadgeConfigs()
        val userBadgeList = badgesBaseUseCase.getUserBadgeList().userHasWonBadges ?: emptyList()

        val userBadgeMap = userBadgeList
            .flatMap { it.userBadges }
            .associateBy { it.badgeConfigId }

        return allModules.associate { module ->
            val moduleName = module.moduleName
            val acquired =
                module.badgeConfigs.filter { userBadgeMap.containsKey(it.badgeConfigId) }
                    .map {
                        BadgesDisplayItem(
                            isAcquired = true,
                            moduleName = moduleName,
                            badgeName = it.badgeName,
                            badgeConfigId = it.badgeConfigId,
                            badgeIconUrl = it.badgeIconUrl,
                            acquiredBadgeIconUrl = it.acquiredBadgeIconUrl,
                            badgeBackgroundImageUrl = it.badgeBackgroundImageUrl,
                            acquisitionTime = userBadgeMap[it.badgeConfigId]?.acquisitionTime
                        )
                    }.sortedByDescending { it.acquisitionTime }
            val notAcquired =
                module.badgeConfigs.filter { !userBadgeMap.containsKey(it.badgeConfigId) }
                    .map {
                        BadgesDisplayItem(
                            isAcquired = false,
                            moduleName = moduleName,
                            badgeName = it.badgeName,
                            badgeConfigId = it.badgeConfigId,
                            badgeIconUrl = it.badgeIconUrl,
                            acquiredBadgeIconUrl = it.acquiredBadgeIconUrl,
                            badgeBackgroundImageUrl = it.badgeBackgroundImageUrl,
                        )
                    }
            moduleName to (acquired + notAcquired)
        }
    }

    suspend fun getMyBadgesInfo(): MyBadgesItem {
        val userBadgeList = myBadgesListUseCase.getUserBadgeList()
        val userWonBadges = userBadgeList.userHasWonBadges ?: emptyList()
        val redPointState = userBadgeList.hasNewBadge ?: false
        val allBadges = userWonBadges.flatMap { it.userBadges }
        val numberOfBadges = allBadges.size
        val recentBadge = allBadges.maxByOrNull { it.acquisitionTime }
        val recentBadgeId = recentBadge?.badgeConfigId
        val recentBadgeImage = recentBadge?.acquiredBadgeIconUrl
        return MyBadgesItem(
            numberOfBadges,
            recentBadgeImage,
            userBadgeList.hasNewBadge == true,
            recentBadgeId,
            redPointState,
        )
    }
}
