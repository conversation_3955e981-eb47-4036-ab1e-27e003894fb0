package com.stt.android.social.workoutlistv2.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.zIndex
import androidx.paging.compose.LazyPagingItems
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.User
import com.stt.android.domain.user.subscription.CurrentPremiumSubscriptionStatus
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.social.friends.Friend
import com.stt.android.social.userprofileV2.DeviceType
import com.stt.android.social.userprofileV2.FollowCountStats
import com.stt.android.social.userprofileV2.MoreMenu
import com.stt.android.social.userprofileV2.WorkoutSummaryStats
import com.stt.android.social.userprofileV2.ui.AvatarFullView
import com.stt.android.social.userprofileV2.ui.BlockedView
import com.stt.android.social.userprofileV2.ui.ProfileHeader
import com.stt.android.social.userprofileV2.ui.ProfileInfo
import com.stt.android.social.userprofileV2.ui.TopActivitiesSection
import com.stt.android.social.workoutlistv2.WorkoutCardItem
import com.stt.android.social.workoutlistv2.WorkoutMediaItem
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import com.stt.android.utils.LocaleUtils

@Composable
fun OtherUserProfileContent(
    user: User,
    workoutPagingItems: LazyPagingItems<WorkoutCardItem>,
    mediaPagingItems: LazyPagingItems<WorkoutMediaItem>,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    modifier: Modifier = Modifier,
    userWorkoutSummaryState: WorkoutSummaryStats? = null,
    measurementUnit: MeasurementUnit = MeasurementUnit.METRIC,
    premiumSubscriptionStatus: CurrentPremiumSubscriptionStatus? = null,
    followCountSummary: FollowCountStats? = null,
    deviceTypeList: List<DeviceType>? = null,
    moreMenuList: List<MoreMenu>? = null,
    friend: Friend? = null,
    blocked: Boolean = false,
    onBackClick: () -> Unit = {},
    onFollowersClicked: (Int) -> Unit = {},
    onFollowingClicked: (Int) -> Unit = {},
    onFollowButtonClicked: () -> Unit = {},
    openWorkout: (WorkoutHeader, String) -> Unit = { _, _ -> },
    onMoreMenuItemClick: (MoreMenu) -> Unit = {},
    onSearchClick: () -> Unit = {},
    updatedCardList: List<WorkoutCardInfo> = emptyList(),
) {
    val lazyListState = rememberLazyListState()
    val context = LocalContext.current

    val location = remember(user.country, user.showLocale) {
        user.country?.takeIf { user.showLocale == true }?.let { country ->
            LocaleUtils.fromCountryCode(country)
                ?.let { LocaleUtils.getDisplayCountry(it, context) }
        } ?: ""
    }

    var selectedPostIndex by remember { mutableStateOf<Int?>(null) }
    var showFullAvatar by remember { mutableStateOf(false) }

    val systemUiController = rememberSystemUiController()
    LaunchedEffect(showFullAvatar) {
        systemUiController.setNavigationBarColor(
            color = if (showFullAvatar) Color.Black else Color.White,
            darkIcons = !showFullAvatar,
        )
    }

    val isUserValid = user.username.isNotEmpty()

    LaunchedEffect(mediaPagingItems.itemCount) {
        if (mediaPagingItems.itemCount == 0) {
            selectedPostIndex = null
        }
    }
    Box(
        modifier = modifier
            .fillMaxSize()
            .narrowContentWithBgColors(
                backgroundColor = MaterialTheme.colorScheme.surface,
                outerBackgroundColor = MaterialTheme.colorScheme.background
            )
    ) {
        ProfileHeader(
            user = user,
            location = location,
            scrollState = lazyListState,
            coverPhotoResId = if (isUserValid) {
                R.drawable.profile_cove_photo_friend_default
            } else null,
            onBackClick = onBackClick,
            isPremium = premiumSubscriptionStatus?.isSubscribed == true,
            moreMenuList = moreMenuList,
            onMoreMenuItemClick = onMoreMenuItemClick,
            onAvatarClick = if (!user.fullSizeProfileImageUrl.isNullOrBlank()) {
                { showFullAvatar = true }
            } else null,
        ) { paddingTop, contentPaddingTop ->
            LazyColumn(
                state = lazyListState,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = paddingTop),
                contentPadding = PaddingValues(
                    top = contentPaddingTop,
                ),
            ) {
                item(key = "profile_info_${user.username}") {
                    ProfileInfo(
                        isCurrentUser = false,
                        username = user.realName ?: user.username,
                        description = user.description,
                        followersSummary = followCountSummary,
                        deviceTypeList = deviceTypeList.takeIf { isUserValid },
                        onFollowersClicked = onFollowersClicked,
                        onFollowingClicked = onFollowingClicked,
                        onFollowButtonClicked = onFollowButtonClicked,
                        friend = friend,
                        blocked = blocked,
                    )
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                }
                if (!blocked && isUserValid) {
                    if (userWorkoutSummaryState?.activityTypeStats?.isNotEmpty() == true) {
                        item(key = "top_activities_${user.username}") {
                            TopActivitiesSection(
                                activities = userWorkoutSummaryState.activityTypeStats,
                                modifier = Modifier.padding(
                                    top = MaterialTheme.spacing.smaller,
                                    bottom = MaterialTheme.spacing.medium,
                                ),
                            )
                        }
                    }
                    stickyHeader(key = "workout_list_${user.username}") {
                        val nestedScrollConnection = remember {
                            object : NestedScrollConnection {
                                override fun onPreScroll(
                                    available: Offset,
                                    source: NestedScrollSource
                                ): Offset {
                                    val canScrollForward = lazyListState.canScrollForward
                                    if (canScrollForward) {
                                        lazyListState.dispatchRawDelta(-available.y)
                                        return available
                                    }
                                    return Offset.Zero
                                }
                            }
                        }
                        WorkoutPageTabContent(
                            workoutPagingItems = workoutPagingItems,
                            mediaPagingItems = mediaPagingItems,
                            workoutCardActionsHandler = workoutCardActionsHandler,
                            workoutSummaryStats = userWorkoutSummaryState,
                            measurementUnit = measurementUnit,
                            onMediaIndexSelected = { index ->
                                selectedPostIndex = index
                            },
                            nestedScrollConnection = nestedScrollConnection,
                            updatedCardList = updatedCardList,
                            onSearchClick = onSearchClick,
                        )
                    }
                } else if (blocked) {
                    item(key = "blocked_${user.username}") {
                        BlockedView(
                            modifier = Modifier.fillMaxWidth(),
                        )
                    }
                }
            }
        }
        selectedPostIndex?.let { index ->
            WorkoutMediaViewerScreen(
                pageItems = mediaPagingItems,
                initialPostIndex = index,
                modifier = Modifier.zIndex(100f),
                onBackClick = {
                    selectedPostIndex = null
                },
                onCheckActivities = {
                    openWorkout(
                        WorkoutHeader.builder()
                            .userName(it.username)
                            .key(it.key)
                            .build(),
                        AnalyticsPropertyValue.WorkoutDetailsSourceProperty.OTHERS_POSTS,
                    )
                }
            )
        }
        if (showFullAvatar) {
            user.fullSizeProfileImageUrl?.let { avatarUrl ->
                AvatarFullView(
                    avatarUrl = avatarUrl,
                    onClose = { showFullAvatar = false },
                )
            } ?: run {
                showFullAvatar = false
            }
        }
    }
}
