package com.stt.android.social.workoutlistv2.search

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.insertSeparators
import androidx.paging.map
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.othersWorkoutUpdated
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.user.User
import com.stt.android.domain.user.follow.IsFolloweeUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.eventtracking.EventTracker
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.social.workoutlist.search.SearchWorkoutActivity.Companion.KEY_USERNAME
import com.stt.android.social.workoutlistv2.WorkoutCardItem
import com.stt.android.social.workoutlistv2.data.SearchWorkoutsPagingSource
import com.stt.android.social.workoutlistv2.usecase.SearchWorkoutPageUseCase
import com.stt.android.social.workoutlistv2.usecase.UpdateSingleWorkoutUseCase
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import com.stt.android.ui.utils.TextFormatter
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@OptIn(ExperimentalCoroutinesApi::class, FlowPreview::class)
@HiltViewModel
class SearchWorkoutViewModel @Inject constructor(
    @param:ApplicationContext private val appContext: Context,
    savedStateHandle: SavedStateHandle,
    private val currentUserController: CurrentUserController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
    private val isFolloweeUseCase: IsFolloweeUseCase,
    private val searchWorkoutPageUseCase: SearchWorkoutPageUseCase,
    private val updateSingleWorkoutUseCase: UpdateSingleWorkoutUseCase,
    private val eventTracker: EventTracker,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    val workoutCardActionsHandler: WorkoutCardActionsHandler,
) : ViewModel() {

    private val _searchQuery = MutableSharedFlow<String>(replay = 1)

    private val username =
        savedStateHandle.get<String>(KEY_USERNAME) ?: currentUserController.username
    private val trackPageName = savedStateHandle.get<String>(AnalyticsEventProperty.PAGE_NAME) ?: ""

    private val _userFlow = MutableStateFlow<User?>(null)
    private val userFlow: StateFlow<User?> = _userFlow.asStateFlow()

    // PagingData flow for search results
    val searchPagingDataFlow: Flow<PagingData<WorkoutCardItem>> =
        combine(
            userFlow,
            _searchQuery.debounce(DELAY_FOR_SEARCH).distinctUntilChanged()
        ) { user, query ->
            user to query
        }
            .flatMapLatest { (user, query) ->
                if (query.trim().isEmpty()) {
                    flow { emit(PagingData.empty()) }
                } else {
                    if (query.isNotBlank()) {
                        eventTracker.trackEvent(
                            eventType = AnalyticsEvent.SEARCH_REQUEST,
                            eventProperties = mapOf(
                                AnalyticsEventProperty.PAGE_NAME to trackPageName,
                                AnalyticsEventProperty.SEARCH_WORD to query,
                            ),
                        )
                    }
                    getWorkoutPager(user, query)
                        .flow
                        .map { page -> page.mapToListItem() }
                }
            }
            .cachedIn(viewModelScope)

    private val _viewState = MutableStateFlow(
        SearchWorkoutViewState("", flow { emit(PagingData.empty()) })
    )
    val viewState: StateFlow<SearchWorkoutViewState> = _viewState.asStateFlow()

    private val _updatedCardFlow = MutableStateFlow<List<WorkoutCardInfo>>(emptyList())
    val updatedCardFlow = _updatedCardFlow.asStateFlow()

    init {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                if (currentUserController.username == username) {
                    currentUserController.currentUser
                } else {
                    getUserByUsernameUseCase.getUserByUsername(
                        username,
                        !isFolloweeUseCase.isFollowee(username)
                    )
                }
            }.onSuccess { user ->
                _userFlow.value = user
            }.onFailure { e ->
                Timber.w(e, "Failed to load user")
            }
        }

        // Update viewState based on search query changes
        _searchQuery
            .debounce(DELAY_FOR_SEARCH)
            .distinctUntilChanged()
            .onEach { query ->
                _viewState.update { currentState ->
                    currentState.copy(
                        keyword = query,
                        dateAndWorkouts = searchPagingDataFlow
                    )
                }
            }
            .launchIn(viewModelScope)

        viewModelScope.launch {
            workoutHeaderController.othersWorkoutUpdated
                .distinctUntilChanged()
                .collect { updateWorkoutByKey(it) }
        }
    }

    fun onQueryChange(query: String) {
        if (_viewState.value.keyword == query) return
        _searchQuery.tryEmit(query)
    }

    private fun getWorkoutPager(
        user: User?,
        query: String,
    ): Pager<Int, WorkoutCardInfo> {
        return Pager(
            config = PagingConfig(
                pageSize = PAGE_SIZE,
                enablePlaceholders = false,
                prefetchDistance = 1,
                initialLoadSize = PAGE_SIZE,
            ),
            pagingSourceFactory = {
                SearchWorkoutsPagingSource(
                    username,
                    user,
                    query,
                    searchWorkoutPageUseCase,
                    PAGE_SIZE
                )
            }
        )
    }

    private fun updateWorkoutByKey(workoutHeader: WorkoutHeader) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                val updated =
                    updateSingleWorkoutUseCase(_userFlow.filterNotNull().first(), workoutHeader)
                _updatedCardFlow.update { currentList ->
                    currentList.filter { it.workoutHeader.key != workoutHeader.key } + updated
                }
            }
        }
    }

    private fun PagingData<WorkoutCardInfo>.mapToListItem(): PagingData<WorkoutCardItem> {
        return map { WorkoutCardItem.Workout(it) }
            .insertSeparators { before, after ->
                val beforeTime = before?.data?.workoutHeader?.startTime
                val afterTime = after?.data?.workoutHeader?.startTime

                if (afterTime == null) return@insertSeparators null

                val afterYearMonth = TextFormatter.formatYearMonth(appContext, afterTime)
                val beforeYearMonth =
                    beforeTime?.let { TextFormatter.formatYearMonth(appContext, it) }

                if (afterYearMonth != beforeYearMonth) {
                    WorkoutCardItem.Header(afterYearMonth)
                } else null
            }
    }

    private companion object {
        const val DELAY_FOR_SEARCH = 500L
        const val PAGE_SIZE = 20
    }
}
