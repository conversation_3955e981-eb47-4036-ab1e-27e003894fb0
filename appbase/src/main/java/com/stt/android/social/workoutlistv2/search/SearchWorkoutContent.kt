package com.stt.android.social.workoutlistv2.search

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.paging.compose.collectAsLazyPagingItems
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.widgets.M3SearchBar
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.social.workoutlistv2.ui.WorkoutCardPageContent
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler

@Composable
fun SearchWorkoutContent(
    viewState: SearchWorkoutViewState,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    onBackClick: () -> Unit,
    onQueryChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    updatedCardList: List<WorkoutCardInfo> = emptyList(),
) {
    val pagingItems = viewState.dateAndWorkouts.collectAsLazyPagingItems()
    Column(modifier = modifier) {
        M3SearchBar(
            query = viewState.keyword,
            onQueryChange = onQueryChange,
            placeholder = stringResource(R.string.search_workouts_hint),
            onCancel = onBackClick,
            cancelText = stringResource(R.string.cancel),
            modifier = Modifier.background(MaterialTheme.colorScheme.surface),
        )
        Box(
            modifier = Modifier
                .fillMaxSize()
                .narrowContentWithBgColors(
                    backgroundColor = MaterialTheme.colorScheme.surface,
                    outerBackgroundColor = MaterialTheme.colorScheme.background,
                )
        ) {
            if (viewState.keyword.isNotBlank()) {
                WorkoutCardPageContent(
                    pageItems = pagingItems,
                    workoutCardActionsHandler = workoutCardActionsHandler,
                    updatedCardList = updatedCardList,
                    emptyContent = {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 200.dp),
                            contentAlignment = Alignment.TopCenter,
                        ) {
                            Text(
                                text = stringResource(R.string.search_phone_contacts_empty),
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.secondary,
                            )
                        }
                    },
                )
            }
        }
    }
}
