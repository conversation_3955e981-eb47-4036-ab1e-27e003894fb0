package com.stt.android.help

import android.app.Application
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.helpshift.Core
import com.helpshift.InstallConfig
import com.helpshift.support.Support
import com.stt.android.R
import com.stt.android.notifications.FCMUtil
import java.util.Locale

object HelpshiftInitializer {
    // For now this needs to be called on the Main thread, because
    // - CurrentUserController constructor calls Helpshift methods
    // - 'Helpshift doesn't like it' when called from background thread (see comment below)
    fun initHelpshift(application: Application) {
        try {
            val installConfig =
                InstallConfig.Builder().setNotificationIcon(R.drawable.icon_notification)
                    .build()
            Core.init(Support.getInstance())
            val domainName = application.getString(R.string.helpshift_domain_name)
            val apiKey = application.getString(R.string.helpshift_api_key)
            val appId = application.getString(R.string.helpshift_app_id)

            // The following call touches the disk (opens a Database) so it should be in the
            // background but we can't because Helpshift doesn't like it
            Core.install(application, apiKey, domainName, appId, installConfig)
            // injection hasn't happened yet
            val fcmToken = FCMUtil.getFcmToken(application)
            // Send FCM token if it's already saved and onTokenRefresh hasn't been called
            if (fcmToken != null) {
                Core.registerDeviceToken(application, fcmToken)
            }
            Support.setSDKLanguage(Locale.getDefault().toString())
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }
}
