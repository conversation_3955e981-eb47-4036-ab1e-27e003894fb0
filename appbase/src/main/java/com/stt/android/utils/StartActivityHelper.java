package com.stt.android.utils;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import androidx.annotation.NonNull;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public abstract class StartActivityHelper {
    private static final String YOUTUBE_PKG_NAME = "com.google.android.youtube";
    private static final String CHROME_PKG_NAME = "com.android.chrome";

    /**
     * Starts an activity to handle for the given URI that we want to open externally.
     *
     * @return true if the activity is started, or false otherwise.
     */
    public static boolean startActivityExternally(@NonNull Activity activity, @NonNull Uri uri) {
        // There's a possibility this will result in a crash if user has an app that registers an
        // unexported IntentFilter for the url. More details:
        // https://commonsware.com/blog/2014/04/30/if-your-activity-has-intent-filter-export-it.html

        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setData(uri);
        forceIntentPackageByLink(activity.getPackageManager(), intent);
        try {
            activity.startActivity(intent);
            return true;
        } catch (Throwable e) {
            FirebaseCrashlytics.getInstance().log("Failed to start activity. Trying with BROWSABLE category");
            FirebaseCrashlytics.getInstance().recordException(new Throwable("Failed to start activity", e));
            // Let's try once again but forcing BROWSABLE category
            intent.addCategory(Intent.CATEGORY_BROWSABLE);
            try {
                activity.startActivity(intent);
                return true;
            } catch (Throwable e1) {
                FirebaseCrashlytics.getInstance().log("Failed to start activity with BROWSABLE category");
                FirebaseCrashlytics.getInstance().recordException(
                    new Throwable("Failed to start activity intent with BROWSABLE", e1));
            }
        }

        return false;
    }

    private static void forceIntentPackageByLink(PackageManager packageManager, Intent intent) {
        List<ResolveInfo> resolveInfos =
            packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY);
        Set<String> resolvedPackageNames = new HashSet<>();
        for (int i = 0; i < resolveInfos.size(); i++) {
            ResolveInfo info = resolveInfos.get(i);
            final String packageName = info.activityInfo.packageName;
            resolvedPackageNames.add(packageName);
        }
        // If youtube is able to handle the intent then use it (so we avoid asking the user what
        // app to select)
        if (resolvedPackageNames.contains(YOUTUBE_PKG_NAME)) {
            intent.setPackage(YOUTUBE_PKG_NAME);
        } else if (resolvedPackageNames.contains(CHROME_PKG_NAME)) {
            intent.setPackage(CHROME_PKG_NAME);
        }
    }
}
