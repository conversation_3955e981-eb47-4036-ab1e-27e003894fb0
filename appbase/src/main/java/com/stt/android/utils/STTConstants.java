package com.stt.android.utils;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import androidx.annotation.IntDef;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

@SuppressLint("NewApi")
public abstract class STTConstants {
    public static final Boolean DEBUG = BuildConfig.DEBUG;
    public static final Boolean DEBUG_NOTIFICATION = DEBUG && false;

    /**
     * Flag indicating if the current device should support Active subscription features.
     * <p><em>NOTE</em>: its value is set in {@link com.stt.android
     * .STTApplication#setActiveSubscriptionsSupport()}</p>
     */
    public static boolean SUPPORTS_ACTIVE_SUBSCRIPTIONS;

    /**
     * The current user is always stored with the ID 1 in the local database
     */
    public static final int CURRENT_USER_DEFAULT_ID = 1;

    /**
     * Intent action used to notify about changes in the A/B comparison graph.
     */
    public static final String COMPARISON_MATCH_CHANGED =
        "com.stt.android.COMPARISON_MATCH_CHANGED";
    public static final String COMPARISON_MATCH_CHANGED_CURRENT =
        "COMPARISON_MATCH_CHANGED_CURRENT";
    public static final String COMPARISON_MATCH_CHANGED_TARGET = "COMPARISON_MATCH_CHANGED_TARGET";

    @Deprecated
    public static final int AXIS_WORKOUT_COMPARISON = 7;

    public static final String DIRECTORY_PICTURES = "Pictures";
    public static final String DIRECTORY_VIDEOS = "Videos";
    public static final String DIRECTORY_WORKOUTS = "Workouts";
    public static final String DIRECTORY_ROUTES = "Routes";
    public static final String DIRECTORY_TILES = "Tiles";
    public static final String DIRECTORY_MISC = "Misc";
    public static final String DIRECTORY_VIDEO_CACHE = "Video_cache";

    public static final String WORKOUT_FILENAME_PREFIX = "workout_";
    public static final String SPORTIE_FILENAME_PREFIX = "sportie_";

    public static abstract class DefaultPreferences {
        /**
         * Key used in shared prefs to fetching/storing last feed sync timestamp epoch ms
         */
        public static final String KEY_LAST_FEED_SYNC_EPOCH_MS = "com.stt.android.prefs_last_feed_sync_epoch_ms";

        /**
         * Key used in shared prefs to fetching/storing last session sync timestamp epoch ms
         */
        public static final String KEY_LAST_STARTUP_SYNC_EPOCH_MS = "com.stt.android.prefs_last_sync_epoch_ms";

        /**
         * Key used in shared prefs to check that Refreshables has run at least once after login
         */
        public static final String KEY_HAS_RUN_INITIAL_REFRESH = "com.stt.android.prefs_has_run_initial_refresh";

        public static final String KEY_PREFERENCES_VERSION = "preferences_version";
        public static final int CURRENT_PREFERENCES_VERSION = 5;

        public static final String KEY_APP_VERSION_ID = "app_version";
        public static final String KEY_INSTALLATION_UNIQUE_ID = "installation_unique_id";
        public static final String KEY_PASSWORD_RESET_OR_DELETE_REQUESTED_AT = "password_reset_requested_at";
        public static final String KEY_APP_STARTS = "application_starts";
        public static final String KEY_DAYS_SINCE_INSTALL = "days_since_install";
        public static final String KEY_APP_OPEN_FIRST_TIME = "app_open_first_time";

        public static final String KEY_OPT_IN_DISPLAYED = "policy_update_opt_in";

        public static final String KEY_REFERENCE_PRESSURE = "reference_pressure";
        public static final String KEY_REFERENCE_PRESSURE_TIMESTAMP =
            "reference_pressure_timestamp";

        public static final String KEY_DASHBOARD_SECONDARY_PAGE = "dashboard_secondary_page";
        public static final String KEY_DASHBOARD_FEED_CONTENT_HASH = "dashboard_feed_content_hash";
        public static final String KEY_DASHBOARD_SWIPE_FOR_FEEDS_SHOWN =
            "dashboard_swipe_for_feeds_shown";
        public static final String KEY_DASHBOARD_INITIAL_SYNC_DONE = "dashboard_initial_sync_done";

        public static final String KEY_DASHBOARD_LAST_SHOW_WORKOUT_ID_ME = "dashboard_last_show_workout_id_me";

        public static final String KEY_DASHBOARD_LAST_SHOW_WORKOUT_ID_FOLLOWING = "dashboard_last_show_workout_id_following";

        public static final String KEY_CRASH_COUNT = "key_crash_count";
        // in-app rating prompt dialog has been removed
        @Deprecated
        public static final String KEY_ALREADY_ASKED_TO_RATE = "key_already_asked_to_rate";

        // tutorial dialogs
        public static final String KEY_HAS_SHOWN_CUSTOMIZATION_TUTORIAL =
            "KEY_HAS_SHOWN_CUSTOMIZATION_TUTORIAL";
        public static final String KEY_HAS_CUSTOMIZED_LAYOUT = "KEY_HAS_CUSTOMIZED_LAYOUT";
        public static final String KEY_HAS_SHOWN_GHOST_EXPLANATION_DIALOG =
            "show_ghost_explanation_dialog";
        public static final String KEY_HAS_SHOWN_FOLLOW_ROUTE_EXPLANATION_DIALOG =
            "show_follow_route_explanation_dialog";

        public static final String KEY_WORKOUT_TREND_ROUTE_SELECTION =
            "workout_trend_route_selection";

        public static final String KEY_FIRST_OPEN_EXPLORE_TAB = "first_open_explore_tab";

        // server timestamp when user's workouts are last modified
        public static final String KEY_CURRENT_USER_WORKOUTS_LAST_MODIFIED_TIMESTAMP =
            "CURRENT_USER_WORKOUTS_LAST_MODIFIED";
        // server timestamp when user's (in-app) notification are last fetched
        public static final String KEY_NOTIFICATION_LAST_CHECKED_TIMESTAMP = "LAST_FEED_CHECK";

        public static final String KEY_RECENT_ACTIVITY_IDS = "KEY_RECENT_ACTIVITY_IDS";
        public static final String KEY_LAP_SETTING_PREFIX = "lap_";
        public static final String KEY_VOICE_FEEDBACK_SETTING_PREFIX = "voice_feedback_";

        public static final String KEY_EXPERIMENT_DASHBOARD_TOOLTIP_START =
            "experiment_android_dashboard_tooltip_start";
        public static final String KEY_EXPERIMENT_WORKOUT_COUNT_BEFORE_SHOWING_ADS =
            "experiment_workout_count_before_showing_ads";
        public static final String KEY_HAS_USED_ROUTE_PLANNER = "has_used_route_planner";
        public static final String KEY_EXPERIMENT_KEEP_BOTTOMBAR =
            "experiment_bottombar";

        public static final String KEY_SESSION_STATUS = "KEY_SESSION_STATUS";
        public static final String KEY_SESSION_STATUS_WARNING = "KEY_SESSION_STATUS_WARNING";
        public static final String LAST_USED_ROUTING_MODE = "last_used_routing_mode";
        public static final String KEY_BACKEND_TIME_OFFSET = "KEY_BACKEND_TIME_OFFSET";

        public static final String FCM_TOKEN = "fcm_token";
        public static final String FCM_TOKEN_SYNCED_TO_BACKEND = "fcm_token_synced_to_backend_";

        public static final String KEY_JUST_SIGNED_UP_USER = "KEY_JUST_SIGNED_UP_USER";

        public static final String KEY_SCHEDULE_SUMMARY_EXTENSION_UPDATE_WITH_ZAPPS = "SCHEDULE_SUMMARY_EXTENSION_UPDATE_WITH_ZAPPS";
        public static final String KEY_DASHBOARD_INDEX = "KEY_DASHBOARD_INDEX";
        public static final String KEY_DASHBOARD_NUM_PAGES = "KEY_DASHBOARD_NUM_PAGES";
        public static final String KEY_FIRST_LAUNCH_TERMS_ACCEPTED = "KEY_FIRST_LAUNCH_TERMS_ACCEPTED";

        public static final String KEY_FIRST_LAUNCH_TERMS_ACCEPTED_MIGRATED_TO_FILE = "KEY_FIRST_LAUNCH_TERMS_ACCEPTED_MIGRATED_TO_FILE";


        public static final String KEY_LOCATION_PERMISSION_DISCLOSURE_POPUP_SHOWN = "KEY_LOCATION_PERMISSION_DISCLOSURE_POPUP_SHOWN";

        public static final String KEY_POST_NOTIFICATION_HAS_ASKED =
            "key_post_notification_has_asked";

        public static final String KEY_CHECKED_PLAYSTORE_INSTALL_REFERRER =
            "key_checked_playstore_install_referrer";

        public static final String KEY_LAST_FILTER_TAG = "KEY_LAST_FILTER_TAG";

        public static final String KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS =
            "KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS";
        public static final boolean KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT = true;

        public static final String KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD =
            "KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD";
        public static final boolean KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD_DEFAULT = true;

        public static final String KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD =
            "KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD";
        public static final boolean KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD_DEFAULT = true;

        public static final String KEY_MENSTRUAL_CYCLE_STOP_TRACKING_TIMESTAMP =
            "KEY_MENSTRUAL_CYCLE_STOP_TRACKING_TIMESTAMP";

        public static final String KEY_GRAPHHOPPER_BASE_URL = "KEY_GRAPHHOPPER_BASE_URL";

        public static final String KEY_NAVIGATE_ONGOING = "KEY_NAVIGATE_ONGOING";
    }

    public static abstract class BluetoothPreferences {
        public static final String PREFS_NAME = "BT_SHARED_PREFS";

        public static final String KEY_LAST_HR_ADDR = "LAST_HR_ADDR";
        public static final String KEY_LAST_HR_NAME = "LAST_HR_NAME";
        public static final String KEY_LAST_HR_TYPE = "LAST_HR_TYPE";
        public static final String KEY_LAST_CADENCE_ADDR = "LAST_CADENCE_ADDR";
        public static final String KEY_LAST_CADENCE_NAME = "LAST_CADENCE_NAME";
        public static final String KEY_LAST_CADENCE_TOTAL_WHEEL_REVOLUTION =
            "LAST_CADENCE_TOTAL_WHEEL_REVOLUTION";
    }

    public static abstract class SuuntoPreferences {
        public static final String PREFS_NAME = "SUUNTO_SHARED_PREFS";
        public static final String KEY_SUUNTO_PAIRED_WATCH_MODEL =
            "key_suunto_paired_watch_model";
        public static final String KEY_SUUNTO_PAIRED_WATCH_FW_VERSION =
            "key_suunto_paired_watch_fw_version";
        public static final String KEY_SUUNTO_PAIRED_WATCH_HW_VERSION =
            "key_suunto_paired_watch_hw_version";
        public static final String KEY_SUUNTO_PAIRED_WATCH_SERIAL_NUMBER =
            "key_suunto_paired_watch_serial_number";
        public static final String KEY_SUUNTO_PAIRED_WATCH_MANUFACTURER =
            "key_suunto_paired_watch_manufacturer";
        public static final String KEY_SUUNTO_PAIRED_WATCH_SKU =
            "KEY_SUUNTO_PAIRED_WATCH_SKU";

        // Headset device info keys
        public static final String KEY_SUUNTO_PAIRED_HEADSET_MODEL =
            "key_suunto_paired_headset_model";
        public static final String KEY_SUUNTO_PAIRED_HEADSET_FW_VERSION =
            "key_suunto_paired_headset_fw_version";
        public static final String KEY_SUUNTO_PAIRED_HEADSET_HW_VERSION =
            "key_suunto_paired_headset_hw_version";
        public static final String KEY_SUUNTO_PAIRED_HEADSET_SERIAL_NUMBER =
            "key_suunto_paired_headset_serial_number";
        public static final String KEY_SUUNTO_PAIRED_HEADSET_MANUFACTURER =
            "key_suunto_paired_headset_manufacturer";
        public static final String KEY_SUUNTO_LAST_HEADSET_PAIR_EVENT_SERIAL =
            "key_suunto_last_headset_pair_event_serial";

        // Placeholder for watch info in case not available
        public static final String KEY_SUUNTO_WATCH_INFO_DEFAULT_VALUE = "";

        public static final String KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION =
            "suunto_watch_weekly_target_duration";
        public static final int KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION_DEFAULT =
            3 * 60 * 60; // 3 hours = 10800 seconds

        public static final String KEY_SUUNTO_WATCH_COACH_ENABLED =
            "suunto_watch_coach_enabled";
        public static final String KEY_WATCH_WEEKLY_TARGET_SYNC_NEEDED =
            "suunto_watch_weekly_target_duration_sync_needed";
        public static final String KEY_WATCH_COACH_ENABLED_SYNC_NEEDED =
            "suunto_watch_coach_enabled_sync_needed";
        public static final String KEY_SUUNTO_WATCH_NOTIFICATIONS_ALLOWED_AT_LEAST_ONCE =
            "key_suunto_watch_notifications_allowed_at_least_once";
        public static final String KEY_SUUNTO_BACKEND_SYNCED_ITEMS_SENT_TO_CONNECTIVITY_STATUS =
            "key_suunto_backend_synced_items_sent_to_connectivity_status";
        public static final String KEY_SUUNTO_BACKEND_SYNCED_AT_LEAST_ONCE_STATUS =
            "key_suunto_backend_synced_at_least_once_status";
        public static final String KEY_SUUNTO_247_ANALYTICS_LAST_SENT_TIME =
            "key_suunto_247_analytics_last_sent_time";
        public static final String KEY_SUUNTO_LAST_PAIR_EVENT_SERIAL =
            "key_suunto_last_pair_event_serial";
        public static final String KEY_HOME_VIEW_HAS_REQUESTED_NOTIFICATION_PREFERENCES =
            "key_home_view_has_requested_notification_preferences";
        public static final String KEY_SUUNTO_DIVE_AUTOLOCATION_ENABLED =
            "key_suunto_dive_autolocation_enabled";
        public static final String KEY_SMOOTH_PAIRING_TRANSITION_DONE_DEVICE_NAMES =
            "key_smooth_pairing_transition_done_device_names";
        public static final String KEY_FIRSTBEAT_SLEEP_THRESHOLD =
            "key_firstbeat_sleep_threshold";
        public static final String KEY_HAS_SEEN_NO_WATCH_PAIRED_QUESTION =
            "key_has_seen_no_watch_paired_question";
        public static final String KEY_SHOULD_START_DEVICE_ACTIVITY_AFTER_LOGIN =
            "key_should_navigate_to_device_activity";
        public static final String KEY_HAS_OPENED_WATCH_WIDGET_CUSTOMIZATION =
            "key_has_opened_watch_widget_customization";
        public static final String KEY_HAS_OPENED_OFFLINE_MAPS =
            "key_has_opened_download_offline_maps";
        // Use only for analytics as it is not 100% accurate in all cases
        public static final String KEY_WATCH_HAS_WIFI_SETUP_FOR_ANALYTICS =
            "key_watch_has_wifi_setup_for_analytics";
        public static final String KEY_SUUNTO_PLUS_STORE_FIRST_TIME_USE_DIALOG_SHOWN =
            "key_suunto_plus_store_first_time_use_dialog_shown";
        public static final String KEY_ADD_PREFABRICATED_WATCH_FACE_TO_LIBRARY_STATUS =
            "key_add_prefabricated_watch_face_to_library_status";
        public static final String KEY_PREVIOUS_SOFTWARE_VERSION =
            "key_previous_software_version_%s";
        public static final String KEY_HEADSET_FILE_STATUS = "key_file_status";
        public static final int FILE_STATUES_DEFAULT = -9999;
        public static final long SYNC_HEADPHONE_WORKOUT_TIME_DEFAULT = 0L;
        public static final String KEY_WATCH_WEARING_DIRECTION =
            "key_watch_wearing_direction";
        public static final String KEY_WATCH_FILE_STATUS = "key_watch_file_status";
        public static final String KEY_SYNC_HEADPHONE_WORKOUT_TIME = "key_sync_headphone_workout_time";

        public static final String KEY_PERSONAL_RECORD = "KEY_PERSONAL_RECORD";

        public static final String KEY_PERSONAL_RECORD_TIMESTAMP = "KEY_PERSONAL_RECORD_TIMESTAMP";
        public static final String KEY_HAS_SHOWN_BACKGROUND_LOCATION_FOR_WEATHER_TUTORIAL =
            "KEY_HAS_SHOWN_BACKGROUND_LOCATION_FOR_WEATHER_TUTORIAL";
        public static final String KEY_CONNECT_HEADPHONE_TIME = "key_connect_headphone_time";

        public static final String KEY_NEW_WORKOUT_SYNCED_MESSAGE_ORDER = "key_new_workout_push_message_order";
        public static final String KEY_NEW_WORKOUT_SYNCED_MESSAGE_TIMESTAMP = "key_new_workout_push_message_timestamp";
    }

    public static abstract class MapPreferences {
        public static final String MAP_PROVIDER_PREFS_NAME = "MAP_PROVIDER_PREFS";
        public static final String KEY_MAP_PROVIDER_VERSION = "KEY_MAP_PROVIDER_VERSION";
        public static final String KEY_MAP_PROVIDER_LAST_FETCHED = "MAP_PROVIDER_LAST_FETCHED";

        public static final String MAP_PREFS_NAME = "prefs_map";
        public static final String KEY_ZOOM_LEVEL = "key_zoom_level";
        public static final float DEFAULT_ZOOM_LEVEL = 14.0F;
        public static final String KEY_BEARING = "key_bearing";
        public static final boolean DEFAULT_BEARING = true;
        public static final String KEY_SELECTED_HEATMAP = "key_selected_heatmap";
        public static final String KEY_SELECTED_ROAD_SURFACES = "key_selected_road_surfaces";
        public static final String KEY_HIDE_CYCLING_FORBIDDEN_ROADS = "key_hide_cycling_forbidden_roads";
        public static final String KEY_SELECTED_MY_TRACKS_GRANULARITY= "key_selected_my_tracks_granularity";
        public static final String KEY_SELECTED_MY_TRACKS_CUSTOM_START_DATE = "key_selected_my_tracks_custom_start_date";
        public static final String KEY_SELECTED_MY_TRACKS_CUSTOM_END_DATE = "key_selected_my_tracks_custom_end_date";
        public static final String KEY_SHOW_POIS = "key_show_pois";
        public static final String KEY_TURN_BY_TURN_ENABLED = "key_turn_by_turn_enabled";
        public static final String KEY_MAP_3D_ENABLED = "key_map_3d_enabled";

        public static final String EXPLORE_MAP_PREFS_NAME = "prefs_explore_map";
        public static final String KEY_CAMERA_LATITUDE = "CAMERA_LATITUDE";
        public static final String KEY_CAMERA_LONGITUDE = "CAMERA_LONGITUDE";
        public static final String KEY_CAMERA_ZOOM = "CAMERA_ZOOM";
        public static final String KEY_CAMERA_BEARING = "CAMERA_BEARING";
        public static final String KEY_CAMERA_TILT = "CAMERA_TILT";
        public static final String KEY_SHOW_MAP_DISCLAIMER = "KEY_SHOW_MAP_DISCLAIMER";
        public static final String KEY_ANIMATE_MY_TRACKS_OVERRIDE = "KEY_ANIMATE_MY_TRACKS_OVERRIDE";
        public static final String KEY_EXPLORE_MAP_CENTERED_TO_USER_LOCATION = "KEY_EXPLORE_MAP_CENTERED_TO_USER_LOCATION";
        public static final String KEY_USER_HAS_ENTERED_EXPLORE_SCREEN = "KEY_USER_HAS_ENTERED_EXPLORE_SCREEN";
        public static final String KEY_SEEN_AVALANCHE_INFO = "KEY_SEEN_AVALANCHE_INFO";
        public static final String KEY_SHOW_LAP_MARKER = "key_show_lap_marker";
        public static final String KEY_WORKOUT_ANALYSIS_CHART_GUIDE = "key_workout_analysis_chart_guide";
        public static final String KEY_SELECTED_POPULAR_ROUTE_ACTIVITY_TYPES = "key_selected_popular_routes_activity_types";
    }

    public static abstract class MiscPreferences {
        public static final String PROMOTION_URLS_PREFS_NAME = "PROMOTION_URLS_PREFS";
        public static final String KEY_PROMOTION_URLS_LAST_FETCHED = "PROMOTION_URLS_LAST_FETCHED";
        public static final String KEY_DASHBOARD_USER_ACTIVE_ANALYTICS_EVENT_SENT = "dashboard_user_active_analytics_event_sent";
        public static final String KEY_PEOPLE_FOLLOWS_ANALYTICS_EVENT_SENT = "people_follows_analytics_event_sent";
        public static final String KEY_PEOPLE_HAS_FOLLOWER_ANALYTICS_EVENT_SENT = "people_has_follower_analytics_event_sent";
    }

    public static abstract  class WorkoutSharingPreferences {
        public static final String PREFS_NAME = "WORKOUT_SHARING_PREFS";
        public static final String MANUAL_WORKOUT_SHARING_OPTION = "MANUAL_WORKOUT_SHARING_OPTION";
    }

    public static abstract class DefaultWorkoutSharingPreferences {
        public static final String PREFS_NAME = "DEFAULT_WORKOUT_SHARING_PREFS";

        public static final String KEY_SHARE = "KEY_SHARE";
        public static final String SHARE_PUBLIC = "SHARE_PUBLIC";
        public static final String SHARE_FOLLOWERS = "SHARE_FOLLOWERS";
        public static final String SHARE_PRIVATE = "SHARE_PRIVATE";
        public static final String SHARE_REMEMBER_LAST_USED = "SHARE_REMEMBER_LAST_USED";
    }

    public static abstract class WorkoutMapPlaybackPreferences {
        public static final String PREFS_NAME = "WORKOUT_MAP_PLAYBACK_PREFS";
        public static final String USERNAME = "USERNAME";
        public static final String START_TIME = "START_TIME";
        public static final String LOCATION = "LOCATION";
        public static final String DEVICE = "DEVICE";
        public static final String GRAPH_TYPE = "GRAPH_TYPE";
        public static final String MAP_TYPE = "MAP_TYPE";
    }

    public static abstract class WorkoutPagePreferences {
        public static final String WORKOUT_PAGES_LAYOUTS_PREFS_NAME = "WORKOUT_PAGES_LAYOUTS_PREFS";
        public static final String KEY_WORKOUT_PAGES_LAYOUTS_VERSION =
            "WORKOUT_PAGES_LAYOUTS_VERSION";
        public static final String KEY_OUTDOOR_WORKOUT_PAGES_AMOUNT = "WORKOUT_PAGES_AMOUNT";
        public static final String KEY_INDOOR_WORKOUT_PAGES_AMOUNT = "INDOOR_WORKOUT_PAGES_AMOUNT";
        public static final String KEY_OUTDOOR_GHOST_WORKOUT_PAGES_AMOUNT =
            "OUTDOOR_GHOST_WORKOUT_PAGES_AMOUNT";
        public static final String KEY_SLOPE_SKI_WORKOUT_PAGES_AMOUNT =
            "SLOPE_SKI_WORKOUT_PAGES_AMOUNT";

        public static final int DEFAULT_OUTDOOR_WORKOUT_PAGES_AMOUNT = 5;
        public static final int DEFAULT_INDOOR_WORKOUT_PAGES_AMOUNT = 2;

        public static final String OUTDOOR_WORKOUT_PAGE_PREFS_NAME_PREFIX = "WORKOUT_PAGE_PREFS_";
        public static final String OUTDOOR_GHOST_WORKOUT_PAGE_PREFS_NAME_PREFIX =
            "GHOST_WORKOUT_PAGE_PREFS_";
        public static final String SLOPE_SKI_WORKOUT_PAGE_PREFS_NAME_PREFIX =
            "SLOPE_SKI_PAGE_PREFS_";
        public static final String INDOOR_WORKOUT_PAGE_PREFS_NAME_PREFIX =
            "INDOOR_WORKOUT_PAGE_PREFS_";

        public static final String WORKOUT_PAGE_TAG_PRIMARY = "WORKOUT_PAGE_TAG_PRIMARY";
        public static final String WORKOUT_PAGE_TAG_HEART_RATE = "WORKOUT_PAGE_TAG_HEART_RATE";
        public static final String WORKOUT_PAGE_TAG_CADENCE = "WORKOUT_PAGE_TAG_CADENCE";
        public static final String WORKOUT_PAGE_TAG_STEP_COUNT = "WORKOUT_PAGE_TAG_STEP_COUNT";
    }

    public static abstract class AnalyticsPreferences {
        public static final String ANALYTICS_PREFS_NAME = "ANALYTICS_PREFS";
        public static final String KEY_LAST_TOTAL_WORKOUTS = "LAST_TOTAL_WORKOUTS";
        public static final String KEY_LAST_TOTAL_WORKOUTS_WITHIN_X_DAYS = "LAST_TOTAL_WORKOUTS_%d_DAYS";
        public static final String KEY_LAST_TOTAL_PICTURES = "LAST_TOTAL_PICTURES";
        public static final String KEY_LAST_TOTAL_COMMENTS = "LAST_TOTAL_COMMENTS";
        public static final String KEY_LAST_TOTAL_LIKES = "LAST_TOTAL_LIKES";
        public static final String KEY_LAST_TOTAL_PRIVATE_WORKOUTS = "LAST_TOTAL_PRIVATE_WORKOUTS";
        public static final String KEY_LAST_TOTAL_PUBLIC_WORKOUTS = "LAST_TOTAL_PUBLIC_WORKOUTS";
        public static final String KEY_LAST_TOTAL_FOR_FOLLOWERS_WORKOUTS =
            "LAST_TOTAL_FOR_FOLLOWERS_WORKOUTS";
        public static final String KEY_LAST_TOTAL_DURATION_IN_MINUTES =
            "LAST_TOTAL_DURATION_IN_MINUTES";
        public static final String KEY_LAST_TOTAL_WORKOUTS_WITH_DESCRIPTION =
            "LAST_TOTAL_WORKOUTS_WITH_DESCRIPTION";
        public static final String KEY_LAST_TOTAL_FOLLOWINGS = "LAST_TOTAL_FOLLOWINGS";
        public static final String KEY_LAST_TOTAL_FOLLOWERS = "LAST_TOTAL_FOLLOWERS";
        public static final String KEY_LAST_TOTAL_WORKOUT_DURATION_IN_MINUTES =
            "LAST_TOTAL_WORKOUT_DURATION_IN_MINUTES";
        public static final String KEY_INITIAL_USER_DETAILS_SENT_TO_ANALYTICS =
            "INITIAL_USER_DETAILS_SENT_TO_ANALYTICS";
    }

    public static abstract class InAppReviewPreferences {
        public static final String KEY_TRIGGER_IN_APP_REVIEW = "TRIGGER_IN_APP_REVIEW";
        public static final String KEY_IN_APP_RATING_ALREADY_SHOWN = "KEY_IN_APP_RATING_ALREADY_SHOWN";
        public static final String KEY_NEVER_SHOW_AGAIN_CLICKED = "KEY_NEVER_SHOW_AGAIN_CLICKED";
        public static final String KEY_ASK_ME_LATER_CLICKED = "KEY_ASK_ME_LATER_CLICKED";

        public static final String KEY_TIMESTAMP_WHEN_ASK_ME_LATER_CLICKED =
            "KEY_TIMESTAMP_WHEN_ASK_ME_LATER_CLICKED";

        public static final String KEY_POSITIVE_FEELING_CLICKED = "KEY_POSITIVE_FEELING_CLICKED";
        public static final String KEY_TIMESTAMP_WHEN_POSITIVE_FEELING_CLICKED =
            "KEY_TIMESTAMP_WHEN_POSITIVE_FEELING_CLICKED";

        public static final String SUUNTO_DISABLED_APP_RATING_SUGGESTIONS_VALUE = "SUUNTO_APP_ANDROID";
        public static final String ST_DISABLED_APP_RATING_SUGGESTIONS_VALUE = "SPORTS_TRACKER_ANDROID";

        public static final String IN_APP_REVIEW_FORCE_SCHEDULE = "IN_APP_REVIEW_FORCE_SCHEDULE";
    }

    public static abstract class FeatureTogglePreferences {
        public static final String FEATURE_TOGGLE_PREFS_NAME = "FEATURE_TOGGLE_SHARED_PREFS";

        public static final String KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG = "KEY_ONLY_FOR_TESTING_PURPOSES_HRV_GRAPH";
        public static final boolean KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG_DEFAULT = false;

        public static final String KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG = "KEY_SHOW_SUUNTO_HEADSET_MAC_DISPLAY";
        public static final boolean KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG_DEFAULT = false;

        public static final String KEY_SHOW_SET_WATCH_LOCATION_FOR_DEBUG = "KEY_SHOW_WATCH_DEBUG_LOCATION_OPTION";
        public static final boolean KEY_SHOW_SET_WATCH_LOCATION_FOR_DEBUG_DEFAULT = false;

        public static final String KEY_ENABLE_PREMIUM_FOR_DEBUG = "KEY_ENABLE_PREMIUM";
        public static final boolean KEY_ENABLE_PREMIUM_FOR_DEBUG_DEFAULT = false;

        public static final String KEY_ENABLE_FIELD_TESTER_FOR_DEBUG = "KEY_ENABLE_FIELD_TESTER_FOR_DEBUG";
        public static final boolean KEY_ENABLE_FIELD_TESTER_FOR_DEBUG_DEFAULT = false;

        public static final String KEY_ENABLE_ZONE_SENSE_DEBUG_INFO = "KEY_ENABLE_ZONE_SENSE_DEBUG_INFO";
        public static final boolean KEY_ENABLE_ZONE_SENSE_DEBUG_INFO_DEFAULT = false;

        public static final String KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES = "KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES";
        public static final boolean KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES_DEFAULT = false;

        public static final String KEY_ENABLE_HEADSET_RUNNING = "KEY_ENABLE_HEADSET_RUNNING";
        public static final boolean KEY_ENABLE_HEADSET_RUNNING_DEFAULT = false;

        public static final String KEY_ENABLE_NEW_WIDGETS = "KEY_ENABLE_NEW_WIDGETS";
        public static final boolean KEY_ENABLE_NEW_WIDGETS_DEFAULT = false;

        public static final String KEY_ENABLE_TOP_ROUTE_FEATURES = "KEY_ENABLE_TOP_ROUTE_FEATURES";
        public static final boolean KEY_ENABLE_TOP_ROUTE_FEATURES_DEFAULT = BrandFlavourConstants.TOP_ROUTE_FEATURES_DEFAULT_VALUE;

        public static final String KEY_ENABLE_WORKOUT_VALUE_GROUPS = "KEY_ENABLE_NEW_ACTIVITY_SUMMARY_GROUPS";
        public static final boolean KEY_ENABLE_WORKOUT_VALUE_GROUPS_DEFAULT = false;

        public static final String KEY_GOOGLE_LOGIN = "KEY_GOOGLE_LOGIN";
        public static final boolean KEY_GOOGLE_LOGIN_DEFAULT = false;

        public static final String KEY_ENABLE_SUUNTO_PLUS_FEEDBACK = "KEY_ENABLE_SUUNTO_PLUS_FEEDBACK";
        public static final boolean KEY_ENABLE_SUUNTO_PLUS_FEEDBACK_DEFAULT = false;

        public static final String KEY_ENABLE_DAY_VIEW_V2 = "KEY_ENABLE_DAY_VIEW_V2";
        public static final boolean KEY_ENABLE_DAY_VIEW_V2_DEFAULT = false;

        public static final String KEY_OFFLINE_MAP_FOR_MOBILE = "KEY_OFFLINE_MAP_FOR_MOBILE";
        public static final boolean KEY_OFFLINE_MAP_FOR_MOBILE_DEFAULT = false;

        public static final String KEY_ENABLE_WORKOUT_PAGING = "KEY_ENABLE_WORKOUT_PAGING";
        public static final boolean KEY_ENABLE_WORKOUT_PAGING_DEFAULT = false;

        public static final String KEY_ENABLE_HOME_SEARCH = "KEY_ENABLE_HOME_SEARCH";
        public static final boolean KEY_ENABLE_HOME_SEARCH_DEFAULT = false;

        public static final String KEY_ENABLE_SYNC_WIDGETS = "KEY_ENABLE_SYNC_WIDGETS";
        public static final boolean KEY_ENABLE_SYNC_WIDGETS_DEFAULT = false;

        public static final String KEY_ENABLE_TRANSIT_CARD = "KEY_ENABLE_TRANSIT_CARD";
        public static final boolean KEY_ENABLE_TRANSIT_CARD_DEFAULT = false;

        public static final String KEY_ENABLE_SU10 = "KEY_ENABLE_SU10";
        public static final boolean KEY_ENABLE_SU10_DEFAULT = false;

        public static final String KEY_ENABLE_MOCK_WIDGET_INFO = "KEY_ENABLE_MOCK_WIDGET_INFO";
        public static final boolean KEY_ENABLE_MOCK_WIDGET_INFO_DEFAULT = false;

    }

    public static abstract class AskoRemoteConfigPreferences {
        public static final String PREFS_NAME = "ASKO_REMOTE_CONFIG_PREFS";
        public static final String PREFS_NAME_CONNECTIVITY = "ASKO_REMOTE_CONFIG_PREFS_CONNECTIVITY";
        public static final String KEY_PERCENTILE_ROLL_RESULT = "KEY_PERCENTILE_ROLL_RESULT";
    }

    public static abstract class TooltipPreferences {
        public static final String PREFS_NAME = "TOOLTIP_PREFS";
        public static final String KEY_SUUNTO_SHOULD_SHOW_POST_PAIRING_TOOLTIP =
            "key_suunto_should_show_post_pairing_tooltip";
        public static final String KEY_SHOULD_SHOW_TRACKING_CO2_EMISSIONS_REDUCED_TOOLTIP =
            "KEY_SHOULD_SHOW_TRACKING_CO2_EMISSIONS_REDUCED_TOOLTIP";
    }

    public static abstract class TrackingPreferences {
        public static final String PREFS_NAME = "TRACKING_PREFS";
        public static final String KEY_TRACKING_NIGHT_MODE = "TRACKING_NIGHT_MODE";
    }

    public static abstract class DayViewPreferences {
        public static final String PREFS_NAME = "DAY_VIEW_PREFS";
        public static final String KEY_HEART_RATE_EXPANDED = "KEY_HEART_RATE_EXPANDED";
        public static final String KEY_BLOOD_OXYGEN_EXPANDED = "KEY_BLOOD_OXYGEN_EXPANDED";
        public static final String KEY_RESOURCES_EXPANDED = "KEY_RESOURCES_EXPANDED";
        public static final String KEY_SLEEP_EXPANDED = "KEY_SLEEP_EXPANDED";
        public static final String KEY_STEPS_EXPANDED = "KEY_STEPS_EXPANDED";
        public static final String KEY_CALORIES_EXPANDED = "KEY_CALORIES_EXPANDED";
    }

    public static abstract class DashboardPreferences {
        public static final String PREFS_NAME = "DASHBOARD_PREFS";
        public static final String KEY_GRANULARITY = "GRANULARITY";
        public static final String KEY_SELECTED_DASHBOARD_WIDGETS_LEGACY = "SELECTED_DASHBOARD_WIDGETS";
        public static final String KEY_SELECTED_DASHBOARD_WIDGETS = "SELECTED_DASHBOARD_WIDGETS_V2"; // Used since dashboard revamp in 2024 / 2025.
        public static final String KEY_AUTO_UPDATING_SELECTED_WIDGETS_ALLOWED = "AUTO_UPDATING_SELECTED_WIDGETS_ALLOWED";
        public static final String KEY_NO_NEED_TO_CHECK_CUSTOMIZATION_TOOLTIP_CONDITIONS = "NO_NEED_TO_CHECK_CUSTOMIZATION_TOOLTIP_CONDITIONS";
        public static final String KEY_NUM_SESSION_STARTS_TO_SHOW_CUSTOMIZATION_TOOLTIP = "NUM_SESSION_STARTS_TO_SHOW_CUSTOMIZATION_TOOLTIP";
        public static final String KEY_SHOULD_SET_CUSTOMIZATION_TOOLTIP_REMINDER_TRIGGER = "SHOULD_SET_CUSTOMIZATION_TOOLTIP_REMINDER_TRIGGER";
        public static final String KEY_SHOW_LATEST_WORKOUT = "KEY_SHOW_LATEST_WORKOUT";
        public static final String KEY_LAST_DASHBOARD_TAB = "KEY_LAST_DASHBOARD_TAB";
        public static final String KEY_DASHBOARD_V2_INTRO_SHOWN = "KEY_DASHBOARD_V2_INTRO_SHOWN";
        public static final String KEY_LATEST_DISMISSED_WORKOUT = "KEY_LATEST_DISMISSED_WORKOUT";
        public static final String KEY_LAST_REPORTED_TOP6_WIDGETS = "KEY_LAST_REPORTED_TOP6_WIDGETS";
        public static final String KEY_DASHBOARD_CONFIG_UPDATED_AT = "KEY_DASHBOARD_CONFIG_UPDATED_AT";
    }

    public static abstract class EmarsysCustomAttributePreferences {
        // old name APP_BOY_CUSTOM_ATTRIBUTES
        public static final String PREFS_NAME = "Emarsys_CUSTOM_ATTRIBUTES";
        // Custom attribute names are used as keys
        public static final String SETCONTACT = "set_contact_v2";
        public static final String CONTACT_ID = "contact_id";

        public static final String ANNUAL_REPORT_DIALOG_PROMPT = "annual_report_dialog_prompt";
    }

    public static abstract class EmarsysConstant{
        // payload json: field name
        public final static String APPLICATION_EXTERNAL_URL_NAME = "url";
        public final static String APPLICATION_DEEPLINK_URL_NAME = "deeplink";
        public final static String APPLICATION_CUSTOM_EVENT_NAME = "name";
    }

    public static abstract class SystemWidgetPreferences {
        public static final String PREFS_NAME = "SYSTEMWIDGET_PREFS";
        public static final String KEY_WIDGET_ID_CACHE_PREFIX = "KEY_WIDGET_ID_CACHE_";
    }

    public static abstract class TrainingHubPreferences {
        public static final String PREFS_NAME = "TRAINING_HUB_PREFERENCES";
    }

    public static abstract class TrainingZoneSummaryPreferences {
        public static final String PREFS_NAME = "TRAINING_ZONE_SUMMARY_PREFERENCES";
    }

    public static abstract class PremiumSubscriptionPreferences {
        public static final String KEY_SHOW_BUY_PREMIUM_BANNER_AT_EARLIEST_AT = "KEY_SHOW_BUY_PREMIUM_BANNER_AT_EARLIEST_AT";
    }

    public static abstract class WeChatPreferences {
        public static final String PREFS_NAME = "WECHAT_PREFERENCES";
        public static final String KEY_REFRESH = "KEY_REFRESH";
    }

    public static abstract class CacheFileSharedPreferences {
        public static final String PREFS_NAME = "CACHE_PREFERENCES";
        public static final String KEY_FILE_URI = "KEY_FILE_URI";
        public static final String KEY_ANNUAL_REPORT_URIS = "KEY_ANNUAL_REPORT_URIS";
    }

    public static abstract class FirstPairedDevicePreferences {
        public static final String PREFS_NAME = "FIRST_PAIRED_PREFERENCES";
        public static final String KEY_FIRST_PAIRED_DEVICE = "first_paired_device_%s_%s";
    }

    public static abstract class TrainingPlannerPreferences {
        public static final String PREFS_NAME = "TRAINING_PLANNER";
    }

    public static abstract class WatchSettingsPreferences {
        public static final String PREFS_NAME = "WATCH_SETTINGS_PREFERENCES";
        public static final String KEY_HR_ZONE_TYPE = "hrZoneType";
        public static final String KEY_INTENSITY_ZONES = "intensityZones";
    }

    public static abstract class UserProfilePreferences {
        public static final String PREFS_NAME = "USER_PROFILE_PREFERENCES";
        public static final String KEY_ALL_WORKOUT_LAST_TAB = "allWorkoutLastTab";
        public static final String KEY_ALL_WORKOUT_LAST_FILTER = "allWorkoutLastFilter";
    }

    /**
     * A full list of permissions can be found at: <a
     * href="http://developers.facebook.com/docs/authentication/permissions/"</a>.
     */
    public static final List<String> FB_READ_PERMISSION_LIST = new ArrayList<>();
    /**
     * A full list of permissions can be found at: <a
     * href="http://developers.facebook.com/docs/authentication/permissions</a>. All the
     * permissions, except the default, public_profile, require that you have Client OAuth Login
     * enabled for your app on the Facebook Login tab of client's app dashboard.
     */
    public static final List<String> FB_PUBLIC_PROFILE_PERMISSION_ONLY = new ArrayList<>();

    static {
        FB_READ_PERMISSION_LIST.add("user_gender");
        FB_READ_PERMISSION_LIST.add("user_birthday");
        FB_READ_PERMISSION_LIST.add("email");
        FB_READ_PERMISSION_LIST.add("user_friends");
        FB_PUBLIC_PROFILE_PERMISSION_ONLY.add("public_profile");
    }

    public static abstract class BroadcastActions {
        /**
         * This broadcast is sent when the user's status is changed. It has one boolean extra with
         * key {@link STTConstants.ExtraKeys#USER_IS_LOGGED_IN} to indicate if the user is
         * currently logged in.
         */
        public static final String USER_STATUS_CHANGED = "com.stt.android.USER_STATUS_CHANGED";

        /**
         * This broadcast is sent when a sync to backend is finished. It has no extras.
         */
        public static final String SYNC_FINISHED = "com.stt.android.SYNC_FINISHED";

        /**
         * This broadcast is sent when one or more new workouts are saved.
         * It has one integer extra with key {@link STTConstants.ExtraKeys#WORKOUT_ID}
         */
        public static final String WORKOUT_SAVED = "com.stt.android.WORKOUT_SAVED";

        /**
         * This broadcast is sent when manual workout is saved.
         * It has one integer extra with key {@link STTConstants.ExtraKeys#WORKOUT_ID}
         */
        public static final String MANUAL_WORKOUT_SAVED = "com.stt.android.MANUAL_WORKOUT_SAVED";

        /**
         * This broadcast is sent when an existing workout is updated. It has one integer extra
         * with key {@link STTConstants.ExtraKeys#WORKOUT_ID}, and one workout header extra with
         * key {@link STTConstants.ExtraKeys#WORKOUT_HEADER}.
         */
        public static final String WORKOUT_UPDATED = "com.stt.android.WORKOUT_UPDATED";

        /**
         * This broadcast is sent when an existing workout is deleted. It has one integer extra
         * with key {@link STTConstants.ExtraKeys#WORKOUT_ID}.
         */
        public static final String WORKOUT_DELETED = "com.stt.android.WORKOUT_DELETED";

        /**
         * This broadcast is sent when a workout is synced to backend for the first time. It has
         * one integer extra with key {@link STTConstants.ExtraKeys#WORKOUT_OLD_ID} representing
         * the old ID before the sync, and one workout header extra with key {@link STTConstants
         * .ExtraKeys#WORKOUT_HEADER} representing the synced workout header.
         */
        public static final String WORKOUT_SYNCED = "com.stt.android.WORKOUT_SYNCED";

        /**
         * This broadcast is sent when one or more workouts are fetched backend. It has no extras.
         */
        public static final String WORKOUT_FETCHED = "com.stt.android.WORKOUT_FETCHED";

        /**
         * This broadcast is sent when a picture or video is stored.
         * It has one integer extra with key {@link STTConstants.ExtraKeys#WORKOUT_ID}
         */
        public static final String PICTURE_OR_VIDEO_STORED = "com.stt.android.PICTURE_OR_VIDEO_STORED";

        /**
         * This broadcast is sent when there is an update from heart rate monitor. It has one
         * Bluetooth heart rate event extra with key {@link STTConstants
         * .ExtraKeys#HEART_RATE_EVENT}.
         */
        public static final String HEART_RATE_UPDATE = "com.stt.android.HEART_RATE_UPDATE";

        /**
         * This broadcast is sent when the current workout recording state is changed. It has one
         * com.stt.android.workouts.TrackingState extra with key {@link STTConstants
         * .ExtraKeys#RECORDING_STATE}.
         */
        public static final String RECORDING_STATE_CHANGED =
            "com.stt.android.RECORDING_STATE_CHANGED";

        /**
         * This broadcast is sent when the user adds a manual lap during a workout. It has one lap
         * extra with key {@link STTConstants.ExtraKeys#LAP}.
         */
        public static final String RECORDING_ADD_LAP = "com.stt.android.RECORDING_ADD_LAP";

        /**
         * This broadcast is sent when the selected lap type is changed. It has one lap type extra
         * with key {@link STTConstants.ExtraKeys#LAP_TYPE}.
         */
        public static final String LAP_TYPE_CHANGED = "com.stt.android.LAP_TYPE_CHANGED";

        /**
         * This broadcast is sent when the selected speed pace state is changed. It has one speed
         * pace state type extra with key {@link STTConstants.ExtraKeys#SPEED_PACE_STATE}.
         */
        public static final String SPEED_PACE_STATE_CHANGED =
            "com.stt.android.SPEED_PACE_STATE_CHANGED";

        /**
         * This broadcast is sent when the selected ghost state is changed. It has one ghost state
         * type extra with key {@link STTConstants.ExtraKeys#GHOST_TIME_DISTANCE_STATE}.
         */
        public static final String GHOST_STATE_CHANGED = "com.stt.android.GHOST_STATE_CHANGED";

        /**
         * This broadcast is sent when the selected lock state is changed. It has one lock state
         * boolean extra with key {@link STTConstants.ExtraKeys#GHOST_TIME_DISTANCE_STATE}.
         */
        public static final String LOCK_STATE_CHANGED = "com.stt.android.LOCK_STATE_CHANGED";

        public static final String ANALYTICS_UUID_CHANGED = "com.stt.android.ANALYTICS_UID_CHANGED";

        public static final String USER_SETTINGS_FIRST_DAY_OF_WEEK_CHANGED = "com.stt.android.USER_SETTINGS_FIRST_DAY_OF_WEEK_CHANGED";

        public static final String USER_PROFILE_CHANGE = "com.stt.android.USER_PROFILE_CHANGED";

        public static final String NAVIGATE_STATE_CHANGE = "com.stt.android.NAVIGATE_STATE_CHANGE";
    }

    public static abstract class ExtraKeys {
        public static final String USER_IS_LOGGED_IN = "com.stt.android.USER_IS_LOGGED_IN";
        public static final String WORKOUT_ID = "com.stt.android.WORKOUT_ID";
        public static final String WORKOUT_OLD_ID = "com.stt.android.WORKOUT_OLD_ID";
        public static final String WORKOUT_HEADER = "workoutHeader";
        public static final String COMMENTS_SHOW_KEYBOARD = "showKeyboard";
        public static final String KEY_WORKOUT_KEY = "com.stt.android.KEY_WORKOUT_KEY";
        public static final String WORKOUT_SUMMARYGRAPH = "com.stt.android.WORKOUT_SUMMARYGRAPH";
        public static final String FOLLOW_WORKOUT_HEADER = "com.stt.android.FOLLOW_WORKOUT_HEADER";
        public static final String GHOST_TARGET_WORKOUT_HEADER =
            "com.stt.android.GHOST_TARGET_WORKOUT_HEADER";
        public static final String FOLLOW_ROUTE_ID = "com.stt.android.FOLLOW_ROUTE_ID";
        public static final String ROUTE_ID = "com.stt.android.ROUTE_ID";
        public static final String TOP_ROUTE_ID = "com.stt.android.TOP_ROUTE_ID";
        public static final String ROUTE_ACTIVITY_TYPE = "com.stt.android.ROUTE_ACTIVITY_TYPE";
        public static final String HEART_RATE_EVENT = "com.stt.android.HEART_RATE_EVENT";
        public static final String GOAL_DEFINITION = "com.stt.android.GOAL_DEFINITION";
        public static final String RECORDING_STATE = "com.stt.android.RECORDING_STATE";
        public static final String LAP = "com.stt.android.LAP";
        public static final String LAP_TYPE = "com.stt.android.LAP_TYPE";
        public static final String SPEED_PACE_STATE = "com.stt.android.SPEED_PACE_STATE";
        public static final String GHOST_TIME_DISTANCE_STATE =
            "com.stt.android.GHOST_TIME_DISTANCE_STATE";
        public static final String LOCK_STATE = "com.stt.android.LOCK_STATE";
        public static final String REACTION_SUMMARY = "com.stt.android.REACTION_SUMMARY";
        public static final String DIVE_EXTENSION = "com.stt.android.DIVE_EXTENSION";
        public static final String NAVIGATED_FROM_SOURCE = "com.stt.android.NAVIGATED_FROM_SOURCE";
        public static final String FROM_NOTIFICATION = "com.stt.android.KEY_FROM_NOTIFICATION";
        public static final String DEEPLINK = "com.stt.android.DEEPLINK";
        public static final String CAMERA_POSITION = "com.stt.android.CAMERA_POSITION";
        public static final String TRACK_USER_LOCATION = "com.stt.android.TRACK_USER_LOCATION";
        public static final String ROUTE_START_POINT = "com.stt.android.ROUTE_START_POINT";
        public static final String ROUTE_END_POINT = "com.stt.android.ROUTE_END_POINT";
        public static final String ROUTING_MODE = "com.stt.android.ROUTING_MODE";

        public static final String IS_NAVIGATE = "com.stt.android.IS_NAVIGATE";
        public static final String CHOOSE_ROUTING_MODE = "com.stt.android.CHOOSE_ROUTING_MODE";
        public static final String ANALYTICS_LOCATION_PIN = "com.stt.android.ANALYTICS_LOCATION_PIN";
        public static final String ANALYTICS_ROUTE_PLANNING_STARTED_SOURCE = "com.stt.android.ANALYTICS_ROUTE_PLANNING_STARTED_SOURCE";
        public static final String ANALYTICS_UUID = "com.stt.android.ANALYTICS_UUID";
        public static final String SPORTIE_SHARE_SOURCE = "com.stt.android.SPORTIE_SHARE_SOURCE";
        public static final String OPENED_FROM_SYSTEM_WIDGET_TYPE = "com.stt.android.OPENED_BY_SYSTEM_WIDGET_TYPE";
        public static final String BUY_PREMIUM_POPUP_OPENED_REASON = "com.stt.android.BUY_PREMIUM_POPUP_OPENED_REASON";
        public static final String ROUTE_ACTION_COPY = "com.stt.android.ROUTE_ACTION_COPY";
        public static final String IS_WATCH_ROUTE_LIST_FULL = "com.stt.android.IS_WATCH_ROUTE_LIST_FULL";
        public static final String USER_PROFILE = "com.stt.android.USER_PROFILE";

        public static final String NAVIGATE_STATE = "com.stt.android.NAVIGATE_STATE";
    }

    public static abstract class NotificationIds {
        public static final int ONGOING_WORKOUT = 1;
        public static final int NEW_MAP = 2;
        public static final int FREE_TRIAL = 3;
        public static final int DEBUG = 4;
        public static final int PREVENT_DOZE_FOREGROUND_SERVICE = 5;
        public static final int FOREGROUND_FEED_SYNC = 6;
        public static final int EXPEDITED_ROUTE_SYNC = 7;
        public static final int EXPEDITED_ICP_REQUEST = 8;
        public static final int EXPEDITED_SUUNTO_PLUS_SYNC = 9;

        public static final int[] CANCEL_ON_START_NOTIFICATION_IDS =
            new int[] { NEW_MAP, FREE_TRIAL };
    }

    public static abstract class RequestCodes {
        public static final int EDIT_GOAL = 1;
        public static final int LOGIN = 2;
        public static final int ENABLE_BLUETOOTH = 4;

        /**
         * Request code used between activities to inform that the workout might have been edited
         * or deleted ({@link Activity#onActivityResult(int, int, Intent)} {@code resultCode}
         * will be set to one of {@link EditWorkoutResult})
         */
        public static final int EDIT_WORKOUT = 5;

        public static final int PICK_USER_PROFILE_PICTURE = 8;
        public static final int PICK_WORKOUT_PICTURE = 9;
        public static final int REQUEST_PERMISSION = 10;
        public static final int EDIT_ROUTE = 11;
        public static final int PICK_WORKOUT_VIDEO = 12;
        public static final int TRIM_WORKOUT_VIDEO = 13;
        public static final int MAPEXPLORE_ROUTE_SAVE = 14;
        public static final int PICK_IMAGE_OR_VIDEO = 15;

        public static final int REQUEST_NEARBY_PERMISSION = 16;
    }

    @IntDef({
        Activity.RESULT_CANCELED, RequestResult.WORKOUT_EDITED, RequestResult.WORKOUT_DELETED,
    })
    @Retention(RetentionPolicy.SOURCE)
    public @interface EditWorkoutResult {
    }

    public static abstract class RequestResult {
        /**
         * Result code when a workout is edited. The result will include an {@link
         * android.content.Intent} with the workout header edited values.
         */
        public static final int WORKOUT_EDITED = 1;

        /**
         * Result code when a workout is deleted. The result will include an {@link
         * android.content.Intent} with the workout header deleted.
         */
        public static final int WORKOUT_DELETED = 2;

        public static final int MEDIA_EDITED = 3;
    }

    public static abstract class HelpShiftPublishId {
        /**
         * The publish id of low GPS related single FAQ
         */
        public final static String GPS_ISSUE_SUPPORT_PUBLISH_ID = "705";

        public final static String GPS_ISSUE_SUPPORT_PUBLISH_ID_URL = "https://www.suunto.com/Support/faq-articles/lifecycle/how-to-get-more-accurate-gps-tracking/";
        // Suunto app publish IDs
        public final static String SUUNTO_7_FAILED_TO_PAIR = "631";

        public final static String SUUNTO_7_FAILED_TO_PAIR_URL = "https://www.suunto.com/Support/faq-articles/Suunto-7/how-do-i-connect-suunto-7-with-suunto-app-android/";
        public final static String SUUNTO_7_NOT_CONNECTED = "634";

        public final static String SUUNTO_7_NOT_CONNECTED_URL = "https://www.suunto.com/Support/faq-articles/Suunto-7/how-do-i-connect-suunto-7-with-suunto-app-android/";
        public final static String SUUNTO_WATCH_SYNC_FAILED = "801";

        public final static String SUUNTO_WATCH_SYNC_FAILED_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/why-cant-i-pair-my-suunto-3-5-9-or-Spartan-watch-to-the-suunto-app-android";
        public final static String SUUNTO_WATCH_SYNCING_OR_PAIRING_ISSUE = "801";

        public final static String SUUNTO_WATCH_SYNCING_OR_PAIRING_ISSUE_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/why-cant-i-pair-my-suunto-3-5-9-or-Spartan-watch-to-the-suunto-app-android";
        public final static String SUUNTO_WATCH_DISCONNECTED_HELP_LEGAGY = "807";

        public final static String SUUNTO_WATCH_DISCONNECTED_HELP_LEGAGY_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/why-cant-i-pair-my-ambit-traverse-to-the-suunto-app-android";
        public final static String SUUNTO_WATCH_PAIRING_INSTRUCTIONS_AMBIT1_AND_AMBIT2 = "730";

        public final static String SUUNTO_WATCH_PAIRING_INSTRUCTIONS_AMBIT1_AND_AMBIT2_URL = "https://www.suunto.com/Support/faq-articles/transition/how-do-i-sync-exercises-from-my-ambit-12-with-suuntolink-to-the-suunto-app";

        public final static String SUUNTO_WATCH_PAIRING_INSTRUCTIONS_TRAVERSE_AND_AMBIT3 = "293";

        public final static String SUUNTO_WATCH_PAIRING_INSTRUCTIONS_TRAVERSE_AND_AMBIT3_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/how-do-i-pair-my-suunto-ambit3-traverse-or-traverse-alpha-with-suunto-app-for-android";

        public final static String SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_7 = "630";

        public final static String SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_7_URL = "https://www.suunto.com/Support/faq-articles/Suunto-7/how-do-i-connect-suunto-7-with-suunto-app-android/";
        public final static String SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_EON = "295";

        public final static String SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_EON_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/how-do-i-pair-my-suunto-eon-steel-and-core-with-suunto-app-for-android/";

        public final static String SUUNTO_WATCH_PAIRING_INSTRUCTIONS_CABLE_CONNECTED_DIVE = "756";

        public final static String SUUNTO_WATCH_PAIRING_INSTRUCTIONS_CABLE_CONNECTED_DIVE_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/how-do-i-sync-dive-logs-from-my-dive-computer-to-the-suunto-app/";

        public final static String SUUNTO_WATCH_PAIRING_INSTRUCTIONS_GENERIC = "298";

        public final static String SUUNTO_WATCH_PAIRING_INSTRUCTIONS_GENERIC_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/how-do-I-pair-my-suunto-watch-with-suunto-app-for-android";

        public final static String SUUNTO_SPORT_MODE_HELP = "744";

        public final static String SUUNTO_SPORT_MODE_HELP_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/how-do-i-customize-sport-modes-with-suunto-app2/";

        public static final String SUUNTO_MAIN_SUPPORT = "001";

        public static final String SUUNTO_MAIN_SUPPORT_URL = getSupportUrl();

        /**
         * China app use simplified Chinese url
         * @return support url
         */
        private static String getSupportUrl() {
            if (FlavorUtils.INSTANCE.isSuuntoAppChina()) {
                return "https://www.suunto.cn/zh-chs/Support";
            } else  {
                return "https://www.suunto.com/support";
            }
        }

        public static final String  SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_D5 = "745";

        public static final String  SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_D5_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/how-to-pair-suunto-d5-with-suunto-app-for-android/";

        public static final String  SUUNTO_WATCH_SYNCING_OR_PAIRING_ISSUE_DIVE = "746";

        public static final String  SUUNTO_WATCH_SYNCING_OR_PAIRING_ISSUE_DIVE_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/why-cant-i-pair-my-suunto-eon-or-d5-to-the-suunto-app-android/";

        public static final String MC_CONNECTION_FAQ_ID = "390";

        public static final String MC_CONNECTION_FAQ_ID_URL = "https://www.suunto.com/Support/faq-articles/movescount/how-do-i-transfer-my-move-history-from-suunto-movescount-to-suunto-app/";

        public static final String DELETE_ACCOUNT_ID = "571";

        public static final String DELETE_ACCOUNT_ID_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/how-can-i-delete-my-data-and-account-from-suunto-app-for-Android/";

        public static final String RESET_PASSWORD = "573";

        public static final String RESET_PASSWORD_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/how-to-change-or-reset-my-suunto-app-password-android/";

        public static final String NOTIFICATIONS_ID = "720";

        public static final String NOTIFICATIONS_ID_URL = "https://www.suunto.com/Support/faq-articles/suunto-app/how-to-manage-notifications-android/";

        public static final String WECHAT_SYNC_HELP = "https://www.suunto.cn/zh-chs/Support/faq-articles/suunto-app/wechat-sports-out-of-sync/";
    }
    public static abstract class SleepQuality {
        public final static float GOOD_TREND_THRESHOLD = 0.95f;
        public final static float NEW_POOR_TREND_THRESHOLD = 0.49f;
        public final static float OLD_POOR_TREND_THRESHOLD = 0.61f;
        public final static int TREND_DAYS_THRESHOLD = 3;

    }

    public static abstract class AltitudeCalculations {
        /**
         * Altitude difference is added only if it's at least this value respect to the
         * previous one
         */
        public static final double MINIMUM_ALTITUDE_DIFF = 3.0;

        /**
         * Number of consecutive altitude values to be averaged and used as a single altitude value
         */
        public static final int ALTITUDE_AVG_AMOUNT = 4;
    }

    public static abstract class WorkoutAnalysis {
        /**
         * Minimum length for route to support playback in the workout map screen
         */
        public static final float MIN_PLAYBACK_DISTANCE_METERS = 10;
    }

    public static abstract class Dashboard {
        public static final int WIDGETS_PER_PAGE = 4;
    }

    public static abstract class PremiumSubscription {
        public static final String PLAY_STORE_MANAGE_SUBSCRIPTIONS_URL =
            "https://play.google.com/store/account/subscriptions";
    }
}
