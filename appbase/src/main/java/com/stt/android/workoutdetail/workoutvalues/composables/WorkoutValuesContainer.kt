package com.stt.android.workoutdetail.workoutvalues.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.component.SuuntoFullscreenDialog
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.Suunto
import com.stt.android.compose.theme.bodyMegaBold
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.infomodel.SummaryItem
import com.stt.android.workoutdetail.workoutvalues.WorkoutValueGroupData
import com.stt.android.workoutdetail.workoutvalues.WorkoutValuesGridItemData
import com.stt.android.workouts.details.values.WorkoutValue
import com.stt.android.compose.ui.R as ComposeR
import com.stt.android.core.R as CR

@Composable
fun WorkoutValuesContainer(
    showHeader: Boolean,
    activityName: String,
    activityIcon: Int,
    workoutValues: List<WorkoutValuesGridItemData>,
    workoutValueGroups: List<WorkoutValueGroupData>,
    workoutValuesGridType: WorkoutValuesGridType,
    showDetailsButton: Boolean,
    onValueClicked: (WorkoutValue) -> Unit,
    onMultisportDetailsClicked: () -> Unit,
    modifier: Modifier = Modifier,
    onViewMoreClicked: (() -> Unit)? = null,
    enableWorkoutValueGroups: Boolean = false,
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = MaterialTheme.colors.surface
    ) {
        // Total number of rows when fully expanded
        val totalRows = (workoutValues.size + 1) / 2
        // Show all items if expanding would add only one more row
        val shouldShowAllItemsByDefault = totalRows - 5 <= 1

        var showAllValues by rememberSaveable { mutableStateOf(false) }
        val maxItems = when (workoutValuesGridType) {
            WorkoutValuesGridType.COMPACT,
            WorkoutValuesGridType.MULTISPORT_PART_COMPACT -> 4

            WorkoutValuesGridType.ANALYSIS -> 8
            WorkoutValuesGridType.MULTISPORT_PART_FULL,
            WorkoutValuesGridType.NORMAL,
            WorkoutValuesGridType.LONG_SCREENSHOT -> if ((showAllValues && !enableWorkoutValueGroups) || shouldShowAllItemsByDefault) {
                workoutValues.size
            } else 10
        }
        val items = workoutValues.take(maxItems)
        val rows = (items.size + 1) / 2

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.wrapContentHeight()
        ) {
            if (showHeader) {
                WorkoutValuesHeader(
                    activityName = activityName,
                    activityIcon = activityIcon,
                    showDetailsButton = showDetailsButton,
                    onMultisportDetailsClicked = onMultisportDetailsClicked,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            WorkoutValuesGrid(
                items = items,
                rows = rows,
                onValueClicked = onValueClicked
            )
            onViewMoreClicked?.let {
                if (!shouldShowAllItemsByDefault && workoutValuesGridType.isExpandSupported(
                        workoutValues.size
                    )
                ) {
                    if (enableWorkoutValueGroups) {
                        // todo replace with 'Show all' button
                        WorkoutValuesFooter(
                            isExpanded = false,
                            onToggleShowAllItems = {
                                showAllValues = true
                                onViewMoreClicked.invoke()
                            }
                        )
                    } else {
                        WorkoutValuesFooter(
                            isExpanded = showAllValues,
                            onToggleShowAllItems = {
                                showAllValues = !showAllValues
                                if (showAllValues) {
                                    onViewMoreClicked.invoke()
                                }
                            }
                        )
                    }
                }
            }
            if (enableWorkoutValueGroups) {
                WorkoutValueGroups(
                    show = showAllValues,
                    onDismissRequest = { showAllValues = false },
                    workoutValueGroups = workoutValueGroups,
                    onValueClicked = onValueClicked
                )
            }
        }
    }
}

@Composable
private fun WorkoutValueGroups(
    show: Boolean,
    onDismissRequest: () -> Unit,
    workoutValueGroups: List<WorkoutValueGroupData>,
    onValueClicked: (WorkoutValue) -> Unit
) {
    SuuntoFullscreenDialog(
        showDialog = show,
        onDismissRequest = onDismissRequest,
        disableScrim = true,
        useSystemBarTheme = false,
        content = { onDismiss ->
            Column {
                TopAppBar(
                    title = {
                        Text(
                            text = stringResource(R.string.workout_values_stats).uppercase()
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = onDismiss) {
                            Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                        }
                    },
                    backgroundColor = MaterialTheme.colors.surface,
                    elevation = 4.dp
                )
                SummaryValueGroups(
                    workoutValueGroups = workoutValueGroups,
                    onValueClicked = onValueClicked,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    )
}

/**
 * Detects if this is a side mount diving case based on gas-related workout values.
 * Side mount case: contains DIVEGASES and has exactly 2 items each of
 * DIVEGASPRESSURE, DIVEGASENDPRESSURE, and DIVEGASUSEDPRESSURE
 */
private fun isSideMountCase(group: WorkoutValueGroupData): Boolean {
    val hasGases = group.workoutValues.any { it.workoutValue.item == SummaryItem.DIVEGASES }
    if (!hasGases) return false

    val pressureCount =
        group.workoutValues.count { it.workoutValue.item == SummaryItem.DIVEGASPRESSURE }
    val endPressureCount =
        group.workoutValues.count { it.workoutValue.item == SummaryItem.DIVEGASENDPRESSURE }
    val usedPressureCount =
        group.workoutValues.count { it.workoutValue.item == SummaryItem.DIVEGASUSEDPRESSURE }

    return pressureCount == 2 && endPressureCount == 2 && usedPressureCount == 2
}

/**
 * Strips gas type suffix from workout value names if present.
 */
private fun stripGasTypeFromWorkoutValues(
    workoutValues: List<WorkoutValuesGridItemData>,
    gasType: WorkoutValuesGridItemData?
): List<WorkoutValuesGridItemData> {
    return workoutValues.map { workoutValue ->
        workoutValue.copy(name = workoutValue.name.removeSuffix(", ${gasType?.value?.trim()}"))
    }
}

private fun splitSideMountGroup(group: WorkoutValueGroupData): Pair<WorkoutValueGroupData, WorkoutValueGroupData> {
    val pressureItems = listOf(
        SummaryItem.DIVEGASPRESSURE,
        SummaryItem.DIVEGASENDPRESSURE,
        SummaryItem.DIVEGASUSEDPRESSURE
    )

    val gasValues = mutableMapOf<SummaryItem, List<WorkoutValuesGridItemData>>()
    val otherValues = mutableListOf<WorkoutValuesGridItemData>()

    // Find gas type for stripping from names
    val gasType = group.workoutValues.firstOrNull { it.workoutValue.item == SummaryItem.DIVEGASES }

    group.workoutValues.forEach { value ->
        when (value.workoutValue.item) {
            in pressureItems -> {
                val item = value.workoutValue.item!!
                gasValues[item] = (gasValues[item] ?: emptyList()) + value
            }

            else -> otherValues.add(value)
        }
    }

    // Create first group: all items except second of each gas type
    val firstGroupValues = mutableListOf<WorkoutValuesGridItemData>()
    pressureItems.forEach { pressureItem ->
        gasValues[pressureItem]?.let { values ->
            if (values.isNotEmpty()) {
                firstGroupValues.add(values.first()) // Add first item
            }
        }
    }
    firstGroupValues.addAll(otherValues)

    // Create second group: all items except first of each gas type
    val secondGroupValues = mutableListOf<WorkoutValuesGridItemData>()
    pressureItems.forEach { pressureItem ->
        gasValues[pressureItem]?.let { values ->
            if (values.size > 1) {
                secondGroupValues.add(values[1]) // Add second item
            }
        }
    }
    secondGroupValues.addAll(otherValues)

    // Strip gas type from names in both groups
    val strippedFirstGroupValues = stripGasTypeFromWorkoutValues(firstGroupValues, gasType)
    val strippedSecondGroupValues = stripGasTypeFromWorkoutValues(secondGroupValues, gasType)

    val firstGroup = group.copy(
        workoutValues = strippedFirstGroupValues
    )

    val secondGroup = group.copy(
        workoutValues = strippedSecondGroupValues
    )

    return firstGroup to secondGroup
}

@Composable
private fun DisplayWorkoutValueGroup(
    group: WorkoutValueGroupData,
    onValueClicked: (WorkoutValue) -> Unit,
    modifier: Modifier = Modifier,
    enableHighlight: Boolean = false,
    transmitterId: Int? = null,
) {
    Column(modifier = modifier) {
        group.name?.let { name ->
            Text(
                text = group.name,
                modifier = Modifier.padding(all = MaterialTheme.spacing.medium),
                style = Suunto.typography.headerL,
                color = MaterialTheme.colors.onSurface,
            )
        }
        // Special case for dive gases to show gas type as subheader
        group.workoutValues.firstOrNull { it.workoutValue.item == SummaryItem.DIVEGASES }
            ?.let { gasTypeValue ->
                Text(
                    text = gasTypeValue.value,
                    modifier = Modifier.padding(
                        start = MaterialTheme.spacing.xlarge,
                        top = MaterialTheme.spacing.medium,
                        end = MaterialTheme.spacing.medium,
                    ),
                    style = Suunto.typography.headerM,
                    color = MaterialTheme.colors.onSurface,
                )
            }

        DisplayWorkoutValueGrid(
            group = group,
            onValueClicked = onValueClicked,
            enableHighlight = enableHighlight,
            transmitterId = transmitterId,
        )
    }
}

@Composable
private fun DisplayWorkoutValueGrid(
    group: WorkoutValueGroupData,
    onValueClicked: (WorkoutValue) -> Unit,
    modifier: Modifier = Modifier,
    enableHighlight: Boolean = false,
    transmitterId: Int? = null,
) {
    val gridItems = group.workoutValues.filter {
        // Filter gas type since it is already shown as a subheader
        (!enableHighlight || !group.highlight.contains(it.workoutValue.item)) && it.workoutValue.item != SummaryItem.DIVEGASES
    }
    val rows = (gridItems.size + 1) / 2

    if (gridItems.isNotEmpty()) {
        Column(
            modifier = modifier
                .padding(MaterialTheme.spacing.medium)
                .clip(MaterialTheme.shapes.large)
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colors.background,
                    shape = MaterialTheme.shapes.large,
                )
        ) {
            group.workoutValues.filter {
                // Filter gas type since it is already shown as a subheader
                enableHighlight && group.highlight.contains(it.workoutValue.item) && it.workoutValue.item != SummaryItem.DIVEGASES
            }
                .forEach { value ->
                    Divider(
                        modifier = Modifier.fillMaxWidth(),
                        color = MaterialTheme.colors.dividerColor
                    )
                    WorkoutValuesGridItem(
                        workoutValueGridItem = value,
                        onValueClicked = onValueClicked,
                        valueTextStyle = MaterialTheme.typography.bodyMegaBold
                    )
                }

            // Show transmitter information if available
            transmitterId?.let { id ->
                Divider(
                    modifier = Modifier.fillMaxWidth(),
                    color = MaterialTheme.colors.dividerColor
                )
                TankPod(id)
            }

            WorkoutValuesGrid(
                items = gridItems,
                rows = rows,
                onValueClicked = onValueClicked,
                drawBorder = false,
            )
        }
    } else {
        NoTankPod(modifier = modifier)
    }
}

@Composable
private fun TankPod(transmitterId: Int, modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(R.drawable.pod_on),
            contentDescription = null,
            tint = MaterialTheme.colors.onSurface,
            modifier = Modifier
                .padding(all = MaterialTheme.spacing.medium)
                .size(MaterialTheme.iconSizes.small),
        )
        Text(
            text = "$transmitterId",
            modifier = Modifier.weight(1f),
            style = Suunto.typography.bodyL,
            color = MaterialTheme.colors.onSurface,
        )
    }
}

@Composable
private fun NoTankPod(modifier: Modifier = Modifier) {
    val interactionSource = remember { MutableInteractionSource() }
    val uriHandler = LocalUriHandler.current
    val url =
        "https://www.suunto.com/Products/dive-computers-and-instruments/Suunto-Tank-POD/"

    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium)
            .clip(MaterialTheme.shapes.large)
            .border(
                width = 1.dp,
                color = MaterialTheme.colors.background,
                shape = MaterialTheme.shapes.large,
            )
            .clickable(
                interactionSource = interactionSource,
                indication = ripple(),
                onClick = { uriHandler.openUri(url) }
            ),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(R.drawable.pod_off),
            contentDescription = null,
            tint = MaterialTheme.colors.onSurface,
            modifier = Modifier
                .padding(all = MaterialTheme.spacing.medium)
                .size(MaterialTheme.iconSizes.small),
        )
        Text(
            text = stringResource(R.string.no_tank_pod),
            modifier = Modifier.weight(1f),
            style = Suunto.typography.bodyL,
            color = MaterialTheme.colors.onSurface,
        )

        Icon(
            painter = painterResource(ComposeR.drawable.ic_info),
            contentDescription = null,
            tint = MaterialTheme.colors.primary,
            modifier = Modifier
                .padding(all = MaterialTheme.spacing.medium)
                .size(MaterialTheme.iconSizes.small),
        )
    }
}

@Composable
private fun SummaryValueGroups(
    workoutValueGroups: List<WorkoutValueGroupData>,
    onValueClicked: (WorkoutValue) -> Unit,
    modifier: Modifier = Modifier,
    enableHighlight: Boolean = false, // todo: Enable when updating the workout details to the new design.
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colors.surface)
            .verticalScroll(rememberScrollState()),
    ) {
        var displayIndex = 0
        workoutValueGroups.forEach { group ->
            // Handle side mount special case
            if (isSideMountCase(group)) {
                val (firstGroup, secondGroup) = splitSideMountGroup(group)

                if (displayIndex > 0) {
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
                }

                // Display header and subheader only once
                group.name?.let { name ->
                    Text(
                        text = group.name,
                        modifier = Modifier.padding(all = MaterialTheme.spacing.medium),
                        style = Suunto.typography.headerL,
                        color = MaterialTheme.colors.onSurface,
                    )
                }
                group.workoutValues.firstOrNull { it.workoutValue.item == SummaryItem.DIVEGASES }
                    ?.let { gasTypeValue ->
                        Text(
                            text = gasTypeValue.value,
                            modifier = Modifier.padding(
                                start = MaterialTheme.spacing.xlarge,
                                top = MaterialTheme.spacing.medium,
                                end = MaterialTheme.spacing.medium,
                            ),
                            style = Suunto.typography.headerM,
                            color = MaterialTheme.colors.onSurface,
                        )
                    }

                DisplayWorkoutValueGrid(
                    group = firstGroup,
                    onValueClicked = onValueClicked,
                    enableHighlight = enableHighlight,
                    transmitterId = group.gasMetadata?.transmitterId
                )
                DisplayWorkoutValueGrid(
                    group = secondGroup,
                    onValueClicked = onValueClicked,
                    enableHighlight = enableHighlight,
                    transmitterId = group.gasMetadata?.transmitterId2
                )
                displayIndex++
            } else {
                // Normal case - display single group
                if (displayIndex > 0) {
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
                }

                // Check if group contains DIVEGASES and strip gas type from names
                val processedGroup =
                    if (group.workoutValues.any { it.workoutValue.item == SummaryItem.DIVEGASES }) {
                        val gasType =
                            group.workoutValues.firstOrNull { it.workoutValue.item == SummaryItem.DIVEGASES }
                        group.copy(
                            workoutValues = stripGasTypeFromWorkoutValues(
                                group.workoutValues,
                                gasType
                            )
                        )
                    } else {
                        group
                    }

                DisplayWorkoutValueGroup(
                    group = processedGroup,
                    onValueClicked = onValueClicked,
                    enableHighlight = enableHighlight,
                    transmitterId = group.gasMetadata?.transmitterId,
                )
                displayIndex++
            }
        }
    }
}

@Preview
@Composable
private fun WorkoutValuesContainerPreview() {
    AppTheme {
        WorkoutValuesContainer(
            showHeader = true,
            activityName = "Scubadiving",
            activityIcon = CR.drawable.ic_activity_scuba,
            workoutValues = WorkoutValueGridDummyData.gridItems,
            workoutValueGroups = emptyList(),
            workoutValuesGridType = WorkoutValuesGridType.NORMAL,
            showDetailsButton = false,
            onValueClicked = {},
            onMultisportDetailsClicked = {},
            onViewMoreClicked = {}
        )
    }
}

@Preview
@Composable
private fun WorkoutValuesContainerMultiSportPartPreview() {
    AppTheme {
        WorkoutValuesContainer(
            showHeader = true,
            activityName = "Scubadiving",
            activityIcon = CR.drawable.ic_activity_scuba,
            workoutValues = WorkoutValueGridDummyData.gridItems.take(4),
            workoutValueGroups = emptyList(),
            workoutValuesGridType = WorkoutValuesGridType.MULTISPORT_PART_COMPACT,
            showDetailsButton = true,
            onValueClicked = {},
            onMultisportDetailsClicked = {},
            onViewMoreClicked = {}
        )
    }
}

@Preview
@Composable
private fun SummaryGroupsPreview() {
    AppTheme {
        SummaryValueGroups(
            workoutValueGroups = listOf(
                WorkoutValueGroupData(
                    name = "Duration",
                    items = listOf(
                        SummaryItem.DURATION,
                        SummaryItem.ASCENTTIME,
                        SummaryItem.DESCENTTIME,
                    ),
                    highlight = listOf(
                        SummaryItem.DURATION,
                        SummaryItem.ASCENTTIME,
                    ),
                    workoutValues = listOf(
                        WorkoutValuesGridItemData(
                            name = "Duration",
                            value = "42’00.5",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty()
                                .copy(item = SummaryItem.DURATION),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Ascent duration",
                            value = "22'25.0",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty()
                                .copy(item = SummaryItem.ASCENTTIME),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Descent duration",
                            value = "19'15.0",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                    )
                ),
                WorkoutValueGroupData(
                    name = "Pace",
                    items = listOf(
                        SummaryItem.AVGPACE,
                        SummaryItem.MAXPACE,
                        SummaryItem.NORMALIZEDGRADEDPACE,
                        SummaryItem.PEAKPACE30S,
                    ),
                    highlight = emptyList(),
                    workoutValues = listOf(
                        WorkoutValuesGridItemData(
                            name = "Average pace",
                            value = "05'02 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty()
                                .copy(item = SummaryItem.AVGPACE),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Max pace",
                            value = "03'21 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Normalized Graded Pace",
                            value = "04'42 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Peak pace, 30 s",
                            value = "03'23 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                    )
                ),
                WorkoutValueGroupData(
                    name = "Nx 99",
                    items = emptyList(),
                    highlight = emptyList(),
                    workoutValues = emptyList(),
                ),
            ),
            onValueClicked = {}
        )
    }
}

@Preview
@Composable
private fun WorkoutValueGroupsPreview() {
    AppTheme {
        WorkoutValueGroups(
            show = true,
            onDismissRequest = {},
            workoutValueGroups = listOf(
                WorkoutValueGroupData(
                    name = "Duration",
                    items = listOf(
                        SummaryItem.DURATION,
                        SummaryItem.ASCENTTIME,
                        SummaryItem.DESCENTTIME,
                    ),
                    highlight = listOf(
                        SummaryItem.DURATION,
                        SummaryItem.ASCENTTIME,
                    ),
                    workoutValues = listOf(
                        WorkoutValuesGridItemData(
                            name = "Duration",
                            value = "42'00.5",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty()
                                .copy(item = SummaryItem.DURATION),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Ascent duration",
                            value = "22'25.0",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty()
                                .copy(item = SummaryItem.ASCENTTIME),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Descent duration",
                            value = "19'15.0",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                    )
                ),
                WorkoutValueGroupData(
                    name = "Pace",
                    items = listOf(
                        SummaryItem.AVGPACE,
                        SummaryItem.MAXPACE,
                        SummaryItem.NORMALIZEDGRADEDPACE,
                        SummaryItem.PEAKPACE30S,
                    ),
                    highlight = listOf(),
                    workoutValues = listOf(
                        WorkoutValuesGridItemData(
                            name = "Average pace",
                            value = "05'02 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty()
                                .copy(item = SummaryItem.AVGPACE),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Max pace",
                            value = "03'21 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Normalized Graded Pace",
                            value = "04'42 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Peak pace, 30 s",
                            value = "03'23 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                    )
                ),
            ),
            onValueClicked = {}
        )
    }
}

object WorkoutValueGridDummyData {
    val gridItems = listOf(
        WorkoutValuesGridItemData("Dive time", "45'54.6"),
        WorkoutValuesGridItemData("Max Depth", "30,7m"),
        WorkoutValuesGridItemData("Avg depth", "12,1"),
        WorkoutValuesGridItemData("Bottom temp.", "4 C"),
        WorkoutValuesGridItemData("In series", "2"),
        WorkoutValuesGridItemData("Surface time", "1:40"),
        WorkoutValuesGridItemData(
            "Algorithm",
            "The Suunto Fused RGBM Algorithm optimizes safe diving practices by integrating two decompression models for enhanced depth and risk management."
        ),
        WorkoutValuesGridItemData("Dive mode", "CCR Trimix"),
        WorkoutValuesGridItemData("Gas type", "CC Oxygen"),
        WorkoutValuesGridItemData("Gas type", "NX5"),
        WorkoutValuesGridItemData("Gas type", "TX21/4"),
        WorkoutValuesGridItemData("Gas type", "NX99"),
        WorkoutValuesGridItemData("Start pressure", "163 bar"),
        WorkoutValuesGridItemData("CNS", "27 %")
    )
}

enum class WorkoutValuesGridType {
    NORMAL,
    COMPACT,
    MULTISPORT_PART_COMPACT,
    MULTISPORT_PART_FULL,
    ANALYSIS,
    LONG_SCREENSHOT;

    fun isExpandSupported(size: Int) = when (this) {
        NORMAL, MULTISPORT_PART_FULL, LONG_SCREENSHOT -> size > 10
        COMPACT -> size > 4
        else -> false
    }
}
