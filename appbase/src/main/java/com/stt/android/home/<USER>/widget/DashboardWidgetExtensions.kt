package com.stt.android.home.dashboard.widget

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.text.style.RelativeSizeSpan
import android.view.View
import com.stt.android.R
import com.stt.android.utils.CustomFontStyleSpan
import com.stt.android.utils.FontUtils
import kotlin.math.roundToInt

fun DashboardWidget.boldAsNoProgressBarPrimaryText(context: Context, spannable: SpannableString) {
    spannable.setSpan(
        CustomFontStyleSpan(FontUtils.getDefaultBoldTypeface(context)),
        0,
        spannable.length,
        Spannable.SPAN_INCLUSIVE_EXCLUSIVE
    )

    val primaryTextSize = context.resources.getDimension(R.dimen.text_size_medium)
    val largeTextSize = context.resources.getDimension(R.dimen.text_size_large)

    spannable.setSpan(
        RelativeSizeSpan(largeTextSize / primaryTextSize),
        0,
        spannable.length,
        Spannable.SPAN_INCLUSIVE_EXCLUSIVE
    )
}

fun DashboardWidget.boldPrimaryTextSection(
    context: Context,
    spannable: SpannableString,
    startInclusive: Int,
    endExclusive: Int
) {
    spannable.setSpan(
        CustomFontStyleSpan(FontUtils.getDefaultBoldTypeface(context)),
        startInclusive,
        endExclusive,
        Spannable.SPAN_INCLUSIVE_EXCLUSIVE
    )

    val primaryTextSize = context.resources.getDimension(R.dimen.text_size_medium)
    val largerTextSize = context.resources.getDimension(R.dimen.text_size_larger)

    spannable.setSpan(
        RelativeSizeSpan(largerTextSize / primaryTextSize),
        startInclusive,
        endExclusive,
        Spannable.SPAN_INCLUSIVE_EXCLUSIVE
    )
}

/**
 * Sets height of the widget in a way that keeps the amount of vertical space
 * the barchart takes proportionally the same as originally
 */
fun DashboardWidget.setHeightKeepingChartProportionalHeight(
    heightPixels: Int,
    context: Context,
    containerView: View,
    chartView: View
) {
    val barchartHeightPercentage = context.resources.run {
        getDimension(R.dimen.dashboard_grid_widget_barchart_height) / getDimension(R.dimen.dashboard_grid_item_height)
    }

    val containerLayoutParams = containerView.layoutParams
    containerLayoutParams.height = heightPixels
    containerView.layoutParams = containerLayoutParams

    val barchartLayoutParams = chartView.layoutParams
    barchartLayoutParams.height = (heightPixels * barchartHeightPercentage).roundToInt()
    chartView.layoutParams = barchartLayoutParams
}
