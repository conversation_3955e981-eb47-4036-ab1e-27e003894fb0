package com.stt.android.home.people.widgets

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnLongClickListener
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat
import coil3.dispose
import coil3.load
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.stt.android.R
import com.stt.android.ThemeColors.attrDrawable
import com.stt.android.ThemeColors.resolveColor
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.core.utils.EventThrottler
import com.stt.android.databinding.WidgetFollowStatusInnerBinding
import com.stt.android.follow.FollowDirection
import com.stt.android.follow.FollowStatus
import com.stt.android.follow.UserFollowStatus
import com.stt.android.ui.utils.ViewHelper
import com.stt.android.core.R as CR

class FollowStatusWidget @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : LinearLayout(context, attrs, defStyleAttr, defStyleRes), View.OnClickListener, OnLongClickListener {
    interface Listener {
        fun onUserInfoClicked(userFollowStatus: UserFollowStatus)
        fun onAcceptPendingRequestClicked(userFollowStatus: UserFollowStatus)
        fun onRejectPendingRequestClicked(userFollowStatus: UserFollowStatus)
        fun onFollowUserClicked(userFollowStatus: UserFollowStatus)
        fun onAskToUnfollowUserClicked(userFollowStatus: UserFollowStatus)
        fun onUnfollowUserClicked(userFollowStatus: UserFollowStatus)
        fun onUserLongTapped(userFollowStatus: UserFollowStatus)
    }

    private val binding: WidgetFollowStatusInnerBinding =
        WidgetFollowStatusInnerBinding.inflate(LayoutInflater.from(context), this)
    private var followingIcon: VectorDrawableCompat? = null
    private var status: UserFollowStatus? = null
    private var listener: Listener? = null
    private val clickThrottler = EventThrottler()

    init {
        val outValue = TypedValue()
        context.theme.resolveAttribute(android.R.attr.selectableItemBackground, outValue, true)
        setBackgroundResource(outValue.resourceId)
        setOnClickListener(this)
        setOnLongClickListener(this)
        binding.followingStatus.setOnClickListener(this)
        binding.rejectRequest.setOnClickListener(this)
        binding.acceptRequest.setOnClickListener(this)
        followingIcon = VectorDrawableCompat.create(resources, R.drawable.ic_check_follow, null)
    }

    fun setListener(listener: Listener?) {
        this.listener = listener
    }

    fun setUserFollowStatus(status: UserFollowStatus, selectionMode: Boolean, selected: Boolean) {
        this.status = status
        if (!selected) {
            binding.profileImage.load(status.profileImageUrl) {
                placeholderWithFallback(binding.profileImage.context, CR.drawable.ic_default_profile_image_light)
                transformations(CircleCropTransformation())
            }
        } else {
            binding.profileImage.dispose()
            binding.profileImage.setImageResource(R.drawable.ic_selection_round)
        }
        binding.username.text = status.realNameOrUsername
        val profileDescription = status.profileDescription
        if (profileDescription.isNullOrBlank()) {
            ViewHelper.setVisibility(binding.userDescription, GONE)
        } else {
            binding.userDescription.text = profileDescription
            ViewHelper.setVisibility(binding.userDescription, VISIBLE)
        }
        binding.loadingSpinner.visibility = GONE
        binding.followingStatus.visibility = VISIBLE
        binding.rejectRequest.visibility = GONE
        binding.acceptRequest.visibility = GONE
        val context = context
        if (status.status == FollowStatus.PENDING) {
            if (status.direction == FollowDirection.FOLLOWER) {
                binding.followingStatus.visibility = GONE
                binding.rejectRequest.visibility = VISIBLE
                binding.acceptRequest.visibility = VISIBLE
            } else if (status.direction == FollowDirection.FOLLOWING) {
                binding.followingStatus.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.accent
                    )
                )
                binding.followingStatus.setCompoundDrawablesWithIntrinsicBounds(
                    null,
                    null,
                    null,
                    null
                )
                binding.followingStatus.setText(R.string.requested)
            }
        } else {
            when (status.currentUserFollowStatus) {
                FollowStatus.FOLLOWING -> {
                    binding.followingStatus.setTextColor(
                        ContextCompat.getColor(context, R.color.secondary_accent)
                    )
                    binding.followingStatus.setCompoundDrawablesWithIntrinsicBounds(
                        followingIcon,
                        null,
                        null,
                        null
                    )
                    binding.followingStatus.setText(R.string.following)
                }
                FollowStatus.UNFOLLOWING -> {
                    binding.followingStatus.setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.accent
                        )
                    )
                    binding.followingStatus.setCompoundDrawablesWithIntrinsicBounds(
                        null,
                        null,
                        null,
                        null
                    )
                    binding.followingStatus.setText(R.string.follow)
                }
                FollowStatus.PENDING -> {}
                FollowStatus.REJECTED -> {
                    binding.followingStatus.setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.accent
                        )
                    )
                    binding.followingStatus.setCompoundDrawablesWithIntrinsicBounds(
                        null,
                        null,
                        null,
                        null
                    )
                    // We don't want to show "REJECTED" to the user, just to be more polite
                    binding.followingStatus.setText(R.string.requested)
                }
                FollowStatus.FAILED -> {
                    binding.followingStatus.setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.accent
                        )
                    )
                    binding.followingStatus.setCompoundDrawablesWithIntrinsicBounds(
                        null,
                        null,
                        null,
                        null
                    )
                    binding.followingStatus.setText(R.string.follow)
                }
                else -> {
                    // do nothing
                }
            }
        }
        if (selectionMode) {
            binding.followingStatus.setTextColor(
                ContextCompat.getColor(
                    context,
                    R.color.medium_grey
                )
            )
            binding.followingStatus.isClickable = false
            binding.followingStatus.background = null
            if (selected) {
                setBackgroundColor(resolveColor(context, R.attr.suuntoBackground))
            } else {
                setBackgroundColor(resolveColor(context, R.attr.suuntoItemBackgroundColor))
            }
        } else {
            val selectableBackground = attrDrawable(getContext(), android.R.attr.selectableItemBackground)
            binding.followingStatus.isClickable = true
            binding.followingStatus.background = selectableBackground
            setBackgroundColor(resolveColor(context, R.attr.suuntoItemBackgroundColor))
        }
    }

    fun showActionSpinner() {
        binding.loadingSpinner.visibility = VISIBLE
        binding.rejectRequest.visibility = GONE
        binding.acceptRequest.visibility = GONE
        binding.followingStatus.visibility = GONE
    }

    fun hideActions() {
        binding.loadingSpinner.visibility = GONE
        binding.rejectRequest.visibility = GONE
        binding.acceptRequest.visibility = GONE
        binding.followingStatus.visibility = GONE
    }

    override fun onClick(v: View) {
        val listener = this.listener
        val status = this.status
        if (status == null || listener == null || !clickThrottler.checkAcceptEvent()) {
            return
        }
        when {
            v === binding.rejectRequest -> {
                listener.onRejectPendingRequestClicked(status)
            }
            v === binding.acceptRequest -> {
                listener.onAcceptPendingRequestClicked(status)
            }
            v === binding.followingStatus -> {
                when (status.currentUserFollowStatus) {
                    FollowStatus.FOLLOWING -> listener.onAskToUnfollowUserClicked(status)
                    FollowStatus.UNFOLLOWING, FollowStatus.FAILED -> listener.onFollowUserClicked(status)
                    // We need to first "unfollow", and then send follow request again.
                    FollowStatus.PENDING, FollowStatus.REJECTED -> listener.onUnfollowUserClicked(status)
                    else -> {
                        // do nothing
                    }
                }
            }
            else -> {
                listener.onUserInfoClicked(status)
            }
        }
    }

    override fun onLongClick(v: View): Boolean {
        val listener = this.listener
        val status = this.status
        if (status == null || listener == null) {
            return false
        }
        listener.onUserLongTapped(status)
        return true
    }
}
