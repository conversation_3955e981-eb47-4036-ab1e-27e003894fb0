package com.stt.android.home.dashboardv2.ui.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import com.stt.android.R
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.home.dashboardv2.ext.getTitleResIdForDashboard
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonWidgetHeader
import com.stt.android.home.dashboardv2.widgets.ActivityWidgetInfo
import com.stt.android.home.diary.diarycalendar.activitygroups.colorRes
import com.stt.android.core.R as CR

@Composable
internal fun ActivityWidget(
    widgetInfo: ActivityWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .clickable(
                enabled = !editMode,
                onClick = onClick,
                onLongClick = onLongClick,
            )
            .padding(MaterialTheme.spacing.medium),
    ) {
        CommonWidgetHeader(
            editMode = editMode,
            headerRes = R.string.dashboard_widget_title_activity,
            iconRes = CR.drawable.ic_activity_running,
            colorRes = R.color.dashboard_widget_duration,
            subheaderText = stringResource(widgetInfo.granularityType.getTitleResIdForDashboard()),
            onRemoveClick = onRemoveClick,
            iconCircleBg = true,
        )

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))

        ActivityList(
            widgetInfo = widgetInfo,
            modifier = Modifier.weight(1f),
        )
    }
}

@Composable
private fun ActivityList(
    widgetInfo: ActivityWidgetInfo,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current

    LazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.SpaceBetween,
    ) {
        items(widgetInfo.chartContainer.activityStatsWithTotals) { (activityType, totalValues) ->
            val showDistanceGroup = totalValues.distance > 0.0
            val showQuantityLabel = activityType?.supportsDiveProfile ?: false
            val progressValue = if (widgetInfo.chartContainer.maxDuration == 0.0) {
                0f
            } else {
                (totalValues.duration / widgetInfo.chartContainer.maxDuration).toFloat()
            }.coerceIn(0f, 1f)
            val durationValue = totalValues.valueAtIndex(0) ?: "0"
            val distanceOrQuantityValue = if (showDistanceGroup || showQuantityLabel) {
                val distanceOrQuantity = totalValues.valueAtIndex(1)
                // Show quantity label for dives
                val label = if (showQuantityLabel) {
                    totalValues.labelAtIndex(1, context)
                } else {
                    totalValues.unitAtIndex(1, context)
                }
                buildString {
                    append(distanceOrQuantity)
                    append(UNICODE_THIN_SPACE)
                    append(label)
                }
            } else {
                null
            }

            val (activityColor, progressBgColor, textColor) = if (totalValues.duration == 0.0) {
                Triple(
                    MaterialTheme.colorScheme.background,
                    MaterialTheme.colorScheme.background,
                    MaterialTheme.colorScheme.background
                )
            } else {
                Triple(
                    colorResource(activityType?.colorId ?: ActivityGroup.Unspecified.colorRes),
                    Color.Transparent,
                    MaterialTheme.colorScheme.onSurface
                )
            }

            val activityIconResId = activityType?.iconId ?: R.drawable.ic_activity_unspecified

            ActivityItem(
                iconResId = activityIconResId,
                activityColor = activityColor,
                textColor = textColor,
                progressBgColor = progressBgColor,
                progressValue = progressValue,
                durationValue = durationValue,
                distanceOrQuantityValue = distanceOrQuantityValue,
                modifier = Modifier
                    .heightIn(max = 22.dp)
                    .fillMaxWidth()
            )
        }
    }
}

@Composable
private fun ActivityItem(
    iconResId: Int,
    activityColor: Color,
    textColor: Color,
    progressBgColor: Color,
    progressValue: Float,
    durationValue: String,
    distanceOrQuantityValue: String?,
    modifier: Modifier = Modifier,
) {
    Row(modifier = modifier) {
        SuuntoActivityIcon(
            iconSize = 22.dp,
            iconRes = iconResId,
            tint = MaterialTheme.colorScheme.onPrimary,
            background = activityColor
        )

        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 6.dp)
                .align(Alignment.CenterVertically)
        ) {
            val (duration, distance, progressBar) = createRefs()
            Text(
                text = durationValue,
                modifier = Modifier.constrainAs(duration) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                },
                color = textColor,
                style = MaterialTheme.typography.bodyBold,
            )

            if (distanceOrQuantityValue != null) {
                Text(
                    text = distanceOrQuantityValue,
                    modifier = Modifier.constrainAs(distance) {
                        start.linkTo(duration.end, 8.dp)
                        baseline.linkTo(duration.baseline)
                    },
                    color = textColor,
                    style = MaterialTheme.typography.body,
                )
            }

            HorizontalProgressBar(
                progress = progressValue,
                progressColor = activityColor,
                backgroundColor = progressBgColor,
                height = 3.dp,
                cornerRadius = 2.dp,
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(progressBar) {
                        start.linkTo(duration.start)
                        top.linkTo(duration.bottom)
                    }
            )
        }
    }
}

@Composable
private fun HorizontalProgressBar(
    progress: Float,
    progressColor: Color,
    backgroundColor: Color,
    height: Dp,
    cornerRadius: Dp,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .height(height)
            .clip(RoundedCornerShape(cornerRadius))
            .background(backgroundColor)
    ) {
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .fillMaxWidth(fraction = progress)
                .clip(RoundedCornerShape(cornerRadius))
                .background(progressColor)
        )
    }
}

@Preview
@Composable
private fun ActivityItemPreview() {
    Box(
        modifier = Modifier.background(Color.White)
    ) {
        ActivityItem(
            iconResId = com.stt.android.core.R.drawable.ic_activity_walking,
            activityColor = colorResource(com.stt.android.core.R.color.suunto_running),
            textColor = MaterialTheme.colorScheme.onSurface,
            progressBgColor = Color.Transparent,
            progressValue = 0.6f,
            durationValue = "3:24",
            distanceOrQuantityValue = "2.5km",
        )
    }
}

@Preview
@Composable
internal fun HorizontalProgressBarPreview() {
    HorizontalProgressBar(
        progress = 0.6f,
        height = 2.dp,
        cornerRadius = 3.dp,
        progressColor = colorResource(com.stt.android.core.R.color.suunto_running),
        backgroundColor = Color.Transparent,
    )
}

private const val UNICODE_THIN_SPACE = "\u200A"
