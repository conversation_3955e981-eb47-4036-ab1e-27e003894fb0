package com.stt.android.home.people;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import static android.view.View.VISIBLE;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.snackbar.BaseTransientBottomBar;
import com.google.android.material.snackbar.Snackbar;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.common.ui.SimpleDialogFragment;
import com.stt.android.databinding.FragmentFollowersBinding;
import com.stt.android.domain.STTErrorCodes;
import com.stt.android.follow.FollowStatus;
import com.stt.android.follow.UserFollowStatus;
import com.stt.android.social.userprofileV2.BaseUserProfileActivity;
import com.stt.android.window.WindowUtilsKt;
import dagger.hilt.android.AndroidEntryPoint;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import javax.inject.Inject;

@AndroidEntryPoint
public class FollowersFragment extends Fragment
    implements FollowersView, PeopleView, RevokeFollowersActionMode.Listener {
    public static FollowersFragment newInstance() {
        return new FollowersFragment();
    }

    @Inject
    FollowersPresenter followersPresenter;

    private FragmentFollowersBinding binding;

    private FollowersAdapter userFollowStatusAdapter;
    private Set<String> selectedUserFollowStatusIds = null;
    private RevokeFollowersActionMode revokeFollowersActionMode;
    private ProgressDialog progressDialog;

    private final String CONFIRM_DIALOG_TAG = "confirm_remove_dlg";
    private final int CONFIRM_REMOVE_FOLLOWERS_REQUEST_CODE = 100;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
        @Nullable Bundle savedInstanceState) {
        binding = FragmentFollowersBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        userFollowStatusAdapter = new FollowersAdapter(followersPresenter,
            AnalyticsPropertyValue.FollowSourceProperty.OWN_FOLLOWERS_LIST,
            (UserFollowStatusAdapter.OnMultiSelectionModeActiveListener) getParentFragment());
        binding.followersRecyclerView.setAdapter(userFollowStatusAdapter);
        binding.followersRecyclerView.setLayoutManager(
            new LinearLayoutManager(getActivity(), RecyclerView.VERTICAL, false));

        binding.findPeopleBtn.setOnClickListener(v -> {
            PeopleFragment peopleFragment = (PeopleFragment) getParentFragment();
            if (peopleFragment != null) {
                peopleFragment.navigateToFindPeople();
            }
        });
    }

    @Override
    public void onStart() {
        super.onStart();
        followersPresenter.takeView(this);
        if (followersPresenter.isUserLoggedIn()) {
            followersPresenter.loadFollowers();
        } else {
            showEmptyView();
        }
    }

    @Override
    public void onStop() {
        followersPresenter.dropView();
        super.onStop();
    }

    @Override
    public void onDestroyView() {
        onExitActionMode();
        if (revokeFollowersActionMode != null) {
            revokeFollowersActionMode.finishActionMode();
            revokeFollowersActionMode = null;
        }
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void updateStatus(UserFollowStatus userFollowStatus) {
        if (binding == null) {
            return;
        }

        binding.noFollowersContainer.setVisibility(View.GONE);
        binding.loadingSpinner.setVisibility(View.GONE);
        binding.followersRecyclerView.setVisibility(View.VISIBLE);
        userFollowStatusAdapter.updateStatus(userFollowStatus);
        //we can't know the order followers tab list gets updated so in some cases
        // pendings are updated later than those who are followers. In this case list
        // initial position is not the first item (Pendings header). So in order to
        // always have list scrolled to top to see pendings we scroll to top when PENDING is updated
        if (userFollowStatus.getStatus() == FollowStatus.PENDING) {
            binding.followersRecyclerView.getLayoutManager().scrollToPosition(0);
        }
    }

    @Override
    public void removeStatus(UserFollowStatus userFollowStatus) {
        userFollowStatusAdapter.removeStatus(userFollowStatus);
    }

    @Override
    public void showUnfollowDialog(final UserFollowStatus userFollowStatus) {
        FollowActionViewHelper.showUnfollowDialog(getContext(), followersPresenter,
            userFollowStatus);
    }

    @Override
    public void showActionError(final UserFollowStatus userFollowStatus,
        View.OnClickListener tryAgainAction) {
        FollowActionViewHelper.showActionError(this, getView(), userFollowStatus, tryAgainAction);
    }

    @Override
    public void showError(@NonNull STTErrorCodes errorCode) {
        FollowActionViewHelper.showError(getView(), errorCode);
    }

    @Override
    public void showUserProfile(@NonNull String username) {
        Context context = getContext();
        context.startActivity(BaseUserProfileActivity.newStartIntent(context, username, false));
    }

    @SuppressLint("ShowToast")
    @Override
    public void showRejectUndo(final UserFollowStatus userFollowStatus) {
        View view = getView();
        if (view == null) {
            return;
        }

        showSnackbar(Snackbar.make(view,
            getString(R.string.rejected_follow_request, userFollowStatus.getRealNameOrUsername()),
            Snackbar.LENGTH_LONG).setAction(R.string.undo, snackView -> {
                // add it back to the list.
                updateStatus(userFollowStatus);
            }).addCallback(new BaseTransientBottomBar.BaseCallback<Snackbar>() {
            @SuppressLint("SwitchIntDef")
            @Override
            public void onDismissed(Snackbar transientBottomBar, int event) {
                switch (event) {
                    case BaseTransientBottomBar.BaseCallback.DISMISS_EVENT_TIMEOUT:
                    case BaseTransientBottomBar.BaseCallback.DISMISS_EVENT_SWIPE:
                        // Now actually reject
                        followersPresenter.rejectRequest(userFollowStatus);
                    default:
                        // pass
                }
            }
        }));
    }

    @Override
    public void showEmptyView() {
        if (binding != null) {
            binding.loadingSpinner.setVisibility(View.GONE);
            binding.followersRecyclerView.setVisibility(View.GONE);
            binding.noFollowersContainer.setVisibility(VISIBLE);
        }
    }

    @Override
    public void showFollowActionSpinner(UserFollowStatus userFollowStatus) {
        if (binding != null) {
            RecyclerView.ViewHolder viewHolder =
                binding.followersRecyclerView.findViewHolderForItemId(
                    userFollowStatus.getId().hashCode());
            if (viewHolder != null) {
                if (viewHolder instanceof FollowStatusViewHolder) {
                    ((FollowStatusViewHolder) viewHolder).showActionSpinner();
                }
            }
        }
    }

    @Override
    public void showPendingActionSpinner(UserFollowStatus userFollowStatus) {
        if (binding != null) {
            RecyclerView.ViewHolder viewHolderForItemId =
                binding.followersRecyclerView.findViewHolderForItemId(
                    userFollowStatus.getId().hashCode());
            if (viewHolderForItemId instanceof FollowStatusViewHolder) {
                ((FollowStatusViewHolder) viewHolderForItemId).showActionSpinner();
            }
        }
    }

    @Override
    public void startActionMode() {
        changeStatusBarToDark();
        FragmentActivity activity = getActivity();
        if (activity instanceof AppCompatActivity) {
            if (revokeFollowersActionMode != null && revokeFollowersActionMode.isActionModeActive()) {
                // Action mode already active
                return;
            }

            revokeFollowersActionMode = new RevokeFollowersActionMode(this);
            ((AppCompatActivity)activity).startSupportActionMode(revokeFollowersActionMode);
        }
    }

    private void changeStatusBarToDark() {
        Activity activity = getActivity();
        if (activity != null) {
            Window window = activity.getWindow();
            if (window != null) {
                WindowUtilsKt.setFlagsAndColors(window, true, true,
                    false, true);
            }
        }
    }

    private void changeStatusBarToWhite() {
        Activity activity = getActivity();
        if (activity != null) {
            Window window = activity.getWindow();
            if (window != null) {
                WindowUtilsKt.setFlagsAndColors(window, true, true,
                    false, false);
            }
        }
    }

    @Override
    public void updateSelectionCount(int count) {
        if (revokeFollowersActionMode != null) {
            revokeFollowersActionMode.setTitle(getString(R.string.selected_item_count, count));
            revokeFollowersActionMode.setEnableDeleteAction(count > 0);
        }
    }

    @Override
    public void showRemovingFollowersSpinner() {
        if (progressDialog != null || getContext() == null) {
            return;
        }

        progressDialog = new ProgressDialog(getContext());
        progressDialog.setMessage(getString(R.string.removing_followers_progress));
        progressDialog.show();
    }

    @Override
    public void hideRemovingFollowersSpinner() {
        if (progressDialog != null) {
            progressDialog.dismiss();
            progressDialog = null;
        }
    }

    @Override
    public void onRevokeFollowersFailed() {
        View view = getView();
        if (view != null) {
            showSnackbar(Snackbar.make(view, R.string.error_generic, Snackbar.LENGTH_LONG));
        }
    }

    @Override
    public void onPendingRequestResolved() {
        if (userFollowStatusAdapter.pendingRequestsEmpty()) {
            // Reload all followers from local to make sure list header is updated and the list
            // is sorted properly.
            userFollowStatusAdapter.clear();
            followersPresenter.loadFollowers();
        }
    }

    @Override
    public RecyclerView getRecyclerView() {
        if (binding != null) {
            return binding.followersRecyclerView;
        } else {
            return null;
        }
    }

    @Override
    public void onExitActionMode() {
        userFollowStatusAdapter.exitMultiSelectionMode();
        changeStatusBarToWhite();
    }

    @Override
    public void onRemoveClicked() {
        FragmentManager fm = getFragmentManager();
        Set<UserFollowStatus> selectedFollowStatuses = new HashSet<>(
            userFollowStatusAdapter.getSelectedUserFollowStatuses());

        selectedUserFollowStatusIds = new HashSet<>();
        for (UserFollowStatus status : selectedFollowStatuses) {
            selectedUserFollowStatusIds.add(status.getId());
        }

        CharSequence message;
        CharSequence title;
        if (fm == null || selectedFollowStatuses.isEmpty()) {
            return;
        } else if (selectedFollowStatuses.size() == 1) {
            title = getString(R.string.revoke_dialog_title);
            message = RevokeFollowersUtilKt.formatRevokeFollowerMessage(
                getResources(),
                selectedFollowStatuses.iterator().next());
        } else {
            title = getString(R.string.revoke_multiple_dialog_title);
            message = RevokeFollowersUtilKt.formatRevokeMultipleFollowersMessage(getResources());
        }

        SimpleDialogFragment simpleDialogFragment = SimpleDialogFragment.newInstance(
            message,
            title,
            getString(R.string.revoke_dialog_confirm),
            getString(R.string.cancel));

        simpleDialogFragment.setTargetFragment(this, CONFIRM_REMOVE_FOLLOWERS_REQUEST_CODE);
        simpleDialogFragment.show(fm, CONFIRM_DIALOG_TAG);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == CONFIRM_REMOVE_FOLLOWERS_REQUEST_CODE && resultCode == SimpleDialogFragment.RESULT_POSITIVE) {
            userFollowStatusAdapter.exitMultiSelectionMode();
            if (revokeFollowersActionMode != null) {
                revokeFollowersActionMode.finishActionMode();
                revokeFollowersActionMode = null;
            }
            if (selectedUserFollowStatusIds != null) {
                followersPresenter.revokeFollowersByUserFollowStatusIds(
                    new ArrayList<>(selectedUserFollowStatusIds));
            } else {
                super.onActivityResult(requestCode, resultCode, data);
            }
        }
    }

    private void showSnackbar(Snackbar snackbar) {
        int bottomPadding = getResources().getDimensionPixelSize(R.dimen.navbar_height);

        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams)snackbar.getView().getLayoutParams();

        params.setMargins(
            params.leftMargin,
            params.topMargin,
            params.rightMargin,
            params.bottomMargin + bottomPadding);
        snackbar.getView().setLayoutParams(params);
        snackbar.show();
    }
}
