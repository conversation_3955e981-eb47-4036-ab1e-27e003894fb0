package com.stt.android.home.diary.diarycalendar.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SuggestionChip
import androidx.compose.material3.SuggestionChipDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workout.ActivityType
import com.stt.android.core.R as CR

@Composable
internal fun DiaryMapActivityFilterTags(
    sortedActivities: List<ActivityType>,
    selectedActivityType: ActivityType?,
    onActivityTypeSelected: (ActivityType?) -> Unit,
    modifier: Modifier = Modifier,
) {
    val listState = rememberLazyListState()

    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        verticalAlignment = Alignment.CenterVertically,
        contentPadding = PaddingValues(all = MaterialTheme.spacing.medium),
        state = listState,
    ) {
        item(
            key = "all",
            contentType = "tag",
        ) {
            Tag(
                activityType = null,
                selected = selectedActivityType == null,
                onClick = { onActivityTypeSelected(null) },
            )
        }
        items(
            sortedActivities,
            key = { it.id },
            contentType = { "tag" },
        ) { activityType ->
            Tag(
                activityType = activityType,
                selected = selectedActivityType?.id == activityType.id,
                onClick = { onActivityTypeSelected(activityType) },
            )
        }
    }

    LaunchedEffect(Unit) {
        if (selectedActivityType != null) {
            sortedActivities
                .indexOfFirst { selectedActivityType.id == it.id }
                .takeIf { it >= 0 }
                ?.let { listState.scrollToItem(it + 1) }
        }
    }
}

@Composable
private fun Tag(
    activityType: ActivityType?,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    SuggestionChip(
        modifier = modifier.height(32.dp),
        onClick = onClick,
        label = {
            Text(
                modifier = Modifier.defaultMinSize(minWidth = 24.dp),
                text = stringResource(activityType?.localizedStringId ?: CR.string.all_filter_tag),
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
            )
        },
        enabled = !selected,
        shape = RoundedCornerShape(32.dp),
        colors = SuggestionChipDefaults.suggestionChipColors(
            containerColor = MaterialTheme.colorScheme.surface,
            disabledContainerColor = MaterialTheme.colorScheme.primary,
            disabledLabelColor = MaterialTheme.colorScheme.onPrimary,
        ),
        border = if (selected) null else BorderStroke(1.dp, MaterialTheme.colorScheme.cloudyGrey),
    )
}
