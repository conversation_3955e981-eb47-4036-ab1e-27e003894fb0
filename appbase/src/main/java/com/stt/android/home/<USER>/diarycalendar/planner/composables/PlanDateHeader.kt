package com.stt.android.home.diary.diarycalendar.planner.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.core.R as CoreR

@Composable
internal fun PlanHeader(
    title: String,
    subtitle: String,
    isExpanded: Boolean,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface)
            .padding(all = MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall)
        ) {
            Text(
                text = title,
                style = if (isExpanded) MaterialTheme.typography.bodyLargeBold else MaterialTheme.typography.bodyLarge,
                color = if (isExpanded) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.onSurface,
            )
        }
        Icon(
            painter = painterResource(id = CoreR.drawable.ic_chevron_right_fill),
            contentDescription = null,
            modifier = Modifier.graphicsLayer(rotationZ = if (isExpanded) 270f else 90f)
        )
    }
}

@Composable
internal fun DoneHeader(
    durationText: String,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(48.dp)
            .background(MaterialTheme.colorScheme.surface)
            .padding(horizontal = MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = painterResource(R.drawable.ic_checkmark_fill),
            contentDescription = null,
            modifier = Modifier
                .size(MaterialTheme.iconSizes.small)
                .offset(y = (-1).dp),
            tint = MaterialTheme.colorScheme.onSurface
        )
        Text(
            text = stringResource(R.string.workout_planner_done_header),
            style = MaterialTheme.typography.body,
            modifier = Modifier.weight(1f),
        )
        Text(
            text = durationText,
            style = MaterialTheme.typography.body,
        )
    }
}

@Composable
fun NoWorkouts(
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium),
        contentAlignment = Alignment.CenterStart
    ) {
        Text(
            text = stringResource(R.string.workout_planner_upcoming_workout_planned_rest_day),
            style = MaterialTheme.typography.body
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun NoWorkoutPreview() {
    M3AppTheme {
        NoWorkouts()
    }
}

@Preview(showBackground = true)
@Composable
private fun NoWorkoutsPreview() {
    M3AppTheme {
        NoWorkouts()
    }
}

@Preview(showBackground = true)
@Composable
private fun PlanDateHeaderPreview() {
    M3AppTheme {
        Column(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
        ) {
            PlanHeader(
                title = "Monday 20.3.",
                subtitle = "1:55'47",
                isExpanded = true,
            )
            PlanHeader(
                title = "Monday 20.3.",
                subtitle = "1:55'47",
                isExpanded = false,
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun DoneHeaderPreview() {
    M3AppTheme {
        Column(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
        ) {
            DoneHeader(
                durationText = "1:55'47",
            )
        }
    }
}
