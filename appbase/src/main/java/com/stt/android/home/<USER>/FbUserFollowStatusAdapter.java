package com.stt.android.home.people;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.follow.FollowStatus;
import com.stt.android.follow.UserFollowStatus;
import java.util.ArrayList;
import java.util.List;

public class FbUserFollowStatusAdapter extends UserFollowStatusAdapter {

    private static final int FB_FRIENDS_HEADER = R.layout.item_add_fb_friends;
    private final View.OnClickListener onClickListener;
    private boolean addAllBtnEnabled = true;

    public FbUserFollowStatusAdapter(FollowStatusPresenter followStatusPresenter,
        List<UserFollowStatus> followStatuses, View.OnClickListener onClickListener,
        @NonNull @AnalyticsPropertyValue.FollowSourceProperty.SourceProperties String viewSource) {
        super(followStatusPresenter, followStatuses, true, viewSource);
        this.onClickListener = onClickListener;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        if (viewType == FB_FRIENDS_HEADER) {
            return new FbFriendsHeaderHolder(inflater.inflate(FB_FRIENDS_HEADER, parent, false),
                onClickListener);
        } else {
            return super.onCreateViewHolder(parent, viewType);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        //bind the header
        int viewType = holder.getItemViewType();
        if (viewType == FB_FRIENDS_HEADER) {
            FbFriendsHeaderHolder fbFriendsHolder = (FbFriendsHeaderHolder) holder;
            fbFriendsHolder.bind(addAllBtnEnabled);
        } else if (viewType == FOLLOW_HEADER_VIEW) {
            UserFollowStatusHeaderViewHolder headerHolder =
                (UserFollowStatusHeaderViewHolder) holder;
            headerHolder.bind(R.string.fb_friends_list_title, false);
        } else {
            // If it's not the headers let the parent handle it
            //-1 because of custom fb header
            position--;
            super.onBindViewHolder(holder, position);
        }
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return FB_FRIENDS_HEADER;
        } else if (position == 1) {
            return FOLLOW_HEADER_VIEW;
        } else {
            return super.getItemViewType(position);
        }
    }

    @Override
    protected UserFollowStatus getItem(int adapterPosition) {
        if (adapterPosition == 0 || adapterPosition == 1) {
            //pos 0 & 1 have headers
            throw new IllegalArgumentException("Invalid position " + adapterPosition);
        }
        return followStatuses.get(adapterPosition - 2);
    }

    @Override
    public int getItemCount() {
        return followStatuses.size() + (followStatuses.isEmpty() ? 0 : 2);
    }

    @Override
    public long getItemId(int position) {
        int itemViewType = getItemViewType(position);
        if (itemViewType == FB_FRIENDS_HEADER) {
            return FB_FRIENDS_HEADER;
        } else if (itemViewType == FOLLOW_HEADER_VIEW) {
            return FOLLOW_HEADER_VIEW;
        } else {
            return super.getItemId(position);
        }
    }

    private void setAddAllBtnEnabled(boolean addAllBtnEnabled) {
        this.addAllBtnEnabled = addAllBtnEnabled;
        notifyItemChanged(0);
    }

    @Override
    public void updateStatus(UserFollowStatus userFollowStatus) {
        super.updateStatus(userFollowStatus);
        //if add all btn is disabled (already called) and user changes someones status back to
        // UNFOLLOWING we enable the add all btn again
        if (!addAllBtnEnabled
            && userFollowStatus.getCurrentUserFollowStatus() == FollowStatus.UNFOLLOWING) {
            setAddAllBtnEnabled(true);
        } else if (addAllBtnEnabled && isAllUsersFollowedOrPending()) {
            setAddAllBtnEnabled(false);
        }
    }

    private boolean isAllUsersFollowedOrPending() {
        for (UserFollowStatus status : getFollowStatuses()) {
            if (status.getCurrentUserFollowStatus() == FollowStatus.UNFOLLOWING) {
                return false;
            }
        }
        return true;
    }

    public final List<UserFollowStatus> getUnFollowedUserFollowStatuses() {
        List<UserFollowStatus> unFollowedStatuses = new ArrayList<>();
        for (UserFollowStatus userFollowStatus : getFollowStatuses()) {
            if (userFollowStatus.getCurrentUserFollowStatus() == FollowStatus.UNFOLLOWING) {
                unFollowedStatuses.add(userFollowStatus);
            }
        }
        return unFollowedStatuses;
    }

    static class FbFriendsHeaderHolder extends RecyclerView.ViewHolder {
        Button addAllFbFriendsBtn;

        public FbFriendsHeaderHolder(View itemView, View.OnClickListener onClickListener) {
            super(itemView);
            addAllFbFriendsBtn = itemView.findViewById(R.id.addAllFbFriendsBtn);
            addAllFbFriendsBtn.setOnClickListener(onClickListener);
        }

        void bind(boolean addAllEnabled) {
            Context context = addAllFbFriendsBtn.getContext();
            addAllFbFriendsBtn.setEnabled(addAllEnabled);
            if (addAllEnabled) {
                addAllFbFriendsBtn.setText(context.getString(R.string.fb_friends_add_all));
            } else {
                addAllFbFriendsBtn.setText(context.getString(R.string.fb_friends_all_added));
            }
        }
    }

    @Override
    protected void updateFollow(UserFollowStatus userFollowStatus, int pos) {
        getFollowStatuses().set(pos, userFollowStatus);
        // + 2 because of custom fb header and follow header
        notifyItemChanged(pos + 2);
    }

    @Override
    protected void addNewFollow(UserFollowStatus userFollowStatus, int pos) {
        getFollowStatuses().add(pos, userFollowStatus);
        if (getFollowStatuses().size() == 1) {
            // If it's the first following we notify about new custom fb header and follow header
            notifyItemRangeInserted(0, 2);
        }
        // + 2 because of custom fb header and follow header
        notifyItemChanged(pos + 2);
    }
}
