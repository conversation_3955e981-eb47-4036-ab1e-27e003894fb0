package com.stt.android.home.settings.accountsettings

import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import javax.inject.Inject

@HiltViewModel
class AccountSettingsViewModel @Inject constructor(
    currentUserController: CurrentUserController,
    userSettingsController: UserSettingsController,
    settingsAnalyticsTracker: SettingsAnalyticsTracker,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
) : BaseAccountSettingsViewModel(
    currentUserController,
    userSettingsController,
    settingsAnalyticsTracker,
    ioThread,
    mainThread,
) {
    override fun retryLoading() {
        viewState.value?.data?.newEmail?.let { changeEmailSuccess() }
    }
}
