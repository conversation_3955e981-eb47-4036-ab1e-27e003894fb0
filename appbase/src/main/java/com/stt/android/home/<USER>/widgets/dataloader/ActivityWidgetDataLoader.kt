package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.SharedPreferences
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.core.R
import com.stt.android.domain.diarycalendar.DailyWorkoutStatisticsWithSummary
import com.stt.android.domain.diarycalendar.GetWorkoutStatisticsWithSummaryUseCase
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.dashboard.DashboardChartContainer
import com.stt.android.home.dashboardv2.widgets.ActivityWidgetInfo
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainerBuilder
import com.stt.android.home.diary.diarycalendar.TotalValueItem
import com.stt.android.home.diary.diarycalendar.TotalValues
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.map.selection.MyTracksGranularity
import com.stt.android.ui.map.selection.toGetWorkoutStatisticsWithSummaryUseCaseParams
import com.stt.android.utils.CalendarProvider
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes

internal class ActivityWidgetDataLoader @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val getWorkoutStatisticsWithSummaryUseCase: GetWorkoutStatisticsWithSummaryUseCase,
    private val diaryCalendarListContainerBuilder: DiaryCalendarListContainerBuilder,
    private val sharedPreferences: SharedPreferences,
    private val calendarProvider: CalendarProvider,
    private val infoModelFormatter: InfoModelFormatter,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WidgetDataLoader<ActivityWidgetInfo>() {

    override suspend fun mockedWidgetInfo(type: WidgetType): ActivityWidgetInfo? {
        return when (type) {
            WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_WEEK -> {
                mockDataForWeek()
            }
            WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH -> {
                mockDataForMonth(MyTracksGranularity.Type.THIS_MONTH)
            }
            WidgetType.DURATION_BY_ACTIVITY_GROUP_LAST_30_DAYS -> {
                mockDataForMonth(MyTracksGranularity.Type.LAST_30_DAYS)
            }
            else -> null
        }
    }

    override suspend fun realLoad(param: Param): WidgetData<ActivityWidgetInfo> {
        val flow = workoutHeaderController.currentUserWorkoutUpdated
            .map { loadWidgetInfo(param) }
            .onStart { emit(loadWidgetInfo(param)) }
            .flowOn(coroutinesDispatchers.io)
        return WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = flow,
        )
    }

    private suspend fun loadWidgetInfo(param: Param): ActivityWidgetInfo {
        val granularity = when (param.type) {
            WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_WEEK -> MyTracksGranularity.Type.THIS_WEEK
            WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH -> MyTracksGranularity.Type.THIS_MONTH
            WidgetType.DURATION_BY_ACTIVITY_GROUP_LAST_30_DAYS -> MyTracksGranularity.Type.LAST_30_DAYS
            else -> throw IllegalStateException("Unsupported param: $param")
        }
        val username = currentUserController.username
        val showPredictions = sharedPreferences.getBoolean(
            KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS,
            KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT,
        )
        val getWorkoutParams = granularity
            .toGetWorkoutStatisticsWithSummaryUseCaseParams(username, showPredictions, calendarProvider)

        val summary = getWorkoutStatisticsWithSummaryUseCase(getWorkoutParams)
        val dashboardChartsPerGranularity = createDashboardChartContainer(summary)
        return ActivityWidgetInfo(
            infoModelFormatter = infoModelFormatter,
            granularityType = granularity,
            chartContainer = dashboardChartsPerGranularity,
        )
    }

    private fun createDashboardChartContainer(data: DailyWorkoutStatisticsWithSummary): DashboardChartContainer {
        val activityStatsWithTotals =
            diaryCalendarListContainerBuilder.getActivityStatsWithTotals(data.totalValuesByActivityType)

        // Summary by activity type
        val maxDurationForSingleActivity =
            diaryCalendarListContainerBuilder.getMaxDurationForSingleActivityClippedAtTop3(
                activityStatsWithTotals
            )

        val dashboardActivities: List<Pair<ActivityType?, TotalValues>> =
            if (activityStatsWithTotals.size > 4) {
                val topThree = activityStatsWithTotals.take(3)
                val remaining = activityStatsWithTotals.drop(3)
                val otherTotalValues = buildCombinedActivityGroup(remaining)
                topThree + (null to otherTotalValues)
            } else {
                val result = mutableListOf<Pair<ActivityType?, TotalValues>>()
                result.addAll(activityStatsWithTotals)
                val missingItemsSize = 4 - result.size
                if (missingItemsSize > 0) {
                    val availableActivities = defaultActivities
                        .filterNot { activityType -> activityStatsWithTotals.any { it.first == activityType } }
                        .take(missingItemsSize - 1)
                    result.addAll(availableActivities.map { it to EMPTY })
                    result.add(null to EMPTY)
                }
                result
            }
        return DashboardChartContainer(
            maxDuration = maxDurationForSingleActivity,
            activityStatsWithTotals = dashboardActivities
        )
    }

    private fun buildCombinedActivityGroup(
        activityStatsWithTotals: List<Pair<ActivityType, TotalValues>>
    ): TotalValues {
        if (activityStatsWithTotals.isEmpty()) return EMPTY
        val lastCombinedDuration = activityStatsWithTotals.sumOf { pair -> pair.second.duration }
        val lastCombinedDistance = activityStatsWithTotals.sumOf { pair -> pair.second.distance }

        val totalDurationValueItem = getDurationTotal(lastCombinedDuration)
        val totalDistanceValueItem = getDistanceTotal(lastCombinedDistance)
        val totalValueItems = listOf(totalDurationValueItem, totalDistanceValueItem)
        return TotalValues(
            duration = lastCombinedDuration,
            distance = lastCombinedDistance,
            values = totalValueItems,
            workoutIds = emptyList()
        )
    }

    private fun getDurationTotal(seconds: Double): TotalValueItem {
        val value = try {
            infoModelFormatter.formatAccumulatedTotalDuration(seconds)
        } catch (e: Exception) {
            Timber.w(e, "Failed to format duration $seconds")
            "-"
        }

        return TotalValueItem(
            value = value,
            unitRes = R.string.hour,
            labelRes = com.stt.android.R.string.duration
        )
    }

    private fun getDistanceTotal(distance: Double): TotalValueItem {
        val value = try {
            infoModelFormatter.formatAccumulatedTotalDistance(distance, false)
        } catch (e: Exception) {
            Timber.w(e, "Failed to format distance $distance")
            "-"
        }

        return TotalValueItem(
            value = value,
            unitRes = infoModelFormatter.unit.distanceUnit,
            labelRes = com.stt.android.R.string.distance
        )
    }

    private val Int.h get() = this.hours.inWholeSeconds.toDouble()

    private val Int.m get() = this.minutes.inWholeSeconds.toDouble()

    private fun mockDataForWeek(): ActivityWidgetInfo {
        val data = mapOf(
            ActivityType.CYCLING to Pair(3.h + 54.m, 36000.0),
            ActivityType.RUNNING to Pair(2.h + 45.m, 15000.0),
            ActivityType.WALKING to Pair(2.h + 10.m, 7000.0),
            null to Pair(2.h + 3.m, 10800.0),
        )
        return mockWidgetInfo(data, MyTracksGranularity.Type.THIS_WEEK)
    }

    private fun mockDataForMonth(granularityType: MyTracksGranularity.Type): ActivityWidgetInfo {
        val data = mapOf(
            ActivityType.RUNNING to Pair(10.h + 25.m, 45000.0),
            ActivityType.CYCLING to Pair(8.h + 50.m, 150000.0),
            ActivityType.WALKING to Pair(8.h + 20.m, 50000.0),
            null to Pair(5.h + 2.m, 10800.0),
        )
        return mockWidgetInfo(data, granularityType)
    }

    private fun mockWidgetInfo(
        mockData: Map<ActivityType?, Pair<Double, Double>>,
        granularityType: MyTracksGranularity.Type,
    ): ActivityWidgetInfo {
        return ActivityWidgetInfo(
            infoModelFormatter = infoModelFormatter,
            granularityType = granularityType,
            chartContainer = DashboardChartContainer(
                maxDuration = mockData.values.maxOfOrNull { it.first } ?: 1.0,
                activityStatsWithTotals = mockData.map { (activityType, pair) ->
                    activityType to TotalValues(
                        duration = pair.first,
                        distance = pair.second,
                        values = listOf(
                            getDurationTotal(pair.first),
                            getDistanceTotal(pair.second)
                        ),
                        workoutIds = listOf()
                    )
                }
            )
        )
    }

    companion object {
        val EMPTY = TotalValues(0.0, 0.0, listOf(), listOf())
        val defaultActivities =
            listOf(ActivityType.CYCLING, ActivityType.RUNNING, ActivityType.WALKING)
    }
}
