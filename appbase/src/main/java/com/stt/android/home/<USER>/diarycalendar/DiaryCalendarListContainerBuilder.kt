package com.stt.android.home.diary.diarycalendar

import android.content.Context
import androidx.annotation.FloatRange
import com.amersports.formatter.Unit
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.google.android.gms.maps.model.LatLngBounds
import com.google.maps.android.PolyUtil
import com.stt.android.R
import com.stt.android.domain.diarycalendar.DailyWorkoutStatisticsWithSummary
import com.stt.android.domain.diarycalendar.DiaryCalendarDailyData
import com.stt.android.domain.diarycalendar.DiaryCalendarTotalValues
import com.stt.android.domain.diarycalendar.LocationWithActivityType
import com.stt.android.domain.diarycalendar.TotalValuesByActivityType
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_DISPLAY_MODE_CALENDAR
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer.Granularity
import com.stt.android.home.diary.diarycalendar.activitygroups.colorRes
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleContainer
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleData
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleParameters
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleType
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.mapping.getActivitySummaryForActivityId
import com.stt.android.ui.utils.DateFormatter
import com.stt.android.utils.CalendarUtils.formatDateRangeTitle
import com.stt.android.utils.CalendarUtils.formatMonthTitle
import com.stt.android.utils.CalendarUtils.formatWeekTitle
import com.stt.android.utils.CalendarUtils.formatYearTitle
import com.stt.android.utils.iterator
import com.stt.android.workouts.details.values.WorkoutValue
import timber.log.Timber
import java.time.Duration
import java.time.LocalDate
import java.time.YearMonth
import java.time.temporal.TemporalAdjusters.firstDayOfMonth
import java.time.temporal.TemporalAdjusters.lastDayOfMonth
import javax.inject.Inject
import kotlin.math.max
import kotlin.math.pow
import kotlin.math.sqrt
import com.stt.android.core.R as CR

class DiaryCalendarListContainerBuilder
@Inject constructor(
    private val infoModelFormatter: InfoModelFormatter,
    private val unitConverter: JScienceUnitConverter
) {
    fun build(
        context: Context,
        startDate: LocalDate,
        endDate: LocalDate,
        dateFormatter: DateFormatter,
        data: DailyWorkoutStatisticsWithSummary,
        weekDayLabels: List<String>,
        activityStatsWithTotals: List<Pair<ActivityType, TotalValues>>,
        granularity: Granularity,
        displayMode: Int,
        loadingComplete: Boolean,
        onDateClicked: (LocalDate) -> kotlin.Unit,
        onMonthClicked: (YearMonth) -> kotlin.Unit,
        onMapClicked: () -> kotlin.Unit,
        onSportRowClicked: (LocalDate, LocalDate, List<Int>, ActivityType) -> kotlin.Unit,
        onShareSummaryButtonClicked: (LocalDate, LocalDate, Int, Int) -> kotlin.Unit
    ): DiaryCalendarListContainer {
        val routes = data.locations
            .filterNot { ActivityType.valueOf(it.activityType).isIndoor }
            .map {
                RouteAndActivityType(
                    startTime = it.startTime,
                    routePoints = it.polyline?.let { polyline -> PolyUtil.decode(polyline) } ?: emptyList(),
                    activityType = it.activityType
                )
            }

        val bounds: LatLngBounds? = data.locations.toBounds()

        // Add support for unspecified activity types
        val activityLocations = data.locations.groupBy { ActivityType.valueOf(it.activityType).id }
        val activityRoutes = routes.groupBy { ActivityType.valueOf(it.activityType).id }
        val activityBounds = activityLocations.mapValues { it.value.toBounds() }
        val mapActivities = activityStatsWithTotals
            .filter { !activityLocations[it.first.id].isNullOrEmpty() }
            .sortedBy { -it.second.duration }
            .map { it.first }

        when (granularity) {
            Granularity.YEAR -> {
                val monthList = mutableListOf<DiaryBubbleData>()
                for (month in 1..12) {
                    val monthStartDate = startDate.withMonth(month).with(firstDayOfMonth())
                    val monthEndDate = monthStartDate.with(lastDayOfMonth())
                    val bubbleList = mutableListOf<DiaryBubbleContainer>()

                    for (day in monthStartDate..monthEndDate) {
                        val diaryCalendarDailyData = data.dailyData[day] ?: DiaryCalendarDailyData.EMPTY

                        bubbleList.add(
                            DiaryBubbleContainer(
                                id = day.toString(),
                                startDate = day,
                                bubbleType = getBubbleType(day, diaryCalendarDailyData),
                                dayData = diaryCalendarDailyData,
                                onDateClicked = onDateClicked
                            )
                        )
                    }

                    monthList.add(
                        DiaryBubbleData(
                            startDate = monthStartDate,
                            endDate = monthEndDate,
                            bubbles = bubbleList,
                            weekDayLabels = weekDayLabels,
                            onMonthClicked = onMonthClicked
                        )
                    )
                }

                return DiaryCalendarListContainer(
                    workoutCount = getWorkoutCount(data),
                    bubbleData = monthList,
                    activityStatsWithTotals = activityStatsWithTotals,
                    locations = data.locations,
                    activityLocations = activityLocations,
                    routes = routes,
                    activityRoutes = activityRoutes,
                    bounds = bounds,
                    activityBounds = activityBounds,
                    mapActivities = mapActivities,
                    loadingComplete = loadingComplete,
                    onMapClicked = onMapClicked,
                    totalValues = data.totalValues,
                    totalsForPeriod = getTotalsForAllActivities(data.totalValues, false),
                    granularity = granularity,
                    startDate = startDate,
                    endDate = endDate,
                    timeRange = formatDateRange(
                        context,
                        dateFormatter,
                        granularity,
                        displayMode,
                        startDate,
                        endDate,
                    ),
                    displayMode = displayMode,
                    onSportRowClicked = onSportRowClicked,
                    onShareSummaryButtonClicked = onShareSummaryButtonClicked
                )
            }
            else -> {
                val bubbleList = mutableListOf<DiaryBubbleContainer>()
                for (day in startDate..endDate) {
                    val diaryCalendarDailyData = data.dailyData[day] ?: DiaryCalendarDailyData.EMPTY

                    bubbleList.add(
                        DiaryBubbleContainer(
                            id = day.toString(),
                            startDate = day,
                            bubbleType = getBubbleType(day, diaryCalendarDailyData),
                            dayData = diaryCalendarDailyData,
                            onDateClicked = onDateClicked
                        )
                    )
                }

                return DiaryCalendarListContainer(
                    workoutCount = getWorkoutCount(data),
                    bubbleData = listOf(
                        DiaryBubbleData(
                            startDate = startDate,
                            endDate = endDate,
                            bubbles = bubbleList,
                            weekDayLabels = weekDayLabels,
                            onMonthClicked = onMonthClicked
                        )
                    ),
                    activityStatsWithTotals = activityStatsWithTotals,
                    locations = data.locations,
                    activityLocations = activityLocations,
                    routes = routes,
                    activityRoutes = activityRoutes,
                    bounds = bounds,
                    activityBounds = activityBounds,
                    mapActivities = mapActivities,
                    loadingComplete = loadingComplete,
                    onMapClicked = onMapClicked,
                    totalValues = data.totalValues,
                    totalsForPeriod = getTotalsForAllActivities(data.totalValues, false),
                    granularity = granularity,
                    startDate = startDate,
                    endDate = endDate,
                    timeRange = formatDateRange(
                        context,
                        dateFormatter,
                        granularity,
                        displayMode,
                        startDate,
                        endDate,
                    ),
                    displayMode = displayMode,
                    onSportRowClicked = onSportRowClicked,
                    onShareSummaryButtonClicked = onShareSummaryButtonClicked
                )
            }
        }
    }

    private fun formatDateRange(
        context: Context,
        dateFormatter: DateFormatter,
        granularity: Granularity,
        displayMode: Int,
        startDate: LocalDate,
        endDate: LocalDate,
    ): String = if (displayMode == DIARY_CALENDAR_DISPLAY_MODE_CALENDAR) {
        when (granularity) {
            Granularity.WEEK -> formatWeekTitle(startDate, endDate, context)
            Granularity.MONTH -> formatMonthTitle(startDate, context)
            Granularity.YEAR -> formatYearTitle(startDate)
            Granularity.LAST_30_DAYS -> if (LocalDate.now() in startDate..endDate) {
                context.getString(R.string.last_30_days)
            } else {
                formatDateRangeTitle(startDate, endDate, context)
            }
        }
    } else {
        when (granularity) {
            Granularity.WEEK -> if (LocalDate.now() in startDate..endDate) {
                context.getString(R.string.this_week)
            } else {
                dateFormatter.formatDateRange(startDate, endDate)
            }

            Granularity.MONTH -> if (LocalDate.now() in startDate..endDate) {
                context.getString(R.string.this_month)
            } else {
                formatMonthTitle(startDate, context)
            }

            Granularity.YEAR -> if (LocalDate.now() in startDate..endDate) {
                context.getString(R.string.this_year)
            } else {
                formatYearTitle(startDate)
            }

            Granularity.LAST_30_DAYS -> dateFormatter.formatDateRange(startDate, endDate)
        }
    }

    private fun List<LocationWithActivityType>.toBounds() = flatMap {
        it.polyline?.let { polyline -> PolyUtil.decode(polyline) } ?: emptyList()
    }
        .let { points ->
            if (points.isNotEmpty()) {
                LatLngBounds.builder().apply { points.forEach { include(it) } }.build()
            } else {
                null
            }
        }

    private fun getWorkoutCount(data: DailyWorkoutStatisticsWithSummary): Int {
        return data.totalValues.getWorkoutCount()
    }

    fun getBubbleType(
        date: LocalDate,
        dayData: DiaryCalendarDailyData
    ): DiaryBubbleType {
        val today = LocalDate.now()
        return when {
            date > today -> {
                // Use planned workout data if available for future dates
                if (dayData.durationByActivityGroup.isNotEmpty()) {
                    val calculateBubbleParametersForDay = calculateBubbleParametersForDay(dayData)
                    DiaryBubbleType.FutureDateBubbleType(
                        activityGroupsBubbleParameters = calculateBubbleParametersForDay
                    )
                } else {
                    DiaryBubbleType.FutureDateBubbleType(
                        activityGroupsBubbleParameters = emptyList()
                    )
                }
            }
            date == today && dayData.isEmpty() -> {
                if (dayData.durationByActivityGroup.isNotEmpty()) {
                    val calculateBubbleParametersForDay = calculateBubbleParametersForDay(dayData)
                    DiaryBubbleType.FutureDateBubbleType(
                        activityGroupsBubbleParameters = calculateBubbleParametersForDay
                    )
                } else {
                    DiaryBubbleType.TodayBubbleType
                }
            }
            dayData.isEmpty() -> DiaryBubbleType.RestDayBubbleType // empty day
            else -> DiaryBubbleType.TrainingDayBubbleType(
                calculateBubbleParametersForDay(dayData)
            )
        }
    }

    private fun calculateBubbleParametersForDay(
        dayData: DiaryCalendarDailyData
    ): List<DiaryBubbleParameters> {
        val oneSecInMillis = Duration.ofSeconds(1).toMillis()
        // note: here durations are coerced at at least 1 second for calculation simplicity
        // and to avoid empty bubbles for activities with duration = 0
        val dailyTotalDuration = dayData.durationByActivityGroup.values.sumOf {
            it.coerceAtLeast(oneSecInMillis).toInt()
        }
        val dailyBubbleSize = bubbleSizeForDuration(dailyTotalDuration)

        val pi = Math.PI.toFloat()
        var previousArea = 0.0f
        val totalArea = pi * dailyBubbleSize.pow(2.0f)

        // Start with longest duration at the center and scale the duration for each activity group
        // so that the area of the bubble is proportional to the total duration.
        return dayData.durationByActivityGroup
            .map { (activityGroup, duration) -> activityGroup to duration }
            .sortedBy { it.second } // Shortest duration first
            .map { (activityGroup, duration) ->
                val areaFraction = duration.coerceAtLeast(oneSecInMillis).toFloat() /
                    dailyTotalDuration.toFloat()
                val radius = sqrt((areaFraction * totalArea + previousArea) / pi)
                previousArea = pi * radius.pow(2.0f)

                DiaryBubbleParameters(
                    radius = radius,
                    colorRes = activityGroup.colorRes
                )
            }
            .reversed() // Reverse so that smaller bubbles are drawn over larger ones
    }

    @FloatRange(from = 0.0, to = 1.0)
    fun bubbleSizeForDuration(millis: Int): Float {
        // Bubble size grows closer to maximum (1.0f) but never reaches it. This matches how
        // bubble sizing works on iOS pretty closely. The bubble size grows much more rapidly for
        // short workouts than long ones in order to cater for all kinds of users and workouts.
        val hours = millis.toFloat() / ONE_HOUR_IN_MILLIS
        return (hours / (hours + 1.2f)).coerceIn(0.0f, 1.0f)
    }

    private fun getDurationTotal(seconds: Double): TotalValueItem {
        val value = try {
            infoModelFormatter.formatAccumulatedTotalDuration(seconds)
        } catch (e: Exception) {
            Timber.w(e, "Failed to format duration $seconds")
            "-"
        }

        return TotalValueItem(
            value = value,
            unitRes = CR.string.hour,
            labelRes = R.string.duration
        )
    }

    private fun getDiveCountTotal(diveCount: Int) =
        TotalValueItem(
            value = diveCount.toString(),
            labelQuantityRes = R.plurals.dives_plural_without_quantity,
            labelQuantityValue = diveCount
        )

    private fun getDistanceTotal(distance: Double, usesNauticalUnits: Boolean = false, isSwimming: Boolean = false): TotalValueItem {
        val value = try {
            infoModelFormatter.formatAccumulatedTotalDistance(distance, usesNauticalUnits, isSwimming)
        } catch (e: Exception) {
            Timber.w(e, "Failed to format distance $distance")
            "-"
        }

        return TotalValueItem(
            value = value,
            unitRes = when {
                usesNauticalUnits -> CR.string.nautical_mile
                isSwimming && showSwimDistanceUnit(distance) -> infoModelFormatter.unit.swimDistanceUnit
                else -> infoModelFormatter.unit.distanceUnit
            },
            labelRes = R.string.distance
        )
    }

    private fun showSwimDistanceUnit(distance: Double): Boolean {
        return infoModelFormatter.unit.toSwimDistanceUnit(distance) < 10_000
    }

    private fun getExerciseCountTotal(exerciseCount: Int) =
        TotalValueItem(
            value = exerciseCount.toString(),
            labelQuantityRes = R.plurals.exercises_plural_without_quantity,
            labelQuantityValue = exerciseCount
        )

    private fun getMaxDepthTotal(depth: Float?): TotalValueItem =
        if (depth != null) {
            val workoutValue = tryFormatValue(SummaryItem.MAXDEPTH, depth)
            TotalValueItem(
                value = workoutValue?.value ?: "-",
                unitRes = workoutValue?.unit,
                unitString = workoutValue?.unitString,
                labelRes = R.string.workout_values_headline_max_depth
            )
        } else {
            TotalValueItem(
                value = "-",
                unitRes = null,
                unitString = null,
                labelRes = R.string.workout_values_headline_max_depth
            )
        }

    private fun getEnergyTotal(energyKcal: Double): TotalValueItem =
        if (energyKcal >= MEGACALORIES_THRESHOLD_KCAL) {
            getEnergyTotalMcal(energyKcal / 1000.0)
        } else {
            getEnergyTotalKcal(energyKcal)
        }

    private fun getEnergyTotalKcal(energyKcal: Double): TotalValueItem {
        val joules = unitConverter.convert(
            energyKcal,
            Unit.KCAL,
            Unit.J
        )

        val energyValue = tryFormatValue(SummaryItem.ENERGY, joules)
        return TotalValueItem(
            value = energyValue?.value ?: "0",
            unitRes = energyValue?.unit,
            unitString = energyValue?.unitString,
            labelRes = R.string.energy
        )
    }

    private fun getEnergyTotalMcal(energyMcal: Double): TotalValueItem {
        val joules = unitConverter.convert(
            energyMcal,
            Unit.KCAL,
            Unit.J
        )

        val energyValue = tryFormatValue(SummaryItem.ENERGY, joules)
        val value = energyValue?.value ?: "-"
        return TotalValueItem(
            value = "$value K",
            unitRes = energyValue?.unit,
            unitString = energyValue?.unitString,
            labelRes = R.string.energy
        )
    }

    private fun getAscentTotal(ascent: Double?): TotalValueItem {
        val ascentValue = tryFormatValue(
            SummaryItem.ASCENTALTITUDE,
            ascent ?: 0.0
        )

        return TotalValueItem(
            value = ascentValue?.value ?: "0",
            unitRes = ascentValue?.unit,
            unitString = ascentValue?.unitString,
            labelRes = CR.string.all_ascent
        )
    }

    private fun tryFormatValue(summaryItem: SummaryItem, value: Any): WorkoutValue? =
        try {
            infoModelFormatter.formatValue(summaryItem, value)
        } catch (e: Exception) {
            Timber.w(e, "Failed to format $summaryItem")
            null
        }

    fun getTotalsForActivity(
        activityType: ActivityType,
        data: DiaryCalendarTotalValues
    ): TotalValues {
        with(data) {
            val showDistance = distance > 0 && getActivitySummaryForActivityId(activityType.id).hasDistance
            val showDiveValues = diveCount > 0 && diveCount == activityCount

            // First value, unit and label
            val first = getDurationTotal(duration)

            // Second value, unit and label
            val second: TotalValueItem = when {
                showDiveValues -> getDiveCountTotal(diveCount)
                showDistance -> getDistanceTotal(distance, activityType.usesNauticalUnits, activityType.isSwimming)
                else -> getExerciseCountTotal(activityCount)
            }

            // Third value, unit and label
            val third: TotalValueItem = if (showDiveValues) {
                getMaxDepthTotal(maxDepth)
            } else {
                getEnergyTotal(energy)
            }

            val workoutIds: List<Int> = data.workoutIdsToKeys.mapNotNull {
                it.key
            }

            return TotalValues(
                duration = duration,
                distance = distance,
                values = listOf(first, second, third),
                workoutIds = workoutIds
            )
        }
    }

    fun getTotalsForAllActivities(data: DiaryCalendarTotalValues, forcedShowExerciseCount: Boolean): TotalValues {
        val items = mutableListOf<TotalValueItem>()
        with(data) {
            val showDistance = distance > 0
            val showDiveValues = diveCount > 0 && diveCount == activityCount

            // First value is always duration
            items.add(getDurationTotal(duration))

            if (showDiveValues) {
                items.add(getDiveCountTotal(diveCount))
                items.add(getMaxDepthTotal(maxDepth))
            } else {
                when {
                    !showDistance -> items.apply {
                        add(getAscentTotal(ascent))
                        add(getEnergyTotal(energy))
                        add(getExerciseCountTotal(activityCount))
                    }
                    forcedShowExerciseCount -> items.apply {
                        add(getDistanceTotal(distance, false))
                        add(getAscentTotal(ascent))
                        add(getExerciseCountTotal(activityCount))
                    }
                    !forcedShowExerciseCount -> items.apply {
                        add(getDistanceTotal(distance, false))
                        add(getAscentTotal(ascent))
                        add(getEnergyTotal(energy))
                    }
                }
            }

            val workoutIds: List<Int> = data.workoutIdsToKeys.mapNotNull {
                it.key
            }

            return TotalValues(
                duration = duration,
                distance = distance,
                values = items,
                workoutIds = workoutIds
            )
        }
    }

    fun getActivityStatsWithTotals(totalValuesByActivityType: TotalValuesByActivityType): List<Pair<ActivityType, TotalValues>> {
        return totalValuesByActivityType.mapKeys { ActivityType.valueOf(it.key) }
            .toList()
            .sortedBy { -it.second.duration }
            .map {
                it.first to getTotalsForActivity(
                    it.first,
                    it.second
                )
            }
    }

    fun getMaxDurationForSingleActivityClippedAtTop3(activityStatsWithTotals: List<Pair<ActivityType, TotalValues>>): Double {
        return if (activityStatsWithTotals.size <= 4) {
            activityStatsWithTotals.maxOfOrNull { it.second.duration } ?: 1.0
        } else {
            val maxFromTop3 = activityStatsWithTotals
                .take(3).maxOfOrNull { it.second.duration } ?: 1.0
            val sumOfRest = activityStatsWithTotals
                .drop(3)
                .sumOf { it.second.duration }
            max(maxFromTop3, sumOfRest)
        }
    }

    companion object {
        // Show accumulated energy larger than this as thousands of kcal (e.g. megacalories)
        private const val MEGACALORIES_THRESHOLD_KCAL = 100000.0

        private val ONE_HOUR_IN_MILLIS: Float = Duration.ofHours(1L).toMillis().toFloat()
    }
}
