package com.stt.android.home.dashboardv2

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionLayout
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.core.content.edit
import androidx.core.net.toUri
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.MessageSource
import com.stt.android.appversion.AppVersionViewModel
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.controllers.CurrentUserController
import com.stt.android.databinding.DashboardFragmentV2Binding
import com.stt.android.di.DiaryPagePreferences
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.marketing.MarketingBannerInfo
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.eventtracking.EventTracker
import com.stt.android.extensions.openAppSettings
import com.stt.android.home.HomeActivityActions
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.home.InsertMenstrualCycleViewModel
import com.stt.android.home.dashboard.DashboardAnalytics
import com.stt.android.home.dashboardv2.edit.DashboardTabEditActivity
import com.stt.android.home.dashboardv2.introduction.createDashboardTutorial
import com.stt.android.home.dashboardv2.search.HomeSearchWorkoutContent
import com.stt.android.home.dashboardv2.search.HomeSearchWorkoutViewModel
import com.stt.android.home.dashboardv2.ui.DashboardScreen
import com.stt.android.home.dashboardv2.ui.DashboardScreenViewEvent
import com.stt.android.home.dashboardv2.ui.DashboardTab
import com.stt.android.home.dashboardv2.widgets.WidgetInfo
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.home.diary.DiaryPagePreferencesKeys
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.home.marketing.MarketingH5Activity
import com.stt.android.launcher.DeepLinkIntentBuilder
import com.stt.android.maps.MapSnapshotSpec
import com.stt.android.maps.MapSnapshotter
import com.stt.android.menstrualcycle.LoggedFrom
import com.stt.android.menstrualcycle.MenstrualCycleOnboardingNavigator
import com.stt.android.menstrualcycle.MenstrualCycleOnboardingNavigator.Companion.EXTRA_TRY_LOG_MENSTRUAL_CYCLE
import com.stt.android.menstrualcycle.OnboardingDoneReason
import com.stt.android.menstrualcycle.log.LogMenstrualCycleFragment
import com.stt.android.menstrualcycle.log.OnLogMenstrualCycleDoneListener
import com.stt.android.menstrualcycle.regularity.MenstrualCycleRegularitySheetCreator
import com.stt.android.multimedia.gallery.MediaGalleryActivity
import com.stt.android.multimedia.sportie.SportieImage
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.newfeed.FeedMapSizeCalculator
import com.stt.android.newfeed.FeedTopBannerNavigator
import com.stt.android.newfeed.FeedTopBannerType
import com.stt.android.newfeed.FilterTag
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.newfeed.binding.shareSportieClicked
import com.stt.android.premium.PremiumPromotionNavigator
import com.stt.android.social.following.PeopleActivity
import com.stt.android.social.notifications.NotificationActivity
import com.stt.android.social.notifications.inbox.MarketingInboxActivity
import com.stt.android.social.userprofileV2.BaseUserProfileActivity
import com.stt.android.tags.TagsNavigator
import com.stt.android.tutorial.api.TutorialNavigator
import com.stt.android.ui.activities.WorkoutActivity
import com.stt.android.ui.activities.WorkoutEditDetailsActivity
import com.stt.android.ui.components.BottomNavigationBarDelegate
import com.stt.android.ui.components.workout.actions.rememberWorkoutCardActionsHandler
import com.stt.android.ui.fragments.login.terms.OnTermsListener
import com.stt.android.utils.PermissionUtils
import com.stt.android.utils.firstOfType
import com.stt.android.watch.WorkoutPlannerNavigator
import com.stt.android.workoutdetail.comments.CommentsDialogFragment
import com.stt.android.workouts.sharepreview.WorkoutSharePreviewActivity
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import com.stt.android.workoutsettings.WorkoutSettingsActivity
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import pub.devrel.easypermissions.EasyPermissions
import timber.log.Timber
import java.time.LocalDate
import java.util.Locale
import javax.inject.Inject

abstract class BaseDashboardFragment : Fragment() {

    private var pageStartTime: Long = 0
    private var lastVisibleTab: String = DashboardTab.DASHBOARD.name

    // Get the last saved tab index from preferences
    private val lastSavedTabIndex: Int
        get() = dashboardTabViewModel.tabIndex.value

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    @Inject
    lateinit var homeActivityNavigator: HomeActivityNavigator

    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    @Inject
    lateinit var premiumPromotionNavigator: PremiumPromotionNavigator

    @Inject
    lateinit var premiumBannerNavigator: FeedTopBannerNavigator

    @Inject
    lateinit var tagsNavigator: TagsNavigator

    @Inject
    lateinit var workoutShareHelper: WorkoutShareHelper

    @Inject
    lateinit var menstrualCycleRegularitySheetCreator: MenstrualCycleRegularitySheetCreator

    @Inject
    lateinit var menstrualCycleOnboardingNavigator: MenstrualCycleOnboardingNavigator

    @Inject
    lateinit var workoutPlannerNavigator: WorkoutPlannerNavigator

    @Inject
    @DiaryPagePreferences
    lateinit var diaryPagePreferences: SharedPreferences

    @Inject
    lateinit var deepLinkIntentBuilder: DeepLinkIntentBuilder

    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var eventTracker: EventTracker

    @Inject
    lateinit var dashboardAnalytics: DashboardAnalytics

    @Inject
    lateinit var tutorialNavigator: TutorialNavigator

    internal val viewModel: DashboardViewModel by viewModels()

    private val dashboardTabViewModel: DashboardTabViewModel by viewModels()

    internal val toolbarViewModel: ToolbarViewModel by viewModels()

    private val activitiesTabViewModel: ActivitiesTabViewModel by viewModels()

    private val insertMenstrualCycleViewModel: InsertMenstrualCycleViewModel by activityViewModels()

    private val appVersionViewModel: AppVersionViewModel by activityViewModels()

    private val searchWorkoutViewModel: HomeSearchWorkoutViewModel by viewModels()

    private val mapSize by lazy { FeedMapSizeCalculator.calculate(resources) }

    private val homeActivityActions: HomeActivityActions? get() = requireActivity() as? HomeActivityActions

    private var workoutHeaderForAddingPhoto: WorkoutHeader? = null
    private val requestPermissionForAddingPhotoLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { result ->
        val allGranted = result.values.all { it }
        if (allGranted) {
            workoutHeaderForAddingPhoto?.let {
                startActivity(
                    MediaGalleryActivity.newIntentForDirectAddToWorkout(
                        requireContext(),
                        it
                    )
                )
            }
            workoutHeaderForAddingPhoto = null

            return@registerForActivityResult
        }

        val shouldShowRationale = result.keys.any(::shouldShowRequestPermissionRationale)
        if (!shouldShowRationale) {
            context?.openAppSettings()
        }
    }

    private val onboardingResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode == Activity.RESULT_OK) {
                openLogMenstrualCyclePicker()
            }
        }

    private lateinit var navController: NavHostController

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        premiumBannerNavigator.register(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val binding = DashboardFragmentV2Binding.inflate(layoutInflater, container, false)

        with(binding.composeView) {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContentWithM3Theme {
                navController = rememberNavController()
                AppNavHost(
                    onBottomBarVisibilityChanged = {
                        (activity as? BottomNavigationBarDelegate)?.let { activity ->
                            if (it) {
                                activity.showBottomBar()
                            } else {
                                activity.hideBottomBar()
                            }
                        }
                    },
                    navController = navController,
                )
            }
        }

        return binding.root
    }

    @OptIn(ExperimentalAnimationApi::class, ExperimentalSharedTransitionApi::class)
    @Composable
    fun AppNavHost(
        onBottomBarVisibilityChanged: (Boolean) -> Unit,
        navController: NavHostController,
        modifier: Modifier = Modifier,
    ) {
        LaunchedEffect(navController) {
            navController.currentBackStackEntryFlow.collect { backStackEntry ->
                when (backStackEntry.destination.route) {
                    ROUTE_DASHBOARD -> onBottomBarVisibilityChanged(true)
                    ROUTE_SEARCH -> onBottomBarVisibilityChanged(false)
                    else -> onBottomBarVisibilityChanged(true)
                }
            }
        }

        SharedTransitionLayout(modifier = modifier) {
            NavHost(navController, startDestination = ROUTE_DASHBOARD) {
                composable(ROUTE_DASHBOARD) {
                    DashboardScreen(
                        viewModel = viewModel,
                        toolbarViewModel = toolbarViewModel,
                        dashboardTabViewModel = dashboardTabViewModel,
                        activitiesTabViewModel = activitiesTabViewModel,
                        appVersionViewModel = appVersionViewModel,
                        viewEvent = ::handleViewEvent,
                        onTabChanged = { tabName -> updateCurrentTab(tabName) },
                        animatedVisibilityScope = this,
                    )
                }
                composable(ROUTE_SEARCH) {
                    val snackbarHostState = remember { SnackbarHostState() }
                    val workoutCardActionsHandler = rememberWorkoutCardActionsHandler(
                        workoutCardActionsHandler = searchWorkoutViewModel.workoutCardActionsHandler,
                        fragmentManager = childFragmentManager,
                        snackbarHostState = snackbarHostState,
                        analyticsSource = AnalyticsPropertyValue.WorkoutDetailsSourceProperty.OTHER,
                    )
                    val viewState by searchWorkoutViewModel.viewState.collectAsStateWithLifecycle()
                    Scaffold(
                        snackbarHost = { SnackbarHost(hostState = snackbarHostState) },
                    ) { paddingValues ->
                        HomeSearchWorkoutContent(
                            viewState = viewState,
                            workoutCardActionsHandler = workoutCardActionsHandler,
                            viewEvent = ::handleViewEvent,
                            onQueryChange = searchWorkoutViewModel::onQueryChange,
                            modifier = Modifier.padding(paddingValues),
                            animatedVisibilityScope = this,
                        )
                    }
                }
            }
        }
    }

    override fun onStart() {
        super.onStart()
        pageStartTime = System.currentTimeMillis()
        viewModel.checkIfNewTermsAvailable()
        toolbarViewModel.loadNotificationsAmount()
    }

    override fun onStop() {
        super.onStop()
        sendLeaveHomeScreenEvent()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (pageStartTime > 0) {
            sendLeaveHomeScreenEvent()
        }
    }

    private fun handleViewEvent(event: DashboardScreenViewEvent) {
        when (event) {
            is DashboardScreenViewEvent.OpenWidget -> openWidget(event.widgetType, event.widgetInfo)
            is DashboardScreenViewEvent.EditDashboard -> openEditDashboard()
            is DashboardScreenViewEvent.OpenWorkout -> openWorkout(
                event.workoutHeader,
                event.analyticsSource
            )

            is DashboardScreenViewEvent.OpenUser -> openUser(event.username)
            is DashboardScreenViewEvent.AddPhoto -> addPhoto(event.workoutHeader)
            is DashboardScreenViewEvent.PlayWorkout -> playWorkout(
                workoutHeader = event.workoutHeader,
                hasPremiumSubscription = event.hasPremiumSubscription,
            )

            is DashboardScreenViewEvent.OpenTag -> openTag(
                tagName = event.tagName,
                isEditable = event.isEditable,
                hasPremiumSubscription = event.hasPremiumSubscription,
            )

            is DashboardScreenViewEvent.AddComment -> addComment(event.workoutHeader)
            is DashboardScreenViewEvent.EditWorkout -> editWorkout(event.workoutHeader)
            is DashboardScreenViewEvent.ShareSportie -> shareSportie(event.sportieImage)
            is DashboardScreenViewEvent.FindPeople -> findPeople()
            is DashboardScreenViewEvent.Refresh -> refresh()
            is DashboardScreenViewEvent.RecordWorkout -> recordWorkout()
            is DashboardScreenViewEvent.OpenFeedTopBanner -> openFeedTopBanner(event.type)
            is DashboardScreenViewEvent.RemovePremiumBanner -> dashboardTabViewModel.removePremiumBanner()
            is DashboardScreenViewEvent.SetIsInlineAppViewEnabled -> dashboardTabViewModel.setIsInlineAppViewNeeded(
                event.enabled
            )

            is DashboardScreenViewEvent.OpenMarketingBanner -> openMarketingBanner(event.banner)
            is DashboardScreenViewEvent.OpenNotification -> openNotification()
            is DashboardScreenViewEvent.OpenWatchDevice -> openWatchDevice()
            is DashboardScreenViewEvent.OpenHeadsetDevice -> openHeadsetDevice()
            is DashboardScreenViewEvent.LogMenstrualCycle -> logMenstrualCycle()
            is DashboardScreenViewEvent.AddWorkoutManually -> addWorkoutManually()
            is DashboardScreenViewEvent.CreateIntervalWorkout -> createIntervalWorkout()
            is DashboardScreenViewEvent.DownloadOfflineMap -> downloadOfflineMap()
            is DashboardScreenViewEvent.PairDevice -> pairDevice()
            is DashboardScreenViewEvent.OpenSearch -> openSearch()
            is DashboardScreenViewEvent.PopBack -> popBack()
        }
    }

    private fun openWidget(widgetType: WidgetType, widgetInfo: WidgetInfo) {
        dashboardAnalytics.sendHomeScreenDashboardV2NavigationEvent(widgetType)
        openWidget(widgetInfo)
    }

    internal abstract fun openWidget(widgetInfo: WidgetInfo)

    private fun recordWorkout() {
        val context = requireContext()
        val startIntent =
            when (val continueOngoingWorkout = viewModel.handleContinueOngoingWorkout()) {
                is ContinueOngoingWorkout.ContinueFollowRoute -> WorkoutActivity.newStartIntentFollowRoute(
                    context,
                    continueOngoingWorkout.activityType,
                    continueOngoingWorkout.shouldCheckGPS,
                    false,
                    continueOngoingWorkout.followRoute.id,
                )

                is ContinueOngoingWorkout.ContinueFollowWorkout -> WorkoutActivity.newStartIntentFollowWorkout(
                    context,
                    continueOngoingWorkout.activityType,
                    continueOngoingWorkout.shouldCheckGPS,
                    false,
                    continueOngoingWorkout.followWorkoutHeader,
                )

                is ContinueOngoingWorkout.ContinueGhostWorkout -> WorkoutActivity.newStartIntentGhostWorkout(
                    context,
                    continueOngoingWorkout.activityType,
                    continueOngoingWorkout.shouldCheckGPS,
                    false,
                    continueOngoingWorkout.ghostWorkoutHeader,
                )

                is ContinueOngoingWorkout.ContinueWorkout -> WorkoutActivity.newStartIntent(
                    context,
                    continueOngoingWorkout.activityType,
                    continueOngoingWorkout.shouldCheckGPS,
                    false,
                )

                is ContinueOngoingWorkout.None -> WorkoutSettingsActivity.newStartIntent(context)
            }

        startActivity(startIntent)
    }

    private fun openFeedTopBanner(type: FeedTopBannerType) {
        premiumBannerNavigator.navigateToContent(requireContext(), type)
    }

    private fun openEditDashboard() {
        startActivity(DashboardTabEditActivity.newStartIntent(requireContext()))
    }

    private fun openWorkout(workoutHeader: WorkoutHeader, analyticsSource: String?) {
        rewriteNavigator.navigate(
            context = requireContext(),
            username = workoutHeader.username,
            workoutId = workoutHeader.id,
            workoutKey = workoutHeader.key,
            analyticsSource = analyticsSource,
        )
    }

    private fun openUser(username: String) {
        val context = requireContext()
        context.startActivity(BaseUserProfileActivity.newStartIntent(context, username, false))
    }

    private fun addPhoto(workoutHeader: WorkoutHeader) {
        if (EasyPermissions.hasPermissions(
                requireContext(),
                *PermissionUtils.STORAGE_PERMISSIONS
            )
        ) {
            startActivity(
                MediaGalleryActivity.newIntentForDirectAddToWorkout(
                    requireContext(),
                    workoutHeader
                )
            )
            return
        }

        workoutHeaderForAddingPhoto = workoutHeader
        requestPermissionForAddingPhotoLauncher.launch(PermissionUtils.STORAGE_PERMISSIONS)
    }

    private fun playWorkout(workoutHeader: WorkoutHeader, hasPremiumSubscription: Boolean) {
        if (hasPremiumSubscription) {
            homeActivityActions?.navigateToWorkoutMapGraphAnalysis(workoutHeader)
        } else {
            premiumPromotionNavigator.openWorkoutPlaybackPromotionDialog(
                fragmentManager = parentFragmentManager,
                analyticsSource = AnalyticsPropertyValue.BuyPremiumPopupShownSource.FEED,
            )
        }
    }

    private fun openTag(tagName: String, isEditable: Boolean, hasPremiumSubscription: Boolean) {
        if (isEditable || hasPremiumSubscription) {
            tagsNavigator.openDiaryForTag(requireContext(), tagName)
        }
    }

    private fun addComment(workoutHeader: WorkoutHeader) {
        CommentsDialogFragment.newInstance(workoutHeader, true)
            .show(parentFragmentManager, null)
    }

    private fun editWorkout(workoutHeader: WorkoutHeader) {
        val intent = WorkoutEditDetailsActivity.newStartIntentForEditing(
            context = requireContext(),
            workoutId = workoutHeader.id,
            source = AnalyticsPropertyValue.SourceProperty.ADD_DESCRIPTION_AND_TAGS,
        )
        startActivity(intent)
    }

    private fun shareSportie(sportieImage: SportieImage) {
        shareSportieClicked(
            context = requireContext(),
            workoutHeader = sportieImage.workoutHeader,
            imageInfo = sportieImage.imageInformation,
            watchName = sportieImage.workoutExtensions.firstOfType<SummaryExtension>()?.displayName.orEmpty(),
            workoutShareHelper = workoutShareHelper,
        )
    }

    private fun findPeople() {
        startActivity(PeopleActivity.newIntent(requireActivity()))
    }

    private fun openSearch() {
        navController.navigate(ROUTE_SEARCH)
    }

    private fun popBack() {
        navController.popBackStack()
        searchWorkoutViewModel.onQueryChange("")
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                mapSnapshotter.runSnapshotterEngine(requireContext().applicationContext)
            }
        }

        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.CREATED) {
                activitiesTabViewModel.viewState
                    .collect { viewData ->
                        (viewData as? ActivitiesTabViewModel.ViewData.Activities)
                            ?.cards
                            ?.mapNotNull { feedCard ->
                                if (feedCard !is WorkoutCardInfo) {
                                    return@mapNotNull null
                                }

                                if (feedCard.workoutHeader.isPolylineEmpty) {
                                    return@mapNotNull null
                                }

                                MapSnapshotSpec.Workout(
                                    workoutId = feedCard.workoutHeader.id,
                                    width = mapSize.width,
                                    height = mapSize.height,
                                )
                            }
                    }
            }
        }

        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.CREATED) {
                activitiesTabViewModel.shareWorkout
                    .collect(::shareWorkout)
            }
        }

        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.CREATED) {
                dashboardTabViewModel.shareWorkout
                    .collect(::shareWorkout)
            }
        }
        
        // Listen for HOME_SCREEN event ready state
        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                dashboardTabViewModel.homeScreenEventReady
                    .filter { it != null }
                    .collect { widgetCount ->
                        widgetCount?.let { count ->

                            val currentTabName = getTabNameByIndex(lastSavedTabIndex)

                            eventTracker.trackEvent(
                                AnalyticsEvent.HOME_SCREEN,
                                mapOf(
                                    AnalyticsEventProperty.WIDGET_COUNT to count,
                                    AnalyticsEventProperty.TAB_NAME to currentTabName
                                )
                            )
                        }
                    }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                insertMenstrualCycleViewModel.menstrualCycleRegularityChanged.collectLatest {
                    menstrualCycleRegularitySheetCreator.create(it)
                        ?.show(parentFragmentManager, it.name)
                }
            }
        }

        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.viewData
                    .collect { viewData ->
                        if (viewData.showNewTerms) {
                            (activity as? OnTermsListener)?.onShowTerms()
                            viewModel.markNewTermsAsShown()
                        } else if (viewData.showIntroduction) {
                            tutorialNavigator.openTutorialScreen(
                                context = requireContext(),
                                tutorials = createDashboardTutorial(),
                            )
                            viewModel.markIntroductionAsShown()
                        }
                    }
            }
        }

        handleActivityIntent(activity?.intent)
    }

    private fun handleActivityIntent(intent: Intent?) {
        if (intent?.getBooleanExtra(EXTRA_LOG_MENSTRUAL_CYCLE, false) == true) {
            intent.removeExtra(EXTRA_LOG_MENSTRUAL_CYCLE)
            logMenstrualCycle()
        }
    }

    private fun shareWorkout(shareWorkout: ActivitiesTabViewModel.ShareWorkout) {
        when (shareWorkout) {
            is ActivitiesTabViewModel.ShareWorkout.MultipleWorkout -> workoutShareHelper
                .toMultipleWorkoutShareWays(
                    context = requireContext(),
                    workoutHeader = shareWorkout.workoutHeader,
                    imageIndex = shareWorkout.imageIndex,
                    watchName = shareWorkout.watchName,
                )

            is ActivitiesTabViewModel.ShareWorkout.Workout -> {
                val context = requireContext()
                val (intent, options) = WorkoutSharePreviewActivity.newStartIntent(
                    context = context,
                    workoutHeader = shareWorkout.workoutHeader,
                    itemIndex = shareWorkout.imageIndex,
                    source = SportieShareSource.FEED,
                )
                context.startActivity(intent, options.toBundle())
            }

            is ActivitiesTabViewModel.ShareWorkout.Link -> workoutShareHelper.sendImplicitWorkoutLinkShareIntent(
                activity = requireActivity(),
                workoutHeader = shareWorkout.workoutHeader,
                source = SportieShareSource.FEED,
            )

            is ActivitiesTabViewModel.ShareWorkout.Error -> view?.let {
                Snackbar.make(it, R.string.error_generic_try_again, Snackbar.LENGTH_LONG)
                    .show()
            }
        }
    }

    private fun openNotification() {
        toolbarViewModel.viewState.value.notificationState.let { notificationState ->
            val context = requireContext()
            // when only has the emarsys messages, open the MarketingInboxActivity directly
            val openMarketingInbox =
                notificationState.notificationsEmpty && !notificationState.marketingEmpty
            val intent = if (openMarketingInbox) {
                MarketingInboxActivity.newIntent(context, MessageSource.INBOX)
            } else {
                NotificationActivity.newStartIntent(context)
            }
            context.startActivity(intent)
        }
    }

    abstract fun openWatchDevice()

    abstract fun openHeadsetDevice()

    private fun openMarketingBanner(banner: MarketingBannerInfo) {
        eventTracker.trackEvent(
            AnalyticsEvent.BANNER_CLICK,
            mapOf(
                AnalyticsEventProperty.BANNER_ID to banner.bannerId,
                AnalyticsEventProperty.BANNER_NAME to banner.name,
                AnalyticsEventProperty.BANNER_CLICK_AREA to "BannerCard",
                AnalyticsEventProperty.BANNER_TARGET_URL to (banner.link ?: ""),
            ),
        )

        val link = banner.link?.takeIf { it.isNotBlank() }?.toUri() ?: return
        when (banner.linkType) {
            MarketingBannerInfo.LinkType.DEEPLINK -> {
                val fragmentOrPathParts = deepLinkIntentBuilder.getFragmentsOrPathParts(link)
                val type = fragmentOrPathParts.getOrNull(1)?.lowercase(Locale.ROOT) ?: ""
                deepLinkIntentBuilder.getDeepLinkIntent(
                    context = requireContext(),
                    uri = link,
                    currentUserController = currentUserController,
                    fragmentOrPathParts = fragmentOrPathParts,
                    type = type
                )?.let {
                    startActivity(it)
                }
            }

            MarketingBannerInfo.LinkType.EXTERNAL_URL -> {
                try {
                    @Suppress("UnsafeImplicitIntentLaunch")
                    startActivity(Intent(Intent.ACTION_VIEW, link))
                } catch (e: ActivityNotFoundException) {
                    Timber.d(e, "No activity found to handle the link: $link")
                }
            }

            MarketingBannerInfo.LinkType.H5 -> {
                startActivity(
                    MarketingH5Activity.newIntent(
                        context = requireContext(),
                        url = link.toString(),
                        MessageSource.BANNER,
                    )
                )
            }

            null -> Unit
        }
    }

    private fun refresh() {
        homeActivityActions?.refreshAppData(force = true)
    }

    protected fun logMenstrualCycle() {
        if (insertMenstrualCycleViewModel.menstrualCycleEnabled) {
            openLogMenstrualCyclePicker()
        } else {
            onboardingResultLauncher.launch(
                menstrualCycleOnboardingNavigator.newOnboardingActivityIntent(
                    requireContext(),
                    OnboardingDoneReason.FIRST_PERIOD_LOGGED
                ).apply { putExtra(EXTRA_TRY_LOG_MENSTRUAL_CYCLE, true) }
            )
        }
    }

    private fun openLogMenstrualCyclePicker() {
        viewLifecycleOwner.lifecycleScope.launch {
            val periodDuration = insertMenstrualCycleViewModel.getPeriodDuration()
            LogMenstrualCycleFragment.create(
                LocalDate.now(),
                periodDuration,
                object : OnLogMenstrualCycleDoneListener {
                    override fun onDone(startDate: LocalDate, endDate: LocalDate) {
                        insertMenstrualCycleViewModel.insertMenstrualCycle(
                            startDate,
                            endDate,
                            LoggedFrom.HOME
                        )
                    }
                }
            ).show(childFragmentManager, "LogMenstrualCycle")
        }
    }

    private fun addWorkoutManually() {
        val context = requireContext()
        context.startActivity(WorkoutEditDetailsActivity.newStartIntentForNew(context))
    }

    private fun createIntervalWorkout() {
        val context = requireContext()
        context.startActivity(
            workoutPlannerNavigator.newListWorkoutPlansIntent(
                context,
                AnalyticsPropertyValue.WorkoutPlanner.PLANNER_SCREEN_SOURCE_HOME_SCREEN,
            )
        )
    }

    abstract fun downloadOfflineMap()

    abstract fun pairDevice()

    protected fun openDiaryCalendar(
        granularity: DiaryCalendarListContainer.Granularity,
        showActivitiesList: Boolean,
        source: String
    ) {
        diaryPagePreferences.edit {
            putString(
                DiaryPagePreferencesKeys.DIARY_CALENDAR_LAST_USED_GRANULARITY,
                granularity.value
            )
        }

        requireActivity().startActivity(
            homeActivityNavigator.newStartIntentToDiaryCalendar(
                content = requireContext(),
                source = source,
                showActivitiesList = showActivitiesList
            )
        )
    }

    fun updateCurrentTab(tabName: String) {
        lastVisibleTab = tabName
    }

    private fun sendLeaveHomeScreenEvent() {
        if (pageStartTime > 0) {
            val durationMs = System.currentTimeMillis() - pageStartTime
            val durationSeconds = durationMs / 1000
            
            eventTracker.trackEvent(
                AnalyticsEvent.LEAVE_HOME_SCREEN,
                mapOf(
                    AnalyticsEventProperty.PAGE_NAME to getTabPropertyValue(lastVisibleTab),
                    AnalyticsEventProperty.BROWSING_DURATION to durationSeconds
                )
            )
            
            pageStartTime = 0
        }
    }

    private fun getTabPropertyValue(tabName: String): String {
        return when (tabName) {
            DashboardTab.DASHBOARD.name -> AnalyticsPropertyValue.LeaveHomeScreenProperty.DASHBOARD
            DashboardTab.ACTIVITIES.name -> AnalyticsPropertyValue.LeaveHomeScreenProperty.ACTIVITY
            else -> AnalyticsPropertyValue.LeaveHomeScreenProperty.DASHBOARD
        }
    }
    
    private fun getTabNameForHomeScreen(tabName: String): String {
        return when (tabName) {
            DashboardTab.DASHBOARD.name -> AnalyticsPropertyValue.HOME_SCREEN_TABLE_NAME_PROPERTY.DASHBOARD
            DashboardTab.ACTIVITIES.name -> getActivitiesTabName()
            else -> AnalyticsPropertyValue.HOME_SCREEN_TABLE_NAME_PROPERTY.DASHBOARD
        }
    }
    
    private fun getActivitiesTabName(): String {
        return when (activitiesTabViewModel.viewState.value.selectedFilterTag) {
            FilterTag.ALL -> AnalyticsPropertyValue.HOME_SCREEN_TABLE_NAME_PROPERTY.ACTIVITY_ALL
            FilterTag.Me -> AnalyticsPropertyValue.HOME_SCREEN_TABLE_NAME_PROPERTY.ACTIVITY_ME
            FilterTag.FOLLOWING -> AnalyticsPropertyValue.HOME_SCREEN_TABLE_NAME_PROPERTY.ACTIVITY_FOLLOWING
            FilterTag.SUUNTO -> AnalyticsPropertyValue.HOME_SCREEN_TABLE_NAME_PROPERTY.ACTIVITY_ALL
        }
    }
    
    private fun getTabNameByIndex(tabIndex: Int): String {
        return when (tabIndex) {
            0 -> getTabNameForHomeScreen(DashboardTab.DASHBOARD.name)
            1 -> getTabNameForHomeScreen(DashboardTab.ACTIVITIES.name)
            else -> getTabNameForHomeScreen(DashboardTab.DASHBOARD.name)
        }
    }

    companion object {
        const val FRAGMENT_TAG = "com.stt.android.home.dashboard.DashboardFragment.FRAGMENT_TAG"
        const val EXTRA_LOG_MENSTRUAL_CYCLE = "com.stt.android.EXTRA_LOG_MENSTRUAL_CYCLE"

        private const val ROUTE_DASHBOARD = "dashboard"
        private const val ROUTE_SEARCH = "search"

        fun newInstance(): BaseDashboardFragment = DashboardFragment()
    }
}
