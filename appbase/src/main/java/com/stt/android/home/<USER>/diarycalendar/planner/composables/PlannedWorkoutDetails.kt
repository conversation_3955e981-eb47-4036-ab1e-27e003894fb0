package com.stt.android.home.diary.diarycalendar.planner.composables

import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.net.toUri
import com.stt.android.R
import com.stt.android.compose.component.SuuntoCoachCardExpandable
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workouts.extensions.intensity.nameRes
import com.stt.android.home.diary.diarycalendar.planner.models.PlannedWorkoutUiState
import com.stt.android.home.diary.diarycalendar.planner.models.fakePlannedWorkoutUiState1
import com.stt.android.home.diary.diarycalendar.planner.models.toViewData
import com.stt.android.ui.components.workout.WorkoutDescription
import com.stt.android.ui.components.workout.WorkoutSummary
import com.stt.android.utils.takeIfNotEmpty
import com.stt.android.workoutdetail.workoutvalues.WorkoutValuesGridItemData
import com.stt.android.workoutdetail.workoutvalues.composables.WorkoutValuesGrid

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PlannedWorkoutDetailsScreen(
    date: String,
    plannedWorkoutUiState: PlannedWorkoutUiState,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = date.uppercase()
                    )
                },
                navigationIcon = {
                    SuuntoIconButton(
                        icon = SuuntoIcons.ActionBack,
                        onClick = onBackClick,
                    )
                }
            )
        }
    ) { internalPadding ->
        Surface(
            modifier = Modifier
                .padding(internalPadding)
                .narrowContentWithBgColors(
                    backgroundColor = MaterialTheme.colorScheme.surface,
                    outerBackgroundColor = MaterialTheme.colorScheme.background
                )
        ) {
            PlannedWorkoutDetails(
                plannedWorkoutUiState = plannedWorkoutUiState,
            )
        }
    }
}

@Composable
private fun PlannedWorkoutDetails(
    plannedWorkoutUiState: PlannedWorkoutUiState,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val configuration = LocalConfiguration.current

    val viewData = remember(plannedWorkoutUiState, configuration) {
        plannedWorkoutUiState.toViewData(context)
    }

    val readMoreUri =
        "https://www.suunto.com/sports/News-Articles-container-page/Figure-out-your-training-zones-and-supercharge-your-fitness/".toUri()
    val items = listOfNotNull(
        // We don't know yet the final names or attributes, so I am leaving them like this in this commit
        // Will be cleaned later when we know what values are returned
        WorkoutValuesGridItemData(
            name = stringResource(R.string.workout_planner_duration),
            value = plannedWorkoutUiState.workoutTargetsUiState.duration
        ),
        plannedWorkoutUiState.workoutTargetsUiState.distance?.let {
            WorkoutValuesGridItemData(
                name = stringResource(R.string.workout_planner_distance),
                value = "$it ${
                    plannedWorkoutUiState.workoutTargetsUiState.distanceUnit?.run {
                        stringResource(this)
                    }.orEmpty()
                }"
            )
        },
        plannedWorkoutUiState.workoutTargetsUiState.avgPace?.let {
            WorkoutValuesGridItemData(
                name = stringResource(R.string.workout_planner_avg_pace_target),
                value = "$it ${
                    plannedWorkoutUiState.workoutTargetsUiState.avgPaceUnit?.run {
                        stringResource(this)
                    }.orEmpty()
                }",
            )
        },
        plannedWorkoutUiState.workoutTargetsUiState.targetHrZone?.let {
            WorkoutValuesGridItemData(
                name = stringResource(R.string.workout_planner_target_hr_zone),
                value = "$it ${
                    plannedWorkoutUiState.workoutTargetsUiState.targetHrZoneUnit?.run {
                        stringResource(this)
                    }.orEmpty()
                }",
            )
        },
        plannedWorkoutUiState.workoutTargetsUiState.targetPaceZone?.let {
            WorkoutValuesGridItemData(
                name = stringResource(R.string.workout_planner_target_pace_zone),
                value = "$it ${
                    plannedWorkoutUiState.workoutTargetsUiState.targetPaceZoneUnit?.run {
                        stringResource(this)
                    }.orEmpty()
                }",
            )
        },
        plannedWorkoutUiState.workoutTargetsUiState.targetPowerZone?.let {
            WorkoutValuesGridItemData(
                name = stringResource(R.string.workout_planner_target_power_zone),
                value = "$it ${
                    plannedWorkoutUiState.workoutTargetsUiState.targetPowerZoneUnit?.run {
                        stringResource(this)
                    }.orEmpty()
                }",
            )
        },
        WorkoutValuesGridItemData(
            name = stringResource(R.string.workout_planner_training_load),
            value = plannedWorkoutUiState.workoutTargetsUiState.trainingLoad
        ),
        WorkoutValuesGridItemData(
            name = stringResource(R.string.workout_planner_intensity_zone),
            value = stringResource(
                R.string.workout_planner_intensity_zone_value,
                plannedWorkoutUiState.workoutTargetsUiState.intensityZone
            )
        ),
        plannedWorkoutUiState.workoutTargetsUiState.cardioImpacts.mapNotNull { it.nameRes() }
            .takeIfNotEmpty()
            ?.let { impactsRes ->
                WorkoutValuesGridItemData(
                    name = stringResource(R.string.workout_planner_impact_cardio),
                    value = impactsRes.map { stringResource(it) }.joinToString(", ")
                )
            },
        plannedWorkoutUiState.workoutTargetsUiState.muscularImpacts.mapNotNull { it.nameRes() }
            .takeIfNotEmpty()
            ?.let { impactsRes ->
                WorkoutValuesGridItemData(
                    name = stringResource(R.string.workout_planner_impact_muscular),
                    value = impactsRes.map { stringResource(it) }.joinToString(", ")
                )
            }
    )

    val rows = (items.size + 1) / 2
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .background(MaterialTheme.colorScheme.surface),
    ) {
        Column(modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium)) {
            WorkoutSummary(
                activityTypeId = viewData.activityTypeId,
                activityIconFlavor = viewData.activityIconFlavor,
                title = viewData.title,
                subtitle = viewData.subtitle,
                username = viewData.username,
                userImageUrl = viewData.userImageUrl,
                isPrivate = viewData.isPrivate,
                onUserClick = null,
                modifier = Modifier.padding(
                    top = MaterialTheme.spacing.medium,
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                ),
                titleTextStyle = MaterialTheme.typography.bodyMegaBold
            )

            if (viewData.description.isNotBlank()) {
                WorkoutDescription(
                    description = viewData.description,
                    maxLines = viewData.maxDescriptionLines,
                    modifier = Modifier.padding(
                        top = MaterialTheme.spacing.medium,
                        start = MaterialTheme.spacing.medium,
                        end = MaterialTheme.spacing.medium,
                    ),
                )
            }
        }
        AppTheme {
            WorkoutValuesGrid(
                items = items,
                rows = rows,
                onValueClicked = {},
                drawBorder = true,
            )
        }
        SuuntoCoachCardExpandable(
            title = stringResource(R.string.workout_planner_zones_update_title),
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
        ) {
            Text(
                text = stringResource(R.string.workout_planner_zones_update_message),
                style = MaterialTheme.typography.bodyLarge,
            )
            TextButton(
                onClick = {
                    val intent = Intent(Intent.ACTION_VIEW, readMoreUri).apply {
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    }
                    val chooser = Intent.createChooser(intent, null)
                    context.startActivity(chooser)
                },
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(top = MaterialTheme.spacing.medium),
                shape = MaterialTheme.shapes.small,
            ) {
                Text(
                    text = stringResource(R.string.workout_planner_read_mode),
                    style = MaterialTheme.typography.bodyBold,
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PlannedWorkoutDetailsPreview() {
    M3AppTheme {
        PlannedWorkoutDetails(
            plannedWorkoutUiState = fakePlannedWorkoutUiState1,
        )
    }
}
