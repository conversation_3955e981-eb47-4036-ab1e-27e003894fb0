package com.stt.android.home.diary.diarycalendar

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.domain.diarycalendar.DiaryCalendarTotalValues
import com.stt.android.domain.diarycalendar.LocationWithActivityType
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleData
import java.time.LocalDate

data class RouteAndActivityType(
    val startTime: Long,
    val routePoints: List<LatLng>,
    val activityType: Int
)

data class DiaryCalendarListContainer(
    val workoutCount: Int,
    val bubbleData: List<DiaryBubbleData>,
    val activityStatsWithTotals: List<Pair<ActivityType, TotalValues>>,
    val locations: List<LocationWithActivityType>,
    val activityLocations: Map<Int, List<LocationWithActivityType>>,
    val routes: List<RouteAndActivityType>,
    val activityRoutes: Map<Int, List<RouteAndActivityType>>,
    val bounds: LatLngBounds?,
    val activityBounds: Map<Int, LatLngBounds?>,
    val mapActivities: List<ActivityType>,
    val loadingComplete: Boolean,
    val onMapClicked: () -> Unit, // make sure not to pass a lambda here
    val totalValues: DiaryCalendarTotalValues,
    val totalsForPeriod: TotalValues,
    val granularity: Granularity,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val timeRange: String,
    val displayMode: Int,
    val onSportRowClicked: (LocalDate, LocalDate, List<Int>, ActivityType) -> Unit, // make sure not to pass a lambda here
    val onShareSummaryButtonClicked: (LocalDate, LocalDate, Int, Int) -> Unit // make sure not to pass a lambda here
) {
    enum class Granularity(val value: String, val analyticsPropertyValue: String) {
        WEEK("WEEK", AnalyticsPropertyValue.DiaryCalendarGranularity.WEEKLY),
        LAST_30_DAYS("LAST_30_DAYS", AnalyticsPropertyValue.DiaryCalendarGranularity.LAST_30_DAYS),
        MONTH("MONTH", AnalyticsPropertyValue.DiaryCalendarGranularity.MONTHLY),
        YEAR("YEAR", AnalyticsPropertyValue.DiaryCalendarGranularity.YEARLY);

        companion object {
            fun valueOfStringOrNull(value: String?): Granularity? =
                entries.firstOrNull { it.value == value }
        }
    }
}
