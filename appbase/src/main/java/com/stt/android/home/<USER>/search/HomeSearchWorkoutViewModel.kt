package com.stt.android.home.dashboardv2.search

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.controllers.loadWorkouts
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.BackendWorkout
import com.stt.android.domain.user.UserWorkoutSummary
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboard.card.WorkoutCardLoader
import com.stt.android.social.workoutlist.DateHeader
import com.stt.android.social.workoutlist.search.SearchWorkoutViewState
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import com.stt.android.ui.utils.TextFormatter
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@OptIn(ExperimentalCoroutinesApi::class, FlowPreview::class)
@HiltViewModel
class HomeSearchWorkoutViewModel @Inject constructor(
    @param:ApplicationContext private val context: Context,
    private val currentUserController: CurrentUserController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val workoutCardLoader: WorkoutCardLoader,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    val workoutCardActionsHandler: WorkoutCardActionsHandler,
) : ViewModel() {

    private val _searchQuery = MutableSharedFlow<String>(extraBufferCapacity = 1)

    private val _viewState = MutableStateFlow(
        SearchWorkoutViewState("", emptyList(), false)
    )
    val viewState: StateFlow<SearchWorkoutViewState> = _viewState.asStateFlow()

    private val _workoutUpdateFlow = workoutHeaderController
        .currentUserWorkoutUpdated
        .onStart { emit(Unit) }

    init {
        viewModelScope.launch {
            _viewState.update {
                updatedViewState("")
            }
        }
        _searchQuery
            .debounce(DELAY_FOR_SEARCH)
            .distinctUntilChanged()
            .flatMapLatest { query ->
                flow {
                    if (query.trim().isEmpty()) {
                        emit(updatedViewState(""))
                    } else {
                        emit(
                            _viewState.value.copy(
                                searching = true,
                            )
                        )
                        emit(updatedViewState(query))
                    }
                }
            }
            .onEach { state ->
                _viewState.update { state }
            }
            .launchIn(viewModelScope)

        _workoutUpdateFlow
            .onEach {
                val keyword = _viewState.value.keyword
                val searched = _viewState.value.dateAndWorkouts.any()
                if (searched) {
                    val state = updatedViewState(keyword)
                    _viewState.update { state }
                }
            }
            .launchIn(viewModelScope)
    }

    private suspend fun updatedViewState(query: String): SearchWorkoutViewState =
        withContext(coroutinesDispatchers.io) {
            val filteredWorkouts = getAllWorkouts()
                .takeUnless { it.isEmpty() }
                ?.filter {
                    (query.isBlank()) || if (query.isBlank()) {
                        false
                    } else {
                        val searchTerms = query.lowercase().split(" ").toTypedArray()
                        it.second.applyFilter(searchTerms, context.resources)
                    }
                }
                ?.sortedByDescending {
                    it.second.startTime
                }
                ?: emptyList()

            val user = currentUserController.currentUser
            val filteredWorkoutCards = workoutCardLoader.buildWorkoutCards(
                userWorkoutPairs = filteredWorkouts.map { user to it.second },
                isOwnWorkout = true,
                includeCover = true,
            )
            val filteredWorkoutsWithDates = filteredWorkoutCards.groupBy {
                TextFormatter.formatYearMonth(context, it.workoutHeader.startTime)
            }.flatMap { (date, workouts) ->
                listOf(DateHeader(date, workouts.size)) + workouts
            }

            _viewState.value.copy(
                searching = false,
                dateAndWorkouts = filteredWorkoutsWithDates,
                workoutSummary = UserWorkoutSummary(
                    filteredWorkouts.size,
                    filteredWorkouts.sumOf { it.second.totalDistance },
                    filteredWorkouts.sumOf { it.second.totalTime },
                    filteredWorkouts.sumOf { it.second.energyConsumption },
                ),
            )
        }

    private suspend fun getAllWorkouts(): List<Pair<BackendWorkout?, WorkoutHeader>> =
        withContext(coroutinesDispatchers.io) {
            runSuspendCatching {
                workoutHeaderController.loadWorkouts(currentUserController.username).last().map {
                    null to it
                }
            }.onFailure {
                Timber.w(it, "Failed to load workouts")
            }.getOrElse { emptyList() }
        }

    fun onQueryChange(query: String) {
        if (_viewState.value.keyword == query) return
        _viewState.update {
            it.copy(
                keyword = query,
                searching = query.trim().isNotBlank(),
            )
        }
        _searchQuery.tryEmit(query)
    }

    private companion object {
        private const val DELAY_FOR_SEARCH = 500L
    }
}
