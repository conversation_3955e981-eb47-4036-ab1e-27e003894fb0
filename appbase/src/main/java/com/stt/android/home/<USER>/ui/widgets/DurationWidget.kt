package com.stt.android.home.dashboardv2.ui.widgets

import android.graphics.RenderEffect
import android.graphics.Shader
import android.os.Build
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asComposeRenderEffect
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonMonthlyLineChartWidget
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonTargetWidget
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonWeeklyBarChartWidget
import com.stt.android.home.dashboardv2.ui.widgets.common.PremiumForeground
import com.stt.android.home.dashboardv2.ui.widgets.common.generateWidgetTitle
import com.stt.android.home.dashboardv2.widgets.DurationWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Period
import java.time.DayOfWeek

@Composable
internal fun DurationWidget(
    widgetInfo: DurationWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier,
    ) {
        val internalModifier = Modifier
            .graphicsLayer {
                if (widgetInfo.premiumRequired && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    renderEffect = RenderEffect.createBlurEffect(
                        10f,
                        10f,
                        Shader.TileMode.CLAMP,
                    ).asComposeRenderEffect()
                }
            }
        when (widgetInfo.period) {
            is Period.ThisWeek -> CommonTargetWidget(
                editMode = editMode,
                headerRes = R.string.dashboard_widget_duration_name,
                subheaderText = stringResource(R.string.this_week),
                colorRes = R.color.dashboard_widget_duration,
                iconRes = R.drawable.dashboard_widget_duration,
                titleText = widgetInfo.title,
                subtitleText = widgetInfo.subtitle,
                progress = widgetInfo.progresses.getOrElse(0) { 0f },
                onClick = onClick,
                onLongClick = onLongClick,
                onRemoveClick = onRemoveClick,
                modifier = internalModifier,
            )

            Period.Last7Days ->
                CommonWeeklyBarChartWidget(
                    editMode = editMode,
                    headerRes = R.string.dashboard_widget_duration_name,
                    subheaderText = stringResource(R.string.last_seven_days),
                    colorRes = R.color.dashboard_widget_duration,
                    iconRes = R.drawable.dashboard_widget_duration,
                    titleText = widgetInfo.title,
                    subtitleText = widgetInfo.subtitle,
                    subtitleIconRes = widgetInfo.subtitleIconRes,
                    progresses = widgetInfo.progresses,
                    onClick = onClick,
                    onLongClick = onLongClick,
                    onRemoveClick = onRemoveClick,
                    modifier = internalModifier,
                )

            Period.ThisMonth, Period.Last30Days, is Period.CustomPeriod -> {
                val subheaderRes = if (widgetInfo.period is Period.ThisMonth) {
                    R.string.this_month
                } else {
                    R.string.last_30_days
                }
                CommonMonthlyLineChartWidget(
                    editMode = editMode,
                    headerRes = R.string.dashboard_widget_duration_name,
                    subheaderText = stringResource(subheaderRes),
                    colorRes = R.color.dashboard_widget_duration,
                    iconRes = R.drawable.dashboard_widget_duration,
                    titleText = widgetInfo.title,
                    subtitleText = widgetInfo.subtitle,
                    subtitleIconRes = widgetInfo.subtitleIconRes,
                    period = widgetInfo.period,
                    progresses = widgetInfo.progresses,
                    onClick = onClick,
                    onLongClick = onLongClick,
                    onRemoveClick = onRemoveClick,
                    modifier = internalModifier,
                )
            }
        }

        if (widgetInfo.premiumRequired) {
            PremiumForeground(
                editMode = editMode,
                onRemoveClick = onRemoveClick,
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun DurationWidgetPreview(
    @PreviewParameter(DurationWidgetInfoProvider::class) widgetInfo: DurationWidgetInfo
) {
    M3AppTheme {
        DurationWidget(
            widgetInfo = widgetInfo,
            editMode = false,
            onClick = {},
            onLongClick = {},
            onRemoveClick = {},
            modifier = Modifier.size(170.dp),
        )
    }
}

private class DurationWidgetInfoProvider : PreviewParameterProvider<DurationWidgetInfo> {
    override val values: Sequence<DurationWidgetInfo> = sequenceOf(
        DurationWidgetInfo(
            period = Period.ThisWeek(DayOfWeek.MONDAY),
            progresses = listOf(0.2f),
            title = generateWidgetTitle("8", "h"),
            subtitle = "-1h 25min of target",
            premiumRequired = false,
        ),
        DurationWidgetInfo(
            period = Period.Last7Days,
            progresses = listOf(0.1f, 0.2f, 0.3f, 0.0f, 0.5f, 1.0f, 0.2f),
            title = generateWidgetTitle("8", "h"),
            subtitle = "1h 15min",
            subtitleIconRes = R.drawable.widget_down_arrow,
            premiumRequired = false,
        ),
        DurationWidgetInfo(
            period = Period.ThisMonth,
            progresses = listOf(0.1f, 0.2f, 0.0f, 0.3f, 0.0f, 0.0f, 0.4f)
                .scanIndexed(0f) { _, acc, value ->
                    acc + value
                },
            title = generateWidgetTitle("8", "h"),
            subtitle = "1h 15min",
            subtitleIconRes = R.drawable.widget_down_arrow,
            premiumRequired = false,
        ),
        DurationWidgetInfo(
            period = Period.Last30Days,
            progresses = (0..5).map {
                listOf(0.1f, 0.2f, 0.0f, 0.3f, 0.0f, 0.0f, 0.4f)
            }.flatten().scanIndexed(0f) { _, acc, value ->
                acc + value / 6f
            },
            title = generateWidgetTitle("8", "h"),
            subtitle = "1h 15min",
            subtitleIconRes = R.drawable.widget_down_arrow,
            premiumRequired = true,
        )
    )
}
