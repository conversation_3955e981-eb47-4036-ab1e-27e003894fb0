package com.stt.android.home.dashboard

import android.content.Context
import android.util.AttributeSet
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.cardview.widget.CardView
import androidx.core.view.isVisible
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.coroutineScope
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.maps.android.PolyUtil
import com.stt.android.R
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.diarycalendar.LocationWithActivityType
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.dashboard.widget.DashboardWidget
import com.stt.android.home.dashboard.widget.DashboardWidgetDelegate
import com.stt.android.home.dashboard.widget.DashboardWidgetView
import com.stt.android.home.diary.diarycalendar.RouteAndActivityType
import com.stt.android.home.mytracks.MyTracksUtils
import com.stt.android.maps.MapType
import com.stt.android.maps.SuuntoBitmapDescriptorFactory
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMapView
import com.stt.android.maps.location.SuuntoLocationSource
import com.stt.android.maps.newLatLngBounds
import com.stt.android.ui.map.MapHelper
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.lang.ref.WeakReference
import kotlin.math.min

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_WRAP_HEIGHT)
class DashboardMapView
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr),
    DefaultLifecycleObserver,
    DashboardWidgetView,
    DashboardWidget by DashboardWidgetDelegate() {

    @set:[ModelProp]
    var locations: List<LocationWithActivityType> = emptyList()

    @set:[ModelProp]
    var routes: List<RouteAndActivityType> = emptyList()

    @set:[ModelProp]
    var bounds: LatLngBounds? = null

    @set:[ModelProp]
    @DrawableRes
    var granularityIconResId: Int? = null

    @set:[ModelProp]
    var mapType: MapType? = null

    @set:[ModelProp]
    var animateMyTracks: Boolean = false

    @set:[
    ModelProp(ModelProp.Option.DoNotHash)
    ]
    var locationSource: SuuntoLocationSource? = null

    @set:[
    ModelProp(ModelProp.Option.DoNotHash)
    ]
    lateinit var myTracksUtils: MyTracksUtils

    @set:[
    ModelProp(ModelProp.Option.DoNotHash)
    ]
    lateinit var fragmentLifecycle: Lifecycle

    private val bitmapDescriptorFactory = SuuntoBitmapDescriptorFactory(context)

    private var animationJob: Job? = null
    private var map: SuuntoMap? = null
    private lateinit var mapView: SuuntoMapView
    override val clickContainer: Button by lazy {
        findViewById(R.id.dashboard_map_touch_area)
    }
    private val cardView: CardView by lazy {
        findViewById(R.id.dashboard_map_card_view)
    }
    private val granularityIcon: ImageView by lazy {
        findViewById(R.id.dashboard_map_granularity_icon)
    }

    override val removeButton: ImageButton by lazy {
        findViewById(R.id.dashboard_map_remove_button)
    }

    private val mapContainer: FrameLayout by lazy {
        findViewById(R.id.map_container)
    }

    private var onScaleListener: SuuntoMap.OnScaleListener? = null

    init {
        inflate(context, R.layout.dashboard_map, this)
        initMapView()
    }

    private fun initMapView() {
        val option = SuuntoMapOptions().apply {
            this.mapsProvider = MapHelper.getMapsProviderNameWithoutGoogle()
            this.minZoomPreference = 0.0f
            this.textureMode = true
            this.uiAttribution = false
            this.uiCompass = false
            this.uiLogo = false
            this.uiMapToolbar = false
            this.uiRotateGestures = false
            this.uiScrollGestures = false
            this.uiTiltGestures = false
            this.uiZoomGestures = false
            this.uiZoomControls = false
            this.showMyLocationMarker = false
        }
        mapView = SuuntoMapView(context, option)
        mapContainer.addView(mapView)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val heightPx = resources.getDimensionPixelSize(R.dimen.dashboard_grid_item_height)
        val heightSpec = MeasureSpec.makeMeasureSpec(heightPx, MeasureSpec.EXACTLY)
        val widthPx = resources.getDimensionPixelSize(R.dimen.dashboard_grid_item_width)
        val widthSpec = MeasureSpec.makeMeasureSpec(widthPx, MeasureSpec.EXACTLY)
        super.onMeasure(widthSpec, heightSpec)
        cardView.radius = min(measuredHeight, measuredWidth) / 2f
    }

    @AfterPropsSet
    fun onPropsSet() {
        mapType?.name
            ?.let(mapView::setInitialMapTypeHint)
        onScaleListener = object : SuuntoMap.OnScaleListener {
            override fun onScaleBegin() {
                // No action
            }

            override fun onScaleEnd() {
                val mapRef = WeakReference(map)
                scaleMarkers(mapRef)
            }
        }

        mapView.getMapAsync { map ->
            <EMAIL> = map
            onScaleListener?.let { map.addOnScaleListener(it) }

            mapType?.let { mapType ->
                MapHelper.updateMapType(map, mapType, null)
            }

            updateMarkers()
        }

        granularityIconResId?.let {
            granularityIcon.setImageResource(it)
            granularityIcon.isVisible = true
        } ?: run {
            granularityIcon.isVisible = false
        }

        bindDashboardWidgetView(this)
    }

    private fun scaleMarkers(mapRef: WeakReference<SuuntoMap?>) {
        val map = mapRef.get() ?: return
        map.getCameraPosition()?.zoom?.let {
            myTracksUtils.scaleDotRadius(map, it)
        }
    }

    private fun updateMarkers() {
        val map = map ?: return

        myTracksUtils.removeMarkers(map)
        myTracksUtils.removePolylines(map)

        cancelAnimation(redraw = false)
        animationJob = fragmentLifecycle.coroutineScope.launch {
            runSuspendCatching {
                delay(100)
                myTracksUtils.drawMyTracksMarkers(
                    locations,
                    map,
                    bitmapDescriptorFactory
                )

                when {
                    routes.isEmpty() -> {
                        zoomMap()
                    }
                    animateMyTracks -> {
                        myTracksUtils.animateMyTracksLatestThenRest(
                            context,
                            map,
                            routes,
                            bounds
                        )
                        // Zooming the map is handled in animateMyTracksLatestThenRest.
                    }
                    else -> {
                        myTracksUtils.drawMyTracks(
                            context,
                            map,
                            routes,
                            simplifyRoutes = true
                        )
                        zoomMap()
                    }
                }
            }.onFailure { e ->
                Timber.w(e, "Drawing tracks failed")
                redrawMyTracks()
            }
        }
    }

    private fun redrawMyTracks() {
        val context = context
        val map = map
        if (context != null && map != null) {
            fragmentLifecycle.coroutineScope.launch {
                myTracksUtils.removePolylines(map)
                myTracksUtils.drawMyTracks(context, map, routes, true)
                zoomMap()
            }
        }
    }

    private fun zoomMap() {
        bounds?.let { bounds ->
            map?.animateCamera(
                newLatLngBounds(
                    bounds,
                    resources.getDimensionPixelOffset(R.dimen.size_spacing_xlarge)
                )
            )
        } ?: locations.firstOrNull()?.let { location ->
            map?.let { map ->
                MapHelper.moveCameraToLatLng(
                    map,
                    LatLng(location.latitude, location.longitude),
                    true
                )
            }
        } ?: zoomToCurrentLocation()
    }

    private fun zoomToCurrentLocation() {
        locationSource?.getLastKnownLocation { location ->
            mapView.getMapAsync { map ->
                MapHelper.moveCameraToLatLng(
                    map,
                    LatLng(location.latitude, location.longitude),
                    true
                )
            }
        }
    }

    private fun cancelAnimation(redraw: Boolean) {
        if (animationJob?.isActive == true) {
            animationJob?.cancel()
            animationJob = null

            if (redraw) {
                redrawMyTracks()
            }
        }
    }

    private var observingFragmentLifecycle = false
    fun bind() {
        Timber.d("bind()")
        map?.let {
            myTracksUtils.removePolylines(it)
            myTracksUtils.removeMarkers(it)
        }
        // This view follows fragment lifecycle, even if unbound from the list. Observer is
        // unregistered in onDestroy
        if (!observingFragmentLifecycle) {
            fragmentLifecycle.addObserver(this)
            observingFragmentLifecycle = true
        } else {
            // Follow lifecycle callbacks similarly than the example at
            // https://github.com/mapbox/mapbox-gl-native/pull/13132
            onStart()
            onResume()
        }
    }

    fun unbind() {
        Timber.d("unbind()")
        cancelAnimation(redraw = false)
        clearMap()

        // Follow lifecycle callbacks similarly than the example at
        // https://github.com/mapbox/mapbox-gl-native/pull/13132
        onPause()
        onStop()
    }

    private fun clearMap() {
        map?.let {
            onScaleListener?.let { listener -> it.removeOnScaleListener(listener) }
            myTracksUtils.removePolylines(it)
            myTracksUtils.removeMarkers(it)
        }
        map = null
    }

    override fun onCreate(owner: LifecycleOwner) {
        // instance state is not saved, map is always initialized from scratch
        mapView.onCreate(savedInstanceState = null)
    }

    override fun onResume(owner: LifecycleOwner) {
        onResume()
    }

    private fun onResume() {
        mapView.onResume()
    }

    override fun onPause(owner: LifecycleOwner) {
        onPause()
    }

    private fun onPause() {
        cancelAnimation(redraw = true)
        mapView.onPause()
    }

    override fun onStart(owner: LifecycleOwner) {
        onStart()
    }

    private fun onStart() {
        mapView.onStart()
    }

    override fun onStop(owner: LifecycleOwner) {
        onStop()
    }

    private fun onStop() {
        Timber.d("onStop")
        mapView.onStop()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        Timber.d("onDestroy")
        clearMap()
        mapView.onDestroy()
        fragmentLifecycle.removeObserver(this)
        observingFragmentLifecycle = false
    }
}

data class DashboardMapViewData(
    val locations: List<LocationWithActivityType> = emptyList()
) {
    val routes: List<RouteAndActivityType> = locations
        .filterNot { ActivityType.valueOf(it.activityType).isIndoor }
        .map {
            RouteAndActivityType(
                startTime = it.startTime,
                routePoints = it.polyline?.let { polyline -> PolyUtil.decode(polyline) } ?: emptyList(),
                activityType = it.activityType
            )
        }

    val bounds: LatLngBounds? = locations.flatMap {
        it.polyline?.let { polyline -> PolyUtil.decode(polyline) } ?: emptyList()
    }
        .let { points ->
            if (points.isNotEmpty()) {
                LatLngBounds.builder().apply { points.forEach { point -> include(point) } }.build()
            } else {
                null
            }
        }
}
