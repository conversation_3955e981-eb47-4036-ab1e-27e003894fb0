package com.stt.android.home.diary.diarycalendar

import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.edit
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavDirections
import com.google.android.material.tabs.TabLayout
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.common.navigation.findNavControllerByFragmentIdUsingChildFragmentManager
import com.stt.android.databinding.FragmentDiaryCalendarBinding
import com.stt.android.di.DiaryPagePreferences
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.home.diary.DiaryPagePreferencesKeys
import com.stt.android.home.diary.diarycalendar.CalendarContainerViewModel.Companion.LAST_30_DAYS_TAB_POSITION
import com.stt.android.home.diary.diarycalendar.CalendarContainerViewModel.Companion.MONTH_TAB_POSITION
import com.stt.android.home.diary.diarycalendar.CalendarContainerViewModel.Companion.WEEK_TAB_POSITION
import com.stt.android.home.diary.diarycalendar.CalendarContainerViewModel.Companion.YEAR_TAB_POSITION
import com.stt.android.home.diary.diarycalendar.last30days.DiaryCalendarLast30DaysPagerFragmentDirections
import com.stt.android.home.diary.diarycalendar.month.DiaryCalendarMonthPagerFragmentArgs
import com.stt.android.home.diary.diarycalendar.month.DiaryCalendarMonthPagerFragmentDirections
import com.stt.android.home.diary.diarycalendar.week.DiaryCalendarWeekPagerFragmentDirections
import com.stt.android.home.diary.diarycalendar.year.DiaryCalendarYearPagerFragmentDirections
import com.stt.android.menstrualcycle.MenstrualCycleOnboardingNavigator
import com.stt.android.menstrualcycle.regularity.MenstrualCycleRegularitySheetCreator
import com.stt.android.remoteconfig.api.RemoteConfig
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.CalendarProvider
import timber.log.Timber
import java.time.LocalDate
import java.time.Year
import javax.inject.Inject

// Note: derived classes should use @AndroidEntryPoint annotation
abstract class BaseDiaryCalendarContainerFragment : Fragment() {

    @Inject
    @DiaryPagePreferences
    lateinit var diaryPagePreferences: SharedPreferences

    @Inject
    lateinit var calendarProvider: CalendarProvider

    @Inject
    lateinit var datahubAnalyticsTracker: DatahubAnalyticsTracker

    @Inject
    lateinit var menstrualCycleOnboardingNavigator: MenstrualCycleOnboardingNavigator

    @Inject
    lateinit var menstrualCycleRegularitySheetCreator: MenstrualCycleRegularitySheetCreator

    @Inject
    @FeatureTogglePreferences
    lateinit var featureTogglePreferences: SharedPreferences

    @Inject
    lateinit var remoteConfig: RemoteConfig

    private var binding: FragmentDiaryCalendarBinding? = null

    private val navigationListener =
        NavController.OnDestinationChangedListener { _, destination, _ ->
            destinationChanged(destination)
        }

    private val onTabSelectedListener = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            viewModel.selectedTabPosition = binding?.tabLayout?.selectedTabPosition
            openTab(tab.position)
        }

        override fun onTabUnselected(tab: TabLayout.Tab?) {
        }

        override fun onTabReselected(tab: TabLayout.Tab?) {}
    }

    private val viewModel by lazy {
        if (remoteConfig.isAiPlannerEnabled()) {
            // Need to share the view model with NewDiaryCalendarContainerFragment
            ViewModelProvider(requireParentFragment().requireParentFragment())[CalendarContainerViewModel::class.java]
        } else {
            ViewModelProvider(this)[CalendarContainerViewModel::class.java]
        }
    }

    private val navController get() = findNavControllerByFragmentIdUsingChildFragmentManager(R.id.diary_calendar_nav_host_fragment)

    private val menuDelegate by lazy {
        CalendarMenuDelegate(
            this,
            menstrualCycleOnboardingNavigator,
            menstrualCycleRegularitySheetCreator,
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = FragmentDiaryCalendarBinding.inflate(inflater, container, false).also {
        binding = it.apply {
            // Remove binding.toolbar from the layout when AI planner is launched and feature toggle removed.
            toolbar.isVisible = !remoteConfig.isAiPlannerEnabled()
        }
    }.root

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Set navGraph dynamically to pass args to the start destination
        val showActivitiesList = arguments?.getBoolean(ARG_SHOW_ACTIVITIES_LIST, false) ?: false
        val navGraph = navController.navInflater.inflate(R.navigation.diary_calendar_nav_graph)
        navController.setGraph(
            navGraph,
            DiaryCalendarMonthPagerFragmentArgs.Builder()
                .setShowActivitiesList(showActivitiesList)
                .build()
                .toBundle()
        )

        handleDeepLinkAndRestoreLastUsedView(showActivitiesList)
        sendCalendarScreenEvent()

        binding?.addButton?.setOnClickListenerThrottled {
            menuDelegate.showAddMenu(
                anchor = it,
                date = viewModel.initPickerDate()
            )
        }

        menuDelegate.onViewCreated()
    }

    override fun onStart() {
        super.onStart()
        navController.addOnDestinationChangedListener(navigationListener)
        binding?.tabLayout?.addOnTabSelectedListener(onTabSelectedListener)
    }

    private fun destinationChanged(destination: NavDestination) {
        val selectedTabPosition = binding?.tabLayout?.selectedTabPosition?.apply {
            // Needed for getting the correct date for logging periods when user enters the calendar
            // and swipes back in time without switching tabs (week/month/year/last 30 days)
            viewModel.selectedTabPosition = this
        }

        val destinationToTabMap = mapOf(
            R.id.diaryCalendarWeekPagerFragment to WEEK_TAB_POSITION,
            R.id.diaryCalendarMonthPagerFragment to MONTH_TAB_POSITION,
            R.id.diaryCalendarYearPagerFragment to YEAR_TAB_POSITION,
            R.id.diaryCalendarLast30DaysFragment to LAST_30_DAYS_TAB_POSITION
        )

        destinationToTabMap[destination.id]?.let { newTabPosition ->
            if (selectedTabPosition != newTabPosition) {
                // Update view model state for logging periods
                viewModel.selectedTabPosition = newTabPosition
                binding?.tabLayout?.getTabAt(newTabPosition)?.select()
            }
        }
    }

    private fun openTab(position: Int?) {
        navController.let {
            val directions = when (position) {
                WEEK_TAB_POSITION -> {
                    saveLastUsedGranularity(DiaryCalendarListContainer.Granularity.WEEK)
                    sendGranularityChangedAnalytics(AnalyticsPropertyValue.DiaryCalendarGranularity.WEEKLY)
                    openWeekTab(it)
                }

                MONTH_TAB_POSITION -> {
                    saveLastUsedGranularity(DiaryCalendarListContainer.Granularity.MONTH)
                    sendGranularityChangedAnalytics(AnalyticsPropertyValue.DiaryCalendarGranularity.MONTHLY)
                    openMonthTab(it)
                }

                YEAR_TAB_POSITION -> {
                    saveLastUsedGranularity(DiaryCalendarListContainer.Granularity.YEAR)
                    sendGranularityChangedAnalytics(AnalyticsPropertyValue.DiaryCalendarGranularity.YEARLY)
                    openYearTab(it)
                }

                LAST_30_DAYS_TAB_POSITION -> {
                    saveLastUsedGranularity(DiaryCalendarListContainer.Granularity.LAST_30_DAYS)
                    sendGranularityChangedAnalytics(AnalyticsPropertyValue.DiaryCalendarGranularity.LAST_30_DAYS)
                    openLast30DaysTab(it)
                }

                else -> null
            }
            if (directions != null) {
                it.navigate(directions)
            }
        }
    }

    private fun openWeekTab(navController: NavController): NavDirections? {
        return when (navController.currentDestination?.id) {
            R.id.diaryCalendarMonthPagerFragment -> {
                DiaryCalendarMonthPagerFragmentDirections.actionDiaryCalendarMonthFragmentToDiaryCalendarWeekFragment()
                    .setStartOfWeek(viewModel.getStartOfWeekFromMonthToWeek().toString())
            }

            R.id.diaryCalendarLast30DaysFragment -> {
                DiaryCalendarLast30DaysPagerFragmentDirections.actionDiaryCalendarLast30DaysFragmentToDiaryCalendarWeekFragment()
                    .setStartOfWeek(viewModel.getStartOfWeekFromLast30DaysToWeek().toString())
            }

            R.id.diaryCalendarYearPagerFragment -> {
                DiaryCalendarYearPagerFragmentDirections.actionDiaryCalendarYearFragmentToDiaryCalendarWeekFragment()
                    .setStartOfWeek(viewModel.getStartOfWeekFromYearToWeek().toString())
            }

            else -> null
        }
    }

    private fun openMonthTab(navController: NavController): NavDirections? {
        return when (navController.currentDestination?.id) {
            R.id.diaryCalendarWeekPagerFragment -> {
                DiaryCalendarWeekPagerFragmentDirections.actionDiaryCalendarWeekFragmentToDiaryCalendarMonthFragment()
                    .setMonth(viewModel.getStartOfMonthFromWeekToMonth().toString())
            }

            R.id.diaryCalendarLast30DaysFragment -> {
                DiaryCalendarLast30DaysPagerFragmentDirections.actionDiaryCalendarLast30DaysFragmentToDiaryCalendarMonthFragment()
                    .setMonth(LocalDate.now().month.toString())
            }

            R.id.diaryCalendarYearPagerFragment -> {
                DiaryCalendarYearPagerFragmentDirections.actionDiaryCalendarYearFragmentToDiaryCalendarMonthFragment()
                    .setMonth(viewModel.getStartOfMonthFromYearToMonth().toString())
            }

            else -> null
        }
    }

    private fun openYearTab(navController: NavController): NavDirections? {
        return when (navController.currentDestination?.id) {
            R.id.diaryCalendarMonthPagerFragment -> {
                DiaryCalendarMonthPagerFragmentDirections.actionDiaryCalendarMonthFragmentToDiaryCalendarYearFragment()
                    .setYear(viewModel.getYearFromMonthToYear().toString())
            }

            R.id.diaryCalendarLast30DaysFragment -> {
                DiaryCalendarLast30DaysPagerFragmentDirections.actionDiaryCalendarLast30DaysFragmentToDiaryCalendarYearFragment()
                    .setYear(LocalDate.now().year.toString())
            }

            R.id.diaryCalendarWeekPagerFragment -> {
                DiaryCalendarWeekPagerFragmentDirections.actionDiaryCalendarWeekFragmentToDiaryCalendarYearFragment()
                    .setYear(viewModel.getYearFromWeekToYear().toString())
            }

            else -> null
        }
    }

    private fun openLast30DaysTab(navController: NavController): NavDirections? {
        return when (navController.currentDestination?.id) {
            R.id.diaryCalendarMonthPagerFragment -> {
                DiaryCalendarMonthPagerFragmentDirections.actionDiaryCalendarMonthFragmentToDiaryCalendarLast30DaysFragment()
            }

            R.id.diaryCalendarWeekPagerFragment -> {
                DiaryCalendarWeekPagerFragmentDirections.actionDiaryCalendarWeekFragmentToDiaryCalendarLast30DaysFragment()
            }

            R.id.diaryCalendarYearPagerFragment -> {
                DiaryCalendarYearPagerFragmentDirections.actionDiaryCalendarYearFragmentToDiaryCalendarLast30DaysFragment()
            }

            else -> null
        }
    }

    private fun handleDeepLinkAndRestoreLastUsedView(showActivitiesList: Boolean) {
        val lastGranularity = getLastUsedGranularity()

        if (navController.handleDeepLink(activity?.intent)) {
            // Reset intent data so that the link is not handled again when returning to this view
            Timber.d("Handled deep link ${activity?.intent?.data} - resetting intent data")
            activity?.intent?.data = null
        } else if (lastGranularity != null) {
            getDirections(lastGranularity, showActivitiesList)?.let {
                if (navController.currentDestination?.id == R.id.diaryCalendarMonthPagerFragment) {
                    navController.navigate(it)
                }
            }
        }
    }

    private fun sendCalendarScreenEvent() {
        val granularityText = when (getLastUsedGranularity()) {
            DiaryCalendarListContainer.Granularity.WEEK -> AnalyticsPropertyValue.DiaryCalendarGranularity.WEEKLY
            DiaryCalendarListContainer.Granularity.MONTH -> AnalyticsPropertyValue.DiaryCalendarGranularity.MONTHLY
            DiaryCalendarListContainer.Granularity.LAST_30_DAYS -> AnalyticsPropertyValue.DiaryCalendarGranularity.LAST_30_DAYS
            DiaryCalendarListContainer.Granularity.YEAR -> AnalyticsPropertyValue.DiaryCalendarGranularity.YEARLY
            else -> ""
        }
        datahubAnalyticsTracker.trackEvent(
            AnalyticsEvent.CALENDAR_SCREEN,
            AnalyticsProperties().put(
                AnalyticsEventProperty.CALENDAR_LEVEL,
                granularityText
            )
        )
    }

    private fun getDirections(
        granularity: DiaryCalendarListContainer.Granularity,
        showActivitiesList: Boolean
    ): NavDirections? {
        return when (granularity) {
            DiaryCalendarListContainer.Granularity.WEEK -> {
                // Remember if week view was used last and show current week, if so
                val dayOfWeek = calendarProvider.getDayOfWeekField()
                val startOfWeek = LocalDate.now().with(dayOfWeek, 1)
                DiaryCalendarMonthPagerFragmentDirections
                    .actionDiaryCalendarMonthFragmentToDiaryCalendarWeekFragment()
                    .setStartOfWeek(startOfWeek.toString())
                    .setShowActivitiesList(showActivitiesList)
            }

            DiaryCalendarListContainer.Granularity.LAST_30_DAYS -> {
                // Remember if last 30 days view was used last and show current year, if so
                DiaryCalendarMonthPagerFragmentDirections
                    .actionDiaryCalendarMonthFragmentToDiaryCalendarLast30DaysFragment()
                    .setShowActivitiesList(showActivitiesList)
            }

            DiaryCalendarListContainer.Granularity.YEAR -> {
                // Remember if year view was used last and show current year, if so
                DiaryCalendarMonthPagerFragmentDirections
                    .actionDiaryCalendarMonthFragmentToDiaryCalendarYearFragment()
                    .setYear(Year.now().toString())
                    .setShowActivitiesList(showActivitiesList)
            }
            // The other case that is month we don't use as it's also the default
            else -> {
                null
            }
        }
    }

    private fun getLastUsedGranularity(): DiaryCalendarListContainer.Granularity? {
        val lastUsed = diaryPagePreferences.getString(
            DiaryPagePreferencesKeys.DIARY_CALENDAR_LAST_USED_GRANULARITY,
            null
        )

        return DiaryCalendarListContainer.Granularity.valueOfStringOrNull(lastUsed)
    }

    private fun saveLastUsedGranularity(
        granularity: DiaryCalendarListContainer.Granularity,
    ) {
        diaryPagePreferences.edit {
            putString(
                DiaryPagePreferencesKeys.DIARY_CALENDAR_LAST_USED_GRANULARITY,
                granularity.value
            )
        }
    }

    private fun sendGranularityChangedAnalytics(granularity: String) {
        datahubAnalyticsTracker.trackEvent(
            AnalyticsEvent.CALENDAR_CHANGE_CALENDAR_LEVEL,
            AnalyticsProperties().put(
                AnalyticsEventProperty.NEW_LEVEL,
                granularity
            )
        )
    }

    override fun onStop() {
        super.onStop()
        navController.removeOnDestinationChangedListener(navigationListener)
        binding?.tabLayout?.removeOnTabSelectedListener(onTabSelectedListener)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    companion object {
        const val FRAGMENT_TAG = "CalendarContainerFragment"

        @JvmStatic
        protected val ARG_SHOW_ACTIVITIES_LIST =
            DiaryCalendarConstants.DIARY_CALENDAR_SCROLL_TO_ACTIVITIES_LIST
    }
}
