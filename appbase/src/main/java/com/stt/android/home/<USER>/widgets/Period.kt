package com.stt.android.home.dashboardv2.widgets

import com.stt.android.controllers.UserSettingsController
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.utils.toEpochMilli
import java.time.DayOfWeek
import java.time.LocalDate

sealed interface Period {
    val beginDate: LocalDate
    val endDate: LocalDate
    val previousPeriod: Pair<Long, Long>

    fun mapGranularity(): DiaryCalendarListContainer.Granularity? =
        when (this) {
            is ThisWeek -> DiaryCalendarListContainer.Granularity.WEEK
            Last30Days -> DiaryCalendarListContainer.Granularity.LAST_30_DAYS
            ThisMonth -> DiaryCalendarListContainer.Granularity.MONTH
            Last7Days, is CustomPeriod -> null
        }

    data class ThisWeek(
        val firstDayOfWeek: DayOfWeek
    ) : Period {
        constructor(
            userSettingsController: UserSettingsController,
        ) : this(userSettingsController.settings.firstDayOfTheWeek)

        override val beginDate: LocalDate
            get() = run {
                val now = LocalDate.now()
                var minus = now.dayOfWeek.value.toLong() - firstDayOfWeek.value.toLong()
                if (minus < 0) minus += 7
                now.minusDays(minus).atStartOfDay().toLocalDate()
            }
        override val endDate: LocalDate
            get() = run {
                val now = LocalDate.now()
                var minus = now.dayOfWeek.value.toLong() - firstDayOfWeek.value.toLong()
                if (minus < 0) minus += 7
                now.plusDays(6 - minus).atStartOfDay().toLocalDate()
            }
        override val previousPeriod: Pair<Long, Long>
            get() = beginDate.minusWeeks(1).atStartOfDay().toEpochMilli() to
                beginDate.minusDays(1).atStartOfDay().toEpochMilli()
    }

    data object Last7Days : Period {
        override val beginDate: LocalDate
            get() = LocalDate.now().minusDays(6).atStartOfDay().toLocalDate()
        override val endDate: LocalDate
            get() = LocalDate.now().atStartOfDay().toLocalDate()
        override val previousPeriod: Pair<Long, Long>
            get() = beginDate.minusWeeks(1).atStartOfDay().toEpochMilli() to
                beginDate.minusDays(1).atStartOfDay().toEpochMilli()
    }

    data object ThisMonth : Period {
        override val beginDate: LocalDate
            get() = run {
                val now = LocalDate.now()
                now.minusDays(now.dayOfMonth.toLong() - 1).atStartOfDay().toLocalDate()
            }
        override val endDate: LocalDate
            get() = LocalDate.now().atStartOfDay().toLocalDate()
        override val previousPeriod: Pair<Long, Long>
            get() = beginDate.minusMonths(1).atStartOfDay().toEpochMilli() to
                beginDate.minusDays(1).atStartOfDay().toEpochMilli()
    }

    data object Last30Days : Period {
        override val beginDate: LocalDate
            get() = LocalDate.now().minusDays(29).atStartOfDay().toLocalDate()
        override val endDate: LocalDate
            get() = LocalDate.now().atStartOfDay().toLocalDate()
        override val previousPeriod: Pair<Long, Long>
            get() = beginDate.minusDays(30).atStartOfDay().toEpochMilli() to
                beginDate.minusDays(1).atStartOfDay().toEpochMilli()
    }

    data class CustomPeriod(
        override val beginDate: LocalDate,
        override val endDate: LocalDate,
        override val previousPeriod: Pair<Long, Long>,
    ) : Period
}
