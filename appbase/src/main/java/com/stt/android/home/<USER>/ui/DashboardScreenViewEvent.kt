package com.stt.android.home.dashboardv2.ui

import com.stt.android.domain.marketing.MarketingBannerInfo
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboardv2.widgets.WidgetInfo
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.multimedia.sportie.SportieImage
import com.stt.android.newfeed.FeedTopBannerType

internal sealed interface DashboardScreenViewEvent {
    data class OpenWidget(
        val widgetType: WidgetType,
        val widgetInfo: WidgetInfo,
    ) : DashboardScreenViewEvent

    data object EditDashboard : DashboardScreenViewEvent

    data class OpenWorkout(val workoutHeader: WorkoutHeader, val analyticsSource: String? = null) : DashboardScreenViewEvent

    data class OpenUser(val username: String) : DashboardScreenViewEvent

    data class AddPhoto(val workoutHeader: WorkoutHeader) : DashboardScreenViewEvent

    data class PlayWorkout(
        val workoutHeader: WorkoutHeader,
        val hasPremiumSubscription: Boolean,
    ) : DashboardScreenViewEvent

    data class OpenTag(
        val tagName: String,
        val isEditable: Boolean,
        val hasPremiumSubscription: Boolean,
    ) : DashboardScreenViewEvent

    data class AddComment(val workoutHeader: WorkoutHeader) : DashboardScreenViewEvent

    data class EditWorkout(val workoutHeader: WorkoutHeader) : DashboardScreenViewEvent

    data class ShareSportie(val sportieImage: SportieImage) : DashboardScreenViewEvent

    data object FindPeople : DashboardScreenViewEvent

    data object Refresh : DashboardScreenViewEvent

    data object RecordWorkout : DashboardScreenViewEvent

    class OpenFeedTopBanner(val type: FeedTopBannerType) : DashboardScreenViewEvent

    data object RemovePremiumBanner : DashboardScreenViewEvent

    data class SetIsInlineAppViewEnabled(val enabled: Boolean) : DashboardScreenViewEvent

    data class OpenMarketingBanner(val banner: MarketingBannerInfo) : DashboardScreenViewEvent

    data object OpenNotification : DashboardScreenViewEvent

    data object OpenWatchDevice : DashboardScreenViewEvent

    data object OpenHeadsetDevice : DashboardScreenViewEvent

    data object LogMenstrualCycle : DashboardScreenViewEvent

    data object AddWorkoutManually : DashboardScreenViewEvent

    data object CreateIntervalWorkout : DashboardScreenViewEvent

    data object DownloadOfflineMap : DashboardScreenViewEvent

    data object PairDevice : DashboardScreenViewEvent

    data object OpenSearch : DashboardScreenViewEvent

    data object PopBack : DashboardScreenViewEvent
}
