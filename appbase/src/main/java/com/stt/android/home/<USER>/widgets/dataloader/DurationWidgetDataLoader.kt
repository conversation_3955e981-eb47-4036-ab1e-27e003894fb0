package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.controllers.userSettings
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.user.UserSettings
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.home.dashboard.widget.goal.GoalWidgetData
import com.stt.android.home.dashboard.widget.goal.WeeklyGoalWidgetDataFetcher
import com.stt.android.home.dashboardv2.ui.widgets.common.NO_DATA_VALUE
import com.stt.android.home.dashboardv2.ui.widgets.common.formatWidgetDurationTitle
import com.stt.android.home.dashboardv2.ui.widgets.common.generateDurationTargetSubtitle
import com.stt.android.home.dashboardv2.ui.widgets.common.roundSecondsToMinutes
import com.stt.android.home.dashboardv2.widgets.DurationWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Period
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.iterator
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onStart
import java.time.DayOfWeek
import java.time.LocalDate
import javax.inject.Inject
import kotlin.math.abs
import kotlin.math.roundToLong
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes

internal class DurationWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val weeklyGoalWidgetDataFetcher: WeeklyGoalWidgetDataFetcher,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val infoModelFormatter: InfoModelFormatter,
    private val isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase
) : WidgetDataLoader<DurationWidgetInfo>() {

    override suspend fun mockedWidgetInfo(type: WidgetType): DurationWidgetInfo? {
        val subscribedToPremium = isSubscribedToPremiumUseCase().first()

        return when (type) {
            WidgetType.TOTAL_DURATION_THIS_WEEK -> DurationWidgetInfo(
                period = Period.ThisWeek(DayOfWeek.MONDAY),
                progresses = listOf(92 / 180f),
                title = 92.minutes.inWholeSeconds.toDouble().formatWidgetDurationTitle(context),
                subtitle = generateDurationTargetSubtitle(
                    context,
                    -88.minutes.inWholeSeconds.toDouble(),
                    infoModelFormatter
                ),
                premiumRequired = !subscribedToPremium,
            )

            WidgetType.TOTAL_DURATION_LAST_7_DAYS -> DurationWidgetInfo(
                period = Period.Last7Days,
                progresses = generateDailyBarProgresses(
                    listOf(
                        65.0,
                        0.0,
                        70.0,
                        75.0,
                        30.0,
                        31.0,
                        61.0
                    )
                ),
                title = (5.hours.inWholeSeconds + 32.minutes.inWholeSeconds).toDouble()
                    .formatWidgetDurationTitle(context),
                subtitle = infoModelFormatter.formatDuration(118.minutes.inWholeSeconds),
                subtitleIconRes = R.drawable.widget_down_arrow,
                premiumRequired = !subscribedToPremium,
            )

            WidgetType.TOTAL_DURATION_THIS_MONTH -> DurationWidgetInfo(
                period = Period.CustomPeriod(
                    LocalDate.of(2025, 2, 1),
                    LocalDate.of(2025, 2, 28),
                    0L to 0L,
                ),
                progresses = listOf(
                    0.0, 0.0, 0.1031, 0.1031, 0.1031, 0.1184, 0.1973, 0.2027,
                    0.2395, 0.2404, 0.2511, 0.2511, 0.2726, 0.3462, 0.3973,
                    0.4395, 0.522, 0.5309, 0.757, 0.8099, 0.8287, 0.9758,
                    0.9758, 0.9758, 0.9758, 0.9758, 0.9812, 1.0
                ).map { it.toFloat() },
                title = (18.hours.inWholeSeconds + 35.minutes.inWholeSeconds).toDouble()
                    .formatWidgetDurationTitle(context),
                subtitle = infoModelFormatter.formatDuration(205.minutes.inWholeSeconds),
                subtitleIconRes = R.drawable.widget_down_arrow,
                premiumRequired = !subscribedToPremium,
            )

            WidgetType.TOTAL_DURATION_LAST_30_DAYS -> DurationWidgetInfo(
                period = Period.CustomPeriod(
                    LocalDate.of(2025, 2, 1),
                    LocalDate.of(2025, 3, 2),
                    0L to 0L,
                ),
                progresses = listOf(
                    0.000,
                    0.000,
                    0.093,
                    0.093,
                    0.093,
                    0.107,
                    0.178,
                    0.183,
                    0.216,
                    0.217,
                    0.227,
                    0.227,
                    0.246,
                    0.313,
                    0.359,
                    0.407,
                    0.471,
                    0.479,
                    0.700,
                    0.731,
                    0.748,
                    0.881,
                    0.881,
                    0.881,
                    0.930,
                    0.978,
                    0.983,
                    0.988,
                    0.996,
                    1.000
                ).map { it.toFloat() },
                title = (20.hours.inWholeSeconds + 35.minutes.inWholeSeconds).toDouble()
                    .formatWidgetDurationTitle(context),
                subtitle = infoModelFormatter.formatDuration(237.minutes.inWholeSeconds),
                subtitleIconRes = R.drawable.widget_down_arrow,
                premiumRequired = !subscribedToPremium,
            )

            else -> null
        }
    }

    override suspend fun realLoad(param: Param): WidgetData<DurationWidgetInfo> {
        val flow = if (param.type == WidgetType.TOTAL_DURATION_THIS_WEEK) {
            combine(
                workoutHeaderController.currentUserWorkoutUpdated
                    .onStart { emit(Unit) },
                userSettingsController.userSettings()
                    .distinctUntilChangedBy(UserSettings::getFirstDayOfTheWeek),
                weeklyGoalWidgetDataFetcher.weeklyGoalWidgetDataFlow(),
                isSubscribedToPremiumUseCase(),
            ) { _, _, goal, subscribedToPremium ->
                loadWidgetInfo(
                    param = param,
                    goal = goal,
                    subscribedToPremium = subscribedToPremium
                )
            }
                .flowOn(coroutinesDispatchers.io)
        } else {
            combine(
                workoutHeaderController.currentUserWorkoutUpdated,
                isSubscribedToPremiumUseCase(),
            ) { _, subscribedToPremium ->
                loadWidgetInfo(
                    param = param,
                    goal = null,
                    subscribedToPremium = subscribedToPremium
                )
            }
                .onStart {
                    emit(
                        value = loadWidgetInfo(
                            param = param,
                            goal = null,
                            subscribedToPremium = isSubscribedToPremiumUseCase().first()
                        )
                    )
                }
                .flowOn(coroutinesDispatchers.io)
        }
        return WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = flow,
        )
    }

    private fun loadWidgetInfo(
        param: Param,
        goal: GoalWidgetData? = null,
        subscribedToPremium: Boolean,
    ): DurationWidgetInfo {
        val period = when (param.type) {
            WidgetType.TOTAL_DURATION_THIS_WEEK -> Period.ThisWeek(userSettingsController)
            WidgetType.TOTAL_DURATION_LAST_7_DAYS -> Period.Last7Days
            WidgetType.TOTAL_DURATION_THIS_MONTH -> Period.ThisMonth
            WidgetType.TOTAL_DURATION_LAST_30_DAYS -> Period.Last30Days
            else -> throw IllegalStateException("Unsupported param: $param")
        }
        val username = currentUserController.username
        val startMillis = period.beginDate.atStartOfDay().toEpochMilli()
        val endMillis = period.endDate.atStartOfDay().toEpochMilli()
        val workouts = fetchWorkoutsByPeriod(
            workoutHeaderController,
            username,
            startMillis,
            endMillis
        )
        val currentPeriodWorkoutsByDay = workouts.groupBy { it.startTime.toLocalDate() }
        val dailyWorkoutTimes = mutableListOf<Double>()
        (period.beginDate..period.endDate).iterator().forEach { day ->
            val workoutsForDay = currentPeriodWorkoutsByDay[day] ?: emptyList()
            dailyWorkoutTimes.add(workoutsForDay.sumOf { it.totalTime })
        }
        val currentPeriodTotalWorkoutSeconds = workouts.sumOf { it.totalTime }
        val (progresses, subtitle, subtitleIconRes) = when (period) {
            is Period.ThisWeek -> {
                goal?.target?.let { goalSeconds ->
                    val roundedWorkoutSeconds =
                        roundSecondsToMinutes(currentPeriodTotalWorkoutSeconds)
                    val exceededSeconds = roundedWorkoutSeconds - goalSeconds
                    val progress = listOf(
                        (roundedWorkoutSeconds / goalSeconds).toFloat().coerceIn(0f, 1f)
                    )

                    val subtitleText =
                        generateDurationTargetSubtitle(context, exceededSeconds, infoModelFormatter)
                    Triple(progress, subtitleText, null)
                } ?: Triple(
                    listOf(NO_DATA_VALUE),
                    context.getString(R.string.widget_no_data_subtitle),
                    null
                )
            }

            Period.Last7Days -> {
                val subtitlePair =
                    formatPeriodChangeSubtitle(period, currentPeriodTotalWorkoutSeconds)
                Triple(
                    generateDailyBarProgresses(dailyWorkoutTimes),
                    subtitlePair.first,
                    subtitlePair.second,
                )
            }

            Period.ThisMonth, Period.Last30Days, is Period.CustomPeriod -> {
                val subtitlePair =
                    formatPeriodChangeSubtitle(period, currentPeriodTotalWorkoutSeconds)
                Triple(
                    generateGrandTotalProgresses(dailyWorkoutTimes),
                    subtitlePair.first,
                    subtitlePair.second,
                )
            }
        }

        val title =
            currentPeriodTotalWorkoutSeconds.coerceAtLeast(0.0).formatWidgetDurationTitle(context)

        return DurationWidgetInfo(
            period = period,
            progresses = progresses,
            title = title,
            subtitle = subtitle,
            subtitleIconRes = subtitleIconRes,
            premiumRequired = !subscribedToPremium,
        )
    }

    private fun formatPeriodChangeSubtitle(
        period: Period,
        currentPeriodTotalWorkoutSeconds: Double
    ): Pair<String, Int?> {
        val (previousPeriodStartMillis, previousPeriodEndMillis) = period.previousPeriod
        val previousPeriodWorkouts = fetchWorkoutsByPeriod(
            workoutHeaderController,
            currentUserController.username,
            previousPeriodStartMillis,
            previousPeriodEndMillis
        )
        val previousPeriodTotalWorkoutSeconds = previousPeriodWorkouts.sumOf { it.totalTime }

        val currentRoundedSeconds = roundSecondsToMinutes(currentPeriodTotalWorkoutSeconds)
        val previousRoundedSeconds = roundSecondsToMinutes(previousPeriodTotalWorkoutSeconds)

        val changeSinceLastPeriod = if (previousRoundedSeconds > 0) {
            currentRoundedSeconds - previousRoundedSeconds
        } else if (currentRoundedSeconds > 0) {
            currentRoundedSeconds
        } else {
            null
        }

        val formatedDuration = changeSinceLastPeriod?.let {
            infoModelFormatter.formatDuration(abs(it).roundToLong())
        } ?: return context.getString(R.string.widget_no_data_subtitle) to null
        val arrowRes =
            if (changeSinceLastPeriod >= 0) R.drawable.widget_up_arrow else R.drawable.widget_down_arrow
        return formatedDuration to arrowRes
    }

    private fun generateGrandTotalProgresses(dailyWorkoutTimes: List<Double>): List<Float> {
        val grandTotalDurations = dailyWorkoutTimes.scanIndexed(0f) { _, acc, value ->
            (acc + value).toFloat()
        }.let { list ->
            // We need at least 2 points in the line chart, otherwise remove the first zero
            if (list.size > 2) {
                list.drop(1)
            } else {
                list
            }
        }

        val maxDurationOfDay = grandTotalDurations.last().coerceAtLeast(1.0f) // Can't be zero
        return if (grandTotalDurations.all { it == 0f }) {
            grandTotalDurations.map { NO_DATA_VALUE }
        } else {
            grandTotalDurations.map {
                it / maxDurationOfDay
            }
        }
    }
}
