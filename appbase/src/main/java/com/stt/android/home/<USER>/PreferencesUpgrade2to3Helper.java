package com.stt.android.home.settings;

import android.content.SharedPreferences;

import com.stt.android.domain.workout.ActivityType;
import com.stt.android.utils.STTConstants;

public class PreferencesUpgrade2to3<PERSON><PERSON>per extends PreferencesUpgradeHelper {
    public PreferencesUpgrade2to3Helper(SharedPreferences preferences) {
        super(preferences);
    }

    @Override
    public void upgrade() {
        upgradePreferences2To3();
    }

    private void upgradePreferences2To3() {
        // LAST_ACTIVITY_TYPE is obsoleted and no longer used anywhere
        preferences.edit()
                .putString(STTConstants.DefaultPreferences.KEY_RECENT_ACTIVITY_IDS, Integer.toString(
                        preferences.getInt("LAST_ACTIVITY_TYPE", ActivityType.DEFAULT.getId())))
                .putInt(STTConstants.DefaultPreferences.KEY_PREFERENCES_VERSION, 3)
                .apply();
    }
}
