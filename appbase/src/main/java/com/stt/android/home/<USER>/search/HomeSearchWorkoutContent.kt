package com.stt.android.home.dashboardv2.search

import android.content.Context
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibilityScope
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.M3SearchBar
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.UserWorkoutSummary
import com.stt.android.home.dashboardv2.ui.DashboardScreenViewEvent
import com.stt.android.home.dashboardv2.ui.SEARCH_TRANSITION_KEY
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.social.workoutlist.DateHeader
import com.stt.android.social.workoutlist.search.SearchWorkoutViewState
import com.stt.android.social.workoutlist.ui.DateHeader
import com.stt.android.ui.components.workout.WorkoutCard
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import com.stt.android.ui.utils.TextFormatter
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import com.stt.android.core.R as CR

@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
internal fun SharedTransitionScope.HomeSearchWorkoutContent(
    viewState: SearchWorkoutViewState,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    viewEvent: (DashboardScreenViewEvent) -> Unit,
    onQueryChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    animatedVisibilityScope: AnimatedVisibilityScope? = null,
) {
    BackHandler {
        viewEvent(DashboardScreenViewEvent.PopBack)
    }

    Column(modifier = modifier) {
        Column(
            modifier = animatedVisibilityScope?.let {
                Modifier
                    .background(MaterialTheme.colorScheme.surface)
                    .sharedElement(
                        rememberSharedContentState(SEARCH_TRANSITION_KEY),
                        animatedVisibilityScope,
                    )
            } ?: Modifier.background(MaterialTheme.colorScheme.surface),
        ) {
            M3SearchBar(
                query = viewState.keyword,
                onQueryChange = onQueryChange,
                placeholder = stringResource(R.string.search_workouts_hint),
                onCancel = { viewEvent(DashboardScreenViewEvent.PopBack) },
                cancelText = stringResource(R.string.cancel),
            )
            if (viewState.keyword.isBlank()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.surface)
                ) {
                    Text(
                        text = stringResource(R.string.search_own_workouts_tips),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.secondary,
                        modifier = Modifier
                            .padding(horizontal = MaterialTheme.spacing.xlarge)
                            .padding(bottom = MaterialTheme.spacing.medium),
                    )
                }
            }
        }

        Box(
            modifier = Modifier
                .fillMaxSize()
                .narrowContentWithBgColors(
                    backgroundColor = MaterialTheme.colorScheme.surface,
                    outerBackgroundColor = MaterialTheme.colorScheme.background,
                )
        ) {
            when {
                viewState.searching -> {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = MaterialTheme.spacing.large),
                        contentAlignment = Alignment.TopCenter,
                    ) {
                        CircularProgressIndicator()
                    }
                }

                viewState.dateAndWorkouts.any() -> {
                    WorkoutsContent(
                        dateAndWorkouts = viewState.dateAndWorkouts,
                        workoutSummary = viewState.workoutSummary,
                        workoutCardActionsHandler = workoutCardActionsHandler,
                    )
                }

                viewState.keyword.isNotBlank() -> {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 200.dp),
                        contentAlignment = Alignment.TopCenter,
                    ) {
                        Text(
                            text = stringResource(R.string.search_phone_contacts_empty),
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.secondary,
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun WorkoutsContent(
    dateAndWorkouts: List<Any>,
    workoutSummary: UserWorkoutSummary?,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    modifier: Modifier = Modifier,
    lazyListState: LazyListState = rememberLazyListState(),
    measurementUnit: MeasurementUnit = MeasurementUnit.METRIC,
) {
    Column(modifier = modifier.background(MaterialTheme.colorScheme.surface)) {
        LazyColumn(
            state = lazyListState,
            modifier = Modifier
                .fillMaxSize()
                .narrowContent(),
        ) {
            workoutSummary?.let {
                item(key = "workout_stats") {
                    WorkoutStats(
                        workoutSummary = workoutSummary,
                        unit = measurementUnit,
                    )
                }
            }

            val dateHeaderIndices =
                dateAndWorkouts.indices.filter { dateAndWorkouts[it] is DateHeader }
            dateHeaderIndices.forEachIndexed { i, headerIndex ->
                val nextHeaderIndex = dateHeaderIndices.getOrNull(i + 1) ?: dateAndWorkouts.size
                val dateHeader = dateAndWorkouts[headerIndex] as DateHeader

                stickyHeader(key = "sticky_${dateHeader.dateText}") {
                    DateHeader(
                        itemData = dateHeader,
                        modifier = Modifier.background(MaterialTheme.colorScheme.surface)
                    )
                }

                val workoutsInSection = dateAndWorkouts.subList(headerIndex + 1, nextHeaderIndex)
                    .filterIsInstance<WorkoutCardInfo>()

                items(
                    items = workoutsInSection,
                    key = { it.workoutHeader.id },
                ) { workoutCardInfo ->
                    val workoutHeader = workoutCardInfo.workoutHeader
                    WorkoutCard(
                        viewData = workoutCardInfo.workoutCardViewData,
                        onClick = { workoutCardActionsHandler.onOpenWorkout(workoutHeader) },
                        onEditClick = { workoutCardActionsHandler.onEditWorkout(workoutHeader) },
                        onAddCommentClick = {
                            workoutCardActionsHandler.onAddComment(
                                workoutHeader
                            )
                        },
                        onReactionClick = { workoutCardActionsHandler.onReaction(workoutHeader) },
                        onShareClick = { shareInfo ->
                            workoutCardActionsHandler.onShareWorkout(
                                workoutHeader,
                                shareInfo
                            )
                        },
                        onTagClicked = { tagName, isEditable ->
                            workoutCardActionsHandler.onTagClicked(
                                tagName,
                                isEditable,
                                workoutCardInfo.workoutCardViewData.isSubscribedToPremium,
                            )
                        },
                        onAddPhotoClick = { workoutCardActionsHandler.onAddPhoto(workoutHeader) },
                        onPlayClick = { workoutCardActionsHandler.onPlayWorkout(workoutHeader) },
                        onUserClick = { username ->
                            workoutCardActionsHandler.onUserClick(
                                username
                            )
                        },
                        modifier = Modifier.padding(
                            vertical = MaterialTheme.spacing.small,
                            horizontal = MaterialTheme.spacing.medium,
                        ),
                    )
                }
            }
        }
    }
}

@Composable
fun WorkoutStats(
    workoutSummary: UserWorkoutSummary,
    unit: MeasurementUnit,
    modifier: Modifier = Modifier,
) {
    val placeholder = AnnotatedString("-")
    val distance = workoutSummary.totalDistance.takeIf { it > 0 }?.let {
        buildAnnotatedString {
            append(TextFormatter.formatDistanceRounded(unit.toDistanceUnit(it)))
            append(" ")
            append(stringResource(unit.distanceUnit))
        }
    } ?: placeholder
    val activityCount =
        workoutSummary.totalWorkouts.takeIf { it > 0 }?.let { AnnotatedString(it.toString()) }
            ?: placeholder
    val duration =
        workoutSummary.totalDuration.takeIf { it > 0 }?.formatDuration(LocalContext.current)
            ?: placeholder
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                top = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.small,
            ),
    ) {
        StatItem(
            value = activityCount,
            label = stringResource(R.string.goal_activities),
            modifier = Modifier.weight(1f),
        )
        StatItem(
            value = duration,
            label = stringResource(R.string.user_profile_total_duration),
            modifier = Modifier.weight(1f),
        )
        StatItem(
            value = distance,
            label = stringResource(R.string.distance),
            modifier = Modifier.weight(1f),
        )
    }
}

private fun Double.formatDuration(context: Context): AnnotatedString {
    val hours = (this + 30.minutes.inWholeSeconds).seconds.inWholeHours
    val hourUnit = context.getString(CR.string.hour)
    return buildAnnotatedString {
        append(hours.toString())
        append(" ")
        append(hourUnit)
    }
}

@Composable
private fun StatItem(
    value: AnnotatedString,
    label: String,
    modifier: Modifier = Modifier,
) {
    Column(
        horizontalAlignment = Alignment.Start,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        modifier = modifier.padding(start = MaterialTheme.spacing.medium),
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyLargeBold,
        )
    }
}
