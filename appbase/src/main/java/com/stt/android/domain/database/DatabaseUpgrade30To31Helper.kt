package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.j256.ormlite.table.TableUtils
import com.stt.android.domain.user.workoutextension.IntensityExtension
import java.sql.SQLException

class DatabaseUpgrade30To31Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper
) :
    DatabaseUpgradeHelper(
        db,
        connectionSource,
        databaseHelper
    ) {

    @Throws(SQLException::class)
    override fun upgrade() {
        TableUtils.createTableIfNotExists(connectionSource, IntensityExtension::class.java)
    }
}
