package com.stt.android.domain.user;

@Deprecated
public class LegacyWorkoutHeader {
    public static final String TABLE_NAME = "workoutheader";

    public abstract static class DbFields {
        public static final String KEY = "key";
        public static final String MANUALLY_CREATED = "manuallyCreated";
        public static final String STEP_COUNT = "stepCount";
        public static final String USERNAME = "username";
        public static final String START_TIME = "startTime";
        public static final String ACTIVITY_ID = "activityId";
        public static final String DELETED = "deleted";
        public static final String LOCALLY_CHANGED = "locallyChanged";
        public static final String TOTAL_DISTANCE = "totalDistance";
        public static final String TOTAL_TIME = "totalTime";
        public static final String ENERGY_CONSUMPTION = "energyConsumption";
        public static final String PICTURE_COUNT = "pictureCount";
        public static final String VIEW_COUNT = "viewCount";
        public static final String COMMENT_COUNT = "commentCount";
        public static final String HEART_RATE_AVG = "heartRateAvg";
        public static final String AVERAGE_SPEED = "avgSpeed";
        public static final String DESCRIPTION = "description";
        public static final String ID = "id";
        public static final String STOP_TIME = "stopTime";
        public static final String MAX_SPEED = "maxSpeed";
        public static final String HEART_RATE_AVG_PERCENTAGE = "heartRateAvgPercentage";
        public static final String HEART_RATE_MAX = "heartRateMax";
        public static final String HEART_RATE_AVG_MAX_PERCENTAGE = "heartRateMaxPercentage";
        public static final String START_POSITION = "startPosition";
        public static final String STOP_POSITION = "stopPosition";
        public static final String CENTER_POSITION = "centerPosition";
        public static final String POLYLINE = "polyline";
        public static final String HEART_RATE_USER_SET_MAX = "heartRateUserSetMax";
        public static final String SHARING_FLAGS = "sharingFlags";
        public static final String AVERAGE_CADENCE = "averageCadence";
        public static final String MAX_CADENCE = "maxCadence";
        public static final String REACTION_COUNT = "reactionCount";
        public static final String TOTAL_ASCENT = "totalAscent";
        public static final String TOTAL_DESCENT = "totalDescent";
        public static final String SEEN = "seen";
        public static final String RECOVERY_TIME = "recoveryTime";
        public static final String MAX_ALTITUDE = "maxAltitude";
        public static final String MIN_ALTITUDE = "minAltitude";
        public static final String EXTENSIONS_FETCHED = "extensions_fetched";
        public static final String TSS = "tss";
        public static final String TSS_LIST = "tss_list";
    }
}
