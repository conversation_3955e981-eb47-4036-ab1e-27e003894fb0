package com.stt.android.domain.user

import com.stt.android.maps.SuuntoRoadSurfaceLayerOptions
import com.stt.android.maps.SuuntoTileOverlayOptions

data class RoadSurfaceType constructor(
    val name: String,
    val titleResource: Int,
    val iconResource: Int,
    val tileOverlayOptions: SuuntoTileOverlayOptions,
    val requiresPremium: Boolean,
    val analyticsName: String? = name
) {

    override fun equals(other: Any?): Boolean {
        if (this === other) {
            return true
        }

        return (other as? RoadSurfaceType)?.name == name
    }

    override fun hashCode(): Int {
        return name.hashCode()
    }

    fun getTileOverlayOptions(hideCyclingForbiddenRoads: Boolean): SuuntoTileOverlayOptions {
        val roadSurfaceLayerOptions =
            (tileOverlayOptions.layerTypeOptions as SuuntoRoadSurfaceLayerOptions)
                .copy(hideCyclingForbiddenRoads = hideCyclingForbiddenRoads)

        return tileOverlayOptions.copy(layerTypeOptions = roadSurfaceLayerOptions)
    }
}
