package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.workoutdetail.comments.WorkoutComment
import java.sql.SQLException

class DatabaseUpgrade43To44Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {
    @Throws(SQLException::class)
    override fun upgrade() {
        DatabaseHelper.addColumnIfNotExist(
            db,
            WorkoutComment.TABLE_NAME,
            WorkoutComment.DbFields.KEY
        )
    }
}
