package com.stt.android.data.workout.jumpropeextension

import com.stt.android.data.source.local.jumpropeextension.JumpRopeExtensionDao
import com.stt.android.data.source.local.jumpropeextension.LocalJumpRopeExtension
import com.stt.android.data.workout.ExtensionDataFetcher
import io.reactivex.Maybe
import javax.inject.Inject

class JumpRopeExtensionDataFetcher
@Inject constructor(private val jumpRopeExtensionDao: JumpRopeExtensionDao) : ExtensionDataFetcher<LocalJumpRopeExtension> {

    override fun fetchAll(): Maybe<List<LocalJumpRopeExtension>> = jumpRopeExtensionDao.fetchAll()

    override fun fetchById(id: Int): Maybe<LocalJumpRopeExtension> = jumpRopeExtensionDao.findById(id)

    override fun fetchByIds(ids: Collection<Int>): List<LocalJumpRopeExtension> =
        jumpRopeExtensionDao.findByIds(ids)
}
