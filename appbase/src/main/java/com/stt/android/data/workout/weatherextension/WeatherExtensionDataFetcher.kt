package com.stt.android.data.workout.weatherextension

import com.stt.android.data.source.local.weatherextension.LocalWeatherExtension
import com.stt.android.data.source.local.weatherextension.WeatherExtensionDao
import com.stt.android.data.workout.ExtensionDataFetcher
import io.reactivex.Maybe
import javax.inject.Inject

class WeatherExtensionDataFetcher
@Inject constructor(
    private val weatherExtensionDao: WeatherExtensionDao
) : ExtensionDataFetcher<LocalWeatherExtension> {

    override fun fetchAll(): Maybe<List<LocalWeatherExtension>> = weatherExtensionDao.fetchAll()

    override fun fetchById(id: Int): Maybe<LocalWeatherExtension> = weatherExtensionDao.findById(id)

    override fun fetchByIds(ids: Collection<Int>): List<LocalWeatherExtension> =
        weatherExtensionDao.findByIds(ids)
}
