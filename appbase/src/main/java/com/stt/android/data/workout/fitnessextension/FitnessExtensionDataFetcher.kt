package com.stt.android.data.workout.fitnessextension

import com.stt.android.data.source.local.fitnessextension.FitnessExtensionDao
import com.stt.android.data.source.local.fitnessextension.LocalFitnessExtension
import com.stt.android.data.workout.ExtensionDataFetcher
import io.reactivex.Maybe
import kotlinx.coroutines.rx2.rxMaybe
import javax.inject.Inject

class FitnessExtensionDataFetcher
@Inject constructor(
    private val fitnessExtensionDao: FitnessExtensionDao
) : ExtensionDataFetcher<LocalFitnessExtension> {

    override fun fetchAll(): Maybe<List<LocalFitnessExtension>> =
        fitnessExtensionDao.fetchAll()

    override fun fetchById(id: Int): Maybe<LocalFitnessExtension> = rxMaybe {
        fitnessExtensionDao.findById(id)
    }

    override fun fetchByIds(ids: Collection<Int>): List<LocalFitnessExtension> =
        fitnessExtensionDao.findByIds(ids)
}
