package com.stt.android.data.workout.summaryextension

import com.stt.android.data.source.local.summaryextension.LocalSummaryExtension
import com.stt.android.data.source.local.summaryextension.SummaryExtensionDao
import com.stt.android.data.workout.ExtensionDataFetcher
import io.reactivex.Maybe
import javax.inject.Inject

class SummaryExtensionDataFetcher
@Inject constructor(
    private val summaryExtensionDao: SummaryExtensionDao
) : ExtensionDataFetcher<LocalSummaryExtension> {

    override fun fetchAll(): Maybe<List<LocalSummaryExtension>> =
        summaryExtensionDao.fetchAll()

    override fun fetchById(id: Int): Maybe<LocalSummaryExtension> =
        summaryExtensionDao.findById(id)

    override fun fetchByIds(ids: Collection<Int>): List<LocalSummaryExtension> =
        summaryExtensionDao.findByIds(ids)
}
