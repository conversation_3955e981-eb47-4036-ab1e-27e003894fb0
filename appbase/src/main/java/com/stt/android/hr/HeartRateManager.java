package com.stt.android.hr;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;

import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import timber.log.Timber;

public class HeartRateManager {
    public interface Callbacks {
        void onHeartRate(BluetoothHeartRateEvent event);

        void onError(Exception e);

        void onStreamEnd();
    }

    private List<Callbacks> listeners = Collections.synchronizedList(new ArrayList<Callbacks>());

    private final ExecutorService callbackExecutor = Executors.newCachedThreadPool();
    private final ExecutorService managerExecutor = Executors.newSingleThreadExecutor();
    private BluetoothManager bluetoothManager;

    /**
     * Start requesting heart rate updates in the given {@link Callbacks}.
     *
     * @param socket   The {@link BluetoothSocket} from which to listen for the
     *                 heart rate data or <code>null</code> to use previously given
     *                 socket that's still on.
     * @param type     The type of heart rate monitor connected to the socket.
     * @param listener The heart rate listener.
     */
    public void requestUpdates(BluetoothSocket socket, HeartRateMonitorType type, Callbacks listener) {
        Timber.d("Requesting heart rate updates");

        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }

        if (bluetoothManager == null) {
            bluetoothManager = new BluetoothManager(socket, type.getProvider());
            managerExecutor.execute(bluetoothManager);
        }
    }

    /**
     * Stops sending updates to the given {@link Callbacks}. If no listeners are
     * present, closes the previously given Bluetooth socket.
     *
     * @param listener The heart rate listener to remove.
     */
    public void removeUpdates(Callbacks listener) {
        listeners.remove(listener);
        if (listeners.isEmpty()) {
            if (bluetoothManager != null) {
                try {
                    bluetoothManager.close();
                } catch (IOException e) {
                    Timber.w(e, "Couldn't close Bluetooth reading");
                }
                bluetoothManager = null;
            }
        }
    }

    /**
     * Parses the {@link InputStream} from the {@link BluetoothSocket} and
     * translates it to {@link HeartRateEvent}s and other calls to
     * {@link HeartRateManager.Callbacks}. When closed, also closes the {@link BluetoothSocket}.
     */
    private class BluetoothManager implements Runnable, Callbacks, Closeable {
        private final BluetoothDevice device;
        private final BluetoothSocket socket;
        private final HeartRateProvider provider;
        private InputStream inHrStream = null;

        public BluetoothManager(BluetoothSocket socket, HeartRateProvider provider) {
            this.socket = socket;
            this.device = socket.getRemoteDevice();
            this.provider = provider;
        }

        @SuppressLint("MissingPermission")
        @Override
        public void run() {
            Timber.d("Starting Bluetooth manager");
            try {
                inHrStream = socket.getInputStream();
                Timber.d("Bluetooth input stream opened");
                // If the device is still pairing then we allow 3 times 2
                // seconds to finish pairing
                int count = 0;
                while (count < 3 && device.getBondState() == BluetoothDevice.BOND_BONDING) {
                    Timber.d("Device " + device.toString() + "(" + device.getName() + ") still bonding");
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                    }
                    count++;
                }

                // Some devices might report to still be pairing but allow
                // reading so we try to read anyway
                provider.start(inHrStream, this);
            } catch (IOException e) {
                Timber.e(e, "Exception while reading Bluetooth data");
            } finally {
                Timber.d("Bluetooth manager started");
            }
        }

        @Override
        public void close() throws IOException {
            Timber.d("Stopping Bluetooth manager");
            provider.stop();

            if (inHrStream != null) {
                inHrStream.close();
                Timber.d("Bluetooth input stream closed");
                inHrStream = null;
            }
        }

        @Override
        public void onHeartRate(BluetoothHeartRateEvent event) {
            for (Callbacks listener : listeners) {
                callbackExecutor.execute(new OnHeartRateRunnable(listener, event));
            }
        }

        @Override
        public void onError(Exception e) {
            for (Callbacks listener : listeners) {
                callbackExecutor.execute(new OnErrorRunnable(listener, e));
            }
        }

        @Override
        public void onStreamEnd() {
            for (Callbacks listener : listeners) {
                callbackExecutor.execute(new OnStreamEndRunnable(listener));
            }
        }
    }

    private static abstract class HeartRateListenerRunnable implements Runnable {
        protected final Callbacks listener;

        public HeartRateListenerRunnable(Callbacks listener) {
            this.listener = listener;
        }
    }

    private static class OnHeartRateRunnable extends HeartRateListenerRunnable {
        private final BluetoothHeartRateEvent event;

        public OnHeartRateRunnable(Callbacks listener, BluetoothHeartRateEvent event) {
            super(listener);
            this.event = event;
        }

        @Override
        public void run() {
            listener.onHeartRate(event);
        }
    }

    private static class OnErrorRunnable extends HeartRateListenerRunnable {
        private final Exception cause;

        public OnErrorRunnable(Callbacks listener, Exception cause) {
            super(listener);
            this.cause = cause;
        }

        @Override
        public void run() {
            listener.onError(cause);
        }
    }

    private static class OnStreamEndRunnable extends HeartRateListenerRunnable {
        public OnStreamEndRunnable(Callbacks listener) {
            super(listener);
        }

        @Override
        public void run() {
            listener.onStreamEnd();
        }
    }
}
