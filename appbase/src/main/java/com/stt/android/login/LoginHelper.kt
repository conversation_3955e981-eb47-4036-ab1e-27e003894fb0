package com.stt.android.login

import java.util.concurrent.atomic.AtomicBoolean

object <PERSON><PERSON><PERSON><PERSON><PERSON> {

    /**
     * Boolean flag for smooth pairing use purposes. The user is considered "just logged in" until the user
     * first visits the DeviceActivity or logs out. If this flag is set, automatic background scanning for BLE
     * and data layer devices is performed in HomeActivity.
     */
    @JvmStatic
    val justLoggedIn = AtomicBoolean(false)
}
