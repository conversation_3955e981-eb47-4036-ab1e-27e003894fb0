package com.stt.android.di;

import android.content.Context;
import android.util.DisplayMetrics;
import androidx.annotation.VisibleForTesting;
import androidx.work.Configuration;
import androidx.work.WorkManager;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.LoginController;
import com.stt.android.controllers.SessionController;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.controllers.WorkoutHeaderController;
import com.stt.android.di.initializer.AppInitializers;
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator;
import com.stt.android.domain.database.DatabaseHelper;
import com.stt.android.easterEgg.EasterEgg;
import com.stt.android.home.settings.NotificationSettingsPreference;
import com.stt.android.hr.HeartRateUpdateProvider;
import com.stt.android.injection.components.BrandApplicationComponent;
import com.stt.android.maps.MapSnapshotter;
import com.stt.android.maps.SuuntoMaps;
import com.stt.android.notifications.PushNotificationHandler;
import com.stt.android.notifications.STTNotification;
import com.stt.android.remote.di.BaseUrlConfiguration;
import com.stt.android.services.FetchStaticConfigFilesWorker;
import com.stt.android.tasks.DeleteWorkoutImageTask;
import com.stt.android.tasks.DeleteWorkoutVideoTask;
import com.stt.android.tasks.RecentWorkoutSummaryLoader;
import com.stt.android.tasks.WorkoutDataLoader;
import com.stt.android.ui.activities.SetupCadenceActivity;
import com.stt.android.ui.components.DistanceEditor;
import com.stt.android.ui.controllers.WorkoutDataLoaderController;
import com.stt.android.ui.tasks.SimilarWorkoutsLoader;
import com.stt.android.ui.tasks.WorkoutSummariesLoader;
import com.stt.android.ui.workout.widgets.AltitudeWidget;
import com.stt.android.ui.workout.widgets.AvgCadenceWidget;
import com.stt.android.ui.workout.widgets.AvgHeartRatePercentageOfMaxWidget;
import com.stt.android.ui.workout.widgets.AvgSpeedPaceWidget;
import com.stt.android.ui.workout.widgets.CadenceWidget;
import com.stt.android.ui.workout.widgets.DistanceWidget;
import com.stt.android.ui.workout.widgets.DurationTimeAutoPauseWidget;
import com.stt.android.ui.workout.widgets.DurationWidget;
import com.stt.android.ui.workout.widgets.EnergyWidget;
import com.stt.android.ui.workout.widgets.GhostAheadBehindWidget;
import com.stt.android.ui.workout.widgets.GhostTimeDistanceWidget;
import com.stt.android.ui.workout.widgets.HeartRateGraphWidget;
import com.stt.android.ui.workout.widgets.HeartRatePercentageOfMaxWidget;
import com.stt.android.ui.workout.widgets.LapAvgSpeedPaceWidget;
import com.stt.android.ui.workout.widgets.LapDistanceWidget;
import com.stt.android.ui.workout.widgets.LapDurationWidget;
import com.stt.android.ui.workout.widgets.LapTableWidget;
import com.stt.android.ui.workout.widgets.LapsTypeSelectorWidget;
import com.stt.android.ui.workout.widgets.LastUnitSpeedPaceWidget;
import com.stt.android.ui.workout.widgets.MaxAltitudeWidget;
import com.stt.android.ui.workout.widgets.MaxHeartRatePercentageWidget;
import com.stt.android.ui.workout.widgets.MaxSpeedPaceWidget;
import com.stt.android.ui.workout.widgets.MinAltitudeWidget;
import com.stt.android.ui.workout.widgets.MinMaxAltitudeWidget;
import com.stt.android.ui.workout.widgets.RunAvgSpeedPaceWidget;
import com.stt.android.ui.workout.widgets.RunCountWidget;
import com.stt.android.ui.workout.widgets.RunDistanceWidget;
import com.stt.android.ui.workout.widgets.RunDurationWidget;
import com.stt.android.ui.workout.widgets.RunMaxSpeedPaceWidget;
import com.stt.android.ui.workout.widgets.RunSpeedWidget;
import com.stt.android.ui.workout.widgets.SkiAngleWidget;
import com.stt.android.ui.workout.widgets.SkiDescentWidget;
import com.stt.android.ui.workout.widgets.SkiDistanceWidget;
import com.stt.android.ui.workout.widgets.SkiDurationWidget;
import com.stt.android.ui.workout.widgets.SkiSpeedWidget;
import com.stt.android.ui.workout.widgets.SpeedAltitudeGraphWidget;
import com.stt.android.ui.workout.widgets.SpeedPaceWidget;
import com.stt.android.ui.workout.widgets.StepCountWidget;
import com.stt.android.ui.workout.widgets.StepRateWidget;
import com.stt.android.usecases.startup.UserSettingsTracker;
import com.stt.android.utils.UpdatePressureTask;
import com.stt.android.workouts.AltitudeConnection;
import com.stt.android.workouts.LocationConnection;
import com.stt.android.workouts.autosave.AutoSaveOngoingWorkoutController;
import com.stt.android.workouts.hardware.BleCadenceConnectionMonitor;
import com.stt.android.workouts.hardware.BleHeartRateConnectionMonitor;
import com.stt.android.workouts.hardware.BluetoothHeartRateConnectionMonitor;
import com.stt.android.workouts.hardware.steps.StepCountConnection;
import dagger.hilt.EntryPoint;
import dagger.hilt.InstallIn;
import dagger.hilt.components.SingletonComponent;

@InstallIn(SingletonComponent.class)
@EntryPoint
public interface MainProcessEntryPoint extends BrandApplicationComponent {
    // These entities are provided directly since we cannot do process-specific injection in STTApplication anymore

    Configuration workManagerConfig();

    DatabaseHelper databaseHelper();

    WorkoutDataLoaderController workoutDataLoaderController();

    CurrentUserController currentUserController();

    UserSettingsController userSettingsController();

    UserSettingsTracker userSettingsTrackerForAnalytics();

    EasterEgg easterEgg();

    SuuntoMaps suuntoMaps();

    WorkManager workManager();

    AppInitializers appInitializers();

    dagger.Lazy<MapSnapshotter> mapSnapshotter();

    // todo remove this when getting rid of ANetworkProvider etc.
    BaseUrlConfiguration baseUrlConfiguration();

    void inject(LoginController loginController);

    void inject(FetchStaticConfigFilesWorker fetchStaticConfigFilesWorker);

    void inject(WorkoutDataLoader workoutDataLoader);

    void inject(SimilarWorkoutsLoader similarWorkoutsLoader);

    void inject(AutoSaveOngoingWorkoutController autoSaveOngoingWorkoutController);

    void inject(PushNotificationHandler pushNotificationHandler);

    void inject(SetupCadenceActivity setupCadenceActivity);

    void inject(DistanceEditor distanceEditor);

    void inject(WorkoutSummariesLoader workoutSummariesLoader);

    void inject(BleCadenceConnectionMonitor bleCadenceConnectionMonitor);

    void inject(BleHeartRateConnectionMonitor bleHeartRateConnectionMonitor);

    void inject(HeartRateUpdateProvider heartRateUpdateProvider);

    void inject(BluetoothHeartRateConnectionMonitor bluetoothHeartRateConnectionMonitor);

    void inject(LocationConnection locationConnection);

    void inject(AltitudeConnection altitudeConnection);

    void inject(StepCountConnection stepCountConnection);

    void inject(UpdatePressureTask updatePressureTask);

    void inject(NotificationSettingsPreference notificationSettingsPreference);

    void inject(RecentWorkoutSummaryLoader recentWorkoutSummaryLoader);

    void inject(DeleteWorkoutImageTask deleteWorkoutImageTask);

    void inject(DeleteWorkoutVideoTask deleteWorkoutVideoTask);

    void inject(STTNotification sttNotification);

    WorkoutDetailsRewriteNavigator rewriteNavigator();

    AltitudeWidget altitudeWidget();

    AvgCadenceWidget avgCadenceWidget();

    AvgHeartRatePercentageOfMaxWidget avgHeartRatePercentageWidget();

    AvgSpeedPaceWidget avgSpeedPaceWidget();

    AvgHeartRatePercentageOfMaxWidget.BigAvgHeartRatePercentageOfMaxWidget
    bigAvgHeartRatePercentageWidget();

    DurationTimeAutoPauseWidget.BigDurationTimeAutoPauseWidget bigDurationTimeWidget();

    EnergyWidget.BigEnergyWidget bigEnergyWidget();

    HeartRatePercentageOfMaxWidget.BigHeartRatePercentageOfMaxWidget bigHeartRatePercentageWidget();

    DurationWidget durationWidget();

    DurationWidget.SmallDurationWidget smallDurationWidget();

    MaxHeartRatePercentageWidget.BigMaxHeartRatePercentageWidget bigMaxHeartRatePercentageWidget();

    CadenceWidget cadenceWidget();

    DistanceWidget distanceWidget();

    DistanceWidget.SmallDistanceWidget smallDistanceWidget();

    SpeedPaceWidget speedPaceWidget();

    SpeedPaceWidget.SmallSpeedPaceWidget smallSpeedPaceWidget();

    AvgSpeedPaceWidget.SmallAvgSpeedPaceWidget smallAvgSpeedPaceWidget();

    EnergyWidget energyWidget();

    EnergyWidget.SmallEnergyWidget smallEnergyWidget();

    LastUnitSpeedPaceWidget lastUnitSpeedPaceWidget();

    LastUnitSpeedPaceWidget.SmallLastUnitSpeedPaceWidget smallLastUnitSpeedPaceWidget();

    AltitudeWidget.SmallAltitudeWidget smallAltitudeWidget();

    MaxSpeedPaceWidget maxSpeedPaceWidget();

    MaxSpeedPaceWidget.SmallMaxSpeedPaceWidget smallMaxSpeedPaceWidget();

    LapDurationWidget lapDurationWidget();

    LapDurationWidget.SmallLapDurationWidget smallLapDurationWidget();

    LapDistanceWidget lapDistanceWidget();

    LapDistanceWidget.SmallLapDistanceWidget smallLapDistanceWidget();

    LapTableWidget lapTableWidget();

    HeartRateGraphWidget hrChartWidget();

    HeartRatePercentageOfMaxWidget hrPercentageWidget();

    HeartRatePercentageOfMaxWidget.SmallHeartRatePercentageOfMaxWidget smallHrPercentageWidget();

    MaxHeartRatePercentageWidget maxHrPercentageWidget();

    MaxHeartRatePercentageWidget.SmallMaxHeartRatePercentageWidget smallMaxHrPercentageWidget();

    AvgHeartRatePercentageOfMaxWidget.SmallAvgHeartRatePercentageOfMaxWidget smallAvgHrPercentage();

    DurationTimeAutoPauseWidget.SmallDurationTimeAutoPauseWidget smallDurationTimeWidget();

    DurationTimeAutoPauseWidget durationTimeWidget();

    LapsTypeSelectorWidget lapsTypeSelectorWidget();

    LapAvgSpeedPaceWidget lapAvgSpeedPaceWidget();

    LapAvgSpeedPaceWidget.SmallLapAvgSpeedPaceWidget smallLapAvgSpeedPaceWidget();

    GhostTimeDistanceWidget ghostTimeDistanceWidget();

    GhostAheadBehindWidget ghostAheadBehindWidget();

    CadenceWidget.SmallCadenceWidget smallCadenceWidget();

    AvgCadenceWidget.SmallAvgCadenceWidget smallAvgCadenceWidget();

    StepCountWidget stepCountWidget();

    StepCountWidget.SmallStepCountWidget smallStepCountWidget();

    StepCountWidget.BigStepCountWidget bigStepCountWidget();

    StepRateWidget stepRateWidget();

    StepRateWidget.BigStepRateWidget bigStepRateWidget();

    StepRateWidget.SmallStepRateWidget smallStepRateWidget();

    SpeedAltitudeGraphWidget speedAltitudeChartWidget();

    RunCountWidget runCountWidget();

    RunCountWidget.SmallRunCountWidget smallRunCountWidget();

    RunDurationWidget runDurationWidget();

    RunDurationWidget.SmallRunDurationWidget smallRunDurationWidget();

    RunDistanceWidget runDistanceWidget();

    RunDistanceWidget.SmallRunDistanceWidget smallRunDistanceWidget();

    RunAvgSpeedPaceWidget runAvgSpeedPaceWidget();

    RunAvgSpeedPaceWidget.SmallRunAvgSpeedPaceWidget smallRunAvgSpeedPaceWidget();

    RunMaxSpeedPaceWidget runMaxSpeedPaceWidget();

    RunMaxSpeedPaceWidget.SmallRunMaxSpeedPaceWidget smallRunMaxSpeedPaceWidget();

    RunSpeedWidget runSpeedWidget();

    RunSpeedWidget.SmallRunSpeedWidget smallRunSpeedWidget();

    SkiSpeedWidget skiSpeedWidget();

    SkiSpeedWidget.SmallSkiSpeedWidget smallSkiSpeedWidget();

    MinMaxAltitudeWidget minMaxAltitudeWidget();

    MinMaxAltitudeWidget.SmallMinMaxAltitudeWidget smallMinMaxAltitudeWidget();

    SkiAngleWidget skiAngleWidget();

    SkiAngleWidget.SmallSkiAngleWidget smallSkiAngleWidget();

    SkiDurationWidget skiDurationWidget();

    SkiDurationWidget.SmallSkiDurationWidget smallSkiDurationWidget();

    SkiDistanceWidget skiDistanceWidget();

    SkiDistanceWidget.SmallSkiDistanceWidget smallSkiDistanceWidget();

    MaxAltitudeWidget maxAltitudeWidget();

    MaxAltitudeWidget.SmallMaxAltitudeWidget smallMaxAltitudeWidget();

    MinAltitudeWidget minAltitudeWidget();

    MinAltitudeWidget.SmallMinAltitudeWidget smallMinAltitudeWidget();

    SkiDescentWidget skiDescentWidget();

    SkiDescentWidget.SmallSkiDescentWidget smallSkiDescentWidget();

    DisplayMetrics displayMetrics();

    Context appContext();

    @VisibleForTesting
    SessionController sessionController();

    @VisibleForTesting
    WorkoutHeaderController workoutHeaderController();
}
