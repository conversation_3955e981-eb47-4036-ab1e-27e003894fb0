package com.stt.android.common.ui

import android.app.Activity
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding

class DataBindingHolder {

    var binding: ViewDataBinding? = null

    fun setContentView(activity: Activity, layoutId: Int): ViewDataBinding =
        DataBindingUtil.setContentView<ViewDataBinding>(activity, layoutId).also {
            binding = it
        }

    fun inflate(
        inflater: LayoutInflater,
        layoutId: Int,
        parent: ViewGroup?,
        attachToParent: Boolean
    ): ViewDataBinding = DataBindingUtil.inflate<ViewDataBinding>(
        inflater,
        layoutId,
        parent,
        attachToParent
    ).also { binding = it }

    fun clear() {
        binding?.unbind()
        binding = null
    }
}
