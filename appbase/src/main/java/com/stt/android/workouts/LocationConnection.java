package com.stt.android.workouts;

import android.content.Context;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.stt.android.STTApplication;
import com.stt.android.location.LocationModel;
import com.stt.android.location.LocationUpdateRequest;
import java.io.Closeable;
import javax.inject.Inject;
import timber.log.Timber;

/**
 * Helper class responsible to start and stop listening for Locations {@link LocationModel}.
 */
public class LocationConnection implements Closeable {
    private final LocationModel.Listener listener;

    @Inject
    LocationModel locationModel;
    @Inject
    Context context;

    LocationConnection(LocationModel.Listener listener, long intervalInMilliSeconds,
        float smallestDistanceInMeters) {
        STTApplication.getComponent().inject(this);
        this.listener = listener;

        String msg = "Starting location updates.";
        Timber.d(msg);
        locationModel.addListener(listener);
        try {
            locationModel.requestLocationUpdate(LocationUpdateRequest.builder()
                .intervalInMilliSeconds(intervalInMilliSeconds)
                .smallestDistanceInMeters(smallestDistanceInMeters)
                .build());
        } catch (SecurityException | IllegalArgumentException e) {
            String message = "Unable to start location provider!";
            FirebaseCrashlytics.getInstance().log(message);
            Timber.e(e, message);
        }
    }

    @Override
    public void close() {
        String message = "Stopping location updates.";
        Timber.d(message);
        try {
            locationModel.removeListener(listener);
            locationModel.stopLocationUpdate();
        } catch (Throwable e) {
            Timber.e(e, "Failed to stop location updates");
        }
    }
}
