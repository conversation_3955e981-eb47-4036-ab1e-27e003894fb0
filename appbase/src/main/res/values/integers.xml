<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Used by Helpshift SDK. Minimum length requirement of text while filing new issue. -->
    <integer name="hs__issue_description_min_chars">15</integer>
    <integer name="workout_button_visibility">@integer/view_visible</integer>

    <integer name="visible_in_portrait_mode">@integer/view_visible</integer>
    <integer name="visible_in_landscape_mode">@integer/view_gone</integer>

    <!-- 6 hours in milliseconds. Widgets mostly rely on data updates triggering updates,
         but for inactive users who don't have a paired watch some periodic updates are also
         needed to make sure the widgets don't show previous dates data until app is opened again -->
    <integer name="systemwidget_update_interval">21600000</integer>

    <integer name="dashboard_grid_span_count">2</integer>

    <integer name="workout_compare_appbar_visibility">@integer/view_gone</integer>

</resources>
