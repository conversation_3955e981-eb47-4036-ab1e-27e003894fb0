<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="map_option_my_tracks">My tracks</string>
    <string name="map_option_heatmap">Heatmap</string>
    <string name="map_option_road_surface">Road surface</string>
    <string name="map_option_pois_toggle">Points of interest (POI)</string>
    <string name="map_option_turn_by_turn_toggle">Turn-by-turn</string>
    <string name="map_options_2d">2D</string>
    <string name="map_options_3d">3D</string>

    <string name="latest_notifications">Latest notifications</string>
    <string name="workouts_capital">ACTIVITIES</string>
    <string name="total_time_capital">TOTAL TIME</string>
    <string name="workout_values_headline_max_depth">Max depth</string>
    <string name="workout_values_headline_swim_stroke_count">Strokes</string>
    <string name="workout_values_headline_swim_stroke_distance">Stroke distance</string>
    <string name="workout_values_headline_revolution_count">Revolutions</string>
    <string name="workout_values_headline_max_cadence">Max cadence</string>
    <string name="workout_values_headline_avg_depth">Avg depth</string>
    <string name="workout_values_headline_min_depth">Min depth</string>
    <string name="workout_values_headline_dive_time">Dive time</string>
    <string name="workout_values_headline_total_time_underwater">Total time underwater</string>
    <string name="workout_values_headline_dive_time_max">Longest dive</string>
    <string name="workout_values_headline_dive_surface_time">Surface time</string>
    <string name="workout_values_headline_dive_recovery_time">Dive recovery time</string>
    <string name="workout_values_headline_dive_in_workout">Number of dives</string>
    <string name="workout_values_headline_avg_speed">Avg. speed</string>
    <string name="workout_values_headline_moving_speed">Moving speed</string>
    <string name="workout_values_headline_dive_mode">Dive mode</string>
    <string name="workout_values_headline_dive_num_in_series">In series</string>
    <string name="workout_values_value_dive_num_in_series">#%d</string>
    <string name="workout_values_headline_dive_max_depth_temperature">Bottom temp.</string>
    <string name="workout_values_headline_dive_gas">Gas type</string>
    <string name="workout_values_headline_dive_start_pressure">Start pressure</string>
    <string name="workout_values_headline_dive_end_pressure">End pressure</string>
    <string name="workout_values_headline_dive_used_pressure">Used pressure</string>
    <string name="workout_values_headline_dive_cns">CNS</string>
    <string name="workout_values_headline_dive_otu">OTU</string>
    <string name="workout_values_headline_dive_algorithm_lock">Algorithm lock</string>
    <string name="workout_values_headline_dive_algorithm">Algorithm</string>
    <string name="workout_values_headline_dive_personal_setting">Personal setting</string>
    <string name="workout_values_headline_dive_altitude_setting">Altitude setting</string>
    <string name="workout_values_headline_dive_gas_consumption">Consumption</string>
    <string name="workout_values_headline_dive_gradient_factors">Gradient factors</string>
    <string name="workout_values_value_dive_gradient_factors">%1$d/%2$d</string>
    <string name="avg_speed_capital">AVG. SPEED</string>
    <string name="avg_speed">Average Speed</string>
    <string name="home">Home</string>
    <string name="diary">Diary</string>
    <string name="people">People</string>
    <string name="explore">Map</string>
    <string name="settings">Settings</string>
    <string name="trends">Training zone</string>
    <string name="no_description_added">No description</string>
    <string name="latest">Latest</string>
    <string name="popular">Popular</string>
    <string name="map">Map</string>
    <string name="community">Community</string>
    <string name="workouts">activities</string>
    <string name="auto_pause_info">If speed is below auto pause threshold the recording is automatically paused until speed is above threshold</string>
    <string name="done">Done</string>
    <string name="badge_new">New</string>
    <string name="beta_feature_tag">Beta</string>

    <plurals name="dives_plural_without_quantity">
        <item quantity="one">Dive</item>
        <item quantity="few">Dives</item>
        <item quantity="many">Dives</item>
        <item quantity="other">Dives</item>
    </plurals>

    <plurals name="workouts_plural_without_quantity">
        <item quantity="one">Activity</item>
        <item quantity="few">Activities</item>
        <item quantity="many">Activities</item>
        <item quantity="other">Activities</item>
    </plurals>

    <plurals name="workouts_plural">
        <item quantity="one">%d activity</item>
        <item quantity="few">%d activities</item>
        <item quantity="many">%d activities</item>
        <item quantity="other">%d activities</item>
    </plurals>

    <plurals name="exercises_plural_without_quantity">
        <item quantity="one">Exercise</item>
        <item quantity="few">Exercises</item>
        <item quantity="many">Exercises</item>
        <item quantity="other">Exercises</item>
    </plurals>

    <plurals name="exercises_plural">
        <item quantity="one">%d exercise</item>
        <item quantity="few">%d exercises</item>
        <item quantity="many">%d exercises</item>
        <item quantity="other">%d exercises</item>
    </plurals>

    <!-- Once we use the release keystore we have to create the right APIKey -->
    <!-- <string name="googlemaps_sdk_key">Visit https://developers.google.com/maps/documentation/android/mapkey to get a key</string> -->
    <!-- Once we use the release keystore we have to provide the hash to FB App and we'll get APP ID -->
    <!-- <string name="facebook_app_id"></string> -->

    <string name="new_workout_settings">New activity</string>
    <string name="activity_type">Activity type</string>
    <string name="auto_pause">Autopause</string>
    <string name="camera_activity_not_found">Camera not found</string>
    <string name="music_activity_not_found">Music player not found</string>
    <string name="start">Start</string>
    <string name="stop">Stop</string>
    <string name="lap">Lap</string>
    <string name="resume">Resume</string>
    <string name="end">End</string>
    <string name="speed_pace_capital">SPEED/PACE</string>
    <string name="avg_speed_avg_pace_capital">AVG SPEED/AVG PACE</string>
    <string name="last_km_speed_pace_capital">LAST KM SPEED/PACE</string>
    <string name="last_mi_speed_pace_capital">LAST MI SPEED/PACE</string>
    <string name="heart_rate_percentage_capital" formatted="false">HEART RATE/% OF MAX</string>
    <string name="max_heart_rate_percentage_capital" formatted="false">MAX HEART RATE/%</string>
    <string name="workout_values_headline_min_heart_rate" formatted="false">Min heart rate</string>
    <string name="workout_values_headline_max_heart_rate" formatted="false">Max heart rate</string>
    <string name="avg_heart_rate_percentage_capital" formatted="false">AVG HEART RATE/%</string>
    <string name="workout_values_headline_avg_heart_rate" formatted="false">Avg heart rate</string>
    <string name="altitude_capital">ALTITUDE</string>
    <string name="max_speed_pace_capital">MAX SPEED/PACE</string>
    <string name="duration_time_capital">DURATION/TIME</string>
    <string name="pause_active_capital">PAUSE ACTIVE</string>
    <string name="auto_pause_active_capital">AUTOPAUSE ACTIVE</string>

    <!-- Settings -->
    <string name="account">Account</string>
    <string name="location">Location</string>
    <string name="state">State</string>
    <string name="select_state">Select state</string>
    <string name="select_state_title">In order to provide better user experience and more localized content for you, please select your state of residence.</string>
    <string name="select_state_button_cancel">I don\'t live in the US</string>
    <string name="settings_general">General</string>
    <string name="settings_general_measurement_unit">Unit system</string>
    <string name="settings_general_measurement_unit_metric">Metric</string>
    <string name="settings_general_measurement_unit_imperial">Imperial</string>
    <string name="settings_general_heart_rate_belt">Heart rate sensor</string>
    <string name="settings_general_heart_rate_belt_summary">Configure heart rate sensor</string>
    <string name="settings_general_heart_rate_belt_connect">Configure your sensor</string>
    <string name="settings_general_heart_rate_belt_connect_setup_summary">Configure your heart rate sensor</string>
    <string name="settings_general_heart_rate_belt_recording_options">Recording options</string>
    <string name="settings_general_heart_rate_belt_recording_options_maximum">Maximum heart rate</string>
    <string name="settings_general_heart_rate_belt_recording_options_maximum_summary">Max value is set to %s</string>
    <string name="settings_general_first_day_of_the_week">Start of the week</string>
    <string name="settings_general_first_day_of_the_week_sunday">Sunday</string>
    <string name="settings_general_first_day_of_the_week_monday">Monday</string>
    <string name="settings_general_user_settings">User settings</string>
    <string name="settings_general_user_settings_summary">Adjust your settings to get more
        accurate calorie consumption estimate</string>
    <string name="settings_general_user_settings_gender">Gender</string>
    <string name="settings_general_user_settings_gender_male">Male</string>
    <string name="settings_general_user_settings_gender_female">Female</string>
    <string name="settings_general_user_settings_weight">Weight</string>
    <string name="settings_general_user_settings_weight_summary">Not visible to other users</string>
    <string name="dialog_title_settings_general_user_settings_weight">Weight (%s)</string>
    <string name="settings_general_user_settings_height">Height</string>
    <string name="dialog_title_settings_general_user_settings_height">Height</string>
    <string name="dialog_subtitle_settings_general_user_settings_height">Please type your height</string>
    <string name="dialog_feet_hint_settings_general_user_settings_height">Height-feet</string>
    <string name="dialog_inch_hint_settings_general_user_settings_height">Height-inch</string>
    <string name="settings_general_user_settings_max_heart">Max HR</string>
    <string name="settings_general_user_settings_rest_heart">Resting HR</string>
    <string name="settings_general_user_settings_age">Age</string>
    <string name="settings_general_user_settings_age_summary">Not visible to other users</string>
    <string name="dialog_title_settings_general_user_settings_age">Age</string>
    <string name="settings_general_map_provider">Map provider</string>
    <string name="settings_general_map_provider_mapbox">Mapbox</string>
    <string name="settings_general_map_provider_amap">Amap</string>
    <string name="settings_general_screen_backlight">Screen backlight</string>
    <string name="settings_general_screen_backlight_automatic">Automatic</string>
    <string name="settings_general_screen_backlight_always_on">Always on</string>
    <string name="settings_general_altitude_offset">Altitude offset</string>
    <string name="settings_general_altitude_offset_summary">Altitude offset</string>
    <string name="dialog_title_settings_general_altitude_offset">Altitude offset (%s)</string>
    <string name="settings_service_sign_out">Sign out</string>
    <string name="account_settings_title">Account settings</string>
    <string name="account_settings_reset_password_title">Change password</string>
    <string name="account_settings_reset_password_button">Change password</string>
    <string name="account_settings_username">Username: %1$s</string>
    <string name="account_settings_email">Email: %1$s</string>
    <string name="account_settings_email_address">Email address</string>
    <string name="account_settings_reset_password_intro">We will send a link to your email (%1$s) to change the password.</string>
    <string name="account_settings_reset_password_confirmation">A link to change your password will be sent to your email (%1$s).\n\nIn case you can’t find the email, please check the trash or other special folders of your mailbox. If the email is still missing, please try again.</string>
    <string name="account_settings_reset_password_support_intro">If you\'re having problems or need additional support, contact us.</string>
    <string name="account_settings_reset_password_email_resent">New email sent</string>
    <string name="settings_notification">Notification</string>

    <string name="export_my_data_title">Export my data</string>
    <string name="export_my_data_intro">You will receive a link to download your data via email (%s)</string>
    <string name="export_my_data_tip">This action can be done only once every 30 days.</string>
    <string name="export_my_data_intro_2">You will soon receive a download link to your email (%s)</string>
    <string name="export_my_data_tip_2">Exporting data will be available to you again on %s.</string>
    <string name="export_my_data_error">Unavailable until %s</string>
    <string name="export_my_data_button">Send download link</string>

    <string name="account_settings_delete_account_title">Delete account</string>
    <string name="account_settings_delete_account_intro">This account is Sports Tracker Community account which consists of Suunto and Sports Tracker services.\n\nUpon deletion, your profile and all your workout and activity data will be deleted from both services.\n\nYou will receive link to your email (%1$s).</string>
    <string name="account_settings_delete_account_intro_new">This account is your account on Suunto – you can use it to log in to the Suunto App, Sports Tracker, and Suunto official website shopping services.\n\nAfter deletion, your personal information and all exercise and activity data will be deleted from these three services.\n\nYou will receive a link to your mail\n(%1$s)</string>
    <string name="account_settings_delete_account_tips">This action can not be undone</string>
    <string name="account_settings_delete_account_send_link_button">Send delete link</string>
    <string name="account_settings_delete_account_confirmation">You will soon receive a confirmation link to your email (%1$s) for deleting account.</string>
    <string name="account_settings_delete_account_cannot_be_undone">Remember that deleting account can not be undone.</string>

    <string name="dialog_title_settings_service_sign_out">You are connected as %1$s</string>
    <string name="positive_button_settings_service_sign_out">Sign out</string>
    <string name="negative_button_settings_service_sign_out">Cancel</string>
    <string name="settings_other">Other</string>
    <string name="settings_help">Support</string>
    <string name="user_profile_support_chat_title">Support Chat</string>
    <string name="support_chat_screen_title">Support chat bot beta</string>
    <string name="support_chat_screen_error">Support chat bot is not available. Please try again later.</string>
    <string name="settings_other_blog_summary">Read the latest news</string>
    <string name="settings_other_app_update">App update</string>
    <string name="settings_other_app_update_summary">Current version %1$s (%2$d)</string>
    <string name="settings_open_source_licenses_title">Open source licenses</string>
    <string name="settings_privacy_policy">Privacy policy</string>
    <string name="settings_service_terms">Service terms</string>
    <string name="settings_data_practices">Data practices</string>

    <string name="power_management_preference">Power management</string>
    <string name="power_management_preference_summary">These settings affect the connectivity and tracking functionality</string>

    <string name="dialog_title_select">Select</string>
    <string name="dialog_title_share">Share via</string>
    <string name="title_activity_save_workout">Save activity</string>
    <string name="workout_values_headline_max_pace">Max pace</string>
    <string name="workout_values_headline_peak_pace_30s">Peak pace 30s</string>
    <string name="workout_values_headline_peak_pace_1m">Peak pace 1min</string>
    <string name="workout_values_headline_peak_pace_3m">Peak pace 3min</string>
    <string name="workout_values_headline_peak_pace_5m">Peak pace 5min</string>
    <string name="workout_values_headline_avg_pace">Avg pace</string>
    <string name="workout_values_headline_swimming_avg_pace">Avg. pace</string>
    <string name="workout_values_headline_moving_pace">Moving pace</string>
    <string name="avg_pace_capital">AVG PACE</string>
    <string name="avg_pace">Average Pace</string>
    <string name="max_speed_capital">MAX SPEED</string>
    <string name="workout_values_headline_max_speed">Max speed</string>
    <string name="workout_values_headline_peak_speed_30s">Peak speed 30s</string>
    <string name="workout_values_headline_peak_speed_1m">Peak speed 1min</string>
    <string name="workout_values_headline_peak_speed_3m">Peak speed 3min</string>
    <string name="workout_values_headline_peak_speed_5m">Peak speed 5min</string>
    <string name="avg_max_hr_capital">AVG / MAX HR</string>
    <string name="avg_hr_capital">AVG HR</string>
    <string name="max_hr_capital">MAX HR</string>

    <string name="max_bpm">%s bpm</string>
    <string name="rest_bpm">%s bpm</string>
    <string name="heart_unit">bpm</string>
    <string name="average_bmp">Average %d bpm</string>
    <string name="write_description">Write description</string>
    <string name="description_hint_for_dives">Describe activity, name dive site etc.</string>
    <string name="sharing_options">Share activity</string>
    <string name="delete">Delete</string>
    <string name="delete_workout">Delete activity?</string>
    <string name="save">Save</string>
    <string name="login">Log in</string>

    <string name="username_or_email">Username or email</string>
    <string name="password">Password</string>
    <string name="password_requirements">Enter a combination of at least eight numbers, letters and special characters (like ! and &amp;).</string>
    <string name="sign_up_with_facebook">Continue with Facebook</string>
    <string name="sign_up_with_google">Continue with Google</string>
    <string name="logging_out">Logging out from the service. If you have a lot of activities, this may take a while.</string>
    <string name="unable_to_logout">Unable to log out. Please try again later.</string>
    <string name="version">Version %1$s release (%2$d)</string>
    <string name="settings_applied">Settings applied</string>

    <string name="welcome">Welcome!</string>
    <string name="diary_description">Diary lets you see your training history and progress analysis.</string>
    <string name="connect_to_sync">Or connect to sync your activities from sports-tracker.com to your phone.</string>
    <string name="connect_to_sync_no_tracking">Connect to sync your activities from sports-tracker.com to your phone.</string>
    <string name="no_activities">No activities yet. Start tracking your first!</string>
    <string name="no_activities_no_tracking">No activity yet.</string>

    <string name="gps_disabled_enable">GPS is disabled! Would you like to enable it?</string>

    <string name="taiwan_display_china">Taiwan Area</string>
    <string name="hong_kong_display_china">HK SAR-China</string>
    <string name="macao_display_china">Macao SAR-China</string>
    <string name="new_email_address">New email address</string>
    <string name="tap_to_add_your_email_address">Tap to add your email address</string>
    <string name="change">Change</string>

    <!-- HR -->
    <string name="wear_hr_to_activate">Wear your heart rate sensor to activate it. Moisten the electrodes contacting your body with water before use.</string>
    <string name="no_belt_found_capital">No sensor found</string>
    <string name="remove_belt_try_again">Remove the sensor for 30 seconds, moisten the contacts with water and try again. Charge the battery if connection fails</string>
    <string name="connecting_belt">Connecting sensor</string>
    <string name="connect_hr_belt">Connect HR sensor</string>
    <string name="pin_code_0000">Pin code is 0000</string>
    <string name="your_current_hr">YOUR CURRENT HEART RATE:</string>
    <string name="note_hr_save_power">NOTE! To save power the heart rate sensor turns off after wearing it for 10 minutes without a connection.</string>
    <string name="hr_ready_use">Your heart rate sensor is now ready to use and next time it will be connected automatically</string>
    <string name="bluetooth_not_enabled">Bluetooth not enabled. Please turn it on and try to connect again.</string>
    <string name="hr_graph_ad_body">Train smarter and get better results with a heart rate sensor. See your heart rate and intensity during and after activity.</string>
    <string name="hr_graph_ad_action">learn more</string>
    <!-- HR -->

    <!-- Report feature -->
    <string name="report">Report</string>
    <string name="report_content_title">Thanks for reporting this.</string>
    <string name="report_content_description">Our team will review the post and if it violates our community guidelines or Terms of use, we\'ll proceed accordingly.</string>
    <string name="report_content_error">Something went wrong.\nPlease try again.</string>
    <!-- Report feature -->

    <!-- Activity type names -->
    <string name="select_activity">Select activity</string>
    <!-- End activity type names -->

    <!-- Generic -->
    <string name="all_from">From</string>
    <string name="all_to">To</string>
    <string name="dismiss">Dismiss</string>
    <string name="remove">Remove</string>
    <!-- Generic -->

    <!-- Auto pause values -->
    <string name="select_autopause_threshold">Select autopause threshold</string>
    <string name="less_amount_unit">Less than %1$s%2$s</string>
    <!-- End auto pause values -->

    <string name="full_name">Full name</string>
    <string name="email">Email</string>
    <string name="email_or_username">Email / Username</string>
    <string name="phone">Phone number</string>
    <string name="required">required</string>

    <!-- Create user error messages -->
    <string name="too_short">Too short</string>
    <string name="unable_to_create_user">Unable to create new user</string>
    <string name="phone_error">Invalid phone number</string>
    <string name="workout_save_error">Something went wrong, and unfortunately the changes could not be saved.</string>
    <!-- End user error messages -->

    <!-- Backend error code descriptions -->
    <!-- End backend error code descriptions -->

    <!-- Application specific error codes descriptions -->
    <string name="error_0">Unknown error</string>
    <string name="unknown_error_id">Unknown error (%d)</string>
    <string name="error_1399">We could not fetch your Facebook user details</string>
    <string name="error_1400">Error while communicating with Facebook</string>
    <string name="error_1410">Your password has been reset, please check your email.</string>
    <string name="error_1412">Verification code is incorrect. Please try again.</string>
    <string name="error_1413">Phone number already exists</string>
    <string name="error_generic">Oops! Something went wrong</string>
    <string name="error_generic_try_again">There was a problem. Please try again.</string>
    <!-- End application specific error codes descriptions -->

    <string name="picture_saved">Image %s saved</string>
    <string name="video_saved">Video %s saved</string>
    <string name="video_save_finished">Finished</string>
    <string name="media_saved">Media saved</string>
    <string name="unable_to_link_picture_workout">We could not save image %s to current activity</string>
    <string name="error_taking_picture">There was an error while using the camera. Please try again later.</string>
    <string name="error_taking_picture_sd_missing">Unable to take images. Make sure your SD Card is available</string>
    <string name="unable_to_login">Login failed</string>
    <string name="login_required">Sign up for a free account to continue. It\'s quick!</string>
    <string name="login_dialog_warning_session_expired_title">Session expired</string>
    <string name="login_dialog_warning_session_expired_message">Please re-login to renew your session.</string>
    <string name="login_dialog_warning_password_reset_title">Security alert!</string>
    <string name="login_dialog_warning_password_reset_message">Our monitoring has detected abnormalities related to your account and automatically locked it. You can unlock it by resetting your password from the link we’ve sent to your email.\n\nIn case of problems or questions, please contact our support.</string>

    <!-- Sharing options -->
    <string name="none">None</string>
    <string name="sharing_private">Private</string>
    <string name="sharing_public">Public</string>
    <string name="sharing_dialog_title">Choose who can see your activities</string>
    <string name="sharing_dialog_description">This is your default privacy setting. You can change the privacy of individual exercises by editing the exercise.</string>
    <!-- End sharing options -->

    <string name="no_latest_workouts_from_map_area">There are no activities in this map area</string>

    <!-- Commenting -->
    <string name="send">Send</string>
    <string name="add_comment">Add a comment</string>
    <string name="be_first_to_comment">Be first to comment!</string>
    <string name="delete_comment">Delete comment?</string>
    <!-- End commenting -->

    <!-- Activity details tabs -->
    <string name="laps">Laps</string>
    <string name="lap_hr">HR</string>
    <!-- End activity details tabs -->

    <!-- User profile view -->
    <string name="userProfile">User Profile</string>
    <string name="publicActivities">Public activities</string>
    <string name="totalDistance">Total distance</string>
    <string name="profileDescription">Add description</string>
    <string name="profileDescriptionMaxCharacters">Maximum 256 characters</string>
    <string name="profileRealName">Name</string>
    <!-- End user profile view -->

    <!-- Find users -->
    <string name="minimum_3_characters">At least 3 characters are needed</string>
    <string name="no_matches_found">No matches found</string>
    <!-- End find users -->

    <!-- Network connection disabled -->
    <string name="network_disabled_enable">Cannot connect to Internet. Please check your connection settings and try again.</string>
    <!-- Short message for Snackbar -->
    <string name="network_cannot_connect">Unfortunately can\'t connect to Internet right now...</string>
    <!-- End network connection disabled -->

    <!-- Activity Loader -->
    <string name="no_workout_data">No activity data</string>
    <!-- End Activity Loader -->

    <!-- Activity Laps -->
    <string name="manual_lap_label">Manual</string>
    <string name="ascend_lap_label">Asc</string>
    <string name="descend_lap_label">Dsc</string>
    <string name="lap_number_label">#</string>
    <string name="ski_run_number_label">#</string>
    <string name="time_lap_label">Time</string>
    <string name="speed_lap_label">Speed %s</string>
    <string name="pace_lap_label">Pace %s</string>
    <!-- End Activity Laps -->

    <!-- Pictures (carousel and stand-alone) -->
    <string name="image_not_available">Image not available</string>
    <string name="open_workout">Open Activity</string>
    <string name="open_workout_error_title">Error</string>
    <string name="open_workout_error_message">Activity could not be loaded</string>
    <!-- End pictures (carousel and stand-alone) -->

    <!-- Notifications -->
    <!--
     Do not use plurals element as it's a grammatical choice and not quantity see:
     http://developer.android.com/guide/topics/resources/string-resource.html#Plurals
    -->
    <!-- %1 is "Name Surname" %2 is "Name Surname" and %3 is "activity_type distance" -->
    <string name="single_commenter_notification">%1$s commented %2$s\'s %3$s</string>
    <string name="two_commenter_notification">%1$s and %2$s commented %3$s\'s %4$s</string>
    <string name="multiple_commenter_notification">%1$s and %2$d others commented %3$s\'s %4$s</string>
    <string name="single_commenter_your_notification">%1$s commented your %2$s</string>
    <string name="two_commenter_your_notification">%1$s and %2$s commented your %3$s</string>
    <string name="multiple_commenter_your_notification">%1$s and %2$d others commented your %3$s</string>
    <string name="single_new_share">%1$s shared new activity</string>
    <string name="two_new_share">%1$s and %2$s shared new activities</string>
    <string name="multiple_new_share">%1$s and others shared %2$d new activities</string>
    <string name="facebook_friend_joined">%1$s just joined</string>
    <string name="friendship_accepted">%1$s has accepted your friend request</string>
    <string name="user_started_following">%1$s started following you</string>
    <string name="follow_req_notification">%1$s requests to follow you</string>
    <string name="follow_req_accepted">%1$s accepted your follow request</string>
    <string name="notifications_title">Notifications</string>
    <string name="marketing_inbox_title">News for you, from Suunto</string>
    <string name="marketing_inbox_delete_dialog_title">Delete?</string>
    <string name="notifications_feed_title">From followers</string>
    <string name="notifications_empty">No notifications</string>
    <string name="notifications_training_plan_updates_title">Great! Your plan is ready</string>
    <string name="notifications_training_plan_updates_content">We have successfully generated a new plan for you</string>
    <!-- End notifications -->

    <!-- App force update -->
    <string name="update">Update</string>
    <string name="later">Later</string>
    <string name="quit">Quit</string>
    <!-- End app force update -->

    <!-- Edit and add activities -->
    <string name="edit_workout">Edit activity</string>
    <string name="start_time">Start time</string>
    <string name="duration">Duration</string>
    <string name="activity_days">Days</string>
    <string name="distance">Distance</string>
    <string name="pace">Pace</string>
    <string name="avg_hr">Avg hr</string>
    <string name="avg_heart_rate">Avg heart rate</string>
    <string name="max_heart_rate">Max heart rate</string>
    <string name="energy">Calories</string>
    <string name="tap_to_select_photos">Tap to select photos</string>
    <string name="toolbar_add_manual_workout">Add activity manually</string>
    <string name="toolbar_record_workout">Record with app</string>
    <string name="download_fit_file">Download FIT file</string>
    <string name="download_json_file">Download JSON file</string>
    <!-- End edit and add activities -->

    <!-- Workout value descriptions -->
    <string name="item_description_v02max">VO2max might sound complicated… but it’s not! It just tells you how well your body can use oxygen when you’re exercising. So, the higher your VO2max number, the better you are at using oxygen when you’re working out. Woo!</string>
    <string name="item_description_epoc">If you want to know about intensity, think EPOC! The higher your EPOC number, the higher the intensity of your workout - and the higher the amount of energy you’ll spend recovering from your (presumably epic) exercise.</string>
    <string name="item_description_pte">Check out PTE when you want to know how big of an impact your exercise had on your overall aerobic fitness. Looking at the PTE scale is the easiest way to grasp why it’s useful:\n\n\t1–2: Improves basic endurance, builds a good foundation for progress.\n\n\t3–4: Doing this 1–2 times a week effectively improves aerobic fitness.\n\n\t5: You’re really exerting yourself and you shouldn’t do this often.</string>
    <string name="item_description_recovery">Recovery time pretty much does what it says: it gives you an estimation on how long you need to recover from your exercise based on stats like duration and intensity. Recovery is a key element of both your training and overall wellness. Taking time to rest when needed is as important as your activities, and will help prepare you for adventures to come.</string>
    <string name="item_description_swolf">SWOLF is a not-all-that-catchy way of saying “how efficiently you swim”. Basically, it measures how many strokes you need to cover a certain distance when you’re swimming. That’s why a lower SWOLF number means a more efficient swimming technique.</string>
    <string name="item_description_feeling">Talking about feelings is important! We ask you to rate how you were feeling (be honest 😊) after a workout to help you avoid overtraining and exhausting yourself.</string>
    <!-- Dive -->
    <string name="item_description_dive_time">Total dive time.</string>
    <!-- Dive -->
    <string name="item_description_total_time_underwater">Total time underwater.</string>
    <!-- Dive -->
    <string name="item_description_max_depth">Maximum depth reached during dive.</string>
    <!-- Dive -->
    <string name="item_description_bottom_temp">Temperature at the deepest point of the dive.</string>
    <!-- Dive -->
    <string name="item_description_avg_depth">Average depth during dive.</string>
    <!-- Dive -->
    <string name="item_description_dive_in_series">Dive number if multiple dives during one day.</string>
    <!-- Dive -->
    <string name="item_description_surface_time">Time spent at surface after a dive.</string>
    <!-- Dive -->
    <string name="item_description_dive_mode">Dive mode used during dive (Air, Nitrox, Trimix, CCR Nitrox, CCR Trimix, Gauge, Free).</string>
    <!-- Dive -->
    <string name="item_description_start_pressure">Tank start pressure at beginning of dive.</string>
    <!-- Dive -->
    <string name="item_description_algorithm">Dive computer algorithm used during dive.</string>
    <!-- Dive -->
    <string name="item_description_gradient_factors">Gradient factor used during dive. Note: this is only for Bühlmann algorithm.</string>
    <!-- Dive -->
    <string name="item_description_personal_settings">Use this five-step personal setting to adjust algorithm conservatism to fit your DCS susceptibility. +2 and +1 add safety margin to decompression calculations, -2 and -1 increase potential risk for DCS and must be used with precaution.</string>
    <!-- Dive -->
    <string name="item_description_air_consumption">The dive computer calculates your gas consumption based on your Tank POD’s pressure readings. If you have two Tank PODs connected to the same gas, they will show the same consumption value.</string>
    <!-- Dive -->
    <string name="item_description_altitude_settings">When diving above 300m you need to set the computer\'s altitude level in order to get more accurate decompression calculations. This setting automatically adjusts the decompression calculation according to the given altitude range: 0 – 300 m (0 – 980 ft) (default), 300 – 1500 m (980 – 4900 ft), 1500 – 3000 m (4900 – 9800 ft).</string>
    <!-- Dive -->
    <string name="item_description_gas_type">Gas used during dive.</string>
    <string name="item_description_link">Read more</string>
    <!-- TODO TSS specific descriptions -->
    <string name="item_title_np">Normalized Power® (NP®)</string>
    <string name="item_description_np">Normalized power® (NP®) is about power variations during your workout. It shows how hard you worked out, taking into account the variance between a steady workout and a fluctuating workout. A high NP® means the workout had a lot of variation and was harder than what a stat like “average power” might suggest.</string>
    <string name="item_title_ngp">Normalized Graded Pace™ (NGP™)</string>
    <string name="item_description_ngp">Normalized Graded Pace™ (NGP™) is a measurement of your adjusted pace, which shows the changes in grade and intensity of your workout. These changes reflect the physiological effect running in terrain or doing workout with high variations in pace has on you. NGP™ estimates what your pace would have been if it had been a steady run on flat terrain.</string>
    <string name="item_description_if">Description of Intensity Factor</string>
    <string name="item_title_tss">TSS</string>
    <string name="item_description_tss">Every activity gets a Training Stress Score® (TSS). It\'s based on the intensity and duration of your activity and it tells you how hard that particular activity was.\nTSS (r) - for running pace based calculation\nTSS (hr) - for heart rate based calculations\nTSS (p) - for power based calculations\nTSS (s) - for swimming pace based calculations\nTSS (MET) - for MET based calculations\nTSS (Manual) - after user has edited the value</string>
    <string name="item_title_co2e_saved">Calculating saved CO₂e emissions</string>
    <string name="item_description_co2e_saved">CO₂e demonstrates the global warming potential (GWP) of all seven greenhouse gases in one number. We calculate your emission reduction by comparing cycling, walking or running to driving your car.*</string>
    <string name="item_secondary_description_co2e_saved"><b>*About the calculation</b>\n\nCO₂e (Carbon Dioxide Equivalent) emissions of travel by car (average of a petrol/diesel powered car). Emission factor: 0.170652 kg CO₂e/km. CO₂e demonstrates the global warming potential (GWP) of all seven greenhouse gases: CO2, CH4, N2O, HFCs, PFCs, SF6 and NF3 in one number. Data source: Govt of UK, Dept. for Business, Energy &amp; Industrial Strategy, 2021</string>
    <string name="item_title_heart_rate_threshold_measurement">Heart rate threshold measurement</string>
    <string name="item_description_heart_rate_threshold_measurement">ZoneSense detects when you cross over the aerobic and anaerobic thresholds and maps them to your heart rate at those moments. This mapping is done during the first 60 minutes of your workout. The time limit’s purpose is to filter out the effects of fatigue from the sample set.\n\nThe threshold can only be located when it is crossed. So, to find out your thresholds, make sure you include high intensity effort in your workout.\n\nThese heart rate thresholds will vary between different sports, and even day to day depending on the environment, recovery status, and many other factors.</string>
    <string name="item_bottom_link_title_heart_rate_threshold_measurement">Learn more about HR zones</string>

    <string name="item_title_pace_threshold_measurement">Pace threshold measurement</string>
    <string name="item_description_pace_threshold_measurement"><![CDATA[ZoneSense detects when you cross over the aerobic and anaerobic thresholds and maps them to your pace at those moments. This mapping is done during the first 60 minutes of your workout. The time limit’s purpose is to filter out the effects of fatigue from the sample set.\n\nThe threshold can only be located when it is crossed. So, to find out your thresholds, make sure you include high intensity effort in your workout.\n\nThese pace thresholds will vary between different sports, and even day to day depending on the environment, recovery status, and many other factors.\n\nThe threshold can only be located when it is crossed. So, to find out your thresholds, make sure you include high intensity effort in your workout.\n\nThese heart rate thresholds will vary between different sports, and even day to day depending on the environment, recovery status, and many other factors.]]></string>
    <string name="item_bottom_link_title_pace_threshold_measurement">Learn more about pace zones</string>

    <string name="item_title_power_threshold_measurement">Power threshold measurement</string>
    <string name="item_description_power_threshold_measurement"><![CDATA[ZoneSense detects when you cross over the aerobic and anaerobic thresholds and maps them to your power at those moments. This mapping is done during the first 60 minutes of your workout. The time limit’s purpose is to filter out the effects of fatigue from the sample set.\n\nThe threshold can only be located when it is crossed. So, to find out your thresholds, make sure you include high intensity effort in your workout.\n\nThese power thresholds will vary between different sports, and even day to day depending on the environment, recovery status, and many other factors.]]></string>
    <string name="item_bottom_link_title_power_threshold_measurement">Learn more about power zones</string>

    <string name="aerobic_zone_data_available">No data available, please try again later.</string>

    <string name="powered_by_zone_sense">Powered by ZoneSense</string>

    <string name="item_title_breathing_rate">Breathing Rate</string>
    <string name="item_description_breathing_rate">Breathing Rate in Swimming refers to how often a swimmer takes a breath during a stroke cycle.\n\nIncorrect breathing frequency can disrupt stroke rhythm, reduce oxygen intake, and negatively impact endurance and performance.\n\nThe recommended breathing frequency varies based on swimming distance and intensity but is often every 2 to 4 strokes for most swimmers.</string>
    <string name="item_title_breaststroke_glide_time">Breaststroke glide time</string>
    <string name="item_description_breaststroke_glide_time">Breaststroke glide time refers to the period during the breaststroke swimming technique where the swimmer extends their body forward after the arm pull and leg kick, essentially gliding through the water before initiating the next stroke cycle.\n\nThis glide allows the swimmer to maintain forward momentum with minimal resistance, conserving energy and improving efficiency.\n\nA longer glide time indicates better technique and efficiency in the stroke, as the swimmer is able to maximize the distance covered with each stroke cycle.</string>
    <string name="item_title_freestyle_pith_angle">Head Angle in freestyle swimming</string>
    <string name="item_description_freestyle_pith_angle">Head Angle in freestyle swimming refers to the vertical tilt of the head relative to the body during the gliding phase of the swim.\n\nIncorrect head pitch angle can cause increased drag and disrupt body alignment, reducing swimming efficiency and speed.\n\nThe recommended head pitch angle typically ranges between 20° and 40° for optimal performance and minimal resistance.</string>
    <string name="item_title_breaststroke_average_angle">Average Breath Angle in breaststroke swimming</string>
    <string name="item_description_breaststroke_average_angle">Average Breath Angle in breaststroke swimming refers to the average upward tilt of the head to take a breath during the stroke.\n\nIncorrect breathing angle can cause excessive head lift, disrupt the balance between buoyant force and gravity, makes it harder to maintain the streamline posture.\n\nThe recommended breathing angle is typically between 50° and 70° to ensure proper technique and good swimming efficiency.</string>
    <string name="item_title_breaststroke_max_angle">Maximum Breath Angle in breaststroke swimming</string>
    <string name="item_description_breaststroke_max_angle">Maximum Breath Angle in breaststroke swimming refers to the largest upward tilt of the head to take a breath during the stroke.\n\nIncorrect breathing angle can cause excessive head lift, disrupt the balance between buoyant force and gravity, makes it harder to maintain the streamline posture.\n\nThe maximum breathing angle is typically lower than 80°, beyond which efficiency can be compromised.</string>
    <string name="item_title_freestyle_max_angle">Maximum Breath Angle in freestyle swimming</string>
    <string name="item_description_freestyle_max_angle">Maximum Breath Angle in freestyle swimming refers to the largest rotation of the head to the side to take a breath during the stroke.\n\nIncorrect breathing angle can cause imbalance, increase drag, and disrupt the swimmer\'s rhythm, reducing overall efficiency.\n\nThe maximum breathing angle is typically lower than 130° to ensure proper technique and streamline posture.</string>
    <string name="item_title_freestyle_average_angle">Average Breath Angle in freestyle swimming</string>
    <string name="item_description_freestyle_average_angle">Average Breath Angle in freestyle swimming refers to the average rotation of the head to the side to take a breath during the stroke.\n\nIncorrect breathing angle can cause imbalance, increase drag, and disrupt the swimmer\'s rhythm, reducing overall efficiency.\n\nThe recommended breathing angle typically ranges between 100° and 120° to ensure proper technique and streamline.</string>
    <string name="item_title_breaststroke_head_angle">Head angle in breaststroke swimming</string>
    <string name="item_description_breaststroke_head_angle">Head angle in breaststroke swimming refers to the vertical tilt of the head relative to the body during the gliding phase of the swim.\n\nIncorrect head pitch angle can cause increased drag and disrupt body alignment, reducing swimming efficiency and speed.\n\nThe recommended head pitch angle typically ranges between 20° and 40° for optimal performance and minimal resistance.</string>
    
    <!-- End workout value descriptions -->

    <!-- Rating -->
    <string name="rate_app_dialog_title">Rate Sports Tracker</string>
    <string name="rate_app_dialog_message">If you like Sports Tracker, would you mind taking a moment to rate it? It won\'t take more than a minute.\nThank you and happy tracking!</string>
    <!-- End rating -->

    <!-- Text to speech error -->
    <string name="tts_not_installed">Text-to-speech not installed. Would you like to install it to enable voice feedback?</string>
    <!-- End text to speech error -->

    <!-- Relative time span -->
    <string name="now">Just Now</string>
    <string name="today">Today</string>
    <string name="yesterday">Yesterday</string>
    <string name="last_year">Last Year</string>
    <string name="yesterday_at">Yesterday at %s</string>
    <string name="tomorrow">Tomorrow</string>
    <string name="tomorrow_at">Tomorrow at %s</string>

    <plurals name="seconds_ago">
        <item quantity="one">A second ago</item>
        <item quantity="few">%d seconds ago</item>
        <item quantity="many">%d seconds ago</item>
        <item quantity="other">%d seconds ago</item>
    </plurals>
    <plurals name="minutes_ago">
        <item quantity="one">A minute ago</item>
        <item quantity="few">%d minutes ago</item>
        <item quantity="many">%d minutes ago</item>
        <item quantity="other">%d minutes ago</item>
    </plurals>
    <plurals name="hours_ago">
        <item quantity="one">An hour ago</item>
        <item quantity="few">%d hours ago</item>
        <item quantity="many">%d hours ago</item>
        <item quantity="other">%d hours ago</item>
    </plurals>
    <plurals name="days_ago">
        <item quantity="one">%d day ago</item>
        <item quantity="few">%d days ago</item>
        <item quantity="many">%d days ago</item>
        <item quantity="other">%d days ago</item>
    </plurals>
    <plurals name="weeks_ago">
        <item quantity="one">A week ago</item>
        <item quantity="few">%d weeks ago</item>
        <item quantity="many">%d weeks ago</item>
        <item quantity="other">%d weeks ago</item>
    </plurals>
    <plurals name="months_ago">
        <item quantity="one">A month ago</item>
        <item quantity="few">%d months ago</item>
        <item quantity="many">%d months ago</item>
        <item quantity="other">%d months ago</item>
    </plurals>
    <plurals name="years_ago">
        <item quantity="one">A year ago</item>
        <item quantity="few">%d years ago</item>
        <item quantity="many">%d years ago</item>
        <item quantity="other">%d years ago</item>
    </plurals>
    <plurals name="in_seconds">
        <item quantity="one">In a second</item>
        <item quantity="few">In %d seconds</item>
        <item quantity="many">In %d seconds</item>
        <item quantity="other">In %d seconds</item>
    </plurals>
    <plurals name="in_minutes">
        <item quantity="one">In a minute</item>
        <item quantity="few">In %d minutes</item>
        <item quantity="many">In %d minutes</item>
        <item quantity="other">In %d minutes</item>
    </plurals>
    <plurals name="in_hours">
        <item quantity="one">In an hour</item>
        <item quantity="few">In %d hours</item>
        <item quantity="many">In %d hours</item>
        <item quantity="other">In %d hours</item>
    </plurals>
    <plurals name="in_days">
        <item quantity="one">In a day</item>
        <item quantity="few">In %d days</item>
        <item quantity="many">In %d days</item>
        <item quantity="other">In %d days</item>
    </plurals>
    <!-- End relative time span -->

    <!-- Actions -->
    <string name="undo">Undo</string>
    <string name="ok">OK</string>
    <string name="ok_got_it">Okay, got it!</string>
    <string name="got_it">Got it</string>
    <string name="cancel">Cancel</string>
    <string name="retry">Retry</string>
    <string name="try_again">Try again</string>
    <string name="refresh">Refresh</string>
    <string name="apply">Apply</string>
    <string name="need_help">Need help?</string>
    <string name="no_thanks">No Thanks</string>
    <string name="ask_later">Ask later</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="off">Off</string>
    <string name="on">On</string>
    <string name="turn_on">Turn on</string>
    <string name="allow">Allow</string>
    <string name="dont_allow">Don\'t allow</string>
    <string name="discard">Discard</string>
    <string name="back">Back</string>
    <string name="edit">Edit</string>
    <string name="continue_str">Continue</string>
    <string name="share">Share</string>
    <string name="share_photo">Share photo</string>
    <string name="share_image">Share image</string>
    <string name="tap_to_add_photo">Tap to add photos</string>
    <string name="swipe_left_to_add_photos">Swipe left to add photos</string>
    <string name="share_activity">Share link to activity</string>
    <string name="share_no_public_activity_title">This activity is not public</string>
    <string name="share_no_public_activity_question">Do you want to change it to public and share?</string>
    <string name="learn_more">Learn more</string>
    <string name="select_data">Select data</string>
    <string name="resize">Resize</string>
    <string name="select_graph">Select graph</string>
    <string name="not_now">Not now</string>
    <string name="skip_str">Skip</string>
    <string name="remove_follower">Remove follower</string>
    <string name="block">Block</string>
    <string name="unBlock">Unblock</string>
    <string name="important">Important!</string>
    <string name="enable">Enable</string>
    <string name="add">Add</string>
    <string name="link">Link</string>
    <string name="set_up">Set up</string>
    <!-- Actions -->

    <!-- Misc -->
    <string name="suitable_for">Suitable for</string>
    <!-- Misc -->

    <!-- Other for later use -->
    <string name="add_workout">Add activity</string>
    <!-- latest_replies should be split into two and refactored for plurals -->
    <string name="heart_rate">Heart rate</string>
    <!-- End other for later use -->

    <!-- Activity Diary Summaries -->
    <string name="this_week">This week</string>
    <string name="last_week">Last week</string>
    <string name="weekly">Weekly</string>
    <string name="monthly">Monthly</string>
    <string name="this_month">This month</string>
    <string name="last_30_days">Last 30 days</string>
    <string name="last_month">Last month</string>
    <string name="list">List</string>

    <!-- Body resource insights -->
    <string name="awake_poor_high_stress">You slept fairly well last night, but your body resources are poor. This is due to a stressful day yesterday.</string>
    <string name="awake_poor_hard_exercise">You slept fairly well last night, but your body resources are poor. This is because of hard exercise yesterday.</string>
    <string name="awake_poor_late_exercise">You slept fairly well last night, but your body resources are poor. Exercising close to bedtime is likely the cause.</string>
    <string name="awake_poor_late_bedtime">You slept fairly well last night, but your body resources are poor. A late bedtime is probably why.</string>
    <string name="awake_poor_acclimatization">You slept fairly well last night, but your body resources are poor. Adapting to higher altitude slows recovery.</string>
    <string name="awake_poor_other">You slept well, but your body resource situation is only fair due to recent straining.</string>
    <string name="awake_poor_bad_sleep">You didn\'t sleep very well. Your body resources are poor as a result.</string>
    <string name="awake_fair_high_stress">You slept well, but your body resource situation is only fair. Your high stress yesterday may be the reason.</string>
    <string name="awake_fair_hard_exercise">You slept well, but your body resource situation is only fair. Your hard exercise yesterday may be the reason.</string>
    <string name="awake_fair_late_exercise">You slept well, but your body resource situation is only fair. Your exercise too close to bedtime may be the reason.</string>
    <string name="awake_fair_late_bedtime">You slept well, but your body resource situation is only fair. Your late bedtime may be the reason.</string>
    <string name="awake_fair_acclimatization">You slept well, but your body resource situation is only fair. Adapting to higher altitude slows recovery.</string>
    <string name="awake_fair_other">You slept well, but your body resource situation is only fair due to recent straining.</string>
    <string name="awake_fair_bad_sleep">Your body resource situation is only fair, because you didn\'t sleep very well.</string>
    <string name="awake_good">You slept well last night. You should be ready for today’s activities.</string>
    <string name="awake_good_bad_sleep">Last night’s sleep was not optimal but you recovered enough for today’s activities.</string>
    <string name="awake_great">You slept well last night and your body resource situation is great. You should be primed for training and today’s activities.</string>
    <string name="awake_great_bad_sleep">Last night’s sleep was not optimal but you recovered well enough for training and today’s activities.</string>
    <string name="awake_full">You are charged up and ready to go. Look forward to being at your best today!</string>

    <!-- Sleep summaries -->
    <string name="sleep_card_quality">Quality</string>
    <string name="sleep_card_quality_excellent">Excellent</string>
    <string name="sleep_card_quality_good">Good</string>
    <string name="sleep_card_quality_moderate">Moderate</string>
    <string name="sleep_card_quality_poor">Poor</string>
    <string name="sleep_summary_text_1">Your sleep quality was very good! You might have more energy than usual to tackle the daily challenges!</string>
    <string name="sleep_summary_text_2">Your sleep quality has been very good for many days now. This is good news for your long-term well-being.</string>
    <string name="sleep_summary_text_3">You slept long and the sleep quality was good enough to recover you from daytime stress. You\'ve earned a head start for your day!</string>
    <string name="sleep_summary_text_4">You slept slightly less than recommended but the sleep quality was good enough to recover you from daytime stress. You are ready for a new day!</string>
    <string name="sleep_summary_text_5">You slept shortly but the sleep quality was still good enough to recover you from daytime stress. You are ready for daily challenges.</string>
    <string name="sleep_summary_text_6">You slept long and the sleep quality was moderate. Calming down in the evening helps achieving good sleep quality.</string>
    <string name="sleep_summary_text_7">You slept slightly less than recommended and the sleep quality was moderate. Optimal sleeping environment helps achieving good sleep quality.</string>
    <string name="sleep_summary_text_8">You slept shortly and the sleep quality was moderate. Regular sleeping schedule helps getting better sleep quality.</string>
    <string name="sleep_summary_text_9">Your sleep quality has been poor for many days now. In the long run this can wear you out.</string>
    <string name="sleep_summary_text_10">You slept long but the sleep quality was poor. Remember to calm down in the evening to get better sleep quality.</string>
    <string name="sleep_summary_text_11">You slept slightly less than recommended and the sleep quality was poor. Pay attention to your sleeping environment to get better sleep quality.</string>
    <string name="sleep_summary_text_12">You slept shortly and the sleep quality was poor. Try to give your sleep more time next night.</string>
    <string name="sleep_data_warning_label">This data is only measured from your regular sleep.</string>

    <!--
    Month abbreviated to one letter (or shortest possible) presentation
    -->
    <string name="january_abbreviated_shortest">J</string>
    <string name="february_abbreviated_shortest">F</string>
    <string name="march_abbreviated_shortest">M</string>
    <string name="april_abbreviated_shortest">A</string>
    <string name="may_abbreviated_shortest">M</string>
    <string name="june_abbreviated_shortest">J</string>
    <string name="july_abbreviated_shortest">J</string>
    <string name="august_abbreviated_shortest">A</string>
    <string name="september_abbreviated_shortest">S</string>
    <string name="october_abbreviated_shortest">O</string>
    <string name="november_abbreviated_shortest">N</string>
    <string name="december_abbreviated_shortest">D</string>

    <!--
    Date range.
    In English: %1$s - %2$s (Mar 31 - Apr 06),
    In English: %1$s - %2$s (Dec 30, 2024 - Jan 5, 2025),
    In English: %1$s - %2$s (Jan - Jun),
    In English: %1$s - %2$s (Jul 2024 - Dec 2024),
    In English: %1$s - %2$s (2018 - 2025),
    -->
    <string name="various_date_range">%1$s - %2$s</string>
    <!--
    Abbreviated format for dates with year and month.
    In English: %2$s %1$d (Jan 2025)
    -->
    <string name="year_abbreviated_month">%2$s %1$d</string>
    <!--
    Abbreviated format for dates with year, month, and date.
    In English: %2$s %3$d, %1$d (Jan 1, 2025)
    -->
    <string name="year_abbreviated_month_date">%2$s %3$d, %1$d</string>
    <!--
    Abbreviated format for date range with month and date.
    In English: %1$s %2$d-%3$d (Jan 1-8)
    -->
    <string name="abbreviated_month_date_range">%1$s %2$d - %3$d</string>
    <!--
    Abbreviated format for date range with month and date.
    In English: %2$s %3$d-%4$d, %1$d (Jan 1-8, 2025)
    -->
    <string name="year_abbreviated_month_date_range">%2$s %3$d - %4$d, %1$d</string>
    <!--
    Abbreviated format for date range with month and date.
    In English: %2$s %3$d-%4$s %5$d, %1$d (Jan 1-Feb 8, 2025)
    -->
    <string name="year_different_abbreviated_month_date_range">%2$s %3$d - %4$s %5$d, %1$d</string>
    <!--
    Abbreviated format for dates with days and month.
    In English: %1$s %2$d (Jan 01)
    In Finnish: %2$d. %1$s (01. Tam)
    -->
    <string name="abbreviated_months_dates">%1$s %2$d</string>
    <string name="totals">Totals</string>
    <string name="january_abbreviated">Jan</string>
    <string name="february_abbreviated">Feb</string>
    <string name="march_abbreviated">Mar</string>
    <string name="april_abbreviated">Apr</string>
    <string name="may_abbreviated">May</string>
    <string name="june_abbreviated">Jun</string>
    <string name="july_abbreviated">Jul</string>
    <string name="august_abbreviated">Aug</string>
    <string name="september_abbreviated">Sep</string>
    <string name="october_abbreviated">Oct</string>
    <string name="november_abbreviated">Nov</string>
    <string name="december_abbreviated">Dec</string>
    <!-- used in diary, the search-element possibilities -->
    <string name="filterTextHint">Description, tag, activity, month or year</string>
    <!-- End Activity Diary Summaries -->

    <!-- Similar activities -->
    <string name="similar_workouts">Ranking</string>
    <string name="sort_by_date">Sort by Date</string>
    <string name="sort_by_duration">Sort by Duration</string>
    <string name="fastest_by">Your fastest by %1$s</string>
    <!-- Meaning: you achieved best time ever, which is xx seconds faster that the second best -->
    <string name="from_your_best">%1$s from your best</string>
    <string name="compare_previous">Compare with your previous</string>
    <string name="compare_fastest">Compare with your fastest</string>
    <string name="compare_second_fastest">Compare with your 2nd fastest</string>
    <string name="compare">Compare</string>
    <string name="compare_current_workout">This workout</string>
    <string name="compare_other_workout">Comparison</string>
    <string name="compare_workout_date_template">%s vs %s</string>
    <string name="compare_difference">Difference</string>
    <!-- End similar activities -->

    <!-- Goal texts -->
    <string name="goal_title_weekly">Weekly Goal</string>
    <string name="goal_title_monthly">Monthly Goal</string>
    <string name="goal_title_duration">Duration Goal</string>
    <string name="goal_title_distance">Distance Goal</string>
    <string name="goal_title_numberofworkouts">Activities Goal</string>
    <string name="goal_title_energy">Energy Goal</string>
    <string name="reached_goal">Reached!</string>
    <string name="ended_goal">Ended</string>
    <!--
    meaning the goal will end on date x.x.xxxx
    Abbreviated format for dates with days (%1$s), month (%2$d), and year (%3$d).
    In English: %1$s %2$d %3$d (Jan 01 2014)
    In Finnish: %2$d. %1$s %3$d (01. Tam 2014)
    -->
    <!-- page title -->
    <string name="title_goal">Goal</string>
    <string name="goal_period">Time Period</string>
    <string name="goal_period_start">Starts</string>
    <string name="goal_period_start_from">Starting from</string>
    <string name="goal_period_end">Ends</string>
    <string name="goal_this_week">This week</string>
    <string name="goal_this_month">This month</string>
    <!-- options for this are distance, duration. etc -->
    <string name="goal_type">Goal Type</string>
    <string name="goal_activities">Activities</string>
    <string name="goal_period_custom">From - To</string>
    <string name="goal_type_amounts">Number of Activities</string>
    <!-- meaning: number of activities -->
    <string name="goal_all_activities">All</string>
    <plurals name="goal_workouts">
        <item quantity="one">%1$d Activity</item>
        <item quantity="few">%1$d Activities</item>
        <item quantity="many">%1$d Activities</item>
        <item quantity="other">%1$d Activities</item>
    </plurals>
    <!-- meaning: goal progress -->
    <string name="goal_summary_progress">Progress</string>
    <plurals name="goal_summary_activities_count">
        <item quantity="one">%d Activity</item>
        <item quantity="few">%d Activities</item>
        <item quantity="many">%d Activities</item>
        <item quantity="other">%d Activities</item>
    </plurals>
    <string name="goal_summary_all_activities">All Activities</string>
    <!-- meaning: goal achieved during those months -->
    <string name="goal_summary_success_month">Success\nMonths</string>
    <!-- meaning: goal achieved during those weeks -->
    <string name="goal_summary_success_week">Success\nWeeks</string>
    <!-- title, meaning: how long is your average activity -->
    <string name="goal_summary_average_workout">Average\nActivity</string>
    <string name="goal_summary_monthly_average">Monthly\nAverage</string>
    <string name="goal_summary_weekly_average">Weekly\nAverage</string>
    <string name="goal_summary_monthly_best">Best\nMonth</string>
    <string name="goal_summary_weekly_best">Best\nWeek</string>
    <!-- meaning: achieved distance, duration etc. -->
    <string name="goal_summary_total">Total achieved</string>
    <!-- meaning: winning streak; successful weeks in a row -->
    <string name="goal_summary_current_streak">Current Streak</string>
    <!-- meaning: longest continuous winning streak; successful weeks in a row -->
    <string name="goal_summary_longest_streak">Longest Streak</string>
    <string name="cancel_confirm">Discard changes?</string>
    <string name="goal_summary_earlier">%d - Earlier history</string>
    <string name="goal_summary_show_earlier">Show earlier history</string>
    <!-- End goal texts -->

    <!-- Follow activity -->
    <string name="follow_route">Follow route</string>
    <string name="follow_workout_explanation_text">Route of the selected activity will be shown on the map.</string>
    <string name="start_following_route">Start following route</string>
    <!-- End follow activity -->

    <!-- Ghost Target -->
    <string name="start_as_ghost_target">Start as ghost target</string>
    <string name="ghost_target">Ghost target</string>
    <string name="ghost_distance_time_capital">TIME/DISTANCE</string>
    <string name="ghost_ahead">AHEAD</string>
    <string name="ghost_behind">BEHIND</string>
    <string name="ghost_off_route">OFF ROUTE</string>
    <string name="ghost_explanation_text">Race against any activity on the same route. See real-time comparison data.</string>
    <!-- End Ghost Target -->

    <!-- What's new -->
    <string name="title_whats_new">What\'s New</string>
    <!-- End What's new -->

    <!-- Cadence -->
    <string name="settings_cadence_title">Speed and Cadence Sensor</string>
    <string name="settings_cadence_summary">Setup your Sports Tracker sensor</string>
    <string name="settings_cadence_not_supported_summary">Not compatible on this device</string>
    <string name="settings_cadence_connect">Connect your sensor</string>
    <string name="settings_cadence_sensor_settings">Sensor settings</string>
    <string name="settings_cadence_wheel_circumference_title">Wheel circumference</string>
    <string name="settings_cadence_wheel_circumference_summary">%s mm</string>
    <string name="settings_cadence_data_source_title">Speed and distance</string>
    <string name="settings_cadence_data_source_summary">Use data from %s</string>
    <string name="settings_cadence_data_source_cadence">Sensor</string>
    <string name="settings_cadence_data_source_phone">GPS</string>
    <string name="cadence_setup_instruction">Spin the crank or wheel to activate the sensor and press Connect.</string>
    <string name="cadence_setup_connect">Connect</string>
    <string name="cadence_setup_connecting">Connecting...</string>
    <string name="cadence_setup_not_found_title">NO SENSOR FOUND</string>
    <string name="cadence_setup_not_found_text">Check that both sensor and magnets are installed properly and try again.</string>
    <string name="cadence_display_title">Connected!\nYour current cadence:</string>
    <string name="cadence_display_text1">Your Speed and Cadence Sensor is now connected and ready for use.</string>
    <string name="cadence_display_text2">From now on it will be connected automatically as you start a activity with Sports Tracker and start cycling.</string>
    <string name="avg_cadence_capital">AVG CADENCE</string>
    <string name="workout_values_headline_avg_cadence">Cadence</string>
    <string name="max_cadence_capital">MAX CADENCE</string>
    <string name="avg_cadence">Avg cadence</string>
    <string name="rpm">rpm</string>
    <string name="order_now">Buy online</string>
    <!-- End cadence -->

    <!-- Map -->
    <string name="map_type_title">Map style</string>
    <string name="map_type_normal">Normal</string>
    <string name="map_type_satellite">Satellite</string>
    <string name="map_type_maanmittauslaitos_finland_terrain">Finland</string>
    <string name="map_type_terrain">Outdoor</string>
    <string name="map_type_hybrid">Hybrid</string>
    <string name="map_type_dark">Dark</string>
    <string name="map_type_light">Light</string>
    <string name="map_type_ski">Winter</string>
    <string name="map_type_avalanche">Avalanche, terrain</string>
    <string name="shop_osm_title">Global Outdoor Maps!</string>
    <string name="shop_osm_body">Enjoy detailed foot and bike paths with four new maps: OpenStreetMap standard, landscape, outdoors and cycling (OpenCycleMap).</string>
    <string name="map_selection_more">More Maps</string>
    <string name="heatmap_title">Heatmap</string>
    <string name="heatmap_subtitle">Display frequently used routes on the map</string>
    <string name="heatmap_none">None</string>
    <string name="road_surface_selection_title">Road surface</string>
    <string name="road_surface_none">None</string>
    <string name="this_year">This year</string>
    <string name="custom_dates">Custom</string>
    <string name="date_range_title">SELECTED RANGE</string>
    <string name="poi_toggle_title">My POIs</string>
    <string name="poi_toggle_subtitle">Display saved points of interest on the map</string>
    <string name="popular_routes_title">Popular routes</string>
    <string name="popular_routes_subtitle">Display popular routes on the map</string>
    <string name="popular_routes_none">None</string>
    <!-- End Map -->

    <!-- Road surface -->
    <string name="road_surface_hide_cycling_forbidden">Hide cycling forbidden roads</string>
    <string name="road_surface_option_paved">Paved</string>
    <string name="road_surface_option_smooth_unpaved">Smooth unpaved</string>
    <string name="road_surface_option_rough_unpaved">Rough unpaved</string>
    <plurals name="road_surface_option_multiple">
        <item quantity="one">1 road surface</item>
        <item quantity="few">%d road surfaces</item>
        <item quantity="many">%d road surfaces</item>
        <item quantity="other">%d road surfaces</item>
    </plurals>
    <string name="road_surface_paved_info">Paved roads include roads with asphalt, concrete and chipseal. These roads are particularly good for road cycling and roller skiing.</string>
    <string name="road_surface_smooth_unpaved_info">Smooth unpaved roads include roads with gravel and dirt. These roads are particularly good for gravel cycling.</string>
    <string name="road_surface_rough_unpaved_info">Rough unpaved roads include roads with ground, grass, mud, and rock. These roads are particularly good for trail running and mountain biking.</string>
    <string name="road_surface_about_title">About</string>
    <string name="road_surface_about_body"><![CDATA[\"Road surface\" is a beta feature that Suunto will improve based on feedback. The road surface data comes from <a href="https://openstreetmap.org/">OpenStreetMap</a> and evolves as the community contributes to it. When planning your adventures, keep in mind that the road conditions might change and the data is incomplete.]]></string>

    <string name="road_surface_acrylic">acrylic</string>
    <string name="road_surface_artificial_turf">artificial turf</string>
    <string name="road_surface_asphalt">asphalt</string>
    <string name="road_surface_carpet">carpet</string>
    <string name="road_surface_chipseal">chipseal</string>
    <string name="road_surface_cobblestone">cobblestone</string>
    <string name="road_surface_concrete">concrete</string>
    <string name="road_surface_glass">glass</string>
    <string name="road_surface_metal">metal</string>
    <string name="road_surface_metal_grid">metal grid</string>
    <string name="road_surface_paving_stones">paving stones</string>
    <string name="road_surface_rubber">rubber</string>
    <string name="road_surface_tartan">tartan</string>
    <string name="road_surface_compacted">compacted</string>
    <string name="road_surface_dirt">dirt</string>
    <string name="road_surface_fine_gravel">fine gravel</string>
    <string name="road_surface_grass">grass</string>
    <string name="road_surface_grass_paver">grass paver</string>
    <string name="road_surface_gravel">gravel</string>
    <string name="road_surface_ground">ground</string>
    <string name="road_surface_mud">mud</string>
    <string name="road_surface_pebblestone">pebblestone</string>
    <string name="road_surface_rock">rock</string>
    <string name="road_surface_salt">salt</string>
    <string name="road_surface_sand">sand</string>
    <string name="road_surface_stepping_stones">stepping stones</string>
    <string name="road_surface_unpaved">unpaved</string>
    <string name="road_surface_water">water</string>
    <string name="road_surface_winter_road">winter road</string>
    <string name="road_surface_wood">wood</string>
    <string name="road_surface_woodchips">woodchips</string>
    <!-- End road surface -->

    <!-- Material date picker
        Material date picker does not yet have localizations in place for languages needed so
        we'll override them with our own.
    -->
    <string name="mtrl_picker_range_header_unselected" tools:override="true">Start date - End date</string>
    <string name="mtrl_picker_range_header_only_start_selected" tools:override="true">%s - End date</string>
    <string name="mtrl_picker_range_header_only_end_selected" tools:override="true">Start date - %s</string>
    <string name="mtrl_picker_text_input_date_range_start_hint" tools:override="true">Start date</string>
    <string name="mtrl_picker_text_input_date_range_end_hint" tools:override="true">End date</string>
    <string name="mtrl_picker_invalid_format" tools:override="true">Invalid format.</string>
    <string name="mtrl_picker_invalid_format_use" tools:override="true">Use: %1$s</string>
    <string name="mtrl_picker_invalid_format_example" tools:override="true">Example: %1$s</string>
    <string name="mtrl_picker_out_of_range" tools:override="true">Out of range: %1$s</string>
    <string name="mtrl_picker_invalid_range" tools:override="true">Invalid range.</string>
    <string name="mtrl_picker_text_input_year_abbr" tools:override="true">y</string>
    <string name="mtrl_picker_text_input_month_abbr" tools:override="true">m</string>
    <string name="mtrl_picker_text_input_day_abbr" tools:override="true">d</string>
    <!-- End Material date picker -->

    <!-- Route -->
    <string name="import_route">Import route (GPX, KML)</string>
    <string name="create_route">Create route</string>
    <string name="save_route">Save route</string>
    <string name="no_information_for_route_altitude_graph">No elevation profile</string>
    <string name="export_gpx_workout">Export GPX workout</string>
    <string name="export_gpx_route">Export GPX route</string>
    <string name="export_kml_route">Export KML route</string>
    <!-- End route -->

    <!-- Barometer / Altitude origin -->
    <string name="settings_altitude_source">Altitude source</string>
    <string name="settings_screen_altitude_use_barometer">Barometer</string>
    <string name="settings_screen_altitude_use_gps">GPS</string>
    <string name="settings_altitude_not_supported_summary">This device does not have
        barometer sensor</string>
    <!-- End Barometer / Altitude origin -->

    <!-- Step Counter -->
    <string name="step_count">Steps</string>
    <string name="step_count_capital">STEPS</string>
    <string name="workout_values_headline_step_count">Steps</string>
    <string name="step_rate_capital">STEP RATE</string>
    <string name="workout_values_headline_step_rate">Step rate</string>
    <string name="workout_values_headline_step_cadence">Step cadence</string>
    <string name="workout_values_headline_max_step_cadence">Max step cadence</string>
    <string name="step_per_minute">spm</string>
    <!-- End Step Counter -->

    <!-- Training Stress Score / TSS -->
    <string name="workout_values_headline_tss">TSS</string>
    <string name="workout_values_headline_tss_pace">TSS (r)</string>
    <string name="workout_values_headline_tss_hr">TSS (hr)</string>
    <string name="workout_values_headline_tss_power">TSS (p)</string>
    <string name="workout_values_headline_tss_swim_pace">TSS (s)</string>
    <string name="workout_values_headline_tss_met">TSS (MET)</string>
    <string name="workout_values_headline_tss_zonesense">TSS (ZoneSense)</string>
    <string name="workout_values_headline_np">NP®</string>
    <string name="workout_values_headline_ngp">NGP™</string>
    <string name="tss_calculation_method_selector_manual">Manual</string>
    <string name="use_selected_tss_calculation_method_title">Do you want to use selected TSS calculation method in</string>
    <string name="use_selected_tss_calculation_method_current_only">This exercise only?</string>
    <string name="use_selected_tss_calculation_method_future_for_type">All future exercises in this sport?</string>
    <!-- End Training Stress Score / TSS-->

    <!-- Customizable Widgets -->
    <string name="customizable_widgets_tutorial">Tap and hold any data field to change it.</string>
    <string name="customizable_widgets_tip">Note that changes are saved for each activity type separately.</string>
    <string name="lap_speed_pace_capital">LAP SPEED/PACE</string>
    <string name="avg_speed_pace_capital">AVG SPEED/PACE</string>
    <!-- End Customizable Widgets -->

    <!-- New Log-in UI -->
    <string name="sign_up">Sign Up</string>
    <string name="forgot_your_password">Forgot password?</string>
    <string name="login_or">or</string>
    <string name="signup_title">Sign up with email</string>
    <string name="signup_fb_button">Sign up with Facebook</string>
    <string name="signup_email_already_in_use">Email already exists. If you have previously created an account for Sports Tracker or Suunto app with this email, you can use this account to log in.</string>
    <string name="welcome_to">Welcome to</string>
    <string name="tos_and_privacy_links"><![CDATA[<a href="https://community.sports-tracker.com/service-terms">Terms of Service</a> and <a href="https://community.sports-tracker.com/privacy-policy">Privacy Policy</a>]]></string>
    <string name="tos_and_privacy_links_accept">I accept the <![CDATA[<a href="https://community.sports-tracker.com/service-terms">Terms of Service</a> and <a href="https://community.sports-tracker.com/privacy-policy">Privacy Policy</a>]]></string>
    <!-- End New Log-in UI -->

    <!-- Voice Feedback Enhancement -->
    <string name="voice_feedback">Audio feedback</string>
    <string name="automatic_laps">Automatic Laps</string>
    <string name="lap_interval">Lap intervals</string>
    <string name="lap_interval_description">As a default all feedback is spoken at the end of lap.</string>
    <string name="lap_speed_pace">Lap speed / pace</string>
    <string name="current_speed_pace">Current speed / pace</string>
    <string name="avg_speed_pace">Avg speed / pace</string>
    <string name="current_heart_rate">Current heart rate</string>
    <string name="speak">Speak</string>
    <string name="speak_on_every">on every...</string>
    <string name="speak_on_lap">on every lap</string>
    <string name="never">Never</string>
    <string name="apply_to_all">Apply to all activities</string>
    <string name="lap_time">Lap time</string>
    <string name="current_cadence">Current cadence</string>
    <string name="content_speak_on_every">Speak on every...</string>
    <!-- End Voice Feedback Enhancement -->

    <!-- Free trial period -->
    <string name="free_trial_title">Try for %d days!</string>
    <string name="free_trial_price">FREE TRIAL, 0.00€</string>
    <string name="free_trial_cancel_info">You can cancel the subscription at any time during the trial period.</string>
    <string name="free_trial_continue">CONTINUES AS:</string>
    <string name="free_trial_choose_plan">CHOOSE SUBSCRIPTION:</string>
    <string name="free_trial_cheaper" formatted="false">40% cheaper than monthly!</string>
    <string name="subscription_auto_renew">Subscription renews automatically at the end of the period.</string>
    <string name="free_trial_login_required">The use of Premium features requires a Sports Tracker account.</string>
    <string name="start_free_trial_capital">START FREE TRIAL</string>
    <string name="start_free_trial">Start free trial</string>
    <!-- End Free trial period -->

    <!-- Facebook Friends -->
    <string name="friends_on_sports_tracker">Facebook Friends</string>
    <!-- End Facebook Friends -->

    <!-- Tool Tips -->
    <string name="tool_tip_change_map_type">Try the new map styles!</string>
    <string name="tool_tip_custom_voice_feedback">Customize voice feedback to get the most out of your training.</string>
    <string name="tool_tip_compare_workout">Here you will see automatic route and distance rankings. Tap for more analysis.</string>
    <string name="tool_tip_plan_route">You can plan your routes here</string>
    <string name="tool_tip_select_activity">Here you can select which activity you\'re up to!</string>
    <!-- End Tool Tips -->

    <!-- Notification Settings -->
    <string name="settings_push_notifications">Push notifications</string>
    <string name="settings_push_notifications_receive">Receive push notifications</string>
    <string name="settings_push_notifications_receive_summary">Stay informed!</string>
    <string name="settings_push_notifications_activities">Activities</string>
    <string name="settings_push_notifications_activity_comments">Comments</string>
    <string name="settings_push_notifications_activity_likes">Likes</string>
    <string name="settings_push_notifications_activity_new_followers">New followers</string>
    <string name="settings_push_notifications_activity_followers_activities">New activities from followers</string>
    <string name="settings_push_notifications_reminders">Reminders</string>
    <string name="settings_push_notifications_reminder_new_activity_synced">New activity synced</string>
    <string name="email_notifications">Email Notifications</string>
    <string name="notification_facebook_friend_joined">Facebook friend joined</string>
    <!-- End Notification Settings -->

    <!-- Tags Settings -->
    <string name="settings_tags">Tags</string>
    <!-- End Tags Settings -->

    <!-- Privacy settings-->
    <string name="settings_privacy">Privacy</string>
    <string name="settings_follow_approval">Follower approval</string>
    <string name="settings_follow_approval_ask">Ask me to approve new followers</string>
    <string name="settings_follow_approval_ask_summmary">If you have this setting on, you will need to approve new followers. The people you already follow will be able to follow you back without approval.</string>
    <string name="settings_private_account">If this setting is enabled, your friends will not be able to search for you by searching for users and will not recommend you to other users.</string>
    <string name="settings_private_account_title">Private account</string>

    <string name="privacy_default">Default Privacy</string>
    <string name="privacy_public_summary">Contribute to the community by letting everyone see your activities.</string>
    <string name="privacy_followers_summary">Only followers can see and comment on your activities.</string>
    <string name="privacy_private_summary">Only you can see your activities.</string>
    <string name="privacy_last_used">Remember last used</string>
    <string name="privacy_last_used_summary">The privacy setting used for latest workout will apply for all future workouts.</string>

    <string name="please_wait">Please wait...</string>

    <!-- Select Activity Target -->
    <string name="workout_target">Activity target</string>
    <!-- End Select Activity Target -->

    <!-- Ahead / Behind Graph -->
    <string name="target_capital">TARGET</string>
    <string name="current_capital">CURRENT</string>
    <string name="target">Target</string>
    <string name="current">Current</string>
    <string name="speed_avg_capital">SPEED (AVG)</string>
    <string name="hr_avg_capital">HR (AVG)</string>
    <string name="ab_ahead"><xliff:g example="01:57" id="ahead_value">%1$s</xliff:g> AHEAD</string>
    <string name="ab_behind"><xliff:g example="01:57" id="behind_value">%1$s</xliff:g> BEHIND</string>
    <!-- End Ahead / Behind Graph -->

    <!-- Trend View -->
    <string name="days_summary">%1$d-Day Summary</string>
    <string name="faster_than_previous">Faster than\nprevious</string>
    <string name="slower_than_previous">Slower than\nprevious</string>
    <string name="more_than_previous">More than\nprevious</string>
    <string name="less_than_previous">Less than\nprevious</string>
    <string name="longer_than_previous">Longer than\nprevious</string>
    <string name="shorter_than_previous">Shorter than\nprevious</string>
    <string name="show_all">Show All</string>
    <string name="previous_capital">Previous</string>
    <string name="on_this_route_capital">Previous on this route</string>
    <string name="on_all_route_capital">Previous on any route</string>
    <string name="previous_on_all_route_capital">Previous on any route</string>
    <!-- End Trend View -->

    <!-- Full Screen Images -->
    <string name="delete_workout_image_confirmation">Delete this image?</string>
    <string name="delete_workout_video_confirmation">Delete this video?</string>
    <string name="workout_image_deleted">Image deleted.</string>
    <string name="workout_video_deleted">Video deleted.</string>
    <string name="workout_image_delete_failed">Failed to delete the image.</string>
    <string name="workout_video_delete_failed">Failed to delete the video.</string>
    <string name="workout_image_add_failed">Failed to add the image.</string>
    <string name="workout_video_add_failed">Failed to add the video.</string>
    <string name="no_workout_image">No images available.</string>
    <string name="workout_media_updated">Media updated.</string>
    <string name="workout_media_updated_failed">Failed to update the media.</string>
    <!-- End Full Screen Images -->

    <!-- Feed -->
    <plurals name="view_all_x_comments">
        <item quantity="one">View %1$d comment</item>
        <item quantity="few">View all %1$d comments</item>
        <item quantity="many">View all %1$d comments</item>
        <item quantity="other">View all %1$d comments</item>
    </plurals>
    <!-- End Feed -->

    <!-- Dashboard -->
    <string name="home_dashboard">Dashboard</string>
    <string name="home_dashboard_widgets_title">Widgets</string>
    <string name="home_dashboard_widgets_progress_ctl_abbrev" translatable="false">CTL</string>
    <string name="home_dashboard_widgets_progress_atl_abbrev" translatable="false">ATL</string>
    <string name="home_dashboard_widgets_progress_tsb_abbrev" translatable="false">TSB</string>
    <string name="home_dashboard_customize_dashboard">Customize dashboard</string>
    <string name="home_activities">Activities</string>
    <string name="home_dashboard_editor_title">Customize</string>
    <string name="home_dashboard_editor_subtitle">Add, remove and reorder items</string>
    <string name="home_dashboard_editor_done">Done</string>
    <string name="home_dashboard_editor_widgets_title">Widgets</string>
    <string name="home_dashboard_editor_widgets_subtitle">Drag and drop to rearrange</string>
    <string name="home_dashboard_editor_add_widget_button">Add widget</string>

    <plurals name="last_x_weeks">
        <item quantity="one">Latest %1$d week</item>
        <item quantity="few">Latest %1$d weeks</item>
        <item quantity="many">Latest %1$d weeks</item>
        <item quantity="other">Latest %1$d weeks</item>
    </plurals>
    <string name="sunset_in">Sunset in <xliff:g example="05:39:27" id="time">%1$s</xliff:g></string>
    <string name="sunrise_in">Sunrise in <xliff:g example="05:39:27" id="time">%1$s</xliff:g></string>
    <string name="tool_tip_goal_new_user">We set a weekly goal for you to start with. Tap to edit or view history.</string>
    <string name="tool_tip_goal_old_user">We set the goal based on your recent activities. Tap to edit or view history.</string>
    <string name="tool_tip_start">Welcome! Track your first activity.</string>
    <string name="welcome_card_body">Get started by tracking your first activity</string>

    <string name="location_not_available">Location not available</string>
    <string name="avg">Avg.</string>
    <!-- End Dashboard -->

    <string name="sharing_updated">Sharing setting updated</string>

    <!-- Video Ads -->
    <string name="watch_full_video">WATCH FULL VIDEO</string>
    <!-- End Video Ads -->

    <!-- Permissions -->
    <string name="location_permissions_rationale">The access to location is needed to track your activities.</string>
    <string name="location_permissions_rationale_map">The access to location is needed to show your location on the map.</string>
    <string name="storage_permission_rationale">The access for storage is needed for saving photos.</string>
    <string name="storage_permission_rationale_picker">The access for storage is needed for adding photos.</string>
    <string name="storage_permission_rationale_video">The access for storage is needed for adding videos.</string>
    <string name="storage_permission_rationale_download">The access for storage is needed for saving files.</string>
    <string name="steps_permission_rationale">This permission is needed to count steps during your workout.</string>
    <string name="background_location_granted">Background location granted</string>
    <string name="background_location_not_granted">Background location not granted</string>
    <string name="location_permission_required">Location permission required</string>
    <string name="storage_permission_rationale_share">The access for storage is needed for sharing photos.</string>
    <string name="camera_permission_rational">The access for camera is needed for adding photos.</string>
    <string name="camera_storage_permission_rationale_picker">The access for storage and camera are needed for adding photos.</string>
    <string name="installed_apps_permission_rationale">Allow Suunto to access apps on your phone to sync notifications?</string>

    <!-- End Permissions -->

    <!-- Newsletter Opt-in-->
    <string name="subscribe">subscribe</string>
    <string name="subscribed">subscribed</string>
    <string name="subscribe_newsletter_title">Get the Newsletter</string>
    <string name="news_and_offer_description">Stay connected to the Suunto community, receive news and offers directly from Suunto to your email. You can unsubscribe at any time.</string>
    <string name="news_and_offers_title">News and offers</string>
    <!-- End Newsletter Opt-in-->

    <!-- Thank you -->
    <string name="thank_you_title">Thank you!</string>
    <string name="thank_you_description">You can now connect your Suunto watch.</string>
    <!-- End Thank you -->

    <!-- Route planner -->
    <string name="title_activity_new_route">New Route</string>
    <string name="title_activity_create_a_copy">Copy route</string>
    <string name="title_activity_new_route_to">New route to</string>
    <string name="title_activity_navigate_on_watch">Navigate to</string>
    <string name="title_activity_edit_top_route">Edit Route</string>
    <string name="invalid_value">Entered value is not valid</string>
    <string name="introduce_route_name">Route name</string>
    <string name="invalid_route_name">Please give a name</string>
    <string name="route_saved">Route saved</string>
    <string name="error_saving_data">Something went wrong while saving. Try again.</string>
    <string name="error_start_navigate">Something went wrong while navigate. Try again.</string>
    <string name="error_importing_route">Oops! Seems like your file is not working. It could be corrupted or the format isn\'t supported.</string>
    <string name="error_point_not_added">Problem adding a waypoint. Try again.</string>
    <string name="routing_mode_title">Routing to next point</string>
    <string name="routing_mode_manual">Free drawing</string>
    <string name="routing_mode_manual_desc">No automatic routing</string>
    <string name="routing_mode_foot">Any road or path</string>
    <string name="routing_mode_foot_desc">Ideal for activities by foot</string>
    <string name="routing_mode_mtb">All road types</string>
    <string name="routing_mode_mtb_desc">Ideal for activities by bike</string>
    <string name="routing_mode_cycling">All road types - avoiding hills</string>
    <string name="routing_mode_cycling_desc">Ideal for activities by bike</string>
    <string name="routing_mode_roadbike">Paved roads</string>
    <string name="routing_mode_roadbike_desc">Ideal for road cycling</string>
    <string name="routing_mode">Mode</string>
    <string name="bearing_navigation_mode">Bearing navigation</string>
    <string name="bearing_navigation_mode_desc">Navigate to destination without route</string>
    <string name="clear_route_confirm">Clear whole route?</string>
    <string name="clear_routes">Clear route</string>
    <string name="route_tip_next_point">Tap next point to continue routing</string>
    <string name="route_activity_type">Activity type</string>
    <string name="route_name">Route name</string>
    <string name="my_routes_label">MY ROUTES</string>
    <string name="my_workouts_label">MY ACTIVITIES</string>
    <string name="route_fab_options_title">Options</string>
    <string name="close_route_title">Close route</string>
    <string name="close_route_subtitle">Loop Back to A</string>
    <string name="reverse_route_title">Reverse route</string>
    <string name="reverse_route_subtitle">Swap start and end points. Please check route safety.</string>
    <string name="back_to_start_title">Back to start</string>
    <string name="back_to_start_subtitle">Quick return to start.</string>
    <string name="back_trace_title">Backtrack</string>
    <string name="back_trace_subtitle">Retrace to start. Please check route safety.</string>

    <string name="route_distance_to">%1$s away</string>
    <string name="title_route_details">Route details</string>
    <string name="no_routes_nearby">Create routes by tapping the + button on the map or try planning one now.</string>
    <string name="no_nearby_workouts_found">You don\’t have any nearby activities.</string>
    <string name="delete_route">Delete route?</string>
    <string name="route_deleted">Route deleted</string>
    <string name="no_routes">You don\'t have any routes. Try planning one now.</string>
    <string name="save_changes">Save changes</string>
    <string name="plan_route">Plan a route</string>
    <string name="routes">Routes</string>
    <string name="my_pois">POIs</string>
    <string name="account_needed_title">Account needed</string>
    <string name="account_needed_desc">Use of this feature requires an account. Please sign
    up now to continue.</string>
    <string name="route_plan_cancel_confirm">Are you sure you want to discard current route?</string>
    <string name="unnamed_location">Unnamed location</string>
    <string name="start_route">Start route</string>
    <string name="end_route">End route</string>
    <string name="route_to_destination">Set as start for route</string>
    <string name="route_from_destination">Set as destination</string>
    <string name="create_save_route">Create and save route</string>
    <string name="navigate_on_watch_now">Navigate on watch now</string>
    <string name="navigate_on_watch_now_description">Starts navigation in watch </string>
    <string name="bearing_navigation">Bearing navigation</string>
    <string name="bearing_navigation_description">Navigate to destination without route</string>
    <string name="connect_watch_to_start_navigation">Connect watch to start navigation</string>
    <string name="watch_is_syncing">Watch is syncing</string>
    <string name="start_navigation_on_watch">Starting navigation on watch</string>
    <string name="navigating_on_watch_now">Navigating on watch now</string>
    <string name="navigation_without_gps_title">Unable to start navigation on watch</string>
    <string name="navigation_without_gps_message">The ongoing exercise on watch doesn’t support navigation. Please end the exercise and tap Start again.</string>
    <string name="copied_coordinates_to_clipboard">Copied to clipboard</string>
    <string name="altitude_at_location">Altitude: %1$s</string>
    <string name="cannot_find_route_title">Route not found</string>
    <string name="cannot_find_route_message">Can\'t find route to selected location. Try out free drawing mode or select another location on map.</string>
    <string name="route_can_not_be_shared_right_now">Route can not be shared right now. Please wait.</string>
    <string name="add_waypoint_tips">Tap to add waypoint</string>
    <!-- End Route planner -->

    <!-- Feeds in Dashboard -->
    <string name="shared_nearby">Shared Publicly</string>
    <string name="view_details_button">View details</string>
    <!-- Feeds in Dashboard -->

    <!-- Policy change for opt in -->
    <string name="agree">Agree</string>
    <string name="i_agree">I agree</string>
    <string name="disagree">Disagree</string>
    <string name="scroll_whole_text">Please scroll down for whole text</string>
    <string name="policy_change_optin_title">Update in our policies</string>
    <string name="policy_change_optin_text">We have updated our policies regarding Suunto newsletter subscription. In order to keep receiving our newsletter, please accept the change.\n\nOur renewed newsletter contains news and offers from Suunto and partner brands within the same group.</string>
    <!-- Policy change for opt in -->

    <!-- Sportie in feed -->
    <string name="sportie_share_your_photos">Share a photo</string>
    <!-- End Sportie in feed -->

    <!-- Reactions -->
    <string name="title_likes">Likes</string>
    <!-- End Reactions -->

    <!-- Ski -->
    <string name="workout_values_headline_ski_runs">Downhill count</string>
    <string name="ski_runs_capital">DOWNHILL COUNT</string>
    <string name="ski_time_capital">DOWNHILL DURATION</string>
    <string name="workout_values_headline_ski_time">Downhill duration</string>
    <string name="workout_values_headline_ski_distance">Downhill distance</string>
    <string name="ski_distance_capital">DOWNHILL DISTANCE</string>
    <string name="ski_run_time_capital">RUN TIME</string>
    <string name="ski_run_distance_capital">RUN DISTANCE</string>
    <string name="ski_run_distance_descent_capital">RUN DISTANCE/DESCENT</string>
    <string name="workout_values_headline_downhill_descent">Downhill descent</string>
    <string name="workout_values_headline_downhill_distance">Downhill distance</string>
    <string name="workout_values_headline_downhill_duration">Downhill duration</string>
    <string name="workout_values_headline_downhill_max_speed">Downhill max speed</string>
    <string name="workout_values_headline_downhill_avg_speed">Downhill avg speed</string>
    <string name="descent_capital">DESCENT</string>
    <string name="max_altitude_capital">MAX ALTITUDE</string>
    <string name="min_altitude_capital">MIN ALTITUDE</string>
    <string name="max_ski_speed_pace_capital">MAX (DOWNHILL) SPEED/PACE</string>
    <string name="avg_ski_run_speed_pace_capital">AVG (RUN) SPEED/PACE</string>
    <string name="workout_values_headline_max_ski_speed">Downhill max speed</string>
    <string name="max_ski_speed_capital">DOWNHILL MAX SPEED</string>
    <string name="avg_ski_speed_capital">DOWNHILL AVG. SPEED</string>
    <string name="workout_values_headline_avg_ski_speed">Downhill avg. speed</string>
    <string name="ski_run_avg_max_speed_capital">AVG/MAX (RUN) SPEED</string>
    <string name="ski_avg_max_speed_capital">AVG/MAX (DOWNHILL) SPEED</string>
    <string name="ski_min_max_altitude_capital">MIN/MAX ALTITUDE</string>
    <string name="ski_angle_capital">AVG SLOPE ANGLE</string>
    <string name="ski_no_runs">No completed runs yet</string>
    <string name="workout_values_downhill_grade_max">Downhill grade max</string>
    <string name="workout_values_downhill_grade_avg">Downhill grade avg</string>
    <string name="workout_values_co2_saved">CO₂e saved</string>

    <!-- End Ski -->

    <!-- Likes Notification -->
    <string name="single_like_your_notification">%1$s liked your %2$s</string>
    <string name="notification_workout_like_action">Like</string>
    <string name="single_like_your_workout">%s liked your workout</string>
    <!-- End Likes Notification -->

    <!-- Achievements -->
    <string name="achievement_route_first">Fastest time on this route</string>
    <string name="achievement_route_second">2nd fastest time on this route</string>
    <string name="achievement_route_third">3rd fastest time on this route</string>
    <!-- End Achievements -->

    <!-- Heart Rate Zones -->
    <string name="heart_rate_zones">Heart rate zones</string>
    <string name="heart_rate_zone_description">The heart rate zones below are calculated based on your maximum heart rate. Working out on different heart rate zones leads to different results.</string>
    <string name="heart_rate_zone_peak_title">Zone 5: Maximal</string>
    <string name="heart_rate_zone_peak_description">Improves your short high intensity performance</string>
    <string name="heart_rate_zone_anaerobic_title">Zone 4: Very hard</string>
    <string name="heart_rate_zone_anaerobic_description">Develops your anaerobic high intensity performance</string>
    <string name="heart_rate_zone_aerobic_title">Zone 3: Hard</string>
    <string name="heart_rate_zone_aerobic_description">Enhances aerobic fitness and cardiovascular performance</string>
    <string name="heart_rate_zone_endurance_title">Zone 2: Moderate</string>
    <string name="heart_rate_zone_endurance_description">Develops aerobic endurance and increases fat metabolism</string>
    <string name="heart_rate_zone_warmup_title">Zone 1: Easy</string>
    <string name="heart_rate_zone_warmup_description">Gets your body ready for exercise and accelerates your recovery</string>
    <string name="heart_rate_zone_bpm">%1$d-%2$d bpm</string>
    <string name="heart_rate_zone_bpm_no_low_limit">&lt; %d bpm</string>
    <string name="heart_rate_zone_percentage">%1$d-%2$d%%</string>
    <string name="heart_rate_zone_percentage_no_low_limit">&lt; %d%%</string>
    <string name="whats_new_heart_rate_zone_action">LEARN MORE</string>
    <!-- End Heart Rate Zones -->

    <!-- Activity Analysis -->
    <string name="analysis">Analysis</string>
    <string name="workout_analysis_view_on_map">VIEW ON MAP</string>
    <string name="workout_playback_route_on_map">Play route</string>
    <string name="workout_open_graph_analysis">Show analysis graphs</string>
    <!-- End Activity Analysis -->

    <!-- Save Activity -->
    <string name="add_photos">Add photos</string>
    <string name="add_videos">Add videos</string>
    <string name="add_photos_and_videos">Add photos and videos</string>
    <!-- End Save Activity -->

    <!-- Gps -->
    <string name="waiting_for_gps">Waiting for GPS</string>
    <!-- Gps -->

    <!-- New Route Comparison -->
    <string name="comparisons">Comparisons</string>
    <string name="rank_on_this_route">Rank on this route</string>
    <string name="rank_of_similar_distance">Rank of similar distance</string>
    <string name="compare_to_best">Compared to your previous best on this route</string>
    <string name="compare_to_previous">Compared to your previous activity on this route</string>
    <!-- End New Route Comparison -->

    <!-- Follow People -->
    <string name="find_people">Find people</string>
    <string name="following">Following</string>
    <string name="followers">Followers</string>
    <string name="personal_records">Personal records</string>
    <string name="add_people_make_sport_fun">Start following people to make sports more
        fun!</string>
    <string name="find_facebook_friends">Find Facebook friends</string>
    <string name="suggestions">suggestions</string>
    <string name="follow">Follow</string>
    <string name="requested">Requested</string>
    <string name="not_following_anyone">You are not following anyone yet</string>
    <string name="sign_up_to_follow_title">Sign up first</string>

    <string name="unfollow_dialog_message">Stop following this user?</string>
    <string name="unfollow_dialog_confirm">unfollow</string>
    <string name="no_followers">You don\'t have followers yet</string>
    <string name="fb_friends_add_all">Add all</string>
    <string name="fb_friends_all_added">All added</string>
    <string name="fb_friends_view_promote_text">Follow your friends and collect some inspiration from their activities and adventures</string>
    <string name="fb_friends_view_empty_text">Sorry, we couldn\'t find any new Facebook friends</string>
    <string name="fb_friends_try_search">Try our search instead!</string>
    <string name="pending_approval_title">pending your approval</string>
    <string name="pending_approval_text">By approving, your followers get to see your shared activities.</string>
    <string name="people_following_you">people following you</string>
    <string name="people_you_are_following">People you are following</string>
    <string name="rejected_follow_request">%s rejected</string>
    <string name="revoke_dialog_title">Remove follower?</string>
    <string name="revoke_multiple_dialog_title">Remove followers?</string>
    <string name="revoke_dialog_confirm">remove</string>
    <plurals name="fb_friends_added">
        <item quantity="one">%1$d friend added</item>
        <item quantity="few">%1$d friends added</item>
        <item quantity="many">%1$d friends added</item>
        <item quantity="other">%1$d friends added</item>
    </plurals>
    <string name="fb_friends_list_title">Your Facebook Friends</string>
    <string name="friends_now_followers_title">Friends are now Followers</string>
    <string name="friends_now_followers_content">Now it\'s simpler to follow friends and inspiring people. Just tap Follow and you get to see their shared activities.\n\nHere you can also manage who can see your shared activities.</string>
    <string name="revoke_single_follower_confirmation">%1$s won\'t be notified about the removal.</string>
    <string name="revoke_multiple_followers_confirmation">The users won\'t be notified that you have removed them.</string>
    <string name="selected_item_count">%d selected</string>
    <string name="removing_followers_progress">Removing followers…</string>
    <string name="error_429">Too many requests. Please try again later.</string>
    <string name="my_username">My Username: %1$s</string>
    <string name="phone_contact_title">Phone Contacts</string>
    <string name="request_read_contact_rationale">Suunto wants to access your address book so we can tell you which of your friends has joined Suunto.</string>
    <!-- Follow People -->

    <!-- Block/Report People -->
    <string name="block_user_message">The blocked user will not be able to follow you in the app and will not see your shared images, videos or activities.</string>
    <string name="block_user_title">Block\n%1$s</string>
    <string name="user_blocked">%1$s blocked</string>
    <string name="user_unblock_hint">You can unblock the user anytime from its profile.</string>
    <string name="unBlock_user_message">The user will now be able to request to follow you on the app.</string>
    <string name="unBlock_user_title">Unblock\n%1$s</string>
    <string name="report_user_message">Continue only if you think this user should be reported.</string>
    <string name="report_user_title">Report\n%1$s</string>
    <string name="user_reported_title">%1$s reported</string>
    <string name="user_reported_message">The user has been reported.</string>
    <!-- Block/Report People -->

    <!-- Media Gallery -->
    <string name="title_activity_media_gallery">Gallery</string>
    <string name="gallery_taken_during_workout">Taken during activity</string>
    <string name="gallery_not_taken_during_workout">Gallery</string>
    <string name="gallery_menu_launch_camera">Use camera</string>
    <string name="gallery_menu_launch_file_picker">Choose file</string>
    <!-- Media Gallery -->

    <string name="invite_friends_btn_title">Invite Friends</string>

    <!-- Video Edit -->
    <string name="edit_video">Edit video</string>
    <string name="audio">Audio</string>
    <string name="audio_included">Included in video</string>
    <string name="audio_excluded">Removed from video</string>
    <!-- Video Edit -->

    <string name="workout_auto_recovered">Auto-recovered</string>

    <string name="am">am</string>
    <string name="pm">pm</string>
    <!-- Premium -->
    <string name="premium_tap_for_detail">Tap for subscription details</string>
    <!-- Premium -->

    <!-- Shop Link -->
    <string name="shop_online">Shop online</string>
    <string name="buy_online">Buy online</string>
    <string name="shop_hrm_title">Suunto smart sensor</string>
    <string name="shop_hrm_summary">Measure heart rate to train on right intensity zones. Wearable heart rate monitor measures accurately and connects with most phones.</string>
    <!-- End Shop Link -->

    <!-- Redeem Voucher -->
    <string name="redeem_voucher">Redeem voucher</string>
    <string name="redeem_voucher_login_required">Please log in or sign up to redeem your voucher</string>
    <string name="redeem_voucher_failed">Code is not valid or has already been used.</string>
    <!-- End Redeem Voucher -->

    <!-- Premium subscription promotion texts -->
    <string name="get_premium">Go Premium</string>
    <string name="get_premium_body">Perfect for trails and adventures! Upgrade to Premium for great outdoor maps and ad-free experience. Swipe left to see maps available on your region.</string>
    <string name="track_your_progress">Track Your Progress</string>
    <string name="recognize_routes">Sports Tracker automatically recognizes the route you\'ve just taken and compares your time to previous ones. Plus, see how your times match up against routes of similar distances.</string>
    <!-- Premium subscription promotion texts -->

    <!-- Subscription purchase texts -->
    <string name="account_none">None</string>
    <!-- Subscription purchase texts -->
    <string name="one_year_auto_renew">1-year, auto-renew</string>
    <string name="one_month_auto_renew">1-month, auto-renew</string>
    <string name="purchase_error">Purchase error: %1$s</string>
    <string name="yearly_price">Yearly, %1$s</string>
    <string name="monthly_price">Monthly, %1$s</string>
    <!-- 'Premium' is product name, not translated!!! -->
    <string name="already_own_premium">You already own a Premium subscription</string>
    <string name="subscription">Subscription</string>
    <string name="please_wait_purchase">Purchasing...</string>
    <!-- 'Premium' is product name, not translated!!! -->
    <string name="premium_subscription_bought_ok">Congrats! You have now upgraded your account to Premium.</string>
    <string name="redeem_voucher_activated_ok">Congrats! You have now upgraded your account to Premium. The app will restart to update to Premium.</string>
    <string name="processing_purchase_retry_later">We are processing your purchase. You will be notified when it has been completed.</string>
    <string name="processing_purchase_subscription_used_by_other_user">Your premium subscription is linked to another Sports Tracker account (%1$s). You are logged in as %2$s. Please log in to the account with the subscription to access premium features.</string>
    <string name="billing_unavailable">Billing services are unavailable on your device. Please make sure your Google Play Store is set up.</string>
    <string name="invalid_purchase_contact_us">We were unable to process your purchase. Please contact us at
        https://sports-tracker.helpshift.com</string>
    <plurals name="days_left">
        <item quantity="one">%1$d day left</item>
        <item quantity="few">%1$d days left</item>
        <item quantity="many">%1$d days left</item>
        <item quantity="other">%1$d days left</item>
    </plurals>
    <!-- 'Premium' is product name, not translated!!! -->
    <string name="premium_subscriptions_not_supported">Unfortunately Premium subscription is not supported on your device or Android version.</string>

    <string name="buy_premium_popup_title">Buy Premium to get the best experience</string>
    <string name="buy_premium_popup_voice_feedback_description">With audio feedback, you can hear the duration, pace, distance and much more while keeping your focus on the workout. No need to stare at your phone.</string>
    <string name="buy_premium_popup_ghost_target_description">Set a ghost target and track your progress in real time. Get info whether you are ahead or behind the set time and beat your own or a friend’s records.</string>
    <string name="buy_premium_popup_follow_route_description">Follow your routes and create new ones. You can share your routes if you and your buddies want to train on the same trail.</string>
    <string name="buy_premium_workout_recent_trends_description">Buy Premium to see timeline graphics of how you did compared to your previous workouts.</string>
    <string name="buy_premium_workout_comparisons_description">Buy Premium to see your ranking compared to your previous workouts.</string>
    <string name="buy_premium_popup_premium_map_types_description">Don’t get lost when you’re enjoying nature. Get access to great outdoor maps such as terrain, landscape and cycling.</string>
    <string name="buy_premium_popup_premium_road_surfaces_description">See what kind of surface - paved, smooth unpaved or rough unpaved - the route has.</string>
    <string name="buy_premium_popup_premium_heatmaps_description">Discover new training routes in your own neighborhood and find the popular local spots when you are somewhere new.</string>
    <string name="buy_premium_popup_premium_my_tracks_description">See all the tracks you have covered this week, month, year, in the last 30 days or a custom period. You can view your tracks on any map type.</string>
    <string name="buy_premium_to_add_user_tags_description">Use tags to group your exercises. Buy Sports Tracker Premium to add custom tags.</string>
    <string name="buy_premium_popup_preselected_voice_feedback_description">We noticed that you had audio feedback on. The feature will be turned off as it is now part of the Sports Tracker Premium.</string>
    <string name="buy_premium_popup_preselected_ghost_target_description">We noticed that you had chosen a ghost target. The feature will be turned off as it is now part of the Sports Tracker Premium.</string>
    <string name="buy_premium_popup_preselected_follow_route_description">We noticed that you had chosen a route to follow. The feature will be turned off as it is now part of the Sports Tracker Premium.</string>
    <string name="buy_premium_popup_3d_workout_playback_description">Explore the ups and downs in your workout with beautiful 3D playback.</string>

    <string name="explore_premium_benefits">Explore Premium benefits</string>
    <string name="explore_benefits">Explore benefits</string>
    <string name="try_premium">Try Premium</string>
    <string name="try_it_free_for_30_days">Try it free for 30 days!</string>
    <!-- End subscription purchase texts -->

    <!-- BLE Scanning ST only-->
    <string name="ble_need_location_service">Scanning for Bluetooth devices requires that your phone\'s location services are on.</string>
    <string name="ble_need_location_permission">Scanning for Bluetooth devices requires access to location services. Please grant permission next.</string>
    <string name="ble_location_permission_denied">Bluetooth scanning not allowed. Please check your permissions settings.</string>
    <!-- End BLE Scanning -->

    <!--New diary-->
    <string name="diary_no_activity">No daily activity tracked</string>
    <string name="diary_item_steps_unit">steps</string>
    <string name="diary_item_total_calories_prefix">Total</string>
    <string name="diary_item_active_calories_prefix">Active</string>
    <string name="diary_summaryitem_avg">Average %1$s</string>
    <string name="diary_daily_activity_header">Daily activity</string>
    <string name="diary_daily_minimum_heart_rate">Minimum heart rate</string>
    <!--End New diary-->

    <!-- Dive details -->
    <string name="dive_profile">Dive Profile</string>
    <string name="dive_profile_show_events">SHOW EVENTS</string>
    <string name="dive_profile_show_events_title">Events</string>
    <string name="dive_gauge_mode">Gauge</string>
    <string name="dive_free_mode">Free</string>
    <string name="dive_air_mode">Air</string>
    <string name="dive_ean_mode">EAN</string>
    <string name="dive_mixed_mode">Mixed</string>
    <!-- Dive details -->

    <!-- Notification channels -->
    <string name="notification_channel_group_my_activities">My activities</string>
    <string name="notification_channel_group_social">Social</string>
    <string name="notification_channel_my_activity_likes">Likes</string>
    <string name="notification_channel_my_activity_comments">Comments</string>
    <string name="notification_channel_personal_achievements">Personal achievements</string>
    <string name="notification_channel_new_followers">New followers</string>
    <string name="notification_channel_facebook_friend_joined">Facebook friend joined</string>
    <string name="notification_channel_friend_activity_shared">Friend shared an activity</string>
    <string name="notification_channel_friend_activity_commented">Follow-up comments</string>
    <string name="notification_channel_critical_information">Critical information</string>
    <string name="notification_channel_app_updates">App updates</string>
    <string name="notification_channel_events_and_challenges">Events and challenges</string>
    <string name="notification_channel_updates_from_community">Updates from the community</string>
    <string name="notification_channel_foreground_sync">Cloud synchronization</string>
    <string name="notification_channel_training_planner_updates">Suunto AI Coach updates</string>
    <string name="notification_channel_braze_default">Other</string>
    <string name="notification_channel_upcoming_period">Upcoming period</string>
    <string name="notification_channel_log_period_reminder">Log period reminder</string>
    <!-- End notification channels -->

    <string name="notification_foreground_sync_content">Syncing your activities with the cloud</string>

    <string name="contact_support">Contact support</string>
    <string name="phone_call_logs_permission_dialog_title">Want to see incoming calls on your watch?</string>
    <string name="phone_call_logs_permission_dialog_message">To show incoming calls and caller\'s name on watch, Suunto app needs access to your phone call logs.</string>
    <string name="changes_saved_success">Changes saved successfully</string>

    <!-- Power management settings-->
    <string name="power_management_settings_header">Power management</string>
    <string name="connectivity_notification_settings_header">Connectivity notification</string>
    <string name="power_management_settings_description">Phone\'s power management settings affects device connectivity and GPS tracking. To ensure best experience please check these settings.</string>
    <string name="battery_optimisation_setting">Battery optimisation</string>
    <string name="power_save_setting">Power save</string>
    <string name="power_settings">Power settings</string>
    <string name="power_settings_summary">These settings may have effect on the device connection</string>
    <string name="power_save_setting_summary_all_good">All good</string>
    <string name="power_save_setting_summary_requires_action">Requires action - Turn off</string>
    <string name="report_gps_tracking_issue_dialog_title">App was shut down during tracking.</string>
    <string name="report_gps_tracking_issue_dialog_message">Please check you mobile power management options.</string>
    <string name="power_save_warning_dialog_message">Open settings and turn off your power saving settings to ensure uninterrupted tracking.</string>
    <string name="power_save_warning_dialog_title">Power save may interrupt tracking.</string>
    <string name="power_management_activity_title">Power management</string>
    <string name="power_management_support_button">Support</string>
    <!-- End power management settings-->

    <!-- Mobile-Connected GPS settings -->
    <string name="mobile_connected_gps_settings_header">Mobile-Connected GPS</string>
    <string name="mobile_connected_gpd_settings_description">To record an exercise with GPS tracking, go to your phone\'s settings and allow your Suunto device access your phone\'s location \"%s\".</string>
    <string name="mobile_connected_gps_location_permission_title">Location permission</string>
    <string name="mobile_connected_gps_location_permission_not_good">Set location permission \"%s\".</string>
    <!-- End Mobile-Connected GPS settings -->

    <!-- Workout location -->
    <string name="tap_to_add_location">Tap to add location</string>
    <string name="tap_to_confirm_location">Confirm location</string>
    <string name="tap_to_select_location_instructions">Tap on map to select location.</string>
    <string name="select_location">Select location</string>
    <string name="add_location">Add location</string>
    <string name="edit_location">Edit location</string>
    <string name="delete_location_query">Delete location?</string>
    <!-- End Workout location -->

    <!-- Mapbox Places Plugin: Autocomplete -->
    <string name="mapbox_plugins_autocomplete_search_hint">Search here</string>
    <string name="mapbox_plugins_offline_message">Offline - Try again</string>
    <string name="mapbox_snackbar_offline_message">Offline</string>

    <!-- Mapbox Places Plugin: Place picker -->
    <string name="mapbox_plugins_place_picker_toolbar_primary_text">Choose location on map</string>
    <string name="mapbox_plugins_place_picker_toolbar_secondary_text">Pan and zoom to adjust</string>
    <string name="mapbox_plugins_place_picker_not_valid_selection">The selected location isn\'t valid</string>

    <!-- Mapbox Places Plugin: PlacePicker Activity device location toasts -->
    <string name="mapbox_plugins_place_picker_user_location_permission_explanation">This app needs location permissions in order to find the device location.</string>
    <string name="mapbox_plugins_place_picker_user_location_permission_not_granted">Location permissions not granted.</string>
    <string name="mapbox_plugins_place_picker_user_location_not_found">Location can\'t be found.</string>

    <!-- Mapbox Search SDK -->
    <string name="mapbox_search_sdk_distance_feet" tools:ignore="PrivateResource">%1$s ft</string>
    <string name="mapbox_search_sdk_distance_km" tools:ignore="PrivateResource">%1$s km</string>
    <string name="mapbox_search_sdk_distance_meters" tools:ignore="PrivateResource">%1$s m</string>
    <string name="mapbox_search_sdk_distance_miles" tools:ignore="PrivateResource">%1$s mi</string>
    <string name="mapbox_search_sdk_no_internet_connection_retry_button" tools:ignore="PrivateResource">Tap to retry</string>
    <string name="mapbox_search_sdk_no_internet_connection_subtitle" tools:ignore="PrivateResource">You’re offline. Try to reconnect.</string>
    <string name="mapbox_search_sdk_no_internet_connection_title" tools:ignore="PrivateResource">No internet connection</string>
    <string name="mapbox_search_sdk_search_result_view_empty_recent_searches" tools:ignore="PrivateResource">Recent searches</string>
    <string name="mapbox_search_sdk_search_result_view_no_results" tools:ignore="PrivateResource">No suggestions found</string>
    <string name="mapbox_search_sdk_search_result_view_recent_searches_title" tools:ignore="PrivateResource">Recent searches</string>

    <string name="diary_calendar_tab_title">Calendar</string>
    <string name="diary_calendar_year_toggle_button">Year</string>
    <string name="diary_calendar_30_days_toggle_button">30 days</string>
    <string name="diary_calendar_month_toggle_button">Month</string>
    <string name="diary_calendar_week_toggle_button">Week</string>
    <string name="diary_calendar_week_date_range_label">Week</string>
    <string name="diary_calendar_empty_state_description">Summary of your activity will appear here.</string>
    <string name="diary_calendar_share_summary_button">Share summary</string>
    <string name="diary_calendar_share_summary_error">Something went wrong, please try again.</string>
    <string name="diary_calendar_share_my_top_sports">My top sports</string>
    <string name="calendar_tab_title_diary">Diary</string>
    <string name="calendar_tab_title_my_plan">My plan</string>
    <string name="diary_calendar_activity_title">Activity</string>
    <string name="diary_calendar_map_title">Map</string>

    <!-- Diary graph time range -->
    <string name="diary_graph_13_months">13 months</string>
    <string name="diary_graph_8_weeks">8 weeks</string>
    <string name="diary_graph_8_months">8 months</string>
    <string name="diary_graph_8_years">8 years</string>
    <string name="diary_graph_week">Week</string>
    <string name="diary_graph_month">Month</string>
    <string name="diary_graph_year">Year</string>
    <string name="diary_graph_6_weeks">6 weeks</string>
    <string name="diary_graph_6_months">6 months</string>
    <string name="diary_graph_7_days">Last 7 days</string>
    <string name="diary_graph_30_days">Last 30 days</string>
    <string name="diary_graph_one_year">Year</string>

    <string name="diary_graph_13_months_short">13 M</string>
    <string name="diary_graph_8_weeks_short">8 W</string>
    <string name="diary_graph_8_months_short">8 M</string>
    <string name="diary_graph_8_years_short">8 Y</string>
    <string name="diary_graph_week_short">W</string>
    <string name="diary_graph_month_short">M</string>
    <string name="diary_graph_year_short">Y</string>
    <string name="diary_graph_6_weeks_short">6 W</string>
    <string name="diary_graph_6_months_short">6 M</string>
    <string name="diary_graph_7_days_short">7 D</string>
    <string name="diary_graph_30_days_short">30 D</string>
    <string name="diary_graph_one_year_short">Y</string>

    <string name="diary_graph_daily_interval">Daily interval</string>
    <string name="diary_graph_weekly_interval">Weekly interval</string>
    <string name="diary_graph_monthly_interval">Monthly interval</string>
    <string name="diary_graph_yearly_interval">Yearly interval</string>
    <!-- End Diary graph time range -->

    <!-- Achievements-->
    <string name="achievements_first_activity_of_any_type">This activity sets your personal bests for this sport. Can you make the next one longer or faster?</string>
    <!-- example: Farthest Running -->
    <string name="achievements_farthest_activity_of_type">Farthest %1$s</string>
    <!-- example: Farthest Running this year -->
    <string name="achievements_farthest_activity_of_type_year">Farthest %1$s this year</string>
    <!-- example: Farthest Running this month -->
    <string name="achievements_farthest_activity_of_type_month">Farthest %1$s this month</string>
    <!-- example: Fastest Running -->
    <string name="achievements_fastest_activity_of_type">Fastest %1$s</string>
    <!-- example: Fastest Running this year -->
    <string name="achievements_fastest_activity_of_type_year">Fastest %1$s this year</string>
    <!-- example: Fastest Running this month -->
    <string name="achievements_fastest_activity_of_type_month">Fastest %1$s this month</string>
    <!-- example: Longest Running -->
    <string name="achievements_longest_activity_of_type">Longest %1$s</string>
    <!-- example: Longest Running this year-->
    <string name="achievements_longest_activity_of_type_year">Longest %1$s this year</string>
    <!-- example: Longest Running this month -->
    <string name="achievements_longest_activity_of_type_month">Longest %1$s this month</string>
    <!-- example: First Running. Great! -->
    <string name="achievements_cumulative_first_activitytype_ever">First %1$s. Great!</string>
    <!-- example: First Running this year -->
    <string name="achievements_cumulative_first_activitytype_year">First %1$s this year</string>
    <!-- example: First Running this month -->
    <string name="achievements_cumulative_first_activitytype_month">First %1$s this month</string>
    <!-- example: 20th Running this year -->
    <string name="achievements_cumulative_activitytype_year">%1$s %2$s this year</string>
    <!-- example: 50th activity this year. Superb! -->
    <string name="achievements_cumulative_activity_year">%1$s activity this year. Superb!</string>
    <!-- example: 200th activity. Great Work! -->
    <string name="achievements_cumulative_activity">%1$s activity. Great work!</string>
    <!-- example: 5th Running this month -->
    <string name="achievements_cumulative_activitytype_month">%1$s %2$s this month</string>
    <!-- example: 4th Running this week, 2 more than last week -->
    <string name="achievements_cumulative_activitytype_count_week">%1$s %2$s this week, %3$d more than last week</string>
    <!-- example: 6th activity this week, 3 more than last week -->
    <string name="achievements_cumulative_activity_count_week">%1$s activity this week, %2$d more than last week</string>
    <!-- example: Streak: 4th training week -->
    <string name="achievements_cumulative_training_week_streak">Streak: %1$s training week</string>
    <!-- example: 6th consecutive training day -->
    <string name="achievements_cumulative_training_day_streak">%1$s consecutive training day</string>
    <!-- example: Early bird Running -->
    <string name="achievements_early_bird_activitytype">Early bird %1$s</string>

    <!-- example: Farthest Running in 4 months -->
    <!-- This is shown when months > 1 -->
    <plurals name="achievements_farthest_activity_of_type_in_months">
        <item quantity="one">Farthest %1$s in %2$d month</item>
        <item quantity="few">Farthest %1$s in %2$d months</item>
        <item quantity="many">Farthest %1$s in %2$d months</item>
        <item quantity="other">Farthest %1$s in %2$d months</item>
    </plurals>

    <!-- example: Fastest Running in 4 months -->
    <!-- This is shown when months > 1 -->
    <plurals name="achievements_fastest_activity_of_type_in_months">
        <item quantity="one">Fastest %1$s in %2$d month</item>
        <item quantity="few">Fastest %1$s in %2$d months</item>
        <item quantity="many">Fastest %1$s in %2$d months</item>
        <item quantity="other">Fastest %1$s in %2$d months</item>
    </plurals>

    <!-- example: Longest Running in 4 months -->
    <!-- This is shown when months > 1 -->
    <plurals name="achievements_longest_activity_of_type_in_months">
        <item quantity="one">Longest %1$s in %2$d month</item>
        <item quantity="few">Longest %1$s in %2$d months</item>
        <item quantity="many">Longest %1$s in %2$d months</item>
        <item quantity="other">Longest %1$s in %2$d months</item>
    </plurals>

    <!-- example: First Running in 3 months -->
    <!-- This is shown when months > 1 -->
    <plurals name="achievements_cumulative_first_in_months">
        <item quantity="one">First %1$s in %2$d month</item>
        <item quantity="few">First %1$s in %2$d months</item>
        <item quantity="many">First %1$s in %2$d months</item>
        <item quantity="other">First %1$s in %2$d months</item>
    </plurals>

    <!-- example: First Running in 3 weeks -->
    <!-- This is shown when weeks > 2 -->
    <plurals name="achievements_cumulative_first_in_weeks">
        <item quantity="one">First %1$s in %2$d week</item>
        <item quantity="few">First %1$s in %2$d weeks</item>
        <item quantity="many">First %1$s in %2$d weeks</item>
        <item quantity="other">First %1$s in %2$d weeks</item>
    </plurals>
    <string name="location_permission_rationale_for_location">This feature requires access to your location.</string>
    <string name="paging_indicator">Paging indicator</string>
    <string name="invalid_email">Please provide a valid email address</string>
    <string name="retry_action">RETRY</string>
    <string name="no_network_error">No network</string>
    <string name="no_internet_connection_error">No internet connection</string>
    <string name="close">Close</string>
    <string name="error_101">Email is already in use.</string>
    <string name="my_tracks_title">My tracks</string>
    <string name="my_tracks_subtitle">Display my activity tracks on the map</string>
    <!-- Achievements-->

    <!-- Duration widget -->
    <string name="duration_in_hours_description">Duration h</string>
    <!-- End Workout location -->

    <!-- TrainingPeaks Form phases -->
    <string name="tss_form_insight_no_activity_data">Complete activities to get insights.</string>
    <string name="tss_form_insight_first_tss_today">Well done, keep on training!</string>
    <string name="tss_phase_description_losing_fitness_or_recovering_title">Losing fitness or recovering</string>
    <string name="tss_phase_description_losing_fitness_title">Losing fitness</string>
    <string name="tss_phase_description_recovering_title">Recovering</string>
    <string name="tss_phase_description_maintaining_fitness_title">Keeping fit</string>
    <string name="tss_phase_description_productive_training_title">Productive training</string>
    <string name="tss_phase_description_going_too_hard_title">Going too hard</string>
    <!-- End TrainingPeaks Form phases -->

    <string name="save_to_pictures">Save to pictures</string>
    <string name="loading_content">Loading content</string>
    <string name="image_saved_at">Image saved at %s</string>

    <string name="creating_share_link">Creating share link...</string>

    <!-- Dashboard widgets -->
    <string name="dashboard_widget_change_to_previous_period">%1$s%% from previous 7-day period.</string>
    <string name="dashboard_widget_no_data_from_last_7_days">No data from last 7 days.</string>
    <string name="dashboard_widget_training_no_data_title">Training</string>
    <string name="dashboard_widget_training_no_data">Aim to train regularly.</string>
    <string name="dashboard_widget_training_only_new_data">Keep on training to follow your patterns.</string>
    <string name="dashboard_widget_training_only_data_for_today">Keep on training to see trends.</string>
    <string name="last_seven_days">Last 7 days</string>
    <string name="progress_title">Progress</string>
    <string name="dashboard_widget_resources_no_data_title">Resources</string>
    <string name="dashboard_widget_resources_now">Resources now</string>
    <string name="dashboard_widget_resources_at">Resources %1$s</string>
    <plurals name="dashboard_widget_resources_n_day_average">
        <item quantity="one">%1$d%% wake-up resources %2$d-day avg.</item>
        <item quantity="few">%1$d%% wake-up resources %2$d-day avg.</item>
        <item quantity="many">%1$d%% wake-up resources %2$d-day avg.</item>
        <item quantity="other">%1$d%% wake-up resources %2$d-day avg.</item>
    </plurals>
    <string name="dashboard_widget_resources_no_data">Follow your stress and recovery.</string>
    <string name="dashboard_widget_sleep_no_data_title">Sleep</string>
    <plurals name="dashboard_widget_sleep_n_day_average">
        <item quantity="one">%1$s h sleep %2$d-day avg.</item>
        <item quantity="few">%1$s h sleep %2$d-day avg.</item>
        <item quantity="many">%1$s h sleep %2$d-day avg.</item>
        <item quantity="other">%1$s h sleep %2$d-day avg.</item>
    </plurals>
    <string name="dashboard_widget_sleep_no_data">Record sleep data to see patterns.</string>
    <string name="dashboard_widget_steps_steps">Steps</string>
    <string name="dashboard_widget_steps_no_data">Every step counts!</string>
    <plurals name="dashboard_widget_steps_n_day_average">
        <item quantity="one">%1$d steps %2$d-day avg.</item>
        <item quantity="few">%1$d steps %2$d-day avg.</item>
        <item quantity="many">%1$d steps %2$d-day avg.</item>
        <item quantity="other">%1$d steps %2$d-day avg.</item>
    </plurals>
    <string name="dashboard_widget_calories_calories">Calories</string>
    <string name="dashboard_widget_calories_no_data">Remember to balance exercise and energy intake.</string>
    <plurals name="dashboard_widget_calories_n_day_average">
        <item quantity="one">%1$d active kcal %2$d-day avg.</item>
        <item quantity="few">%1$d active kcal %2$d-day avg.</item>
        <item quantity="many">%1$d active kcal %2$d-day avg.</item>
        <item quantity="other">%1$d active kcal %2$d-day avg.</item>
    </plurals>

    <string name="sleep_hrv_title">HRV</string>
    <string name="sleep_hrv_7_day_hrv_label">7-d avg ms</string>
    <string name="sleep_hrv_unsupported">Your watch doesn\'t support HRV</string>
    <string name="sleep_hrv_no_data">Track your sleep in order to get your HRV data</string>
    <string name="sleep_hrv_normal_range_missing">Track your sleep for 14 days to establish your HRV range.</string>
    <string name="sleep_hrv_avg_low">Low</string>
    <string name="sleep_hrv_avg_high">High</string>
    <string name="sleep_hrv_avg_in_normal_range">Normal</string>
    <string name="dashboard_widget_sleep_hrv_description">Follow Heart Rate Variability to see your recovery status</string>
    <string name="dashboard_widget_sleep_hrv_description_new">Today\'s heart rate variability, range, and 7-day trend</string>
    <string name="dashboard_widget_sleep_hrv_no_7_day_data">No 7-d avg</string>

    <string name="dashboard_widget_commute_title">Commutes</string>
    <string name="dashboard_widget_commute_kg_co2e_saved_this_month">kg CO₂e saved this month</string>

    <string name="dashboard_widget_ascent_title">Ascent</string>
    <string name="dashboard_widget_ascent_no_data_from_previous_7_day_period">Ascent last 7 days.</string>

    <string name="dashboard_widget_minimum_heart_rate_title">Heart rate</string>
    <string name="dashboard_widget_minimum_heart_rate_min_heart_rate">Min. heart rate</string>
    <plurals name="dashboard_widget_minimum_heart_rate_n_day_average">
        <item quantity="one">%1$d-day avg. %2$d bpm min. heart rate.</item>
        <item quantity="few">%1$d-day avg. %2$d bpm min. heart rate.</item>
        <item quantity="many">%1$d-day avg. %2$d bpm min. heart rate.</item>
        <item quantity="other">%1$d-day avg. %2$d bpm min. heart rate.</item>
    </plurals>

    <string name="dashboard_widget_description_progress">Daily status of your training load and current training phase</string>
    <string name="dashboard_widget_description_progress_new">Current training phase and load</string>
    <string name="dashboard_widget_description_training">7-day exercise duration and comparison to the previous period</string>
    <string name="dashboard_widget_description_training_new">Cumulative activity duration and trend over the last 7 days</string>
    <string name="dashboard_widget_description_calories">Follow your daily active calories and see the 7-day progress</string>
    <string name="dashboard_widget_description_calories_new">Daily active calories compared to your target</string>
    <string name="dashboard_widget_description_resources">See the current status of your resources and the 7-day trend</string>
    <string name="dashboard_widget_description_resources_new">Current resource status and wake-up resource trend over the last 7 days</string>
    <string name="dashboard_widget_description_sleep">See your daily sleep duration and the 7-day trend</string>
    <string name="dashboard_widget_description_sleep_new">Last night\'s sleep duration, target, and 7-day trend</string>
    <string name="dashboard_widget_description_steps">See your daily steps and the 7-day summary</string>
    <string name="dashboard_widget_description_steps_new">Daily steps compared to your target</string>
    <string name="dashboard_widget_description_commute_this_month">See your saved CO₂e emissions on monthly basis</string>
    <string name="dashboard_widget_description_commute_this_month_new">Cumulative CO₂e saved this month through commuting and commute sports</string>
    <string name="dashboard_widget_description_activities_this_week">See your weekly sports and training hours</string>
    <string name="dashboard_widget_description_activities_this_week_new">Top sports, durations and distances this week</string>
    <string name="dashboard_widget_description_activities_this_month">See your monthly sports and training hours</string>
    <string name="dashboard_widget_description_activities_this_month_new">Top sports, durations and distances this month</string>
    <string name="dashboard_widget_description_activities_last_30_days">See your 30-day sports and training hours</string>
    <string name="dashboard_widget_description_activities_last_30_days_new">Top sports, durations and distances over the last 30 days</string>
    <string name="dashboard_widget_description_duration_this_week">See your weekly training time</string>
    <string name="dashboard_widget_description_duration_this_week_new">Cumulative activity duration compared to your weekly target</string>
    <string name="dashboard_widget_description_duration_this_month">See your monthly training time</string>
    <string name="dashboard_widget_description_duration_this_month_new">Cumulative activity duration this month, compared to last month</string>
    <string name="dashboard_widget_description_duration_last_30_days">See your 30-day training time</string>
    <string name="dashboard_widget_description_duration_last_30_days_new">Cumulative activity duration and daily average over the last 30 days</string>
    <string name="dashboard_widget_description_calendar_this_week">Follow your weekly activities and sports in the calendar</string>
    <string name="dashboard_widget_description_calendar_this_week_new">Activities and sports this week in the calendar</string>
    <string name="dashboard_widget_description_calendar_this_month">Follow your monthly activities and sports in the calendar</string>
    <string name="dashboard_widget_description_calendar_this_month_new">Activities and sports this month in the calendar</string>
    <string name="dashboard_widget_description_calendar_last_30_days">Follow your 30-day activities and sports in the calendar</string>
    <string name="dashboard_widget_description_calendar_last_30_days_new">Activities and sports over the last 30 days in the calendar</string>
    <string name="dashboard_widget_description_map_this_week">See your weekly exercise routes on a map</string>
    <string name="dashboard_widget_description_map_this_week_new">Activity routes this week on the map</string>
    <string name="dashboard_widget_description_map_this_month">See your monthly exercise routes on a map</string>
    <string name="dashboard_widget_description_map_this_month_new">Activity routes this month on the map</string>
    <string name="dashboard_widget_description_map_last_30_days">See your 30-day exercise routes on a map</string>
    <string name="dashboard_widget_description_map_last_30_days_new">Activity routes over the last 30 days on the map</string>
    <string name="dashboard_widget_description_ascent">7-day ascent and comparison to the previous period</string>
    <string name="dashboard_widget_description_ascent_new">Ascent this week, compared to the previous period</string>
    <string name="dashboard_widget_description_minimum_heart_rate">See your last 7 day minimum heart rate values</string>
    <string name="dashboard_widget_description_minimum_heart_rate_new">Today\'s minimal daytime heart rate, avg, and 7-day trend</string>

    <string name="dashboard_widgets">Dashboard widgets</string>
    <string name="select_new_dashboard_widget_description">Widgets give you useful information of your activity and training. Choose which ones to see on your dashboard.</string>
    <string name="no_widgets_available">No widgets available</string>
    <string name="add_dashboard_widget">Add %1$s widget</string>
    <string name="remove_dashboard_widget">Remove widget</string>
    <string name="dashboard_widget_type_not_available_on_all_devices">Not available with all devices</string>
    <string name="dashboard_widget_type_not_available_on_all_devices_new">Not available on all devices</string>
    <string name="dashboard_long_press_to_customize_tooltip">Long-press widgets to customize the dashboard</string>

    <string name="dashboard_widget_calories_name">Calories</string>
    <string name="dashboard_widget_progress_name">Progress</string>
    <string name="dashboard_widget_resources_name">Resources</string>
    <string name="dashboard_widget_sleep_name">Sleep</string>
    <string name="dashboard_widget_steps_name">Steps</string>
    <string name="dashboard_widget_training_name">Training</string>
    <string name="dashboard_widget_goal_name">Goal</string>
    <string name="dashboard_widget_commute_this_month_name">Commutes this month</string>
    <string name="dashboard_widget_commute_this_month_name_new">Commutes</string>
    <string name="dashboard_widget_calendar_this_week_name">Calendar this week</string>
    <string name="dashboard_widget_calendar_this_month_name">Calendar this month</string>
    <string name="dashboard_widget_calendar_last_30_days_name">Calendar last 30 days</string>
    <string name="dashboard_widget_activities_this_week_name">Activities this week</string>
    <string name="dashboard_widget_activities_this_week_name_new">Sports this week</string>
    <string name="dashboard_widget_activities_this_month_name">Activities this month</string>
    <string name="dashboard_widget_activities_this_month_name_new">Sports this month</string>
    <string name="dashboard_widget_activities_last_30_days_name">Activities last 30 days</string>
    <string name="dashboard_widget_activities_last_30_days_name_new">Sports last 30 days</string>
    <string name="dashboard_widget_map_this_week_name">Map this week</string>
    <string name="dashboard_widget_map_this_month_name">Map this month</string>
    <string name="dashboard_widget_map_last_30_days_name">Map last 30 days</string>
    <string name="dashboard_widget_duration_this_week_name">Duration this week</string>
    <string name="dashboard_widget_duration_this_month_name">Duration this month</string>
    <string name="dashboard_widget_duration_last_30_days_name">Duration last 30 days</string>
    <string name="dashboard_widget_ascent_name">Ascent</string>
    <string name="dashboard_widget_minimum_heart_rate_selection_list_name">Minimum HR last 7 days</string>
    <string name="dashboard_widget_minimum_heart_rate_name">Minimum daytime heart rate</string>
    <string name="dashboard_widget_hrv_name">Heart rate variability</string>
    <string name="dashboard_widget_duration_last_7_days_name">Duration last 7 days</string>
    <string name="dashboard_widget_duration_name">Duration</string>

    <string name="dashboard_widget_buy_premium_description_progress">Are you losing or gaining fitness? Follow your progress with Premium</string>

    <string name="widget_tss_header">Training load</string>
    <string name="widget_activity_speed_header">Avg speed</string>
    <string name="widget_minimum_sleep_heart_rate_title">Min. sleep HR</string>
    <string name="widget_resting_heart_rate_title">Resting HR</string>

    <string name="dashboard_widget_cycling_distance_this_week_name">Cycling distance</string>
    <string name="dashboard_widget_hiking_distance_this_week_name">Hiking distance</string>
    <string name="dashboard_widget_running_distance_this_week_name">Running distance</string>
    <string name="dashboard_widget_running_pace_this_week_name">Avg. running pace</string>
    <string name="dashboard_widget_cycling_speed_this_week_name">Avg. cycling speed</string>
    <string name="dashboard_widget_hiking_pace_this_week_name">Avg. hiking pace</string>
    <string name="dashboard_widget_menstrual_period_name">Period</string>
    <string name="dashboard_widget_minimum_sleep_heart_rate_name">Minimal sleep heart rate</string>
    <string name="dashboard_widget_resting_heart_rate_name">Resting HR</string>

    <string name="dashboard_widget_description_cycling_distance_this_week">This week\'s total cycling distance and trend compared to the previous period</string>
    <string name="dashboard_widget_description_hiking_distance_this_week">This week\'s total hiking distance and trend compared to the previous period</string>
    <string name="dashboard_widget_description_running_distance_this_week">This week\'s average running pace and trend compared to the previous period</string>
    <string name="dashboard_widget_description_running_pace_this_week">This week\'s total running distance and trend compared to the previous period</string>
    <string name="dashboard_widget_description_cycling_speed_this_week">This week\'s average cycling speed and trend compared to the previous period</string>
    <string name="dashboard_widget_description_hiking_pace_this_week">This week\'s average hiking pace and trend compared to the previous period</string>
    <string name="dashboard_widget_description_menstrual_period">Current menstrual cycle status and phase</string>
    <string name="dashboard_widget_description_minimum_sleep_heart_rate">Today\'s minimal sleep heart rate, average, and 7-day trend</string>
    <string name="dashboard_widget_description_resting_heart_rate">Today\'s resting heart rate, average, and 7-day trend</string>
    <string name="widget_menstrual_period_header">Period</string>
    <plurals name="widget_menstrual_period_subheader">
        <item quantity="one">Avg. %d day</item>
        <item quantity="few">Avg. %d days</item>
        <item quantity="many">Avg. %d days</item>
        <item quantity="other">Avg. %d days</item>
    </plurals>
    <string name="widget_menstrual_period_day">Day %d</string>
    <plurals name="widget_menstrual_period_late_days">
        <item quantity="one">Late for %d day</item>
        <item quantity="few">Late for %d days</item>
        <item quantity="many">Late for %d days</item>
        <item quantity="other">Late for %d days</item>
    </plurals>
    <!-- End Dashboard widgets -->

    <!--  Start Home widgets  -->
    <string name="home_widget_monthly_calendar_name">Monthly Calendar</string>
    <string name="home_widget_monthly_calendar_description">Check out this month\'s workout records</string>
    <string name="home_widget_progress_description">Check the latest progress of your physical condition</string>
    <string name="home_widget_daily_overview_name">Daily Overview</string>
    <string name="home_widget_daily_overview_description">View daily activity status</string>
    <!--  End Home widgets  -->

    <!-- Predefined replies (max 32 characters) -->
    <string name="default_predefined_reply_0">Yes</string>
    <string name="default_predefined_reply_1">No</string>
    <string name="default_predefined_reply_2">Thanks!</string>
    <string name="default_predefined_reply_3">I\'m exercising.</string>
    <string name="default_predefined_reply_4">I can\'t talk right now.</string>
    <!-- End Predefined replies -->

    <string name="empty_value_not_allowed">Empty value is not allowed</string>

    <string name="background_location_request_dialog_button">Request</string>
    <string name="background_location_request_allow_all_the_time">Allow all the time</string>
    <string name="device_icon">Device\'s icon</string>

    <!-- Avalanche info -->
    <string name="avalanche_info_header_1">Avalanche terrain</string>
    <string name="avalanche_info_beta"><sub>BETA</sub></string>
    <string name="avalanche_info_body_1">This map helps you to identify avalanche terrain when planning your backcountry adventures. The darker the color, the steeper the slope. Most avalanches occur on slopes between 30 and 45 degrees. Nevertheless, they can happen on slopes as flat as 25 degrees and as steep as 60 degrees.\n\nOn this map, the sun is shining from the south, which helps identify slopes affected by sunlight during the day. In addition to your route, remember to pay attention to the terrain above you.</string>
    <string name="avalanche_info_header_2">Constantly assess the slope steepness and snow conditions on the spot to stay safe!</string>
    <string name="avalanche_info_body_2">This map is created by using high-resolution elevation data from multiple countries. The accuracy of the data varies. The map should only be used as general guidance.</string>
    <!-- END Avalanche info -->

    <string name="add_description_and_tags">Add description and tags</string>

    <!-- Avalanche map legend -->
    <string name="avalanche_info_label">Avalanche terrain</string>
    <!-- END Avalanche map legend -->

    <string name="watch_activity_title">PAIRING</string>
    <string name="commute_distance_in">Distance in %s</string>
    <string name="commute_during_this_month">COMMUTES THIS MONTH</string>
    <string name="co2e_saved_dashboard">kg CO₂e saved</string>
    <string name="tag_commutes_automatically">Tag commutes automatically</string>
    <string name="tag_commutes_automatically_description"><![CDATA[Allow us to automatically tag all your runs, rides and walks that have a min. %s direct route between the start and endpoint as commutes. Start tracking the CO₂e emissions you save.]]></string>
    <string name="commute_auto_tagged_description_1"><![CDATA[With our new feature, you can tag workouts as <b>commutes</b> and track your reduced CO₂e emissions when you\'re burning calories instead of gas.]]></string>
    <string name="commute_auto_tagged_description_2"><![CDATA[To make things easier, we will <b>automatically tag</b> all your runs, rides and walks that have a min. %s direct route between the start and endpoint as commutes, starting from this workout:]]></string>
    <string name="commute_auto_tagged_turn_tagging_off">Turn this off in settings</string>
    <string name="commute_auto_tagged_keep_auto_tagging_on">Keep auto-tagging on</string>
    <string name="we_tagged_your_activity_as_commute">we tagged your %s as a commute</string>
    <string name="dialog_remove_tag_from_latest_workout">Do you also want to remove the tag from your most recent workout?</string>
    <string name="edit_the_activity">Edit the activity</string>
    <string name="tool_tip_start_tracking_saved_co2e">Start tracking your saved CO₂e emissions in the exercise summary &amp; dashboard.</string>
    <string name="auto_tagged_type_run">run</string>
    <string name="auto_tagged_type_walk">walk</string>
    <string name="auto_tagged_type_ride">ride</string>
    <string name="auto_tagged_type_general_workout">workout</string>

    <string name="next_page">Next page</string>
    <string name="previous_page">Previous page</string>
    <string name="commute_auto_tagged_min_distance_in_metric">500m</string>
    <string name="commute_auto_tagged_min_distance_in_imperial">0.3mi</string>
    <string name="add_tags">Add tags</string>
    <string name="dialog_title_remove_tag">Are you sure you want to remove this tag?</string>
    <string name="user_tags_dialog_suggested_tags">Suggested tags</string>
    <string name="user_tags_dialog_add_tags">Add tags</string>

    <string name="created_with_a_third_party_service">Created with a third-party service</string>

    <!-- In-app review -->
    <string name="thank_you">Thank you!</string>
    <string name="rate_it_now">Rate it now</string>
    <string name="ask_me_later">Ask me later</string>
    <string name="never_ask_again">Never ask again</string>
    <string name="in_app_review_ask_for_opinion_title">Your opinion matters to us</string>
    <string name="customer_support">Customer support</string>
    <!-- End In-app review -->

    <string name="download_offline_maps">Download offline maps</string>

    <string name="tts_lap_cadence">Lap cadence: %d .</string>
    <string name="tts_lap_max_heart_rate">Lap Max heart rate: %d.</string>
    <string name="tts_lap_duration">Lap duration %1$s</string>
    <string name="tts_current_heart_rate_with_current">Current Heart rate: %d .</string>
    <string name="tts_lap_heart_rate">Lap heart rate: %d.</string>
    <string name="apply_to_headset">APPLY TO HEADSET</string>

    <!--Edit SportInfo    -->
    <string name="nothing">None</string>
    <string name="sport_type">Activity type</string>
    <string name="sport_start_time">Start time</string>
    <string name="sport_info_title">Activity information</string>
    <string name="no_tracked_activity">No tracked activity</string>
    <string name="find_people_tips">After following a friend, you can view your friend\'s exercise records.\n\nTraining with friends is more fun, come and see which friends have joined Suunto!</string>

    <!--share-->
    <string name="share_fail">Share failed</string>
    <!--end share-->

    <string name="max_cadence">Max cadence</string>

    <string name="tags_impact_non_premium">Impact (%s)</string>

    <!--sleep-->
    <string name="sleep_duration">%1$sh Sleep</string>
    <string name="nap_duration">%1$sh Nap</string>
    <string name="sleep_duration_without_unit">%1$s Sleep</string>
    <string name="nap_duration_without_unit">%1$s Nap</string>
    <!--sleep End-->

    <!-- Phone Contacts   -->
    <string name="search_contact_hint">name/username/phone</string>

    <string name="login_with_password">Password login</string>
    <string name="login_with_passwordless">Passwordless login</string>
    <string name="phone_number_or_email">Phone number/Email</string>
    <string name="get_vertify_code">Verification code</string>
    <string name="login_sign_up_with_phone">Phone number Log in/Sign up</string>
    <string name="enter_the_code_sent_by_sms">Enter the code sent by SMS</string>
    <string name="verification_code">Verification code</string>
    <string name="check_verification_code_summary_from_phone">We have sent a code via SMS to %s.\nEnter the code here to verify your phone number.</string>
    <string name="check_verification_code_summary_from_phone_new">We have sent a code to %s.\nEnter the code here to verify your phone number.</string>
    <string name="did_not_receive_code">Didn\'t receive the code?</string>
    <string name="resend_verification_code">Resend code%s</string>
    <string name="bind_email">Add email address</string>
    <string name="enter_the_code_sent_by_email">Enter the code sent by Email</string>
    <string name="check_verification_code_summary_from_email">We have sent a code to %s. Enter the code here to verify your email.</string>
    <string name="input_new_password">New password</string>
    <string name="input_password_again">New password again</string>
    <string name="input_password_tips">Enter a combination of at least eight numbers, letters and special characters (like ! and &amp;).</string>
    <string name="recover_by_email">Recover by email</string>
    <string name="recover_by_phone_number">Recover by phone number</string>
    <string name="forget_password_title_email">We will send you a link to change your password.</string>
    <string name="forget_password_title_phone">Forgot password</string>
    <string name="forget_password_email_send_success">We have sent you an email with a password recovery link. It should arrive within minutes.</string>
    <string name="set_password_title">Set password</string>
    <string name="reset_password_title">Reset password</string>
    <string name="change_password_by_phone_summary">We will send a link to your phone number(%s) to change the password.</string>
    <string name="i_have_read_terms_and_policy">I have read and agreed\n%1s and %2s</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="recover_password">Recover password</string>
    <string name="terms_of_service">Terms of Service</string>
    <string name="incorrect_combination">Incorrect phone number/email and password combination</string>
    <string name="verify_str">Verify</string>
    <string name="two_password_inconsistent">The two passwords entered are inconsistent</string>
    <string name="change_successful">Change successful</string>
    <string name="password_format_is_error">Password format is wrong</string>
    <string name="phone_number_has_been_registered">phone number has been registered</string>
    <string name="email_has_been_registered">email address has already been registered</string>
    <string name="input_new_email">New email address</string>
    <string name="verification_code_has_expired">The verification code has expired or verification code is wrong</string>
    <string name="phone_number_not_registered">The phone number is not registered</string>
    <string name="search">Search</string>
    <string name="change_email">Change email</string>
    <string name="checking_spam_folder">Check your email’s spam folder.</string>

    <!--  Workout Planner  -->
    <string name="workout_planner_search_hint">Search programs</string>
    <string name="workout_planner_question_add_sports">Add sports</string>
    <string name="workout_planner_sports_picker_maximum_selections_reached">You have reached the maximum selections allowed. Please deselect an item to select a new one.</string>
    <string name="workout_planner_sports_picker_title">Select Sport (%1$s)</string>
    <string name="workout_planner_sports_picker_section_recent">Recent</string>
    <string name="workout_planner_sports_picker_section_all">All</string>
    <string name="workout_planner_summary_title">Summary</string>
    <string name="workout_planner_program_information">Program information</string>
    <string name="workout_planner_summary_description">Congratulations! Please go through the answers and then you are able to generate a new plan for you.</string>
    <string name="workout_planner_summary_start_program">Start this program</string>
    <string name="workout_planner_summary_no_selection">No selection</string>
    <string name="workout_planner_upcoming_workout_planned_rest_day">Planned rest day</string>
    <string name="workout_planner_zones_update_title">Keep your zones updated</string>
    <string name="workout_planner_zones_update_message">Ensure your heart rate zones are up to date on your watch. Adjust them in the watch settings and remember to sync with the Suunto app.</string>
    <string name="workout_planner_workout_targets">Workout targets</string>
    <string name="workout_planner_cardio_impacts">Cardio impacts</string>
    <string name="workout_planner_muscular_impacts">Muscular impacts</string>
    <string name="workout_planner_week_number">Week %d</string>
    <string name="workout_planner_no_survey_available">Survey not available, please retry.</string>
    <string name="workout_planner_invalid_program_id">Invalid program</string>
    <string name="workout_planner_intensity_zone_value">Zone %d</string>
    <string name="workout_planner_week_header">Schedule</string>
    <string name="workout_planner_done_header">Done</string>
    <string name="workout_planner_no_planned_workouts">No planned workouts</string>
    <string name="workout_planner_planned_value">Planned %s</string>
    <plurals name="workout_planner_number_of_weeks">
        <item quantity="one">%d week</item>
        <item quantity="few">%d weeks</item>
        <item quantity="many">%d weeks</item>
        <item quantity="other">%d weeks</item>
    </plurals>
    <string name="workout_planner_targets_this_week">Targets</string>
    <string name="workout_planner_program_generating_title">Plan generation in process</string>
    <string name="workout_planner_program_generating_description">Your Suunto AI Coach is creating a personalized multi-week plan just for you. This may take a little time as it\'s being built from scratch.\n\nFeel free to close this tab — we\'ll notify you as soon as your plan is ready!</string>
    <string name="workout_planner_elapsed_time_title">Elapsed time</string>
    <string name="workout_planner_target_training_load">Training load TSS</string>
    <string name="workout_planner_target_duration">Duration h</string>
    <string name="workout_planner_target_running">Running %1$s</string>
    <string name="workout_planner_chart_training_load">Training load</string>
    <string name="workout_planner_chart_duration">Duration</string>
    <string name="workout_planner_chart_running">Running</string>
    <string name="workout_planner_more_details">More details</string>
    <string name="workout_planner_less_details">Less details</string>
    <string name="workout_planner_more_targets">More targets</string>
    <string name="workout_planner_less_targets">Less targets</string>
    <string name="workout_planner_week_out_of_weeks">%1$d out of %2$d weeks</string>
    <string name="workout_planner_plan_end_date">Ends %s</string>
    <string name="workout_planner_plan_ended_date">Ended %s</string>
    <string name="workout_planner_plan_cancelled">Cancelled</string>
    <string name="workout_planner_plan_tag">Plan</string>
    <string name="workout_planner_personalize">Personalize this program</string>
    <string name="workout_planner_sports">Sports</string>
    <string name="workout_planner_plan">Plan</string>
    <string name="workout_planner_end_program">End plan</string>
    <string name="workout_planner_weekly_plan">Weekly plan</string>
    <string name="workout_planner_program_info">Plan info</string>
    <string name="workout_planner_targets_for">Targets for %s</string>
    <string name="workout_planner_week_compliance">Week compliance</string>
    <string name="workout_planner_compliance_1">Off-track</string>
    <string name="workout_planner_compliance_2">Unbalanced</string>
    <string name="workout_planner_compliance_3">Good</string>
    <string name="workout_planner_compliance_4">Excellent</string>
    <string name="workout_planner_my_old_plans_title">My past plans</string>
    <string name="workout_planner_my_old_plans_empty">No plans completed – time to begin your training</string>
    <string name="workout_planner_level">Level</string>
    <string name="workout_planner_level_any">Any</string>
    <string name="workout_planner_level_beginner">Beginner</string>
    <string name="workout_planner_level_intermediate">Intermediate</string>
    <string name="workout_planner_level_advanced">Advanced</string>
    <string name="workout_planner_category">Category</string>
    <string name="workout_planner_focus">Focus</string>
    <string name="workout_planner_duration">Duration</string>
    <string name="workout_planner_distance">Distance</string>
    <string name="workout_planner_training_load">Training load</string>
    <string name="workout_planner_avg_pace_target">Avg. pace</string>
    <string name="workout_planner_target_hr_zone">HR zone</string>
    <string name="workout_planner_target_pace_zone">Pace zone</string>
    <string name="workout_planner_target_power_zone">Power zone</string>
    <string name="workout_planner_intensity_zone">Intensity zone</string>
    <string name="workout_planner_impact_cardio">Cardio impact</string>
    <string name="workout_planner_impact_muscular">Muscular impact</string>
    <string name="workout_planner_event_info">Event info</string>
    <string name="workout_planner_event_info_ascent">Ascent</string>
    <string name="workout_planner_event_info_date">Date</string>
    <string name="workout_planner_event_info_terrain">Terrain</string>
    <string name="workout_planner_event_info_weather">Weather</string>
    <string name="workout_planner_personalized_details">Personalized details</string>
    <string name="workout_planner_personalize_program">Personalize the program</string>
    <string name="workout_planner_personalize_program_subtitle">These features are open for personalization</string>
    <string name="workout_planner_choose_your_own">Choose your own</string>
    <string name="workout_planner_customizable_sports">Note: You can also customize this program by adding your own sports.</string>
    <string name="workout_planner_personalize_a_program">Personalize a program for you</string>
    <string name="workout_planner_personalize_a_program_content">Hi, I’m your Suunto Coach. Choose a program, personalize it to your level, schedule, and sport preferences, and start training with your personal plan today!</string>
    <string name="workout_planner_onboarding_page_1_title">Suunto AI Coach is finally here</string>
    <string name="workout_planner_onboarding_page_1_content">Make your training plans smarter, faster, and fully personalized with our Suunto AI Coach. Whether you\'re training for your next big race, building long-term fitness, or just getting started, our AI Coach helps you create training plans tailored to your goals, schedule, and progress.</string>
    <string name="workout_planner_onboarding_page_2_title">How does Suunto AI coach work?</string>
    <string name="workout_planner_onboarding_page_2_content">1. Choose a program built by professionals at Suunto.\n\n2. Personalize it to match your goals and experience.\n\n3. Start training and get ready for the journey ahead.</string>
    <string name="workout_planner_onboarding_page_3_title">You are good to go!</string>
    <string name="workout_planner_onboarding_page_3_content">Get your training plan from Suunto AI Coach and start your journey towards reaching your targets!</string>
    <string name="workout_planner_onboarding_page_3_button">Start now!</string>
    <string name="workout_planner_plan_completed_title">Great job finishing your plan: %s</string>
    <string name="workout_planner_plan_completed_content">Congratulations, you made it. This isn\'t just a finish line — you are building a foundation. Let\'s see how far you can go from here.</string>
    <string name="workout_planner_info_targets">
        <![CDATA[
        <b>Note: To get the most out of your training, update your HR zones in the watch.</b><br/><br/>
        <b>How are your weekly targets generated?</b><br/>
        Your weekly targets are set by our Suunto AI Coach based on your questionnaire answers. It analyzes your level, goals and preferences to create personalized and achievable targets that support steady progress without overtraining<br/><br/>

        <b>How to follow your targets</b><br/>
        Your weekly targets guide your training by setting goals for workout duration, intensity, or specific activities. To stay on track:<br/>
        • Check your progress regularly in the Suunto app to see how you\'re advancing.<br/>
        • Balance your training by following the suggested workouts while listening to your body.<br/>
        • Adjust if needed — our Suunto AI Coach adapts your targets as you train, so focus on consistency rather than perfection.<br/><br/>
        By following your targets, you\'ll build steady progress and maintain motivation throughout your training journey.<br/><br/>

        <b>What kind of Targets will Suunto AI Coach give?</b><br/>
        Suunto AI Coach sets personalized targets based on your training goals and the questionnaire.<br/><br/>
        These include:<br/>
        • <b>TSS (Training Stress Score)</b> – a measure of workout intensity and load.<br/>
        • <b>Duration</b> – total training time for the week.<br/>
        • <b>Running Distance</b> – specific kilometer goals (only for programs with running).<br/>
        • <b>Cardio Impact</b> – how your training affects your cardiovascular system and endurance.<br/>
        • <b>Muscular Impact</b> – the strain on your muscles to ensure balanced training and recovery.<br/><br/>
        These targets help you train effectively, balancing effort and recovery for steady progress.
        ]]>
    </string>
    <string name="workout_planner_info_level">
        <![CDATA[
        <b>Why do we have different levels?</b><br/>
        Whether you\'re just getting started or you\'re an experienced athlete, having different levels ensures that your training plan matches your current fitness, experience, and goals.<br/><br/>

        Personalized Progression: The right level helps you train effectively and avoid injury.<br/><br/>
        Motivation &amp; Engagement: A well-matched challenge keeps training enjoyable.<br/><br/>
        Performance Optimization: Progressing through levels ensures long-term improvement.<br/><br/>

        <b>How to know your level?</b><br/>
        You can determine your level by considering:<br/><br/>

        Training history: How long and consistently have you been training?<br/><br/>
        Current fitness: Can you handle long or intense sessions with ease?<br/><br/>
        Training volume: How many hours per week do you train?<br/><br/>
        Goals &amp; experience: Are you looking to complete your first event or set a personal best?<br/><br/>

        <b>What are the 4 different levels?</b><br/>
        1. <b>Beginner</b> – New to structured training or exercising occasionally.<br/>
        2. <b>Novice</b> – Some experience but still developing consistency and endurance.<br/>
        3. <b>Intermediate</b> – Training regularly with moderate intensity and volume.<br/>
        4. <b>Advanced</b> – High training load, structured workouts, and performance-focused.
        ]]>
    </string>
    <string name="workout_planner_info_personalize">
        <![CDATA[
        <b>How to personalize a program?</b><br/>
        Everyone\'s schedule, fitness level, and goals are different—that\'s why your training program adapts to you. Depending on the program, the questions you\'re asked will vary. For example, if your program is designed for race preparation, you\'ll get questions related to race-specific training. Personalizing your plan ensures it fits your lifestyle while keeping you on track for progress.<br/><br/>

        With personalization, you can choose for example:<br/><br/>

        <b>Training hours per week</b> – Set how much time you can commit to training.<br/><br/>
        <b>Rest days</b> – Choose when you need recovery days to stay fresh and avoid burnout.<br/><br/>
        <b>Workout days</b> – Decide which days work best for your training sessions.<br/><br/>
        <b>Max effort day</b> – Pick the day when you can push your limits.<br/><br/>
        <b>Sports</b> – Focus on the sport(s) you want to improve in.<br/><br/>

        By adjusting these settings, you get a plan that aligns with your life, helping you stay consistent and reach your goals efficiently.
        ]]>
    </string>
    <string name="workout_planner_read_mode">Read more</string>
    <string name="workout_planner_info_read_more">
        <![CDATA[
        <b>Personalized Training Plans with Suunto AI Coach</b><br/>
        Suunto AI Coach creates a training plan tailored just for you. Simply answer a short questionnaire about your goals, training hours, sports, and preferences, and our Suunto AI Coach will generate a structured plan that fits your needs.<br/><br/>

        Using AI-designed and athlete-tested program structure, the Suunto AI Coach adjusts the plan based on the program you choose — whether you’re training for a race, improving fitness, or just getting started. Suunto AI Coach follows your progress, ensuring optimal results with smart, data-driven guidance.
        ]]>
    </string>
    <string name="workout_planner_cancel_survey_title">Stop your consultation with the Suunto AI Coach.</string>
    <string name="workout_planner_cancel_survey_message">If you cancel, this conversation won\'t be saved.</string>
    <string name="workout_planner_cancel_survey_go_back">Go back</string>
    <string name="workout_planner_cancel_survey_cancel">Cancel consultation</string>
    <string name="workout_planner_end_plan_dialog_title">End ongoing plan</string>
    <string name="workout_planner_end_plan_dialog_content">Are you sure you want to end your ongoing plan? Once you do this, you will need to personalize a new plan from our library.</string>
    <string name="workout_planner_end_plan_dialog_button">End ongoing plan</string>
    <string name="workout_planner_generation_failed">There was an issue generating your training plan. Please create a new one to get started.</string>
    <string name="workout_planner_generation_failed_retry">There was an issue generating your training plan. Please try again.</string>
    <string name="workout_planner_generation_succeeded">The Suunto AI Coach has successfully generated a new plan for you</string>
    <string name="workout_planner_view_my_plan">View my plan</string>

    <!-- Diary Graph highlight -->
    <string name="diary_graph_highlight_duration">Duration</string>
    <string name="diary_graph_highlight_steps">Steps</string>
    <string name="diary_graph_highlight_calories">kcal</string>
    <string name="diary_graph_highlight_vo2max">Estimated VO₂max</string>
    <string name="diary_graph_highlight_asleep">Asleep</string>
    <string name="diary_graph_highlight_average_sleep">Average sleep</string>
    <string name="diary_graph_highlight_nap">Nap</string>
    <string name="diary_graph_highlight_average_nap">Average Nap</string>
    <string name="diary_graph_highlight_total_time">Total time</string>
    <string name="privacy_edit_past_activity_title">Edit past activities\' privacy</string>
    <string name="privacy_edit_change_all_to">Change all to %s</string>
    <string name="privacy_edit_past_activity_to">Change all past activities to %s</string>
    <string name="past_activity_privacy_processing">Processing past activities\' privacy</string>
    <string name="privacy_edit_past_activity_tips">It may take a while to process. Are you sure you want to change all past activities to %s?</string>
    <string name="privacy_edit_past_activity_process_complete">Processing complete</string>

    <string name="sleep_graph_quality">Quality</string>
    <string name="sleep_graph_heart_rate">Heart rate</string>
    <string name="sleep_graph_spo2">Blood oxygen</string>
    <string name="sleep_graph_resources">Resources</string>
    <string name="step_graph_y_axis_value">%.2f k</string>

    <string name="summary_tab_title">Summary</string>
    <string name="diary_view_totals_btn_text">View totals</string>

    <string name="set_up_now">Set up now</string>
    <string name="toolbar_log_period">Log period</string>
    <string name="log_menstrual_cycle_period_error_title">Period is logged to the future</string>
    <string name="log_menstrual_cycle_period_error_message">Please choose a valid start and end date for your period.</string>

    <string name="day_view_cycle_day">Cycle day</string>
    <string name="day_view_period_day">Period day</string>
    <string name="toolbar_add_period">Add period</string>
    <string name="settings_menstrual_cycle">Menstrual cycle</string>
    <string name="settings_menstrual_cycle_show_predictions">Show predictions</string>
    <string name="settings_menstrual_cycle_stop_tracking_title">Stop tracking menstrual cycle</string>
    <string name="settings_menstrual_cycle_stop_tracking_description">Cycle predictions and cycle day marks for the future will be removed. All the cycles you have logged are kept. You can start tracking again any time.</string>
    <string name="settings_menstrual_cycle_stop_tracking">Stop tracking</string>
    <string name="settings_menstrual_cycle_stop_tracking_edit_description">Stop tracking menstrual cycle will remove cycle predictions and cycle day marks for the future. All the cycles you have logged are kept. You can start tracking again any time.</string>
    <string name="settings_menstrual_cycle_cycle_length_edit_description">Cycle prediction is primarily based on the periods you have logged. When logged data is not sufficient, cycle length setting will be used to predict the start of your next cycle.</string>
    <string name="settings_menstrual_cycle_average_cycle_length">According to your data, currently your average cycle length is %d days.</string>
    <string name="settings_menstrual_cycle_period_length_edit_description">Period prediction is primarily based on the periods you have logged. When logged data is not available, the period length setting will be used to predict the duration of your next cycle.</string>
    <string name="settings_menstrual_cycle_average_period_length">According to your data, currently your average period length is %d days.</string>
    <string name="alarm_menstrual_cycle_predict_title">Next cycle is coming</string>
    <string name="alarm_menstrual_cycle_predict_content">Likely starts in %d days</string>
    <string name="alarm_menstrual_cycle_log_title">Has your period started?</string>
    <string name="alarm_menstrual_cycle_log_content">Remember to log the days</string>
    <string name="alarm_menstrual_cycle_log_action_remind_later">Remind me later</string>
    <string name="settings_menstrual_cycle_tracking_stopped_description">See your training data against menstrual cycles. A few simple steps to help us provide period predictions for you.</string>

    <string name="menstrual_cycle_keep_predictions_coming">Keep predictions coming</string>
    <string name="menstrual_cycle_turn_predictions_off">Turn predictions off</string>
    <string name="menstrual_cycle_become_irregular_title">Cycles seem irregular</string>
    <string name="menstrual_cycle_become_irregular_document">
        # Cycles seem irregular\n

        Based on the cycles you have logged, your cycles appear to be irregular.\n\n

        This is common and can be influenced by various factors such as stress, lifestyle changes, or hormonal fluctuations. We recommend continuing to track your cycles to observe patterns and consider consulting with a healthcare provider for personalized advice.\n\n

        Unfortunately, we are not able to predict irregular cycles. You can:\n
        * turn the predictions off, or\n
        * use the **default cycle length** for future predictions. You can check and adjust it in [cycle length setting](com.stt.android.MENSTRUAL_CYCLE_SETTINGS).\n\n

        Once your cycles become regular, we will notify you and the predictions will be based on your input.
    </string>
    <string name="menstrual_cycle_become_regular_document">
        # Your cycles are regular again!\n

        Based on the recent cycles you have logged, your cycles have become regular again, which is a positive sign of overall health.\n\n

        Your cycle predictions are now based on your input.
    </string>

    <string name="settings_menstrual_cycle_notify_upcoming_period">Notify me of my upcoming period</string>
    <string name="settings_menstrual_cycle_remind_log_period">Remind me to log period</string>
    <string name="menstrual_cycle_period_started_on">Period started on</string>
    <string name="menstrual_cycle_duration">Duration</string>

    <string name="workout_values_grid_view_more">View more</string>
    <string name="workout_values_grid_view_less">View less</string>
    <string name="workout_values_stats">Stats</string>
    <string name="no_tank_pod">No tank pressure data.</string>
    <string name="multisport_details">DETAILS</string>

    <string name="tap_the_map_altitude_graph">Tap the map to continue routing</string>

    <string name="share_3d_video_activity">Share 3D video link to activity</string>

    <!-- updated settings text -->
    <string name="settings_activity_training">Activity &amp; training</string>
    <string name="settings_outdoor">Outdoor</string>
    <string name="settings_general_pair_hr_sensor">Pair HR sensor</string>
    <string name="settings_about_app">About app</string>
    <string name="settings_privacy_setting">Privacy settings</string>
    <string name="settings_marketing_permission">Marketing permission</string>
    <string name="target_setting_preference_title">Target settings</string>
    <string name="target_setting_page_title">Target setting</string>
    <string name="settings_logout">Logout</string>
    <string name="settings_general_hr_intensity_zones">HR &amp; intensity zones</string>
    <string name="settings_birth_date">Birth date</string>
    <string name="settings_user_profile_guide_new">These are needed for calculating your calorie consumption.</string>

    <string name="go_back_to_previous_settings">Go back to previous settings</string>
    <string name="reset_to_default">Reset to default</string>
    <string name="default_hr_zones">Default HR zones</string>
    <string name="zone_toggle_description_running">When on, you can customise exclusive heart rate zones specifically for running</string>
    <string name="zone_toggle_title_running">Use special HR zones for running</string>
    <string name="zone_toggle_description_cycling">When on, you can customise exclusive heart rate zones specifically for cycling</string>
    <string name="zone_toggle_title_cycling">Use special HR zones for cycling</string>
    <string name="hr_zones_type">HR zones type</string>
    <string name="max_hr_zones">Max HR zones</string>
    <string name="running_hr_zones">Running HR zones</string>
    <string name="cycling_hr_zones">Cycling HR zones</string>
    <string name="for_all_sports">For all sports</string>
    <string name="hr_zones">HR zones</string>
    <string name="running_pace_zones">Running pace zones</string>
    <string name="running_power_zones">Running power zones</string>
    <string name="cycling_power_zones">Cycling power zones</string>
    <string name="pace_zones">Pace zones</string>
    <string name="pace_zones_summary">Available for running related sports</string>
    <string name="power_zones_summary">Available for running &amp; cycling</string>
    <string name="running_power_zones_summary">Available for running related sports</string>
    <string name="cycling_power_zones_summary">Available for cycling related sports</string>
    <string name="hr_zones_intensity_title"><![CDATA[HR & Intensity zones]]></string>
    <string name="power_zones">Power zones</string>
    <string name="hr_reserve_zones">HR reserve zones</string>
    <string name="hr_threshold_zones">Lactate threshold HR zones</string>
    <string name="zone_zone1">Zone 1</string>
    <string name="zone_zone2">Zone 2</string>
    <string name="zone_zone3">Zone 3</string>
    <string name="zone_zone4">Zone 4</string>
    <string name="zone_zone5">Zone 5</string>


    <string name="zone_rest_hr_zone5_title">Maximal</string>
    <string name="zone_rest_hr_zone4_title">Very hard</string>
    <string name="zone_rest_hr_zone3_title">Hard</string>
    <string name="zone_rest_hr_zone2_title">Moderate</string>
    <string name="zone_rest_hr_zone1_title">Easy</string>

    <string name="zone_rest_hr_zone5_value">LTHR</string>
    <string name="zone_max_hr_zone4_value">Anaerobic threshold</string>
    <string name="zone_hr_zone3_value">Threshold 3</string>
    <string name="zone_hr_zone2_value">Aerobic threshold</string>
    <string name="zone_hr_zone1_value">Threshold 1</string>

    <string name="zone_max_hr_zone5_title">VO2 Max</string>
    <string name="zone_max_hr_zone4_title">Anaerobic</string>
    <string name="zone_max_hr_zone3_title">Light anaerobic</string>
    <string name="zone_max_hr_zone2_title">Aerobic</string>
    <string name="zone_max_hr_zone1_title">Light aerobic</string>


    <string name="zone_rest_hr">Resting HR</string>
    <string name="zone_limit">%s limit</string>


    <string name="zone_reset_value_title">Reset the value</string>
    <string name="zone_reset_value_content">If you confirm the reset, these values will be restored to the default state.</string>

    <string name="zone_confirm">CONFIRM</string>

    <string name="zone_go_back_previous_title">Go back to previous settings</string>
    <string name="zone_go_back_previous_content">If you return to the previous settings, your values will revert to the last saved state.</string>

    <string name="zone_update_hr_values_below_too_title">Update the following zone HR values too?</string>
    <string name="zone_update_hr_values_below_too_content_max_hr">Would you like to automatically calculate and adjust the following zone HR values based on the MAX HR you\'ve just set? </string>
    <string name="zone_update_hr_values_below_too_content_rest_hr">Would you like to automatically calculate and adjust the following zone HR values based on the Resting HR you\'ve just set? </string>
    <string name="zone_update_hr_values_below_too_content_lthr">Would you like to automatically calculate and adjust the following zone HR values based on the LTHR you\'ve just set? </string>


    <string name="current_version_prefix">Current version %s</string>
    <string name="old_versions">Old versions</string>
    <string name="view_in_suunto_com">View in suunto.com</string>
    <string name="no_record">No record</string>
    <string name="competition_target_select_title">Select competition target</string>
    <string name="assessment_attention">Attention</string>
    <string name="birth_year">Birth year</string>
    <string name="understand">Understood</string>

    <plurals name="more_comments">
        <item quantity="one">%d more comment</item>
        <item quantity="few">%d more comments</item>
        <item quantity="many">%d more comments</item>
        <item quantity="other">%d more comments</item>
    </plurals>

    <string name="error_deleted_by_user">The user has deleted this record, so it cannot be compared.</string>
    <string name="error_forbidden_by_user">This user has not made the record public, so it cannot be compared.</string>
    <string name="competition_usernames">%1$s vs %2$s</string>
    <string name="competition_show_less">Show less</string>
    <string name="competition_show_more">Show more</string>
    <string name="me">Me</string>
    <string name="vs">Vs</string>
    <string name="comparison_details_data_title">Comparison of detailed data</string>
    <string name="unfinished">Unfinished</string>

    <!-- new dashboard -->
    <string name="dashboard_widget_title_activity">Activity</string>
    <string name="dashboard_widget_title_commute">Commutes</string>
    <string name="dashboard_widget_title_max_vo2">VO₂max</string>
    <string name="dashboard_widget_max_vo2_state_superior">Superior</string>
    <string name="dashboard_widget_max_vo2_state_excellent">Excellent</string>
    <string name="dashboard_widget_max_vo2_state_goode">Good</string>
    <string name="dashboard_widget_max_vo2_state_fair">Fair</string>
    <string name="dashboard_widget_max_vo2_state_poor">Poor</string>
    <string name="dashboard_widget_max_vo2_state_very_poor">Very poor</string>
    <string name="dashboard_start_workout">Start workout</string>

    <string name="widget_step_subtitle">%s steps of target</string>
    <string name="widget_of_target">%s of target</string>
    <string name="widget_on_target">On target</string>
    <string name="widget_now">Now</string>
    <string name="widget_last_night">Last night</string>
    <string name="widget_no_data_subtitle">No data</string>
    <string name="widget_n_day_avg_bpm">%1$d-day avg %2$d bpm</string>
    <string name="widget_no_avg_bpm">No avg data</string>
    <string name="widget_min_heart_rate">Min. daytime HR</string>
    <string name="widget_hrv_normal">Normal</string>
    <string name="widget_hrv_above_normal">Above normal</string>
    <string name="widget_hrv_below_normal">Below normal</string>
    <string name="widget_hrv_no_baseline">No baseline</string>
    <string name="widget_avg_heart_rate">Avg %d bpm</string>
    <string name="widget_feature_not_supported">Your watch doesn\'t support this feature</string>

    <string name="dashboard_latest_activity">Latest activity</string>
    <string name="dashboard_latest_activity_dismissed_snackbar">Activity dismissed from dashboard</string>
    <string name="dashboard_show_more">Show more</string>
    <string name="dashboard_latest_activity_placeholder_description">Your new activities will show up in this section.\nGet out there and make some new ones!</string>

    <string name="resource_widget_stress_state_active">Active</string>
    <string name="resource_widget_stress_state_inactive">Inactive</string>
    <string name="resource_widget_stress_state_stressful">Stressed</string>
    <string name="resource_widget_stress_state_recovering">Recovering</string>

    <string name="dashboard_widget_daily_heart_rate_name">Heart rate</string>
    <string name="dashboard_widget_description_daily_heart_rate">Today\'s heart rate range, average, and trend</string>

    <string name="dashboard_widget_vo2max_name">VO₂max</string>
    <string name="dashboard_widget_recovery_name">Recovery</string>
    <string name="dashboard_widget_description_vo2max">Latest VO₂max data and status</string>
    <string name="dashboard_widget_description_recovery">Today\'s recovery status</string>
    <string name="dashboard_widget_description_tss">This week\'s training load and trend compared to the previous period</string>

    <!--headphone suunto coach    -->
    <string name="suunto_coach_breaststroke_percent_more_than_80">Your training session primarily focused on breaststroke.</string>
    <string name="suunto_coach_breaststroke_percent_less_than_80">You used multiple strokes during your training, with breaststroke being the main one.</string>
    <string name="suunto_coach_breaststroke_head_angle_more_than_40">During your glide, your head was tilted too far back, which may increase drag. When swimming breaststroke, keep your gaze slightly ahead toward the pool bottom to maintain a neutral head position.</string>
    <string name="suunto_coach_breaststroke_head_angle_less_than_40">Your head position was good while moving forward in the water, maintaining a streamlined posture. Keep up the good work!</string>
    <string name="suunto_coach_breaststroke_avg_breath_angle_more_than_55">Your head raise during breathing was too high, which may create unnecessary resistance. In breaststroke, coordinate with your stokes and raise your head just enough to clear your mouth and nose above the water.</string>
    <string name="suunto_coach_breaststroke_avg_breath_angle_less_than_55">Your head movement during breathing was well-controlled, minimizing unnecessary drag. Keep up with this approach!</string>
    <string name="suunto_coach_breaststroke_max_angle_more_than_70">During one of your breaths, your head  raise exceeded the recommended angle, which may interrupt the flow of your glide. Avoid over-lifting your head when breathing.</string>
    <string name="suunto_coach_breaststroke_max_angle_less_than_70">During your single breath, the head lift was within the recommended range, and your breaststroke was smooth and continuous.</string>
    <string name="suunto_coach_freestyle_percent_more_than_80">Your training session primarily focused on freestyle.</string>
    <string name="suunto_coach_freestyle_percent_less_than_80">You used multiple strokes during your training, with freestyle being the main one.</string>
    <string name="suunto_coach_freestyle_head_angle_more_than_40">When moving forward in the water, your head was tilted too far back, which may increase drag. When swimming freestyle, keep your gaze slightly ahead toward the pool bottom to maintain a neutral head position.</string>
    <string name="suunto_coach_freestyle_head_angle_less_than_40">Your head position was good while moving forward in the water, maintaining a streamlined posture while moving forward. Keep up the good work!</string>
    <string name="suunto_coach_freestyle_avg_breath_angle_more_than_120">Your head rotation during breathing was too wide, which may lead to unnecessary energy consumption. In freestyle, coordinate with your torso and rotate your head just enough to clear your mouth and nose above the water.</string>
    <string name="suunto_coach_freestyle_avg_breath_angle_less_than_120">Your head rotation during breathing was well-controlled and your torso rotation was ideal, minimizing unnecessary drag. Keep up the good work!</string>
    <string name="suunto_coach_freestyle_max_angle_more_than_140">During one of your breaths, your head rotation exceeded the recommended angle, which may disrupt the flow of your freestyle stroke. Avoid over-rotating your head when breathing.</string>
    <string name="suunto_coach_freestyle_max_angle_less_than_140">During your single breath, the head rotation was within the recommended range, and your freestyle was smooth and continuous.</string>

    <string name="suunto_plus_store_training_plan_how_to_use">How to use</string>

    <!--  App Updates -->
    <string name="app_updates_version">Version %s</string>
    <string name="app_updates_title">App update</string>
    <string name="app_updates_old_versions_title">Old versions</string>
    <string name="app_updates_version_note_title">Version note</string>
    <string name="app_updates_update_available">Update available</string>
    <string name="app_updates_latest_version">Latest version</string>
    <string name="app_updates_update_now">Update now</string>
    <string name="app_updates_confirm_dialog_title">Leaving the app and opening \"%s\"</string>
    <string name="app_updates_google_play_store">Google Play</string>
    <string name="app_updates_app_store">App Store</string>
    <string name="app_updates_open">Open</string>
    <string name="app_updates_error">Failed to connect with the service</string>
    <string name="app_updates_error_tips">Something went wrong. Please try again.</string>
    <string name="app_updates_store_not_found">App store app haven\'t installed</string>

    <!--  App Update  -->
    <string name="app_update_force_update_title">Mandatory update required</string>
    <string name="app_update_force_update_message">A critical update is required to continue using the App.</string>
    <string name="app_update_recommend_update_title">New version update reminder</string>
    <string name="app_update_recommend_update_message">To ensure the best experience, a major update is now available!</string>


    <!--Recovery state-->
    <string name="recovery_state_title">Recovery state</string>
    <string name="recovery_state_limited">Limited recovery</string>
    <string name="recovery_state_poor">Poor recovery</string>
    <string name="recovery_state_fair">Fair recovery</string>
    <string name="recovery_state_good">Good recovery</string>
    <string name="recovery_state_optimal">Optimal recovery</string>
    <string name="recovery_state_no_data">Track data</string>
    <string name="recovery_state_not_enough_data">Not enough data</string>
    <string name="recovery_state_zones_header">Recovery states:</string>
    <string name="recovery_state_limited_tips">Your recovery is very low, which may indicate high fatigue. Awareness of this can help in managing your training.</string>
    <string name="recovery_state_poor_tips">Your recovery is below optimal, suggesting your body is still in the process of adjusting.</string>
    <string name="recovery_state_fair_tips">Your recovery is moderate, which may indicate ongoing adaptation.</string>
    <string name="recovery_state_good_tips">Your recovery is at a good level, indicating a well-recovered state for training.</string>
    <string name="recovery_state_optimal_tips">Your recovery is excellent, suggesting full recovery and readiness for high performance.</string>
    <string name="recovery_state_no_data_tips">For optimal recovery tracking, wear your Suunto watch during training, daily activities, and sleeping to measure HRV and sleep.</string>
    <string name="recovery_state_limited_for_widget">Limited</string>
    <string name="recovery_state_poor_for_widget">Poor</string>
    <string name="recovery_state_fair_for_widget">Fair</string>
    <string name="recovery_state_good_for_widget">Good</string>
    <string name="recovery_state_optimal_for_widget">Optimal</string>

    <!-- SummaryItem groups -->
    <string name="summary_group_depth" translatable="false">Depth</string>
    <string name="summary_group_duration" translatable="false">Duration</string>
    <string name="summary_group_distance" translatable="false">Distance</string>
    <string name="summary_group_pace" translatable="false">Pace</string>
    <string name="summary_group_elevation" translatable="false">Elevation</string>
    <string name="summary_group_speed" translatable="false">Speed</string>
    <string name="summary_group_verticalspeed" translatable="false">Vertical speed</string>
    <string name="summary_group_power" translatable="false">Power</string>
    <string name="summary_group_heartrate" translatable="false">Heart rate</string>
    <string name="summary_group_physiology" translatable="false">Physiology</string>
    <string name="summary_group_climbs" translatable="false">Climbs</string>
    <string name="summary_group_downhill" translatable="false">Downhill</string>
    <string name="summary_group_technique" translatable="false">Technique</string>
    <string name="summary_group_configuration" translatable="false">Configuration</string>
    <string name="summary_group_gas" translatable="false">Gases</string>
    <string name="summary_group_catch" translatable="false">Catch</string>
    <string name="summary_group_conditions" translatable="false">Conditions</string>
    <string name="summary_group_experience" translatable="false">Experience</string>
    <string name="summary_group_other" translatable="false">Other</string>
    <!-- END SummaryItem groups -->

    <!--  App profile -->
    <string name="profile_name">Profile</string>
    <string name="profile_description_default">Say something about yourself.</string>
    <string name="edit_bio">Edit bio</string>
    <string name="leaderboard">Leaderboard</string>
    <string name="achievements">Achievements</string>
    <string name="all_activities_title">All activities</string>
    <string name="photo">Photos</string>
    <string name="posts">Posts</string>
    <string name="media">Media</string>
    <string name="check_activities">Check activities</string>
    <string name="settings_repair_service">Repair service</string>
    <string name="settings_contact_customer_service">Contact customer service</string>
    <string name="settings_user_settings_title">User settings</string>
    <string name="please_enter_to_characters">Please enter %1$d to %2$d characters</string>
    <string name="maximum_characters">Maximum %d characters</string>
    <string name="user_settings_username">Username</string>
    <string name="user_settings_display_location">Display Location tag</string>
    <string name="user_settings_bio">Bio</string>
    <string name="user_settings_cover_photo">Cover photo</string>
    <string name="settings_user_profile_guide_new_v2">These are needed for calculating your calorie consumption. You can modify these settings in your watch.</string>
    <string name="user_settings_edit_name">Edit name</string>
    <string name="minimum_2_characters">Minimum 2 characters</string>
    <string name="name_regex_not_matches">Only letters, numbers, spaces, emojis, and underscores are allowed.</string>
    <string name="profile_buy_premium">Buy Premium</string>
    <string name="selected">Selected</string>
    <string name="crop_avatar_button">Set as profile photo</string>
    <string name="crop_cover_button">Set as cover photo</string>
    <string name="user_settings_sensitive_words_error">Failed to save. The content contains sensitive words.</string>
    <plurals name="user_profile_activity_count">
        <item quantity="one">%1$s activity</item>
        <item quantity="few">%1$s activities</item>
        <item quantity="many">%1$s activities</item>
        <item quantity="other">%1$s activities</item>
    </plurals>
    <string name="load_failed_tap_retry">Loading failed. Tap here to try again.</string>
    <string name="loaded_all">All content loaded.</string>
    <string name="loading">Loading...</string>

    <string name="top_activities">Top activities</string>
    <string name="only_show_public_activities">Only public activities are shown.</string>
    <string name="user_profile_total_duration">Duration</string>

    <string name="crop_image_activity_title">Preview</string>
    <string name="crop_image_menu_crop">Crop</string>
    <string name="weekly_goal_bottom_sheet_title">Weekly training target</string>

    <plurals name="workout_details_repeat_count">
        <item quantity="one">%1$d rep</item>
        <item quantity="many">%1$d reps</item>
        <item quantity="other">%1$d reps</item>
    </plurals>

    <string name="friends">Friends</string>
    <string name="discover">Discover</string>
    <string name="find_friends">Find friends</string>
    <string name="discover_friends_title">People you might know</string>
    <string name="phone_contacts">Phone contacts</string>
    <string name="facebook_friends">Facebook friends</string>
    <string name="invite_friends">Invite friends</string>
    <string name="my_following_prefix">My following (%d)</string>
    <string name="my_followers_prefix">My followers (%d)</string>
    <string name="no_people_discovered">Follow others to make sports more fun!</string>
    <string name="no_followers_new">No one\'s following you yet.</string>
    <string name="no_following_new">You haven\'t followed anyone yet.</string>
    <string name="accept">Accept</string>
    <string name="ignore">Ignore</string>

    <string name="search_workouts_hint">Activities, tags, date, description</string>
    <string name="search_own_workouts_tips">Your own activities only</string>
    <string name="search_friends_hint">Name, username</string>
    <string name="search_phone_contacts_empty">No results found.</string>
    <string name="dialog_unfollow_title">Are you sure you want to unfollow this user?</string>
    <string name="user_removed_tips">Successfully removed.</string>
    <string name="user_reported_tips">Your report has been received.</string>
    <string name="user_duplicate_reported_tips">Please do not report repeatedly.</string>
    <string name="dialog_title_remove_follower">Remove this user?</string>
    <string name="dialog_message_remove_follower">This person will be removed from your follower list, and they won\'t be notified.</string>
    <string name="dialog_title_block_user">Block this user?</string>
    <string name="dialog_message_block_user">They won\'t be able to follow you or see your profile or activity. You won\'t see theirs either.</string>
    <string name="dialog_title_report_user">Report this user?</string>
    <string name="dialog_message_report_user">This action can\'t be undone. Please proceed with caution.</string>
    <string name="blocked_tips">You\'ve blocked this user and can\'t view their profile.</string>
    <string name="invite_friends_share_title">Join me on %s App.</string>
    <string name="invite_friends_share_description">It\'s an easy and free way to keep up with my sports and to track your own activities.</string>
    <string name="no_people_found">No people found yet.</string>

    <string name="delete_account_enter_the_code">Enter the code</string>
    <string name="delete_account_description">Deleting your account will permanently erase your profile and all workout and activity data.\nTo confirm, we\'ll send a verification code via SMS to (%s).</string>
    <string name="delete_account_verification_code_description">We\'ve sent a verification code to %s.\nEnter the code below to verify your phone number.</string>
    <string name="delete_account_send_code">Send code</string>
    <string name="delete_account_too_many_request">Too many requests. Please try again later.</string>
    <string name="delete_account_error_verification_code">Incorrect code. Please try again.</string>
    <string name="delete_account_delete_confirm_message">Once deleted, the account cannot be recovered.</string>
    <string name="delete_account_resend">Resend</string>
    <string name="delete_account_resend_countdown">Resend (%ds)</string>

    <!-- Top routes -->
    <string name="add_favorite">favorite</string>

    <string name="public_routes">Public routes</string>

    <!-- Offline maps -->
    <string name="offline_maps_title">Offline maps</string>

    <!-- New_home_onboarding -->
    <string name="new_home_onboarding_title_2">New homepage designed just for you</string>
    <string name="new_home_onboarding_title_3">Activities are now in their own section</string>
    <string name="new_home_onboarding_title_4">New training zone with smarter insights</string>
    <string name="new_home_onboarding_title_5">New map with a cleaner interface</string>
    <string name="new_home_onboarding_title_6">Refreshed profile layout</string>
    <string name="new_home_onboarding_description_2">Easily customize widgets and dive into clearer, more relevant insights with comparison views.</string>
    <string name="new_home_onboarding_description_3">Your activities have their own space. View them on the dashboard and manage with a swipe or a tap.</string>
    <string name="new_home_onboarding_description_4">Track your full training journey with clearer structure and expanded time views.</string>
    <string name="new_home_onboarding_description_5">A cleaner interface and quicker access to popular routes make navigating smoother than ever.</string>
    <string name="new_home_onboarding_description_6">Your achievements and activity history are now easier to access in a clean, organized, and visually improved profile.</string>
    <string name="new_home_onboarding_button_1">Show me</string>
    <string name="new_home_onboarding_button_6">Let\'s go</string>
    <!-- End New home onboarding -->

    <!-- Badges -->
    <string name="main_badges_screen_title">Badges</string>
    <string name="my_badges_title">My badges</string>
    <plurals name="badges_earned">
        <item quantity="one">%1$s badge earned</item>
        <item quantity="few">%1$s badges earned</item>
        <item quantity="many">%1$s badges earned</item>
        <item quantity="other">%1$s badges earned</item>
    </plurals>
    <string name="badges_earned_ranking">No.%1$s earned this badge</string>
    <string name="acquisition_time_summary_badges">Earned on %1$s</string>
    <string name="total_activities_badges">Total activities</string>
    <string name="total_workoutDays_badges">Total workout days</string>
    <string name="total_ascent_badges">Total ascent</string>
    <string name="total_duration_badges">Total duration</string>
    <string name="total_distance_badges">Total distance</string>
    <string name="max_pace_badges">Max. pace</string>
    <string name="max_distance_badges">Max. distance</string>
    <string name="max_cycling_speed_badges">Max. cycling speed</string>
    <string name="max_diving_depth_badges">Max. diving depth</string>
    <string name="max_speed_badges">Max. speed</string>
    <string name="max_duration_badges">Max. duration</string>
    <string name="description_badges">Description</string>
    <string name="explore_more_badges">Explore more</string>
    <string name="activity_times">Activities</string>
    <string name="progress_items">%1$s of %2$s </string>

    <!--  Sleep Quality  -->
    <string name="sleep_quality_arg_sleep_hr">Avg. sleep HR</string>
    <string name="sleep_quality_min_sleep_hr">Min. sleep HR</string>
    <string name="sleep_quality_arg_sleep_hrv">Avg. sleep HRV</string>
    <string name="sleep_quality_max_sleep_spo2">Max. sleep SpO₂</string>
    <string name="sleep_quality_max_altitude">Max %d</string>
    <string name="sleep_time_duration_of_target">%s of target</string>
    <string name="sleep_time_duration_of_sleep">%s Sleep</string>
    <string name="sleep_time_duration_of_nap">%s Nap</string>
    <string name="sleep_wake_up_resources_title">Wake-up resources</string>
    <string name="sleep_wake_up_resources_gained_during_sleep">gained during sleep</string>
    <string name="sleep_wake_up_resources_lost_during_sleep">lost during sleep</string>

    <!-- Weekly training bottom sheet -->
    <string name="weekly_goal_bottom_sheet_training_plan_enabled">Adjusting weekly goal is not
        possible while training plan is in use.</string>
    <string name="weekly_goal_bottom_sheet_training_plan_title">Adaptive training guidance</string>
    <string name="weekly_goal_bottom_sheet_training_plan_message">Personal training plan with
        real-time guidance to improve your fitness.</string>
    <!-- Weekly training bottom sheet -->

    <!-- Activity Data Goals -->
    <string name="daily_goal_setting_title_2_sleep">Time in bed</string>
    <!-- Activity Data Goals -->

</resources>
