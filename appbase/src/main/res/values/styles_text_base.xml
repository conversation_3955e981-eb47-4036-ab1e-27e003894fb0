<?xml version="1.0" encoding="utf-8"?>
<resources>


    <!--Here are all common button styles for BOTH BUILD VARIANTS.
        Styles include base styles that are extended with variant-specific styles, and common styles that are applied directly in both variants.
        Base themes are *not* intended to be used directly in views. -->


    <!-- Base style for the data labels -->
    <style name="BaseDatalabel" parent="TextAppearance.AppCompat.Display1">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>


    <!-- Base style for the headers -->
    <style name="BaseHeaderLabel" parent="TextAppearance.AppCompat.Headline">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>


    <!-- Base style for toolbar header -->
    <style name="BaseToolbarHeader">
        <item name="android:layout_gravity">center</item>
        <item name="android:textSize">@dimen/text_size_medium</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>


    <!-- Base style for save activity summary headers -->
    <style name="ActivitySummaryHeader" parent="HeaderLabel.Medium">
    </style>

    <!-- Base style for save activity summary value texts -->
    <style name="ActivitySummaryValue" parent="Body.Larger">
    </style>

    <!-- Base style for regular text -->
    <style name="BaseBody" parent="TextAppearance.AppCompat.Medium">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textColorLink">@color/accent</item>
    </style>
</resources>
