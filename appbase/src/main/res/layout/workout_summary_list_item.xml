<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    style="@style/SectionLayout"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="?suuntoItemBackgroundColor"
    android:theme="@style/WhiteTheme">

    <View
        android:id="@+id/gradientBackground"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_spacing_large"
        android:background="@drawable/section_divider_gradient"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/workoutSummaryTimeFrame"
        android:layout_width="@dimen/workout_summary_timestamp_width"
        android:gravity="center_horizontal"
        android:paddingStart="@dimen/size_spacing_small"
        android:paddingEnd="@dimen/size_spacing_small"
        app:layout_constraintBottom_toBottomOf="@+id/summaryProgress"
        app:layout_constraintEnd_toEndOf="@id/timeBarrier"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/summaryTotalValue"
        tools:text="@string/this_month"
        style="@style/Body.Small"/>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/timeBarrier"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        app:barrierDirection="right"
        app:constraint_referenced_ids="workoutSummaryTimeFrame"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/summaryTotalValue"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/summaryTotalValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:layout_marginTop="@dimen/size_spacing_medium"
        app:layout_constraintBottom_toTopOf="@+id/summaryProgress"
        app:layout_constraintStart_toEndOf="@id/timeBarrier"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="04:06:40"
        style="@style/Body.Large.Bold"/>

    <TextView
        android:id="@+id/summaryTotalLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_xsmall"
        app:layout_constraintBottom_toBottomOf="@+id/summaryTotalValue"
        app:layout_constraintStart_toEndOf="@id/summaryTotalValue"
        tools:text="km"
        style="@style/Body.Small"/>

    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/summaryProgress"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_spacing_xsmall"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_xsmall"
        app:layout_constraintBottom_toTopOf="@+id/workoutSummaryDistance"
        app:layout_constraintEnd_toStartOf="@+id/firstActivityIcon"
        app:layout_constraintStart_toStartOf="@id/summaryTotalValue"
        app:layout_constraintTop_toBottomOf="@id/summaryTotalValue"
        tools:progress="65"
        style="@style/DiarySummaryListItemProgressBar"/>

    <TextView
        android:id="@+id/workoutSummaryDistance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:paddingTop="@dimen/size_spacing_small"
        android:singleLine="true"
        app:layout_constraintVertical_bias="0"
        app:layout_goneMarginBottom="@dimen/size_spacing_medium"
        app:layout_constraintStart_toStartOf="@id/summaryProgress"
        app:layout_constraintTop_toBottomOf="@id/summaryProgress"
        app:layout_constraintBottom_toTopOf="@+id/summaryProgress"
        tools:text="2309 km"
        style="@style/Body.Small"/>

    <TextView
        android:id="@+id/workoutSummaryEnergy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:paddingTop="@dimen/size_spacing_small"
        android:singleLine="true"
        app:layout_constraintEnd_toStartOf="@+id/workoutSummaryBpm"
        app:layout_constraintStart_toEndOf="@id/workoutSummaryDistance"
        app:layout_constraintTop_toBottomOf="@id/summaryProgress"
        tools:text="4987 kcal"
        style="@style/Body.Small"/>

    <TextView
        android:id="@+id/workoutSummaryBpm"
        android:layout_width="wrap_content"
        android:ellipsize="end"
        android:paddingTop="@dimen/size_spacing_small"
        android:singleLine="true"
        app:layout_constraintEnd_toEndOf="@+id/summaryProgress"
        app:layout_constraintTop_toBottomOf="@id/summaryProgress"
        tools:text="123 bpm"
        style="@style/Body.Small"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/workoutItemSummaryContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="workoutSummaryDistance, workoutSummaryEnergy, workoutSummaryBpm"/>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/workoutsBarrier"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        app:barrierDirection="left"
        app:constraint_referenced_ids="totalWorkoutsValue, firstActivityIcon, secondActivityIcon, thirdActivityIcon, extraActivityTypes"
        app:layout_constraintBottom_toTopOf="@+id/bottomDivider"
        app:layout_constraintStart_toEndOf="@id/summaryProgress"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/totalWorkoutsValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/size_spacing_xxsmall"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/firstActivityIcon"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/workoutsBarrier"
        app:layout_constraintTop_toTopOf="@id/summaryTotalValue"
        tools:text="7 workouts"
        style="@style/Body.Small"/>

    <ImageView
        android:id="@+id/firstActivityIcon"
        android:layout_marginEnd="@dimen/size_spacing_xsmall"
        android:contentDescription="@null"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintEnd_toStartOf="@+id/secondActivityIcon"
        app:layout_constraintStart_toEndOf="@id/workoutsBarrier"
        app:layout_constraintTop_toBottomOf="@id/totalWorkoutsValue"
        tools:src="@drawable/ic_activity_basketball"
        style="@style/Icon.Mini"/>

    <ImageView
        android:id="@+id/secondActivityIcon"
        android:layout_marginEnd="@dimen/size_spacing_xsmall"
        android:layout_toEndOf="@id/firstActivityIcon"
        android:contentDescription="@null"
        app:layout_constraintEnd_toStartOf="@id/thirdActivityIcon"
        app:layout_constraintStart_toEndOf="@id/firstActivityIcon"
        app:layout_constraintTop_toBottomOf="@id/totalWorkoutsValue"
        tools:src="@drawable/ic_activity_basketball"
        style="@style/Icon.Mini"/>

    <ImageView
        android:id="@+id/thirdActivityIcon"
        android:layout_marginEnd="@dimen/size_spacing_xsmall"
        android:layout_toEndOf="@id/secondActivityIcon"
        android:contentDescription="@null"
        app:layout_constraintEnd_toStartOf="@+id/extraActivityTypes"
        app:layout_constraintStart_toEndOf="@id/secondActivityIcon"
        app:layout_constraintTop_toBottomOf="@+id/totalWorkoutsValue"
        tools:src="@drawable/ic_activity_basketball"
        style="@style/Icon.Mini"/>

    <TextView
        android:id="@+id/extraActivityTypes"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:minWidth="@dimen/size_spacing_large"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/thirdActivityIcon"
        app:layout_constraintTop_toBottomOf="@id/totalWorkoutsValue"
        tools:text="+99"
        style="@style/Body.Small"/>

    <View
        android:id="@+id/bottomDivider"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:layout_marginTop="@dimen/size_spacing_medium"
        android:layout_marginStart="@dimen/size_spacing_xxxxlarge"
        android:background="?suuntoDividerColor"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/workoutSummaryDistance"/>

</androidx.constraintlayout.widget.ConstraintLayout>
