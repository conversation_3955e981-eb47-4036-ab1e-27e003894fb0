<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?suuntoBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            style="@style/Toolbar.Native"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:theme="@style/Toolbar.Native" />
    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:id="@+id/layout_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/white"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appbar"
        app:layout_constraintWidth_max="@dimen/content_max_width">

        <RelativeLayout
            android:id="@+id/layout_nothing"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium">

            <TextView
                style="@style/Body.Large.Bold"
                android:layout_width="match_parent"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/size_spacing_smaller"
                android:layout_toStartOf="@+id/nothing_check"
                android:text="@string/nothing" />

            <ImageView
                android:id="@+id/nothing_check"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@drawable/icon_check" />
        </RelativeLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_divider"
            android:background="@color/suunto_light_gray" />

        <RelativeLayout
            android:id="@+id/layout_sport_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium">

            <TextView
                android:id="@+id/sport_type_title"
                style="@style/Body.Large.Bold"
                android:text="@string/sport_type" />

            <TextView
                android:id="@+id/sport_type"
                style="@style/Body.Medium"
                android:layout_below="@+id/sport_type_title"
                android:layout_alignStart="@+id/sport_type_title" />

            <ImageView
                android:id="@+id/activity_type_check"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@drawable/icon_check" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_divider"
            android:background="@color/suunto_light_gray"
            app:layout_constraintTop_toBottomOf="@+id/layout_sport_type" />

        <RelativeLayout
            android:id="@+id/layout_sport_start_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="@dimen/size_spacing_medium">

            <TextView
                android:id="@+id/start_time_title"
                style="@style/Body.Large.Bold"
                android:text="@string/sport_start_time" />

            <TextView
                android:id="@+id/start_time"
                style="@style/Body.Medium"
                android:layout_below="@+id/start_time_title"
                android:layout_alignStart="@+id/start_time_title" />

            <ImageView
                android:id="@+id/activity_start_time_check"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:src="@drawable/icon_check" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_divider"
            android:background="@color/suunto_light_gray"
            app:layout_constraintTop_toBottomOf="@+id/layout_sport_start_time" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
