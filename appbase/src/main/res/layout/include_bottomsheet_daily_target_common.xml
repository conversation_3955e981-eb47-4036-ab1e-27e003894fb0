<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="@layout/bottomsheet_daily_target_sleep">

    <TextView
        android:id="@+id/bottomSheetTitle"
        style="@style/HeaderLabel.Large"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_medium"
        android:textAllCaps="false"
        android:textColor="@color/weekly_goal_bottom_sheet_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Steps target" />

    <!-- SeekBar need to set maxHeight to make progress have intrinsic height  -->
    <SeekBar
        android:id="@+id/seekBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxHeight="6dp"
        android:paddingVertical="@dimen/size_spacing_small"
        android:progressDrawable="@drawable/progress_seek_bar"
        android:thumb="@drawable/thumb_seek_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/seekbarTitle"
        tools:progress="30" />

    <TextView
        android:id="@+id/seekbarTitle"
        style="@style/Body.Larger"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_medium"
        app:layout_constraintEnd_toStartOf="@+id/seekbarValue"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bottomSheetTitle"
        tools:text="Sleep window" />

    <TextView
        android:id="@+id/seekbarValue"
        style="@style/Body.Larger"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        app:layout_constraintBottom_toBottomOf="@+id/seekbarTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/seekbarTitle"
        tools:text="3h 15min" />

</merge>
