<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="item"
            type="com.stt.android.ui.activities.settings.countrysubdivision.CountrySubdivisionKeyValueItem" />

        <variable
            name="selectedCountrySubdivision"
            type="String" />

        <variable
            name="onClick"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:onClick="@{onClick}">

        <TextView
            android:id="@+id/key"
            style="@style/Body.Larger"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:text="@{item.name}"
            android:textAllCaps="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/state" />

        <RadioButton
            android:id="@+id/value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:checked="@{item.value.equals(selectedCountrySubdivision)}"
            android:contentDescription="@null"
            android:onClick="@{onClick}"
            app:layout_constraintBottom_toBottomOf="@id/key"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/key" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?suuntoDividerColor"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
