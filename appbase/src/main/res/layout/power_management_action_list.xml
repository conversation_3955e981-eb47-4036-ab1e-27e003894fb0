<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/companion_device_association_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?suuntoBackground"
            android:orientation="vertical"
            android:paddingBottom="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_medium"
            android:visibility="gone">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="false"
                android:paddingEnd="@dimen/size_spacing_medium"
                android:paddingStart="@dimen/size_spacing_medium"
                android:text="@string/connectivity_notification_settings_header"
                android:textAllCaps="true"
                tools:text="Applications"
                style="@style/Body.Small.Bold" />
        </LinearLayout>

        <include layout="@layout/title_summary_preference"
            android:visibility="gone"
            android:id="@+id/companion_device_association"/>

        <LinearLayout
            android:id="@+id/mobile_connected_gps_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?suuntoBackground"
            android:orientation="vertical"
            android:visibility="gone"
            android:paddingBottom="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_medium">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="false"
                android:paddingEnd="@dimen/size_spacing_medium"
                android:paddingStart="@dimen/size_spacing_medium"
                android:text="@string/mobile_connected_gps_settings_header"
                android:textAllCaps="true"
                tools:text="Mobile-Connected GPS"
                style="@style/Body.Small.Bold" />
            <TextView
                android:id="@+id/mobile_connected_gps_settings_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="false"
                android:lineSpacingMultiplier="1.3"
                android:paddingEnd="@dimen/size_spacing_medium"
                android:paddingStart="@dimen/size_spacing_medium"
                android:paddingTop="@dimen/size_spacing_small"
                android:text="@string/mobile_connected_gpd_settings_description"
                tools:text="To record an exercise with GPS tracking, go to your phone\'s settings and allow your Suunto device accesss your phone\'s location"
                style="@style/Body.Small" />
        </LinearLayout>

        <include layout="@layout/title_summary_preference"
            android:visibility="gone"
            android:id="@+id/mobile_connected_gps"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?suuntoBackground"
            android:orientation="vertical"
            android:paddingBottom="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_medium">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="false"
                android:paddingEnd="@dimen/size_spacing_medium"
                android:paddingStart="@dimen/size_spacing_medium"
                android:text="@string/power_management_settings_header"
                android:textAllCaps="true"
                tools:text="Power management"
                style="@style/Body.Small.Bold" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="false"
                android:lineSpacingMultiplier="1.3"
                android:paddingEnd="@dimen/size_spacing_medium"
                android:paddingStart="@dimen/size_spacing_medium"
                android:paddingTop="@dimen/size_spacing_small"
                android:text="@string/power_management_settings_description"
                tools:text="Choose the apps whose notifications you want to see on your watch. "
                style="@style/Body.Small" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/very_light_gray"/>

        <include layout="@layout/title_summary_preference"
                 android:id="@+id/power_management_battery_optimisation"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/very_light_gray"/>

        <include layout="@layout/title_summary_preference"
                 android:id="@+id/power_management_power_save"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/very_light_gray"/>

        <include layout="@layout/title_summary_preference"
                 android:id="@+id/power_management_manufacturers_settings"/>
    </LinearLayout>
</layout>

