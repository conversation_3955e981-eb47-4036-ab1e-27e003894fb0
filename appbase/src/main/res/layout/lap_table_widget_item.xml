<?xml version="1.0" encoding="utf-8"?>
<androidx.percentlayout.widget.PercentFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:background="?suuntoItemBackgroundColor"
    android:paddingTop="@dimen/smaller_padding">

    <TextView
        android:id="@+id/lapDistanceOrSkiRun"
        android:layout_height="wrap_content"
        app:layout_widthPercent="10%"
        tools:text="100.9"
        style="@style/LapTable.Values.LapNumber"/>

    <androidx.percentlayout.widget.PercentFrameLayout
        android:id="@+id/altitudeChartContainer"
        android:layout_height="32dp"
        android:layout_gravity="center_vertical"
        android:visibility="gone"
        app:layout_marginStartPercent="10%"
        app:layout_widthPercent="42%">
        <com.github.mikephil.charting.charts.LineChart
            android:id="@+id/altitudeChart"
            android:layout_height="match_parent"
            app:layout_widthPercent="90%"/>
    </androidx.percentlayout.widget.PercentFrameLayout>

    <LinearLayout
        android:id="@+id/avgSpeedContainer"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_marginStartPercent="10%"
        app:layout_widthPercent="38%">
        <TextView
            android:id="@+id/avgSpeed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="18.4"
            style="@style/LapTable.Values"/>

        <com.stt.android.ui.components.TextProgressBar
            android:id="@+id/avgSpeedBar"
            android:layout_width="105dp"
            android:layout_height="2.7dp"
            android:layout_marginTop="5dp"
            app:backgroundColor="?suuntoBackground"
            app:fullColor="?newAccentColor"
            app:progressColor="?newAccentColor"/>
    </LinearLayout>


    <TextView
        android:id="@+id/lapDuration"
        android:layout_height="wrap_content"
        app:layout_marginStartPercent="48%"
        app:layout_widthPercent="18%"
        tools:text="1:30:15"
        style="@style/LapTable.Values"/>

    <TextView
        android:id="@+id/lapAvgHr"
        android:layout_height="wrap_content"
        android:textAllCaps="true"
        app:layout_marginStartPercent="66%"
        app:layout_widthPercent="12%"
        tools:text="104"
        tools:visibility="visible"
        style="@style/LapTable.Values"/>

    <TextView
        android:id="@+id/lapAscendOrSkiDistance"
        android:layout_height="wrap_content"
        app:layout_marginStartPercent="78%"
        app:layout_widthPercent="12%"
        tools:text="16"
        style="@style/LapTable.Values"/>

    <TextView
        android:id="@+id/lapDescent"
        android:layout_height="wrap_content"
        app:layout_marginStartPercent="90%"
        app:layout_widthPercent="10%"
        tools:text="11"
        style="@style/LapTable.Values"/>

</androidx.percentlayout.widget.PercentFrameLayout>
