<?xml version="1.0" encoding="utf-8"?>
<merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:id="@+id/startWorkoutBt"
        android:layout_width="@dimen/start_workout_button_width"
        android:layout_height="@dimen/start_workout_button_height"
        android:layout_gravity="center"
        android:background="@drawable/start_workout_button_bg"
        android:foreground="@drawable/start_workout_button_ripple"
        android:visibility="gone"/>

    <View
        android:id="@+id/goBackToWorkoutBt"
        android:layout_width="@dimen/start_workout_button_width"
        android:layout_height="@dimen/start_workout_button_height"
        android:layout_gravity="center"
        android:background="@drawable/resume_workout_button_bg"
        android:foreground="@drawable/start_workout_button_ripple"
        android:visibility="gone"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/title"
        android:layout_width="@dimen/start_workout_button_width"
        android:layout_height="@dimen/start_workout_button_height"
        android:layout_gravity="center"
        android:gravity="center"
        android:textAllCaps="true"
        android:textColor="?android:textColorPrimaryInverse"
        tools:text="Start"
        style="@style/Body.Medium.Black"/>
</merge>
