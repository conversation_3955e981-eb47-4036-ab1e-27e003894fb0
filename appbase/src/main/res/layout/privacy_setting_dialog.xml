<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <include
                android:id="@+id/sharePublic"
                layout="@layout/privacy_setting_dialog_toggle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent" />

            <include
                android:id="@+id/shareFollowers"
                layout="@layout/privacy_setting_dialog_toggle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/sharePublic" />

            <include
                android:id="@+id/sharePrivate"
                layout="@layout/privacy_setting_dialog_toggle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/shareFollowers" />

            <TextView
                android:id="@+id/description"
                style="@style/Body.Small"
                android:layout_width="0dp"
                android:duplicateParentState="true"
                android:includeFontPadding="false"
                android:lineSpacingMultiplier="1.3"
                android:padding="@dimen/size_spacing_medium"
                android:textColor="@color/settings_item_color"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/sharePrivate"
                tools:text="Description" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</layout>
