<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:color="@color/accent" android:drawable="@drawable/tab_bar_background_selected" android:state_enabled="true" android:state_pressed="true"/>
    <item android:color="@color/laps_nav_bar_btn_text" android:drawable="@drawable/tab_bar_background_selected" android:state_enabled="false"/>
    <item android:color="@color/accent" android:drawable="@drawable/top_nav_background" android:state_enabled="true" android:state_focused="true"/>
    <item android:drawable="@drawable/top_nav_background"/>
</selector>
