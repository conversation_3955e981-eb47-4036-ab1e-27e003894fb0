<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="tts_language">it</string>
    <string name="tts_start">Iniziato</string>
    <string name="tts_stop">Fermato</string>
    <string name="tts_autopause">Messo in pausa automaticamente</string>
    <string name="tts_autoresume">Riavviato automaticamente</string>
    <string name="tts_resume">Riavviato</string>
    <string name="tts_lap_pace_metric">Andatura sul giro %s a chilometro.</string>
    <string name="tts_lap_pace_imperial">Andatura sul giro %s a miglio.</string>
    <string name="tts_lap_swim_pace_metric">Andatura sul giro %s ogni 100 metri.</string>
    <string name="tts_lap_swim_pace_imperial">Andatura sul giro %s ogni 100 iarde.</string>
    <string name="tts_lap_time">Tempo sul giro %1$s</string>
    <string name="tts_total_time">%1$s.</string>

    <!-- We need two tts_kilometers -->
    <!-- we use string when it's not a full unit (1.23 kilometers, 2.1 kilometers) -->
    <string name="tts_kilometers">%1$s chilometri</string>
    <!-- we can use the plurals when it's a full unit (1 kilometer, 2 kilometers) -->
    <plurals name="tts_kilometers">
        <item quantity="one">%1$s kilometro</item>
        <item quantity="other">%1$s kilometri</item>
    </plurals>

    <!-- Same as per kilometers -->
    <string name="tts_miles">%1$s miglia</string>

    <plurals name="tts_miles">
        <item quantity="one">%1$s miglio</item>
        <item quantity="other">%1$s miglia</item>
    </plurals>

    <!-- Same as per kilometers -->
    <string name="tts_nautical_miles">%1$s miglia nautiche</string>

    <plurals name="tts_nautical_miles">
        <item quantity="one">%1$s miglio nautico</item>
        <item quantity="few">%1$s miglia nautiche</item>
        <item quantity="many">%1$s miglia nautiche</item>
        <item quantity="other">%1$s miglia nautiche</item>
    </plurals>

    <plurals name="tts_seconds">
        <item quantity="one">%1$d secondo</item>
        <item quantity="other">%1$d secondi</item>
    </plurals>

    <plurals name="tts_minutes">
        <item quantity="one">%1$d minuto</item>
        <item quantity="other">%1$d minuti</item>
    </plurals>

    <plurals name="tts_hours">
        <item quantity="one">%1$d ora</item>
        <item quantity="other">%1$d ore</item>
    </plurals>

    <string name="tts_ghost_ahead">Sei %1$s avanti</string>
    <string name="tts_ghost_behind">Sei %1$s indietro</string>
    <string name="tts_ghost_neutral">Al momento sei in linea</string>

    <string name="tts_decimal_separator">virgola</string>

    <!-- Added in the Custom VoiceFeedback release -->
    <string name="tts_lap_speed">Velocità sul giro: %s.</string>
    <string name="tts_current_speed">Velocità: %s.</string>
    <string name="tts_current_pace_metric">Andatura: %s a chilometro.</string>
    <string name="tts_current_pace_imperial">Andatura: %s a miglio.</string>
    <string name="tts_average_speed">Velocità media: %s.</string>
    <string name="tts_average_pace_metric">Andatura media: %s a chilometro.</string>
    <string name="tts_average_pace_imperial">Andatura media: %s a miglio.</string>
    <string name="tts_total_distance">%1$s.</string>

    <string name="tts_current_heart_rate">Frequenza cardiaca: %d.</string>
    <string name="tts_average_heart_rate">Frequenza cardiaca media: %d.</string>

    <string name="tts_energy">%1$d calorie.</string>
    <plurals name="tts_energy">
        <item quantity="other">%1$d caloria.</item>
    </plurals>

    <string name="tts_current_cadence">Cadenza: %d.</string>
    <string name="tts_average_cadence">Cadenza media: %d.</string>

    <string name="tts_lap_number">Giro %d.</string>
    <string name="tts_lap_distance">Distanza giro: %s.</string>
    <string name="tts_max_heart_rate">Frequenza cardiaca massima: %d.</string>
    <string name="tts_total_time_with_title">Tempo totale: %1$s.</string>
    <string name="tts_heart_zone">Zona di frequenza cardiaca: %d.</string>
    <string name="tts_lap_power">Potenza giro: %s.</string>
    <string name="tts_lap_ascent">Ascesa giro: %s.</string>
    <string name="tts_lap_descent">Discesa giro: %s.</string>
</resources>
