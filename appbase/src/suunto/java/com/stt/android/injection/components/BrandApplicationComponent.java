package com.stt.android.injection.components;

import com.stt.android.home.dashboard.bottomsheet.WeeklyGoalBottomSheetFragment;
import com.stt.android.home.settings.AgePreference;
import com.stt.android.home.settings.FirstDayOfWeekPreference;
import com.stt.android.home.settings.GenderPreference;
import com.stt.android.home.settings.HeightDialogPreference;
import com.stt.android.home.settings.MaxHeartRatePreference;
import com.stt.android.home.settings.MaxHeartPickerDialogPreference;
import com.stt.android.home.settings.MeasurementUnitPreference;
import com.stt.android.home.settings.RestHeartPickerDialogPreference;
import com.stt.android.home.settings.WeightDialogPreference;
import com.stt.android.watch.preference.personalinfo.SetupPreferenceAgePreference;

public interface BrandApplicationComponent {

    void inject(AgePreference agePreference);

    void inject(SetupPreferenceAgePreference agePreference);

    void inject(GenderPreference genderPreference);

    void inject(WeightDialogPreference weightDialogPreference);

    void inject(WeeklyGoalBottomSheetFragment weeklyGoalBottomSheetFragment);

    void inject(MaxHeartRatePreference maxHeartRatePreference);

    void inject(MeasurementUnitPreference measurementUnitPreference);

    void inject(FirstDayOfWeekPreference firstDayOfWeekPreference);

    void inject(HeightDialogPreference heightDialogPreference);

    void inject(MaxHeartPickerDialogPreference maxHeartPickerDialogPreference);

    void inject(RestHeartPickerDialogPreference restHeartPickerDialogPreference);
}
