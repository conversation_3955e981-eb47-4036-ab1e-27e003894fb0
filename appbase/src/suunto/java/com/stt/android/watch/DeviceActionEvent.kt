package com.stt.android.watch

import androidx.annotation.StringRes
import com.stt.android.domain.device.DeviceInfoWear
import com.stt.android.domain.sportmodes.SupportMode
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import java.io.File

/**
 * DeviceActionEvent represents a user action event sent from the shared
 * action list shown in connecting, connected, syncing, busy and disconnected
 * views.
 */
sealed class DeviceActionEvent

object ShowHelp : DeviceActionEvent()
object TransferFirmware : DeviceActionEvent()
object ImportWorkout : DeviceActionEvent()
object GetEonSettings : DeviceActionEvent()
object SetEonSettings : DeviceActionEvent()
object ShowWifiNetworks : DeviceActionEvent()
object ShowOfflineMaps : DeviceActionEvent()
object GetToKnowNewWatch : DeviceActionEvent()
data class ShowEonSettingsShareDialog(
    val eonSettingsFile: File
) : DeviceActionEvent()

data class ShowHelpArticle(val articleId: String) : DeviceActionEvent()
data class ShowIntroduction(val deviceType: SuuntoDeviceType) : DeviceActionEvent()
data class ShowUserGuide(val deviceType: SuuntoDeviceType) : DeviceActionEvent() {
    @StringRes
    val url: Int? = WatchHelper.getUserGuideLinkForSuuntoDeviceType(deviceType)
}

data class ShowPostUnpairWarning(val deviceType: SuuntoDeviceType) : DeviceActionEvent()
data class CustomizeSportModes(
    val fteCompleted: Boolean,
    val suuntoDeviceType: SuuntoDeviceType,
    val supportMode: SupportMode,
) : DeviceActionEvent()

object ShowStructuredWorkoutPlanner : DeviceActionEvent()

object ShowSuuntoPlusFeatures : DeviceActionEvent()

object ShowSuuntoPlusWatchface : DeviceActionEvent()

object ShowSuuntoPlusGuides : DeviceActionEvent()

data class ShowSuuntoPlusStore(val isRunDevice: Boolean) : DeviceActionEvent()

object ShowWidgetCustomization : DeviceActionEvent()

data class CustomizeDiveModes(
    val deviceSerial: String,
    val deviceHwVersion: String,
    val deviceFwVersion: String,
    val deviceVariantName: String
) : DeviceActionEvent()

data class ShowAboutDevice(
    val serial: String,
    val version: String,
    val deviceInfoWear: DeviceInfoWear,
    val otaUpdateSupported: Boolean,
    val suuntoDeviceType: SuuntoDeviceType,
    val updateAvailable: Boolean,
) : DeviceActionEvent()

data class LogSendStarted(val watchConnected: Boolean) : DeviceActionEvent()
data class LogSendEnded(val throwable: Throwable? = null) : DeviceActionEvent()
object ConnectionInstability : DeviceActionEvent()
data class UpdateFirmware(val supportsOta: Boolean, val suuntoDeviceType: SuuntoDeviceType?) :
    DeviceActionEvent()

object ShowNotificationsSettings : DeviceActionEvent()
object ShowPowerManagementSettings : DeviceActionEvent()
object ShowMovescountUninstallDialog : DeviceActionEvent()
data class ShowFirmwareUpdateRequired(val deviceType: SuuntoDeviceType) : DeviceActionEvent()
object RequestBackgroundLocationPermission : DeviceActionEvent()
object ShowKeepConnectedSettings : DeviceActionEvent()
data class ConnectWeRun(val connected: Boolean) : DeviceActionEvent()
data object ShowOfflineMusic : DeviceActionEvent()
data object PairAnotherDevice : DeviceActionEvent()
data object FindWatch : DeviceActionEvent()
data object TransitCard : DeviceActionEvent()
