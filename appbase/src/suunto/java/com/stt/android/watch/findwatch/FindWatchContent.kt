package com.stt.android.watch.findwatch

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieAnimatable
import com.airbnb.lottie.compose.rememberLottieComposition
import com.stt.android.R
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

@Composable
fun FindWatchContent(
    findWatchState: Boolean,
    imageResource: Int,
    onFindWatchToggle: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.fillMaxSize(),
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = MaterialTheme.spacing.large),
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .size(140.dp)
                    .clip(CircleShape)
                    .background(Color.Black),
            ) {
                FindWatchStateView(
                    findWatchState = findWatchState,
                    iconSize = 58.dp,
                )
            }

            Image(
                painter = painterResource(id = imageResource),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(304.dp),
                contentDescription = null,
                contentScale = ContentScale.FillHeight,
            )
        }

        HorizontalDivider(thickness = MaterialTheme.spacing.small)

        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            modifier = Modifier
                .fillMaxWidth()
                .clickableThrottleFirst(onClick = onFindWatchToggle)
                .padding(horizontal = MaterialTheme.spacing.medium),
        ) {
            Text(
                stringResource(R.string.find_watch_play_sound),
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier
                    .weight(1f)
                    .padding(vertical = MaterialTheme.spacing.medium),
            )
            FindWatchStateView(
                findWatchState = findWatchState,
                modifier = Modifier.padding(start = MaterialTheme.spacing.medium),
            )
        }

        HorizontalDivider()
    }
}

@Composable
private fun FindWatchStateView(
    findWatchState: Boolean,
    modifier: Modifier = Modifier,
    iconSize: Dp = MaterialTheme.iconSizes.small,
) {
    val composition by rememberLottieComposition(
        LottieCompositionSpec.Asset(stringResource(R.string.find_watch_lottie))
    )

    val animatable = rememberLottieAnimatable()
    var animationJob by remember { mutableStateOf<Job?>(null) }

    LaunchedEffect(composition, findWatchState) {
        if (composition != null) {
            if (findWatchState) {
                animationJob?.cancel()
                animationJob = launch {
                    animatable.animate(
                        composition = composition,
                        iterations = LottieConstants.IterateForever
                    )
                }
            } else {
                animationJob?.cancel()
                animatable.snapTo(
                    composition = composition,
                    progress = 0f,
                    iteration = 0,
                    resetLastFrameNanos = true
                )
            }
        }
    }

    LottieAnimation(
        composition = composition,
        progress = { animatable.progress },
        modifier = modifier.size(iconSize)
    )
}

@Preview
@Composable
private fun FindWatchContentPreview() {
    M3AppTheme {
        FindWatchContent(
            findWatchState = true,
            imageResource = R.drawable.watch_activity_suunto_run,
            onFindWatchToggle = {},
        )
    }
}
