package com.stt.android.watch.findwatch

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.R
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.device.DeviceInfoApi
import com.stt.android.domain.device.DeviceConnectionStateUseCase
import com.stt.android.watch.WatchHelper
import com.stt.android.watch.device.FindWatchUseCase
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.rx2.await
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration.Companion.seconds

@HiltViewModel
class FindWatchViewModel @Inject constructor(
    private val findWatchUseCase: FindWatchUseCase,
    deviceConnectionStateUseCase: DeviceConnectionStateUseCase,
    private val deviceInfoApi: DeviceInfoApi,
) : ViewModel() {

    private val _imageResource = deviceConnectionStateUseCase
        .connectedWatchState()
        .asFlow()
        .map {
            val sku = it.deviceInfo?.sku ?: deviceInfoApi.sku().await()
            val variantName = it.deviceInfo?.variantName
            val deviceType = SuuntoDeviceType.fromVariantName(variantName)
            WatchHelper.getDrawableResIdForSuuntoDeviceType(deviceType, sku)
        }
        .catch { e ->
            Timber.i(e, "Failed to get device info")
            emit(R.drawable.watch_activity_suunto_run)
        }
        .distinctUntilChanged()
        .stateIn(
            viewModelScope,
            SharingStarted.WhileSubscribed(5_000),
            R.drawable.watch_activity_suunto_run
        )
    val imageResource: StateFlow<Int> = _imageResource

    private val _originalFindWatchFlow = findWatchUseCase.subscribeFindWatch()
        .catch {
            emit(false)
            Timber.w(it, "Failed to subscribe find watch")
        }
        .distinctUntilChanged()

    private val _findWatchStartTrigger = MutableSharedFlow<Unit>(extraBufferCapacity = 1)

    @OptIn(ExperimentalCoroutinesApi::class)
    private val _timeoutFlow: Flow<Boolean> = _findWatchStartTrigger
        .flatMapLatest {
            merge(
                flow {
                    delay(60L.seconds)
                    Timber.d("Find watch timeout after 60 seconds, auto stopping")
                    stopFindWatch()
                    emit(false)
                },
                _originalFindWatchFlow
                    .filter { !it }
                    .map {
                        Timber.d("Find watch stopped by watch api, cancelling timeout")
                        false
                    }
            ).take(1)
        }

    val findWatchState: StateFlow<Boolean> = merge(
        _originalFindWatchFlow,
        _timeoutFlow,
    )
        .distinctUntilChanged()
        .stateIn(
            viewModelScope,
            SharingStarted.WhileSubscribed(5_000),
            false,
        )

    init {
        viewModelScope.launch {
            _originalFindWatchFlow
                .filter { it }
                .collect {
                    Timber.d("Find watch started, triggering 60s timeout")
                    _findWatchStartTrigger.tryEmit(Unit)
                }
        }
    }

    fun toggleFindWatch() = viewModelScope.launch {
        findWatchUseCase(!findWatchState.value)
    }

    suspend fun onBackPressed() {
        if (findWatchState.value) {
            stopFindWatch()
        }
    }

    private suspend fun stopFindWatch() {
        runSuspendCatching {
            findWatchUseCase(false)
        }.onFailure {
            Timber.i(it, "Failed to stop find watch")
        }
    }
}
