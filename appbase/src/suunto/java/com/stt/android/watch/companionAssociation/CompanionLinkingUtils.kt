package com.stt.android.watch.companionAssociation

import android.companion.CompanionDeviceManager
import android.content.Context
import android.content.pm.PackageManager
import com.gojuno.koptional.Optional
import com.gojuno.koptional.toOptional
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.watch.SuuntoWatchModel

object CompanionLinkingUtils {

    @JvmStatic
    fun createCompanionAssociationHelper(
        context: Context,
        suuntoWatchModel: SuuntoWatchModel,
        datahubAnalyticsTracker: DatahubAnalyticsTracker,
    ): Optional<CompanionAssociationHelper> =
        if (hasSystemCompanionFeature(context)) {
            CompanionAssociationHelper(
                suuntoWatchModel,
                context.getSystemService(CompanionDeviceManager::class.java),
                datahubAnalyticsTracker
            )
        } else {
            null
        }.toOptional()

    @JvmStatic
    fun hasSystemCompanionFeature(context: Context): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_COMPANION_DEVICE_SETUP)
    }
}
