package com.stt.android.watch

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import androidx.core.content.IntentCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.squareup.moshi.Moshi
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.LogbookEntryModel
import com.stt.android.controllers.SessionController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.coroutines.await
import com.stt.android.coroutines.awaitSuspend
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.activitydata.dailyvalues.ActivityDataProvider
import com.stt.android.data.activitydata.goals.ActivityDataGoalController
import com.stt.android.data.pois.POIWatchSyncProvider
import com.stt.android.data.routes.RouteSyncProvider
import com.stt.android.data.sleep.SleepTrackingSettingsProvider
import com.stt.android.data.trenddata.TrendDataSettingsProvider
import com.stt.android.domain.sleep.SleepTrackingMode
import com.stt.android.domain.user.DomainUserSettings
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.utils.STTConstants
import com.stt.android.utils.STTConstants.ExtraKeys.USER_IS_LOGGED_IN
import com.stt.android.utils.toV2
import com.stt.android.utils.toV2Flowable
import com.stt.android.watch.SuuntoWatchModel.ModelState.ConnectStarted
import com.stt.android.watch.SuuntoWatchModel.ModelState.DisconnectStarted
import com.stt.android.watch.SuuntoWatchModel.ModelState.None
import com.stt.android.watch.device.Watch247DataSupport
import com.stt.android.watch.suuntoplusguide.SuuntoPlusGuideWatchSyncProvider
import com.stt.android.watch.wifi.entity.WifiGeneralSettings
import com.suunto.connectivity.ScLib
import com.suunto.connectivity.Spartan
import com.suunto.connectivity.SpartanUserSettings
import com.suunto.connectivity.battery.BatteryLevel
import com.suunto.connectivity.battery.ChargingState
import com.suunto.connectivity.battery.UsbCableState
import com.suunto.connectivity.btscanner.ScannedSuuntoBtDevice
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.logbook.Logbook
import com.suunto.connectivity.notifications.NotificationsSettings
import com.suunto.connectivity.repository.AppInfo
import com.suunto.connectivity.repository.SetAppInfoResponse
import com.suunto.connectivity.repository.commands.CheckForOtaUpdatesResponse
import com.suunto.connectivity.repository.commands.FirmwareTransferStartResponse
import com.suunto.connectivity.repository.commands.GetOrSetSettingsFileResponse
import com.suunto.connectivity.repository.commands.GetSavedWifiNetworksCountResponse
import com.suunto.connectivity.repository.commands.GetSavedWifiNetworksResponse
import com.suunto.connectivity.repository.commands.GetSelectedFirmwareResponse
import com.suunto.connectivity.repository.commands.InstallSelectedFirmwareResponse
import com.suunto.connectivity.repository.commands.SaveWifiNetworkResponse
import com.suunto.connectivity.repository.commands.ScanAvailableWifiNetworksResponse
import com.suunto.connectivity.repository.commands.SelectFirmwareResponse
import com.suunto.connectivity.repository.commands.ServiceStabilityResponse
import com.suunto.connectivity.repository.commands.SetupPreferenceInfo
import com.suunto.connectivity.repository.commands.StopOtaUpdateResponse
import com.suunto.connectivity.repository.commands.SyncTrainingZoneResponse
import com.suunto.connectivity.repository.entities.AskoUserSettings
import com.suunto.connectivity.settings.UnitSystem
import com.suunto.connectivity.settings.WearDirection
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.suuntoplusguides.SyncSuuntoPlusGuidesQueryConsumer
import com.suunto.connectivity.watch.SpartanSyncResult
import com.suunto.connectivity.watch.WatchState
import com.suunto.connectivity.widget.WidgetVersion
import com.suunto.connectivity.widget.Widgets
import hu.akarnokd.rxjava.interop.RxJavaInterop
import io.reactivex.rxkotlin.Singles
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.rx2.asFlow
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.withContext
import okio.buffer
import okio.sink
import rx.Completable
import rx.Observable
import rx.Single
import rx.android.schedulers.AndroidSchedulers
import rx.schedulers.Schedulers
import rx.subjects.BehaviorSubject
import timber.log.Timber
import java.io.File
import java.net.ConnectException
import java.time.DayOfWeek
import java.util.Collections
import javax.inject.Inject
import javax.inject.Singleton
import io.reactivex.Single as SingleV2
import io.reactivex.subjects.BehaviorSubject as BehaviorSubject2

@Singleton
class SuuntoWatchModel @Inject constructor(
    val context: Context,
    private val scLib: ScLib,
    private val workoutHeaderController: WorkoutHeaderController,
    localBroadcastManager: LocalBroadcastManager,
    private val logbookEntryModel: LogbookEntryModel,
    // Persistent current user and device specific statuses.
    private val syncedItemsSentToConnectivityStatus: BackendSyncedItemsSentToConnectivityStatus,
    private val currentUserSyncedAtLeastOnceStatus: BackendSyncedAtLeastOnceStatus,
    private val moshi: Moshi,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ActivityDataGoalController,
    ActivityDataProvider,
    SleepTrackingSettingsProvider,
    RouteSyncProvider,
    POIWatchSyncProvider,
    SuuntoPlusGuideWatchSyncProvider,
    TrendDataSettingsProvider {
    private val watchModelStateSubject = BehaviorSubject.create(WatchModelState(None))
    val connectAttemptState: BehaviorSubject2<ConnectAttemptState> = BehaviorSubject2.createDefault(
        ConnectAttemptState(
            connectPhase = ConnectPhase.Cleared,
            suuntoDeviceType = SuuntoDeviceType.Unrecognized
        )
    )

    /**
     * Broadcast receiver for receiving workout synced event.
     */
    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (STTConstants.BroadcastActions.WORKOUT_SYNCED == intent.action) {
                IntentCompat.getParcelableExtra(
                    intent,
                    STTConstants.ExtraKeys.WORKOUT_HEADER,
                    WorkoutHeader::class.java
                )
                    ?.let(::onWorkoutSynced)
            } else if (STTConstants.BroadcastActions.SYNC_FINISHED == intent.action) {
                if (currentUserSyncedAtLeastOnceStatus.get()) {
                    // Already synced once.
                    return
                }
                if (intent.getBooleanExtra(SessionController.LOGIN_SYNC_FINISHED, false)) {
                    currentUserSyncedAtLeastOnceStatus.set()
                    sendAlreadySyncedEntriesToConnectivity()
                        .onErrorComplete()
                        .subscribe()
                }
            } else if (STTConstants.BroadcastActions.USER_STATUS_CHANGED == intent.action) {
                if (!intent.getBooleanExtra(USER_IS_LOGGED_IN, true)) {
                    onUserLoggedOut()
                }
            }
        }
    }

    init {
        localBroadcastManager.registerReceiver(
            receiver,
            IntentFilter(STTConstants.BroadcastActions.WORKOUT_SYNCED)
        )
        localBroadcastManager.registerReceiver(
            receiver,
            IntentFilter(STTConstants.BroadcastActions.SYNC_FINISHED)
        )
        localBroadcastManager.registerReceiver(
            receiver,
            IntentFilter(STTConstants.BroadcastActions.USER_STATUS_CHANGED)
        )
        configureOTAUpdate()
    }

    /**
     * Returns current watch. Currently it is assumed there is only one current watch.
     *
     * @return a [Single] that emits the currently connected or connecting [Spartan] or
     * calls onError with [MissingCurrentWatchException] if no watch has been connected
     * previously
     */
    @get:Throws(MissingCurrentWatchException::class)
    final val currentWatch: Single<Spartan> = watchModelStateSubject
        .first()
        .toSingle()
        .flatMap<Spartan> { modelState ->
            if (modelState.hasState(ConnectStarted)) {
                Single.just(modelState.connectingWatch)
            } else {
                scLib.availableDevices
                    .flatMap { spartans ->
                        spartans?.let {
                            if (it.isEmpty()) {
                                Single.error(MissingCurrentWatchException())
                            } else {
                                Single.just(it.iterator().next())
                            }
                        } ?: Single.error(MissingCurrentWatchException())
                    }
            }
        }
        .subscribeOn(Schedulers.io())

    suspend fun currentWatch(): Spartan = currentWatch.await()

    /**
     * @return an [Observable] that emits [WatchState] or calls onError if there's no
     * watch available
     */
    final val stateChangeObservable: Observable<WatchState> =
        currentWatch.flatMapObservable { it.stateChangeObservable }

    fun watchStates(): Flow<WatchState> = stateChangeObservable.toV2().asFlow()

    private suspend fun currentWatchState(
        waitForConnect: Boolean,
    ): WatchState = watchStates()
        .filter { !waitForConnect || it.deviceInfo != null }
        .first()

    final val isConnectedFlow = stateChangeObservable
        .map { it.isConnected }
        .distinctUntilChanged()
        .toV2Flowable()
        .asFlow()

    val lastSyncResult: Single<SpartanSyncResult> = currentWatch.flatMap { it.latestSyncResult }

    suspend fun logbookEntries(): List<Logbook.Entry> =
        currentWatch.flatMap { it.logbookEntries }.await()

    suspend fun watchSettings(): SpartanUserSettings = currentWatch.flatMap { it.settings }.await()

    val adaptiveCoachEnabled: Single<Boolean> = currentWatch.flatMap { it.adaptiveCoachEnabled }

    suspend fun adaptiveCoachEnabled(): Boolean = adaptiveCoachEnabled.await()

    suspend fun weeklyTargetDuration(): Float =
        currentWatch.flatMap { it.weeklyTargetDuration }.await()

    internal enum class ModelState {
        None,

        // Spartan connect has been called, but there has not yet been any response.
        ConnectStarted,

        // Spartan disconnect has been called, but there has not yet been any response.
        DisconnectStarted
    }

    internal inner class WatchModelState {
        private val state: ModelState

        val connectingWatch: Spartan?

        /**
         * Create WatchModelState with state ConnectStarted.
         *
         * @param connectingWatch Watch to be connected.
         */
        constructor(connectingWatch: Spartan) {
            this.state = ConnectStarted
            this.connectingWatch = connectingWatch
        }

        /**
         * Create WatchModelState with all the other states, except ConnectStarted.
         *
         * @param state Watch model state.
         */
        constructor(state: ModelState) {
            if (state == ConnectStarted) {
                throw IllegalArgumentException()
            }
            this.state = state
            this.connectingWatch = null
        }

        fun hasState(state: ModelState): Boolean {
            return this.state == state
        }
    }

    private fun onWorkoutSynced(header: WorkoutHeader) {
        Single.fromCallable { logbookEntryModel.queryWorkoutVsEntryIdMap() }
            .zipWith(currentWatch) { idMap, spartan -> idMap to spartan }
            .flatMapCompletable { (idMap, spartan) ->
                val watchSerial = spartan.suuntoBtDevice.serial
                val completables = ArrayList<Completable>()

                // Try to mark single entry as synced to watch.
                val entry = idMap[header.id]
                if (entry != null && entry.second == watchSerial) {
                    completables.add(
                        spartan.markAsSynced(Collections.singletonList(entry.first))
                            .onErrorComplete()
                    )

                    if (!currentUserSyncedAtLeastOnceStatus.get() &&
                        !syncedItemsSentToConnectivityStatus.get(spartan)
                    ) {
                        // At this point current user workouts are synced for sure.
                        // Ensure synced entries are sent to connectivity.
                        currentUserSyncedAtLeastOnceStatus.set()
                        completables.add(
                            sendAlreadySyncedEntriesToConnectivity()
                                .onErrorComplete()
                        )
                    }
                }
                Completable.concat(completables)
            }
            .subscribeOn(Schedulers.io())
            .subscribe({}, {
                logWarning(it, "Error during onWorkoutSynced")
            })
    }

    private fun configureOTAUpdate() {
        scLib.setOTAUpdate()
    }

    // Send all backend synced entry ids to connectivity. This is done only once when user
    // or device has changed. Connectivity needs this information for preventing
    // unnecessary loading of already synced workout and for marking workouts as
    // synced in device.
    private fun sendAlreadySyncedEntriesToConnectivity(): Completable {
        if (!currentUserSyncedAtLeastOnceStatus.get()) {
            // Can not send synced entries yet as there has not been sync done yet for current user.
            return Completable.complete()
        }
        return currentWatch
            .flatMapCompletable { watch ->
                if (syncedItemsSentToConnectivityStatus.get(watch)) {
                    // Synced items for current user and watch already sent once.
                    Completable.complete()
                } else {
                    Single.fromCallable { logbookEntryModel.queryWorkoutVsEntryIdMap() to watch }
                        .flatMapCompletable { (idMap, watch) ->
                            Observable.from(idMap.entries)
                                .subscribeOn(Schedulers.io())
                                // Filter entries that are from the current watch
                                .filter { entry -> entry.value.second == watch.serial }
                                // Get workout header
                                .map { entry -> workoutHeaderController.find(listOf(entry.key)) }
                                // Filter headers that have been synced to backend (key != null)
                                .filter { workoutHeaders -> workoutHeaders.isNotEmpty() && workoutHeaders[0].key != null }
                                .observeOn(AndroidSchedulers.mainThread())
                                // Convert workout id to watch entry id
                                .map { workoutHeaders -> idMap[workoutHeaders[0].id]?.first }
                                .toList()
                                // Mark as synced
                                .flatMapCompletable { toBeMarkedAsSynced ->
                                    watch.markAsSynced(toBeMarkedAsSynced)
                                }
                                .toCompletable()
                                .doOnCompleted {
                                    // Set items sent to connectivity for this user and watch.
                                    syncedItemsSentToConnectivityStatus.set(watch)
                                }
                        }
                }
            }
    }

    // User logged out. Connectivity needs to clean up user specific data. Ignores any errors.
    fun onUserLoggedOut() {
        Completable.fromAction {
            syncedItemsSentToConnectivityStatus.clear()
            currentUserSyncedAtLeastOnceStatus.clear()
        }
            .andThen(
                currentWatch
                    .flatMapCompletable { it.userLogout() }
                    .onErrorComplete()
            )
            .subscribe()
    }

    fun clearConnectAttemptState() {
        connectAttemptState.onNext(
            ConnectAttemptState(
                connectPhase = ConnectPhase.Cleared,
                suuntoDeviceType = SuuntoDeviceType.Unrecognized
            )
        )
    }

    /**
     * @param markAlreadySynced Whether to pass the list of already synced (to backend) logbook
     * entries to the connectivity side. This will trigger a full sync to watch, if the list
     * contains more than one logbook entry.
     */
    fun connect(
        scannedSuuntoBtDevice: ScannedSuuntoBtDevice,
        markAlreadySynced: Boolean = true
    ): Single<SuuntoBtDevice> {
        val spartan = scLib.toSpartan(scannedSuuntoBtDevice)

        // Set model state to ConnectStarted immediately in order to make getCurrentWatch
        // to return this watch from now on.
        watchModelStateSubject.onNext(WatchModelState(spartan))
        val connectSingle = spartan.connect()
            .doOnSubscribe {
                // Clear synced status because new connection is going to be created.
                syncedItemsSentToConnectivityStatus.clear()
                connectAttemptState.onNext(
                    ConnectAttemptState(
                        connectPhase = ConnectPhase.Connecting,
                        suuntoDeviceType = spartan.suuntoBtDevice.deviceType
                    )
                )
            }
            .doOnSuccess {
                connectAttemptState.onNext(
                    ConnectAttemptState(
                        connectPhase = ConnectPhase.ConnectSucceeded,
                        suuntoDeviceType = spartan.suuntoBtDevice.deviceType
                    )
                )
            }
            .doOnError {
                connectAttemptState.onNext(
                    ConnectAttemptState(
                        connectPhase = ConnectPhase.ConnectFailed,
                        suuntoDeviceType = spartan.suuntoBtDevice.deviceType,
                        throwable = it
                    )
                )
            }
            .flatMap { connected ->
                watchModelStateSubject.onNext(WatchModelState(None))
                if (!connected) {
                    Single.error(
                        ConnectException("Unable to connect to [${spartan.suuntoBtDevice.macAddress}]")
                    )
                } else if (markAlreadySynced) {
                    sendAlreadySyncedEntriesToConnectivity()
                        .onErrorComplete()
                        .andThen(Single.just(spartan.suuntoBtDevice))
                } else {
                    Single.just(spartan.suuntoBtDevice)
                }
            }
            .doAfterTerminate { watchModelStateSubject.onNext(WatchModelState(None)) }

        // Isolate connect single from the the returned single.
        // This is done in order not to cancel connect even if returned single is
        // unsubscribed before connection response is received.
        return Single.fromEmitter { suuntoBtDeviceSingleEmitter ->
            connectSingle.subscribe(
                { suuntoBtDeviceSingleEmitter.onSuccess(scannedSuuntoBtDevice) }
            ) { throwable -> suuntoBtDeviceSingleEmitter.onError(throwable) }
        }
    }

    fun disconnect(spartan: Spartan): Single<Boolean> {
        return spartan.disconnect()
            .doOnSubscribe { watchModelStateSubject.onNext(WatchModelState(DisconnectStarted)) }
            .doAfterTerminate { watchModelStateSubject.onNext(WatchModelState(None)) }
            .subscribeOn(Schedulers.io())
    }

    fun unpair(spartan: Spartan): Single<Boolean> {
        return spartan.unpair().subscribeOn(Schedulers.io())
    }

    fun syncCurrentWatchFull(): Single<SpartanSyncResult> {
        return currentWatch.flatMap { spartan -> spartan.synchronize(false) }
    }

    fun requestTrainingZoneSync(): Single<SyncTrainingZoneResponse> {
        return currentWatch.flatMap { spartan -> spartan.requestTrainingZoneSync() }
    }

    fun setAdaptiveCoachEnabledRx(enabled: Boolean): Completable {
        return currentWatch
            .flatMapCompletable { spartan -> spartan.setAdaptiveCoachEnabled(enabled) }
    }

    suspend fun setAdaptiveCoachEnabled(enabled: Boolean) {
        setAdaptiveCoachEnabledRx(enabled).awaitSuspend()
    }

    fun clearConnectionInstability(): Completable {
        return currentWatch
            .flatMapCompletable { spartan -> spartan.clearConnectionInstability() }
    }

    suspend fun setWeeklyTargetDuration(duration: Float) {
        currentWatch.flatMapCompletable { spartan -> spartan.setWeeklyTargetDuration(duration) }
            .awaitSuspend()
    }

    fun getServiceStability(): io.reactivex.Single<ServiceStabilityResponse> {
        return RxJavaInterop.toV2Single(
            currentWatch.flatMap { it.serviceStability }
        )
    }

    override suspend fun getStepsGoal(): Int = currentWatch.flatMap { it.stepsTarget }.await()

    override suspend fun setStepsGoal(stepsTarget: Int) {
        currentWatch.flatMapCompletable { it.setStepsTarget(stepsTarget) }.awaitSuspend()
    }

    override suspend fun getEnergyGoal(): Int = currentWatch.flatMap { it.energyTarget }.await()

    override suspend fun setEnergyGoal(energyTarget: Int) {
        currentWatch.flatMapCompletable { it.setEnergyTarget(energyTarget) }.awaitSuspend()
    }

    override suspend fun getSleepGoal(): Int = currentWatch.flatMap { it.sleepTarget }.await()

    override suspend fun setSleepGoal(sleepTarget: Int) {
        currentWatch.flatMapCompletable { it.setSleepTarget(sleepTarget) }.awaitSuspend()
    }

    /**
     * Method to get steps made this day,
     *
     * @return [<] with current steps value
     */
    override suspend fun getCurrentSteps(): Int = currentWatch.flatMap { it.currentSteps }.await()

    /**
     * Method to get spent energy on activities this day, it will return it in Joules
     *
     * @return [<] with current energy value
     */
    override suspend fun getCurrentActiveEnergy(): Int =
        currentWatch.flatMap { it.activeEnergy }.await()

    /**
     * Method to get current balance 0..1
     *
     * @return current balance value
     */
    override suspend fun getCurrentBalance(): Float = currentWatch
        .flatMap { it.currentBalance }
        .map { it / 100f }
        .await()

    /**
     * Method to get current stress state
     *
     * @return current stress state value
     */
    override suspend fun getCurrentStressState(): Int = currentWatch
        .flatMap { it.currentStressState }
        .await()

    /**
     * Method to get spent energy on daily metabolic activities, it will return it in Joules
     *
     * @return [<] with metabolic activities energy value
     */
    override suspend fun getMetabolicEnergy(): Int =
        currentWatch.flatMap { it.metabolicEnergy }.await()

    override suspend fun fetchSleepTrackingMode(): SleepTrackingMode {
        return currentWatch.flatMap { it.sleepTrackingMode }
            .map { it.toDomain() }
            .await()
    }

    override suspend fun updateSleepTrackingMode(sleepTrackingMode: SleepTrackingMode) {
        currentWatch.flatMapCompletable { it.setSleepTrackingMode(sleepTrackingMode.toWatch()) }
            .awaitSuspend()
    }

    override suspend fun isSpO2NightlyEnabled(): Boolean =
        currentWatch.flatMap { it.isSpO2NightlyEnabled }
            .await()

    override suspend fun setSpO2NightlyEnabled(enabled: Boolean) {
        currentWatch.flatMapCompletable { it.setSpO2NightlyEnabled(enabled) }.awaitSuspend()
    }

    override suspend fun isHrvEnabled(): Boolean =
        currentWatch.flatMap { it.isHrvEnabled }
            .await()

    override suspend fun setHrvEnabled(enabled: Boolean) {
        currentWatch.flatMapCompletable { it.setHrvEnabled(enabled) }.awaitSuspend()
    }

    /**
     * Method to get bedtime start value in seconds
     *
     * @return [<] with bedtime start
     */
    override suspend fun getBedtimeStart(): Int = currentWatch.flatMap { it.bedtimeStart }.await()

    /**
     * Method to get bedtime end value in seconds
     *
     * @return [<] with bedtime end
     */
    override suspend fun getBedtimeEnd(): Int = currentWatch.flatMap { it.bedtimeEnd }.await()

    /**
     * Method to set sleep goal with bedtime start and bedtime end in seconds
     */
    override suspend fun setBedtimes(bedtimeStart: Int, bedtimeEnd: Int) {
        currentWatch.flatMapCompletable { it.setBedtimeStart(bedtimeStart) }
            .concatWith(currentWatch.flatMapCompletable { it.setBedtimeEnd(bedtimeEnd) })
            .awaitSuspend()
    }

    override suspend fun setBedtimeStart(bedtimeStart: Int) {
        currentWatch.flatMapCompletable { it.setBedtimeStart(bedtimeStart) }.awaitSuspend()
    }

    override suspend fun setBedtimeEnd(bedtimeEnd: Int) {
        currentWatch.flatMapCompletable { it.setBedtimeEnd(bedtimeEnd) }.awaitSuspend()
    }

    override suspend fun syncRoutes(navigateRouteId: String?, navigateOngoing: Boolean) =
        withContext(coroutinesDispatchers.io) {
            val supportRouteSync = supportsRouteSync(true).await()
            if (!supportRouteSync) {
                return@withContext
            }

            // Make it so that routes cannot be synced while navigation is ongoing, as this could trigger an issue on the watch side
            if (navigateRouteId.isNullOrEmpty() && navigateOngoing) {
                Timber.d("Syncing routes is not supported during navigation")
                return@withContext
            }

            val spartan = currentWatch()
            spartan.suuntoRepositoryClient
                .routesQueryConsumer
                .syncRoutes(spartan.suuntoBtDevice.macAddress, navigateRouteId)
        }

    suspend fun getWidgets(): Widgets {
        return supportsWidgets(waitForConnect = true)
            .flatMap { supportsWidgets ->
                if (supportsWidgets) {
                    currentWatch.toV2().flatMap { spartan ->
                        spartan.suuntoRepositoryClient.getWidgetsQueryConsumer.getWidgets(
                            spartan.suuntoBtDevice.serial
                        ).toV2()
                    }
                } else {
                    io.reactivex.Single.just(Widgets.EMPTY)
                }
            }.await()
    }

    suspend fun setWidgets(widgets: Widgets, widgetVersion: WidgetVersion) {
        return supportsWidgets(waitForConnect = true)
            .flatMapCompletable { supportsWidgets ->
                if (supportsWidgets) {
                    currentWatch.toV2().flatMapCompletable { spartan ->
                        spartan.suuntoRepositoryClient.setWidgetsQueryConsumer.setWidgets(
                            spartan.suuntoBtDevice.serial,
                            widgets,
                            widgetVersion
                        ).toV2()
                    }
                } else {
                    io.reactivex.Completable.complete()
                }
            }.await()
    }

    override suspend fun sendPOIWatchSyncQuery() {
        if (!supportsPOISync()) {
            return
        }

        runSuspendCatching {
            val currentWatch = currentWatch()
            currentWatch.suuntoRepositoryClient.poisQueryConsumer.syncPOIs(
                macAddress = currentWatch.suuntoBtDevice.macAddress,
            )
        }.onFailure { e ->
            // We prevent sync errors from terminating the stream
            // Errors are reported separately per POI
            logWarning(e, "Failed to send POI watch sync query")
        }
    }

    suspend fun suuntoPlusGuidesQueryConsumerWithMacAddress(): Pair<SyncSuuntoPlusGuidesQueryConsumer, String> {
        val spartan = currentWatch.await()
        val macAddress = spartan.suuntoBtDevice.macAddress
        return spartan.suuntoRepositoryClient.syncSuuntoPlusGuidesQueryConsumer to macAddress
    }

    override suspend fun sendSuuntoPlusGuideWatchSyncQuery(): Unit =
        withContext(coroutinesDispatchers.io) {
            runSuspendCatching {
                val (suuntoPlusGuidesQueryConsumer, macAddress) = suuntoPlusGuidesQueryConsumerWithMacAddress()
                suuntoPlusGuidesQueryConsumer.syncSuuntoPlusGuides(macAddress)
            }.onFailure { e ->
                logWarning(e, "Error during syncSuuntoPlusGuides()")
            }
        }

    override suspend fun is247HrEnabled(): Boolean =
        currentWatch.flatMap { it.is247HrEnabled }
            .await()

    override suspend fun set247HrEnabled(enabled: Boolean) {
        currentWatch.flatMapCompletable { it.set247HrEnabled(enabled) }.awaitSuspend()
    }

    fun getNotificationSettings(): NotificationsSettings? {
        return scLib.notificationSettings
    }

    fun <T : Any> getWatchWithStateRx(
        waitForConnect: Boolean,
        zipper: (Spartan, WatchState) -> T
    ): io.reactivex.Single<T> =
        Singles.zip(
            currentWatch.toV2(),
            stateChangeObservable
                .filter { !waitForConnect || it.deviceInfo != null }
                .toV2()
                .firstOrError(),
            zipper
        )

    private suspend fun supportsPOISync(): Boolean = runSuspendCatching {
        val watch = currentWatch()
        val watchState = currentWatchState(waitForConnect = true)
        val type = watch.suuntoBtDevice.deviceType
        val compatibility = SuuntoDeviceCapabilityInfoProvider.get(type)
        compatibility.supportsPOISync(watchState.deviceInfo)
    }.getOrElse { e ->
        logWarning(e, "Failed to check if watch supports POI sync")
        false
    }

    fun supportsRouteSync(waitForConnect: Boolean = false): io.reactivex.Single<Boolean> {
        return getWatchWithStateRx(waitForConnect) { watch, watchState ->
            val type = watch.suuntoBtDevice.deviceType
            val compatibility = SuuntoDeviceCapabilityInfoProvider.get(type)
            compatibility.supportsRoutesSync(watchState.deviceInfo)
        }.onErrorReturn {
            logWarning(it, "Error in supportsRouteSync($waitForConnect)")
            false
        }
    }

    private fun logWarning(throwable: Throwable, message: String) {
        if (throwable is MissingCurrentWatchException) {
            Timber.i("Missing current watch")
        } else {
            Timber.w(throwable, message)
        }
    }

    fun supports247Data(waitForConnect: Boolean = false): io.reactivex.Single<Watch247DataSupport> {
        return getWatchWithStateRx(waitForConnect) { watch, watchState ->
            val type = watch.suuntoBtDevice.deviceType
            val whiteboardCompatible = watch.suuntoBtDevice.isWhiteboardCompatible
            val compatibility = SuuntoDeviceCapabilityInfoProvider.get(type)
            val deviceInfo = watchState.deviceInfo
                ?: throw IllegalArgumentException("device info is null")
            if (!whiteboardCompatible) {
                Watch247DataSupport(
                    supportsTrendData = false,
                    supportsSleepData = false
                )
            } else {
                Watch247DataSupport(
                    supportsTrendData = compatibility.supportsTrendData(deviceInfo),
                    supportsSleepData = compatibility.supportsSleepData(deviceInfo)
                )
            }
        }
            .onErrorReturn { e ->
                logWarning(e, "Error checking if watch supportsTrendData()")
                Watch247DataSupport(
                    supportsTrendData = false,
                    supportsSleepData = false
                )
            }
    }

    fun supportsWidgets(waitForConnect: Boolean = false): io.reactivex.Single<Boolean> {
        return getWatchWithStateRx(waitForConnect) { watch, watchState ->
            val type = watch.suuntoBtDevice.deviceType
            val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
            compatibility.supportsWidgets(watchState.deviceInfo)
        }.onErrorReturn {
            logWarning(it, "Error in supportsWidgets($waitForConnect)")
            false
        }
    }

    fun supportsQuickNavigation(waitForConnect: Boolean = false): io.reactivex.Single<Boolean> {
        return getWatchWithStateRx(waitForConnect) { watch, watchState ->
            val type = watch.suuntoBtDevice.deviceType
            val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
            compatibility.supportsQuickNavigation(watchState.deviceInfo)
        }.onErrorReturn {
            logWarning(it, "Error in supportsQuickNavigation($waitForConnect)")
            false
        }
    }

    suspend fun getWidgetVersion(waitForConnect: Boolean = false): WidgetVersion {
        return getWatchWithStateRx(waitForConnect) { _, watchState ->
            val versionId =
                SuuntoWatchCapabilities(watchState.deviceInfo?.capabilities).widgetVersion
            WidgetVersion.fromValue(versionId)
        }.await()
    }

    fun supportsWifiSetup(waitForConnect: Boolean = false): io.reactivex.Single<Boolean> {
        return getWatchWithStateRx(waitForConnect) { watch, watchState ->
            val type = watch.suuntoBtDevice.deviceType
            val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
            compatibility.supportsWifiSetup(watchState.deviceInfo)
        }.onErrorReturn {
            logWarning(it, "Error in supportsWifiSetup($waitForConnect)")
            false
        }
    }

    fun supportsOfflineMaps(waitForConnect: Boolean = false): io.reactivex.Single<Boolean> {
        return getWatchWithStateRx(waitForConnect) { watch, watchState ->
            val type = watch.suuntoBtDevice.deviceType
            val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
            compatibility.supportsOfflineMaps(watchState.deviceInfo)
        }.onErrorReturn {
            logWarning(it, "Error in supportsOfflineMaps($waitForConnect)")
            false
        }
    }

    fun requiresForcedUpdate(waitForConnect: Boolean = false): io.reactivex.Single<Boolean> {
        return getWatchWithStateRx(waitForConnect) { watch, watchState ->
            val type = watch.suuntoBtDevice.deviceType
            val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
            compatibility.requiresForcedUpdate(watchState.deviceInfo)
        }.onErrorReturn {
            logWarning(it, "Error in requiresForcedUpdate($waitForConnect)")
            false
        }
    }

    fun startFirmwareTransfer(
        fileUri: Uri,
        firmwareVersion: String?
    ): io.reactivex.Single<FirmwareTransferStartResponse> {
        return RxJavaInterop.toV2Single(
            currentWatch
                .flatMap { it.startFirmwareTransfer(fileUri, firmwareVersion) }
        )
    }

    suspend fun selectFirmware(
        packageId: Long,
        forceUpdate: Boolean = false
    ): SelectFirmwareResponse {
        return currentWatch
            .flatMap { it.selectFirmware(packageId, forceUpdate) }
            .onErrorReturn { SelectFirmwareResponse(success = false, it.message) }
            .await()
    }

    suspend fun getSelectedFirmware(): GetSelectedFirmwareResponse {
        return currentWatch
            .flatMap { it.getSelectedFirmware() }
            .onErrorReturn {
                GetSelectedFirmwareResponse(
                    success = false,
                    message = it.message,
                    selectedFirmwareDescriptor = null,
                    selectedFirmwarePackageId = null
                )
            }
            .await()
    }

    fun checkForNewOtaUpdates(): SingleV2<CheckForOtaUpdatesResponse> =
        currentWatch.flatMap { it.checkForOtaUpdates(null) }.toV2()

    fun checkForNewOtaUpdatesRx(): Single<CheckForOtaUpdatesResponse> =
        currentWatch.flatMap { it.checkForOtaUpdates(null) }

    fun stopOtaUpdate(): io.reactivex.Single<StopOtaUpdateResponse> {
        return RxJavaInterop.toV2Single(
            currentWatch
                .flatMap { it.stopOtaUpdate() }
        )
    }

    suspend fun installSelectedFirmware(
        packageId: Long,
        forceUpdate: Boolean = false
    ): InstallSelectedFirmwareResponse =
        currentWatch
            .flatMap { it.installSelectedFirmware(packageId, forceUpdate) }
            .onErrorReturn { InstallSelectedFirmwareResponse(success = false, it.message) }
            .await()

    // TODO: Remove these once the we enable the dive ui feature
    fun getSettingsFile(fileUri: Uri): SingleV2<GetOrSetSettingsFileResponse> {
        return currentWatch.flatMap { it.getSettingsFile(fileUri) }.toV2()
    }

    fun setSettingsFile(fileUri: Uri): SingleV2<GetOrSetSettingsFileResponse> {
        return currentWatch.flatMap { it.setSettingsFile(fileUri) }.toV2()
    }

    suspend fun getSettingsFileCoroutine(fileUri: Uri): GetOrSetSettingsFileResponse {
        return currentWatch.flatMap { it.getSettingsFile(fileUri) }.await()
    }

    suspend fun setSettingsFileCoroutine(fileUri: Uri): GetOrSetSettingsFileResponse {
        return currentWatch.flatMap { it.setSettingsFile(fileUri) }.await()
    }

    suspend fun saveUserSettings(userSettings: DomainUserSettings) =
        withContext(coroutinesDispatchers.io) {
            val userSettingsFile = scLib.userSettingsToWatchFile()
            // let's write all the user settings to file even though there might be no device connected
            userSettingsFile.sink().buffer().use {
                moshi.adapter(AskoUserSettings::class.java)
                    .toJson(it, userSettings.toAskoUserSettings())
            }
        }

    suspend fun sendFirstDayOfWeek(firstDayOfWeek: DayOfWeek) =
        withContext(coroutinesDispatchers.io) {
            currentWatch.flatMapCompletable { spartan ->
                spartan.setFirstDayOfTheWeek(firstDayOfWeek)
            }.awaitSuspend()
        }

    fun sendUserGender(gender: String): Completable {
        return currentWatch.flatMapCompletable { spartan ->
            spartan.setUserGender(gender)
        }
    }

    suspend fun sendUserHeight(heightCentimeter: Int) =
        withContext(coroutinesDispatchers.io) {
            currentWatch.flatMapCompletable { spartan ->
                spartan.setUserHeight(heightCentimeter / 100f)
            }.awaitSuspend()
        }

    fun sendUserWeight(weight: Float): Completable {
        return currentWatch.flatMapCompletable { spartan ->
            spartan.setUserWeight(weight)
        }
    }

    fun sendUserBirthYear(birthYear: Int): Completable {
        return currentWatch.flatMapCompletable { spartan ->
            spartan.setUserBirthYear(birthYear)
        }
    }

    suspend fun sendUserMaxHeart(maxHR: Int) =
        withContext(coroutinesDispatchers.io) {
            currentWatch.flatMapCompletable { spartan ->
                spartan.setUserMaxHeart(maxHR)
            }.awaitSuspend()
        }

    suspend fun sendUserRestHeart(restHR: Int) =
        withContext(coroutinesDispatchers.io) {
            currentWatch.flatMapCompletable { spartan ->
                spartan.setUserRestHeart(restHR)
            }.awaitSuspend()
        }

    suspend fun sendUserUnitSystem(unitSystem: UnitSystem) {
        withContext(coroutinesDispatchers.io) {
            currentWatch.flatMapCompletable { spartan ->
                spartan.setUserUnitSystem(unitSystem)
            }.awaitSuspend()
        }
    }

    suspend fun sendPredefinedReplies(predefinedReplies: List<String>) =
        withContext(coroutinesDispatchers.io) {
            currentWatch.flatMapCompletable { spartan ->
                spartan.setPredefinedReplies(predefinedReplies)
            }.awaitSuspend()
        }

    suspend fun getWUIDumpFile(): File {
        return currentWatch.flatMap { it.wuiDumpFile }.toV2().await()
    }

    fun getSavedWifiNetworksCount(): Flow<GetSavedWifiNetworksCountResponse> =
        currentWatch.flatMapObservable(Spartan::getSavedWifiNetworksCount).toV2Flowable().asFlow()

    suspend fun getSavedWifiNetworks(): GetSavedWifiNetworksResponse =
        currentWatch.flatMap { it.savedWifiNetworks }.await()

    suspend fun saveWifiNetwork(ssid: String, key: String?): SaveWifiNetworkResponse =
        currentWatch.flatMap { it.saveWifiNetwork(ssid, key) }.await()

    suspend fun forgetWifiNetwork(index: Int) =
        currentWatch.flatMapCompletable { it.forgetWifiNetwork(index) }.awaitSuspend()

    suspend fun scanAvailableWifiNetworks(): ScanAvailableWifiNetworksResponse =
        currentWatch.flatMap(Spartan::scanAvailableWifiNetworks).await()

    suspend fun setWifiGeneralSettings(settings: WifiGeneralSettings) =
        currentWatch.flatMapCompletable { it.setWifiGeneralSettings(settings.countryCode) }
            .awaitSuspend()

    suspend fun setOfflineMapsUrl(url: String) =
        currentWatch.flatMapCompletable { it.setOfflineMapsUrl(url) }.awaitSuspend()

    suspend fun setAuthToken(token: String) =
        currentWatch.flatMapCompletable { it.setAuthToken(token) }.awaitSuspend()

    fun observeWifiEnabled(): Flow<Boolean> =
        currentWatch.flatMapObservable(Spartan::observeWifiEnabled).toV2Flowable().asFlow()

    suspend fun setWifiEnabled(enabled: Boolean) =
        currentWatch.flatMapCompletable { it.setWifiEnabled(enabled) }.awaitSuspend()

    suspend fun enableInboxWifi() =
        currentWatch.flatMapCompletable { it.enableInboxWifi() }.awaitSuspend()

    suspend fun notifyAreaSelectionChanged() =
        currentWatch.flatMapCompletable { it.notifyAreaSelectionChanged() }.awaitSuspend()

    suspend fun notifyAreaUnderDownloadDeleted(areaId: String) =
        currentWatch.flatMapCompletable { it.notifyAreaUnderDownloadDeleted(areaId) }.awaitSuspend()

    suspend fun numberOfAreas(): Int =
        currentWatch.flatMap { it.numberOfAreas() }.await()

    suspend fun getBatteryLevel(): Flow<BatteryLevel> =
        currentWatch.flatMapObservable { it.batteryLevel }.toV2Flowable().asFlow()

    suspend fun getChargingState(): ChargingState =
        currentWatch.flatMap { it.chargingState }.await()

    suspend fun getUsbCableState(): UsbCableState =
        currentWatch.flatMap { it.usbCableState }.await()

    fun getUsbCableStateObservable(): Flow<UsbCableState> =
        currentWatch.flatMapObservable { it.usbCableStateObservable }.toV2Flowable().asFlow()

    suspend fun lockSportsAppSettings(pluginId: String) =
        currentWatch.flatMapCompletable { it.lockSportsAppSettings(pluginId) }.awaitSuspend()

    suspend fun unlockSportsAppSettings(pluginId: String) =
        currentWatch.flatMapCompletable { it.unlockSportsAppSettings(pluginId) }.awaitSuspend()

    suspend fun getFile(deviceFilePath: String, localPath: String) =
        currentWatch.flatMapCompletable { it.getFile(deviceFilePath, localPath) }.awaitSuspend()

    suspend fun putFile(localPath: String, deviceFilePath: String) =
        currentWatch.flatMapCompletable { it.putFile(localPath, deviceFilePath) }.awaitSuspend()

    suspend fun getZappPluginDirectory(): String =
        currentWatch.flatMap { it.zappPluginDirectory }.await()

    suspend fun setNotificationEnabled(enabled: Boolean) =
        runSuspendCatching {
            currentWatch.flatMapCompletable { it.setNotificationEnabled(enabled) }.awaitSuspend()
        }.onFailure {
            logWarning(it, "Can't set the notification enabled=$enabled.")
        }.isSuccess

    suspend fun setNotificationCategoryEnabled(call: Boolean, sms: Boolean, application: Boolean) =
        runSuspendCatching {
            currentWatch.flatMapCompletable {
                it.setNotificationCategoryEnabled(call, sms, application)
            }.awaitSuspend()
        }.onFailure {
            logWarning(
                it,
                "Can't set the notification category [call=$call, sms=$sms, application=$application]."
            )
        }.isSuccess

    suspend fun fetchNotificationEnabled(): Boolean =
        runSuspendCatching {
            currentWatch.flatMap { it.notificationEnabled }.await()
        }.onFailure {
            logWarning(it, "Can't fetch the notification enabled state.")
        }.getOrDefault(false)

    suspend fun setDebugLocationCoordinates(latitude: Int, longitude: Int) {
        currentWatch.flatMapCompletable {
            it.setDebugLocationCoordinates(latitude, longitude)
        }.awaitSuspend()
    }

    suspend fun fetchCurrentWatchfaceId(): Flow<String?> =
        runSuspendCatching {
            val (suuntoPlusGuidesQueryConsumer, macAddress) = suuntoPlusGuidesQueryConsumerWithMacAddress()
            return suuntoPlusGuidesQueryConsumer.getCurrentWatchfaceId(macAddress)
        }.getOrElse {
            logWarning(it, "Can't get the current watch face id.")
            flowOf(null)
        }

    suspend fun setCurrentWatchface(watchfaceId: String) =
        runSuspendCatching {
            val (suuntoPlusGuidesQueryConsumer, macAddress) = suuntoPlusGuidesQueryConsumerWithMacAddress()
            suuntoPlusGuidesQueryConsumer.setCurrentWatchface(macAddress, watchfaceId)
        }.getOrElse {
            logWarning(it, "Can't set the watch face: $watchfaceId")
            false
        }

    suspend fun fetchDefaultWatchfaceId(): String? =
        runSuspendCatching {
            val (suuntoPlusGuidesQueryConsumer, macAddress) = suuntoPlusGuidesQueryConsumerWithMacAddress()
            suuntoPlusGuidesQueryConsumer.getDefaultWatchfaceId(macAddress)
        }.getOrElse {
            logWarning(it, "Can't fetch the default watch face id.")
            null
        }

    fun supportSetupPreference(waitForConnect: Boolean = false): io.reactivex.Single<Boolean> =
        getWatchWithStateRx(waitForConnect) { watch, watchState ->
            val type = watch.suuntoBtDevice.deviceType
            val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
            compatibility.supportsScanConnect(watchState.deviceInfo?.capabilities)
        }.onErrorReturn {
            logWarning(it, "Error in supportSetupPreference($waitForConnect)")
            false
        }

    fun subscribeSetupPreferenceCancel(): Flow<SetupPreferenceInfo> =
        currentWatch.flatMapObservable { it.subscribeSetupPreferenceCancel() }.toV2Flowable()
            .asFlow()

    suspend fun getSetupPreferenceState(): SetupPreferenceInfo =
        currentWatch.flatMap { it.setupPreferenceState }.await()

    suspend fun setSetupPreferenceState(userId: String, start: Boolean) {
        currentWatch.flatMapCompletable { it.setSetupPreferenceState(userId, start) }.awaitSuspend()
    }

    fun setAppInfo(appInfo: AppInfo): Flow<SetAppInfoResponse> = currentWatch
        .flatMapObservable { it.setAppInfo(appInfo) }
        .toV2()
        .asFlow()

    suspend fun supportsSendingAppInfo(waitForConnect: Boolean = false): Boolean =
        runSuspendCatching {
            getWatchWithStateRx(waitForConnect) { watch, watchState ->
                val type = watch.suuntoBtDevice.deviceType
                val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
                compatibility.supportsDeviceSetting(watchState.deviceInfo)
            }.await()
        }.getOrElse { e ->
            Timber.w(e, "Error while checking if sending app info is supported")
            false
        }

    suspend fun setDeviceWearDirection(direction: WearDirection) =
        runSuspendCatching {
            currentWatch.flatMapCompletable { it.setWatchWearDirection(direction) }.awaitSuspend()
        }.onFailure {
            logWarning(it, "Can't set device wear direction.")
        }.isSuccess

    suspend fun getLogFiles(): List<File> = runSuspendCatching {
        currentWatch.flatMap { spartan -> spartan.logFiles }.await()
    }.getOrDefault(emptyList())

    fun isRunSportModeSupported(waitForConnect: Boolean = false): io.reactivex.Single<Boolean> {
        return getWatchWithStateRx(waitForConnect) { watch, watchState ->
            val type = watch.suuntoBtDevice.deviceType
            val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
            compatibility.supportsRunSportMode(watchState.deviceInfo?.capabilities)
        }.onErrorReturn {
            logWarning(it, "Error in isRunSportModeSupported($waitForConnect)")
            false
        }
    }

    suspend fun isCompetitionModeV2Supported(waitForConnect: Boolean = false): Boolean =
        runSuspendCatching {
            getWatchWithStateRx(waitForConnect) { watch, watchState ->
                val type = watch.suuntoBtDevice.deviceType
                val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
                compatibility.supportsCompetitionModeV2(watchState.deviceInfo?.capabilities)
            }.await()
        }.getOrElse { e ->
            Timber.w(e, "Error while checking if competition mode v2 is supported")
            false
        }

    suspend fun isWidgetCountUnlimited(waitForConnect: Boolean = false): io.reactivex.Single<Boolean> {
        return getWatchWithStateRx(waitForConnect) { watch, watchState ->
            val type = watch.suuntoBtDevice.deviceType
            val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
            compatibility.isWidgetCountUnlimited(watchState.deviceInfo?.capabilities)
        }.onErrorReturn {
            logWarning(it, "Error in isWidgetCountUnlimited($waitForConnect)")
            false
        }
    }

    suspend fun supportsOtaUpdateCheckV2(waitForConnect: Boolean = false): Boolean =
        runSuspendCatching {
            getWatchWithStateRx(waitForConnect) { watch, watchState ->
                val type = watch.suuntoBtDevice.deviceType
                val compatibility = SuuntoDeviceCapabilityInfoProvider.get(type)
                compatibility.supportsOtaUpdateCheckV2(watchState.deviceInfo?.capabilities)
            }.await()
        }.getOrElse { e ->
            Timber.w(e, "Error in supportsOtaUpdateCheckV2($waitForConnect)")
            false
        }

    suspend fun updateOtaManualDownloadFlag() {
        if (supportsOtaUpdateCheckV2()) {
            runSuspendCatching {
                currentWatch.flatMapCompletable { it.updateOtaManualDownloadFlag() }.awaitSuspend()
            }.onFailure { e ->
                Timber.w(e, "Error in updateOtaManualDownloadFlag")
            }
        }
    }

    suspend fun supportsRunOta2(waitForConnect: Boolean = false): Boolean =
        runSuspendCatching {
            getWatchWithStateRx(waitForConnect) { watch, watchState ->
                val type = watch.suuntoBtDevice.deviceType
                val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
                compatibility.supportsRunOta2(watchState.deviceInfo)
            }.await()
        }.getOrElse { e ->
            Timber.w(e, "Error in supportsRunOta2($waitForConnect)")
            false
        }

    suspend fun supportsInstallSelectedFirmware(waitForConnect: Boolean = false): Boolean =
        runSuspendCatching {
            getWatchWithStateRx(waitForConnect) { watch, watchState ->
                val type = watch.suuntoBtDevice.deviceType
                val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
                compatibility.supportsInstallSelectedFirmware(watchState.deviceInfo)
            }.await()
        }.getOrElse { e ->
            Timber.w(e, "Error in supportsInstallSelectedFirmware($waitForConnect)")
            false
        }

    suspend fun supportsBesFindWatch(waitForConnect: Boolean = false): Boolean =
        runSuspendCatching {
            getWatchWithStateRx(waitForConnect) { watch, watchState ->
                val type = watch.suuntoBtDevice.deviceType
                val compatibility = SuuntoDeviceCapabilityInfoProvider[type]
                compatibility.supportsBesFindWatch(watchState.deviceInfo)
            }.await()
        }.getOrElse { e ->
            Timber.w(e, "Error in supportsRunOta2($waitForConnect)")
            false
        }

    suspend fun getBatteryFile(): File {
        return currentWatch.flatMap { it.batteryFile }.toV2().await()
    }

    suspend fun getDrtLogsFile(): File {
        return currentWatch.flatMap { it.drtLogsFile }.toV2().await()
    }
}
