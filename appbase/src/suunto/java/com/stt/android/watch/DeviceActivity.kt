package com.stt.android.watch

import android.Manifest
import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.SharedPreferences
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import androidx.core.content.FileProvider
import androidx.core.content.edit
import androidx.core.net.toUri
import androidx.fragment.app.DialogFragment
import androidx.navigation.findNavController
import com.emarsys.core.provider.activity.fragmentManager
import com.gojuno.koptional.Optional
import com.google.android.material.snackbar.Snackbar
import com.stt.android.AppConfig
import com.stt.android.R
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.DownloadMapsScreenSource
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.ui.IntentFactory
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.common.ui.SingleChoiceDialogFragment
import com.stt.android.common.ui.ViewModelActivity2
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.coroutines.launchOnLifecycle
import com.stt.android.databinding.ActivityDeviceBinding
import com.stt.android.di.AppVersionNumberForSupport
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.di.WeChatPreferences
import com.stt.android.domain.device.ConnectedWatchConnectionState
import com.stt.android.domain.sportmodes.SupportMode
import com.stt.android.help.BaseSupportHelper
import com.stt.android.home.devicetype.DeviceType
import com.stt.android.home.devicetype.DeviceTypeSelectActivity
import com.stt.android.home.settings.connectedservices.WeChatConnectServiceHelper
import com.stt.android.intentresolver.IntentKey
import com.stt.android.login.LoginHelper
import com.stt.android.nfc.api.NfcNavigator
import com.stt.android.offlinemaps.OfflineMapsNavigator
import com.stt.android.social.userprofile.HeadsetNavigator
import com.stt.android.social.userprofile.SupportActivity
import com.stt.android.ui.activities.settings.ManageConnectionActivity
import com.stt.android.ui.activities.settings.PowerManagementSettingsActivity
import com.stt.android.ui.activities.settings.watch.notifications.WatchNotificationsPermissionsActivity
import com.stt.android.ui.fragments.settings.PrivacySettingDialog
import com.stt.android.utils.BatteryOptimizationUtils
import com.stt.android.utils.CustomTabsUtils
import com.stt.android.utils.PreferencesUtils
import com.stt.android.utils.STTConstants
import com.stt.android.utils.isBackgroundLocationPermissionGranted
import com.stt.android.utils.permissions.backgroundLocation.BackgroundLocationRequestHelper
import com.stt.android.utils.putEnumExtra
import com.stt.android.watch.companionAssociation.CompanionAssociationHelper
import com.stt.android.watch.companionAssociation.CompanionAssociationUIContext
import com.stt.android.watch.detail.WatchDetailActivity
import com.stt.android.watch.divemodes.DiveModesNavigator
import com.stt.android.watch.findwatch.FindWatchActivity
import com.stt.android.watch.manage.ManageConnectionFragment
import com.stt.android.watch.preference.SetupPreference
import com.stt.android.watch.preference.SetupPreferenceActivity
import com.stt.android.watch.sportmodes.SportModeActivity
import com.stt.android.watch.watchupdates.WatchUpdatesActivity
import com.stt.android.watch.wifi.WifiNetworksActivity
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper.getWatchModelNameForSuuntoDeviceType
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.util.NotificationSettingsHelper
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.Job
import timber.log.Timber
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject

/**
 * DeviceActivity handles the devices related actions like scanning, pairing
 * and syncing. The different states are handled within different fragments
 * and show with the Android Jetpack navigation library.
 */
@AndroidEntryPoint
class DeviceActivity :
    ViewModelActivity2(),
    BatteryOptimizationUtils.DialogListener,
    SingleChoiceDialogFragment.Callback,
    SimpleDialogFragment.Callback {

    override val viewModel: DeviceHolderViewModel by viewModels()

    private val viewDataBinding: ActivityDeviceBinding get() = requireBinding()

    override fun getLayoutResId() = R.layout.activity_device

    @Inject
    lateinit var notificationSettingsHelper: NotificationSettingsHelper

    @Inject
    lateinit var batteryOptimizationUtils: BatteryOptimizationUtils

    @Inject
    lateinit var analyticsUtils: DeviceAnalyticsUtil

    @Inject
    lateinit var emarsysAnalytics: EmarsysAnalytics

    @Inject
    lateinit var datahubAnalyticsTracker: DatahubAnalyticsTracker

    @Inject
    lateinit var suuntoPlusNavigator: SuuntoPlusNavigator

    @Inject
    lateinit var workoutPlannerNavigator: WorkoutPlannerNavigator

    @Inject
    lateinit var suuntoPlusStoreNavigator: SuuntoPlusStoreNavigator

    @Inject
    @AppVersionNumberForSupport
    internal lateinit var appVersionNumberForSupport: String

    @Inject
    lateinit var diveModesNavigator: DiveModesNavigator

    @Inject
    lateinit var deviceOnboardingNavigator: DeviceOnboardingNavigator

    @Inject
    lateinit var watchWidgetCustomizationNavigator: WatchWidgetCustomizationNavigator

    @Inject
    @SuuntoSharedPrefs
    lateinit var suuntoSharedPrefs: SharedPreferences

    @Inject
    lateinit var companionAssociationHelperOptional: Optional<CompanionAssociationHelper>

    @Inject
    internal lateinit var supportHelper: BaseSupportHelper

    @Inject
    @WeChatPreferences
    internal lateinit var weChatPreferences: SharedPreferences

    @Inject
    internal lateinit var weChatConnectServiceHelper: WeChatConnectServiceHelper

    @Inject
    lateinit var intentFactory: IntentFactory

    @Inject
    lateinit var newSportModeNavigator: NewSportModeNavigator

    @Inject
    lateinit var offlineMusicNavigator: OfflineMusicNavigator

    @Inject
    lateinit var headsetNavigator: HeadsetNavigator

    @Inject
    lateinit var offlineMapsNavigator: OfflineMapsNavigator

    @Inject
    lateinit var nfcNavigator: NfcNavigator

    private var sendingLogsSnackBar: Snackbar? = null

    private var connectionInstability: Boolean = false
    private val backgroundLocationRequestHelper = BackgroundLocationRequestHelper()
    private var backgroundLocationRequestDisposable: Disposable? = null

    /**
     * Ensure that the battery optimization dialog is shown only once per visit to DeviceActivity.
     */
    private var batteryOptimizationDialogShown = false

    /**
     * Keep track if notification access dialog is shown to the user. It is shown once per DeviceActivity start.
     */
    private var notificationAccessDialogShown = AtomicBoolean(false)

    /**
     * Keep track if notification settings have been opened during activity's active state.
     */
    private var notificationSettingsShown = AtomicBoolean(false)

    private val navController get() = findNavController(R.id.device_activity_nav_host_fragment)

    private lateinit var deviceTypeSelectLauncher: ActivityResultLauncher<Unit>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setSupportActionBar(viewDataBinding.toolbarDevice)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        // Set empty default title to prevent flickering
        title = ""

        deviceTypeSelectLauncher = registerForActivityResult(
            DeviceTypeSelectActivity.ResultContract()
        ) { deviceType ->
            when (deviceType) {
                DeviceType.WATCH -> viewModel.prepareToPairAnotherDevice()
                DeviceType.HEADPHONES -> launchHeadsetActivity()
                null -> {}
            }
        }

        viewModel.onboardingResultLauncher =
            registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
                // Start post-pairing watch sync after the user exits the onboarding
                viewModel.onSyncNow()
            }

        // Handle common user actions
        viewModel.actionEvent.observeNotNull(this) { event ->
            when (event) {
                is ShowHelp ->
                    supportHelper.showFAQ(
                        this,
                        STTConstants.HelpShiftPublishId.SUUNTO_MAIN_SUPPORT,
                        appVersionNumberForSupport
                    )

                is ShowHelpArticle ->
                    supportHelper.showFAQ(
                        this,
                        event.articleId,
                        appVersionNumberForSupport
                    )

                is ShowIntroduction -> {
                    deviceOnboardingNavigator
                        .newOnboardingActivityIntent(this, event.deviceType)
                        ?.let { startActivity(it) }
                }

                is ShowAboutDevice -> {
                    startActivity(
                        WatchDetailActivity.newStartIntent(
                            this,
                            event.version,
                            event.serial,
                            event.deviceInfoWear,
                            otaUpdateSupported = event.otaUpdateSupported,
                            suuntoDeviceType = event.suuntoDeviceType,
                            updateAvailable = event.updateAvailable,
                        )
                    )
                }

                is ShowPowerManagementSettings -> showManageConnection()
                is ShowNotificationsSettings -> {
                    startActivity(WatchNotificationsPermissionsActivity.newStartIntent(this))
                }

                is LogSendStarted -> {
                    if (sendingLogsSnackBar == null) {
                        val snackBar = Snackbar.make(
                            viewDataBinding.root,
                            if (event.watchConnected) {
                                R.string.watch_snackbar_sending_logs
                            } else {
                                R.string.watch_snackbar_sending_logs_with_no_watch
                            },
                            Snackbar.LENGTH_INDEFINITE
                        )
                        snackBar.show()
                        sendingLogsSnackBar = snackBar
                    }
                }

                is LogSendEnded -> {
                    sendingLogsSnackBar?.let {
                        it.dismiss()
                        sendingLogsSnackBar = null
                    }
                }

                is ShowSuuntoPlusStore -> {
                    startActivity(
                        if (event.isRunDevice) {
                            suuntoPlusStoreNavigator.newSuuntoPlusStoreGuidesPageIntent(this, true)
                        } else {
                            suuntoPlusStoreNavigator.newSuuntoPlusStoreIntent(this)
                        }
                    )
                }

                is CustomizeSportModes -> {
                    if (event.suuntoDeviceType.isRunDevice) {
                        newCustomizeSportModes()
                    } else {
                        customizeSportModes(
                            event.fteCompleted,
                            event.suuntoDeviceType,
                            event.supportMode
                        )
                    }
                }

                is ShowStructuredWorkoutPlanner -> showStructuredWorkoutPlanner()
                is ShowSuuntoPlusFeatures -> showSuuntoPlusFeatures()
                is ShowSuuntoPlusWatchface -> showSuuntoPlusWatchface()
                is ShowSuuntoPlusGuides -> showSuuntoPlusGuides()
                is ShowWidgetCustomization -> showWidgetCustomization()
                is CustomizeDiveModes -> startActivity(
                    diveModesNavigator.newInstance(
                        this,
                        deviceSerial = event.deviceSerial,
                        deviceHwVersion = event.deviceHwVersion,
                        deviceFwVersion = event.deviceFwVersion,
                        deviceVariantName = event.deviceVariantName
                    )
                )

                is ConnectionInstability -> showConnectionInstabilityDialog()
                is UpdateFirmware -> openUpdateFirmware(event.supportsOta, event.suuntoDeviceType)
                is ShowFirmwareUpdateRequired -> showFirmwareUpdateRequired(event.deviceType)
                is ShowUserGuide -> {
                    event.url?.let {
                        CustomTabsUtils.launchCustomTab(this, getString(it))

                        datahubAnalyticsTracker.trackEvent(
                            AnalyticsEvent.SUUNTO_WATCH_USER_GUIDE_OPENED,
                            AnalyticsProperties()
                                .put(
                                    AnalyticsEventProperty.SUUNTO_WATCH_MODEL,
                                    getWatchModelNameForSuuntoDeviceType(event.deviceType)
                                )
                                .map
                        )
                    }
                }

                is ShowPostUnpairWarning -> showPostUnpairWarningDialogIfNeeded(event.deviceType)
                is TransferFirmware -> transferFirmware()
                is ImportWorkout -> importWorkout()
                is ShowMovescountUninstallDialog -> showMovescountUninstallDialog()
                is RequestBackgroundLocationPermission -> requestBackgroundLocationPermission()
                is GetEonSettings -> getEonSettings()
                is SetEonSettings -> setEonSettings()
                is ShowOfflineMaps -> offlineMapsNavigator.openOfflineMapSelection(
                    context = this,
                    analyticsSource = DownloadMapsScreenSource.WATCH_MANAGEMENT_SCREEN,
                )

                is ShowEonSettingsShareDialog -> shareEonSettingsFile(event.eonSettingsFile)
                is ShowWifiNetworks -> startActivity(WifiNetworksActivity.newStartIntent(this))
                is ShowKeepConnectedSettings -> startActivity(
                    PowerManagementSettingsActivity.newStartIntent(
                        this,
                        CompanionAssociationUIContext.SETTINGS_POWER_MANAGEMENT
                    )
                )

                is ConnectWeRun -> {
                    if (event.connected) {
                        weChatConnectServiceHelper.toWeRunConnectedPage(this)
                    } else {
                        weChatConnectServiceHelper.toWeRunConnectGuidePage(this)
                    }
                }

                is GetToKnowNewWatch -> {
                    SupportActivity.newIntent(
                        this,
                        getString(R.string.updates_info_url_forced_update_phoenix),
                        title = ""
                    )
                }

                is ShowOfflineMusic -> {
                    startActivity(offlineMusicNavigator.newOfflineMusicIntent(this))
                }

                is PairAnotherDevice -> {
                    deviceTypeSelectLauncher.launch(Unit)
                }
                is FindWatch -> {
                    startActivity(FindWatchActivity.newStartIntent(this))
                }

                TransitCard -> {
                    nfcNavigator.openTransitCardScreen(this)
                }
            }
        }

        viewModel.deviceStateEvent.observeNotNull(this) { event ->
            when (event) {
                is DeviceStateUpdate -> {
                    // Check that the firmware version is supported when the device is connected
                    // TODO shouldn't this require a registered watch? aka paired from what I understand
                    event.deviceType?.let {
                        if (event.watchState?.connectedWatchConnectionState == ConnectedWatchConnectionState.CONNECTED &&
                            event.firmwareUpdateRequired
                        ) {
                            showFirmwareUpdateRequired(event.deviceType)
                        }
                    }

                    // When no device is attached, close all device related dialogs that might have been left open
                    if (event.deviceType == null) {
                        viewModel.cancelBatteryNotificationCheck()
                        batteryOptimizationUtils.closeDialog(this)
                        val fragment =
                            supportFragmentManager.findFragmentByTag(NOTIFICATION_ACCESS_DIALOG) as? DialogFragment
                        fragment?.dismiss()
                    }
                }

                else -> {} // do nothing
            }
        }

        viewModel.batteryAndNotificationCheckEvent.observeK(this) {
            if (!batteryOptimizationDialogShown &&
                !batteryOptimizationUtils.checkBatteryOptimizationState(this)
            ) {
                // No need to request user to whitelist app.
                showNotificationDialogConditionally()
            }
            batteryOptimizationDialogShown = true
        }

        viewModel.toastLiveData.observeNotNull(this) { message ->
            showToast(message)
        }

        viewModel.openOnboardingEvent.observeNotNull(this) {
            openOnboarding(it)
        }

        viewModel.openNotificationAccess.observeK(this) {
            showNotificationSettings()
        }

        // Navigation listener
        navController.addOnDestinationChangedListener { _, destination, _ ->
            if (destination.id == R.id.deviceConnectedFragment) {
                // todo the flow in this activity of all the requests done after pairing is almost impossible to follow, needs refactoring
                // companion flow goes first
                companionAssociationHelperOptional.toNullable()?.refreshCompanionState()
                // otherwise check for doze mode when connected
                checkBatteryOptimizationState()
                // privacy setting
                if (!viewModel.isPrivacyAlreadyPrompted()) {
                    fragmentManager()?.let {
                        PrivacySettingDialog().show(it, PRIVACY_SETTING_DIALOG)
                    }
                }
                // refresh notification section summary
                viewModel.updateWatchNotificationsSummary(applicationContext)
            }
        }

        LoginHelper.justLoggedIn.set(false)
        if (intent.action == SEND_LOGS_TO_FILE_INTENT_ACTION) {
            viewModel.sendLogs(filesDir, true)
        }
        handlePairDeviceIntent(intent)
        viewModel.checkBackgroundLocationPermissionStatus(startRequestIfNeeded = true)

        checkNeedForCompanionAssociation()
        refreshCompanionAssociation()

        // set the state which if this user have device connect weChat
        viewModel.weChatConnectionState()

        viewModel.setupPreferenceResultLauncher = registerForActivityResult(
            SetupPreferenceActivity.ResultContract()
        ) { result: Result<SetupPreference> ->
            viewModel.setupPreferenceContinuation?.let { continuation ->
                if (continuation.context[Job]?.isCompleted == false) {
                    continuation.resumeWith(result)
                } else {
                    Timber.w("setupPreferenceContinuation already resumed, ignoring...")
                }
                viewModel.setupPreferenceContinuation = null
            }
        }
    }

    /**
     * Both [com.suunto.headset.ui.HeadsetActivity] and [DeviceActivity] use the
     * [com.stt.android.utils.AndroidFeatureStates] singleton which only support one subscriber to
     * subscribe Bluetooth status, so we need to unsubscribe it in advance and exit [DeviceActivity].
     */
    private fun launchHeadsetActivity() {
        viewModel.onCleared()
        headsetNavigator.launchHeadsetActivity(this)
        finish()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handlePairDeviceIntent(intent)
    }

    private fun handlePairDeviceIntent(intent: Intent) {
        if (intent.action == FOR_PAIRING_DEVICE_INTENT_ACTION) {
            val macAddress = intent.getStringExtra(FOR_PARING_DEVICE_MAC_ADDRESS)
            viewModel.trySwitchDevice(this, macAddress)
        }
    }

    private fun refreshCompanionAssociation() {
        val companionAssociationHelper = companionAssociationHelperOptional.toNullable() ?: return
        launchOnLifecycle {
            companionAssociationHelper.refreshCompanionState()
            companionAssociationHelper.companionState.collect { companionState ->
                if (companionState is CompanionAssociationHelper.CompanionState.Associated) {
                    viewModel.setCompanionAssociation(true)
                } else {
                    viewModel.setCompanionAssociation(false)
                }
            }
        }
    }

    private fun checkNeedForCompanionAssociation() {
        val companionAssociationHelper = companionAssociationHelperOptional.toNullable() ?: return
        launchOnLifecycle {
            companionAssociationHelper.companionState.collect { companionState ->
                if (companionState is CompanionAssociationHelper.CompanionState.NotAssociated &&
                    !viewModel.isNeedForCompanionAssociationAsked()
                ) {
                    companionAssociationHelperOptional.toNullable()
                        ?.startCompanionAssociationRequest(
                            CompanionAssociationUIContext.WATCH_SCREEN_POPUP
                        )
                    viewModel.setNeedForCompanionAssociationAsked()
                }
            }
        }
        launchOnLifecycle {
            companionAssociationHelper.pairingRequestState.collect {
                if (it is CompanionAssociationHelper.PairingRequestState.Pending && !it.consumed &&
                    it.intentSender != null
                ) {
                    startIntentSenderForResult(
                        it.intentSender,
                        DEVICE_MANAGER_ASSOCIATION_REQUEST_CODE,
                        null,
                        0,
                        0,
                        0,
                        null
                    )
                    it.consumed = true
                }
            }
        }
    }

    private fun transferFirmware() {
        showFileChooser(REQUEST_FIRMAWARE_FILE)
    }

    private fun importWorkout() {
        showFileChooser(REQUEST_IMPORT_WORKOUT)
    }

    private fun onDebugLocationClick() {
        val intent = intentFactory.createIntent(
            this,
            IntentKey.DebugWatchLocation
        )
        startActivity(intent)
    }

    private fun setEonSettings() {
        showFileChooser(REQUEST_EON_SETTINGS_FILE)
    }

    private fun getEonSettings() {
        viewModel.getEonSettings(applicationContext)
    }

    private fun openOnboarding(suuntoDeviceType: SuuntoDeviceType) {
        deviceOnboardingNavigator.newOnboardingActivityIntent(
            this,
            suuntoDeviceType
        )?.let { intent -> startActivity(intent) }
    }

    private fun showFileChooser(requestCode: Int) {
        val intent = Intent(Intent.ACTION_GET_CONTENT)
        intent.type = "*/*"
        intent.addCategory(Intent.CATEGORY_OPENABLE)
        startActivityForResult(
            Intent.createChooser(intent, "Select a File to Upload"),
            requestCode
        )
    }

    private fun showPostUnpairWarningDialogIfNeeded(deviceType: SuuntoDeviceType) {
        if (deviceType.isSuunto7) {
            val dialog = SimpleDialogFragment.newInstance(
                message = getString(R.string.manage_connection_forget_watch_warning_suunto_7),
                title = getString(R.string.important),
                positiveButtonText = getString(R.string.got_it),
                cancellable = true
            )
            dialog.show(supportFragmentManager, POST_UNPAIR_WARNING_DIALOG)
        }
    }

    private fun shouldShowNoWatchPairedQuestionDialog(): Boolean {
        if (hasSeenExitNoWatchQuestionDialog()) {
            return false
        }

        if (!suuntoSharedPrefs.getString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
                null
            ).isNullOrEmpty()
        ) {
            return false
        }
        // No device in shared prefs then
        // 1. Check if there is a device state
        val deviceState = viewModel.deviceStateRelay.value
        if (deviceState !is DeviceStateUpdate) {
            return true
        }
        // 2. If there is and has a device then don't show
        return deviceState.deviceType == null
    }

    private fun showNoWatchPairedQuestionDialog() {
        val dialog = SingleChoiceDialogFragment.newInstance(
            getString(R.string.dialog_no_watch_paired_question_title),
            resources.getStringArray(R.array.dialog_no_watch_paired_question_answers),
            -1
        )
        dialog.show(supportFragmentManager, NO_WATCH_PAIRED_DIALOG)
        markHasSeenNoWatchPairedQuestionDialog()
        datahubAnalyticsTracker.trackEvent(AnalyticsEvent.SUUNTO_NO_WATCH_PAIRED_DIALOG_SHOWN)
    }

    override fun onStart() {
        super.onStart()
        // when jump to wechat App and back, refresh connection state
        if (weChatPreferences.getBoolean(STTConstants.WeChatPreferences.KEY_REFRESH, false)) {
            viewModel.weChatConnectionState()
        }
        viewModel.sendAnalyticsEventFromWatchState(
            AnalyticsEvent.SUUNTO_WATCH_SCREEN,
            toAmplitude = true,
            toBraze = true,
            includeMovescountInstalledStatus = true
        )
    }

    override fun onResume() {
        super.onResume()

        if (notificationSettingsShown.getAndSet(false)) {
            checkPhoneAndContactsPermissions()
            viewModel.trySetNotificationsEnabled(this)
        }
        viewModel.updateWatchNotificationsSummary(applicationContext)
        viewModel.updateAutoLocationSetting()
        viewModel.checkBackgroundLocationPermissionStatus(startRequestIfNeeded = false)
    }

    @SuppressLint("MissingSuperCall")
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        // Device activity has predefined states and moving between those states
        // require that some conditions are, so it doesn't make sense to allow
        // the user to change the state by pressing the back button.
        if (shouldShowNoWatchPairedQuestionDialog()) {
            showNoWatchPairedQuestionDialog()
            return
        }
        finish()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.device_menu_about_device -> viewModel.showAboutDevice()
            R.id.device_menu_help -> viewModel.onHelpClick()
            R.id.device_menu_transfer_firmware -> viewModel.onTransferFirmwareClick()
            R.id.device_menu_import_workout -> viewModel.onImportWorkoutClick()
            R.id.device_menu_debug_location -> onDebugLocationClick()
            R.id.device_menu_get_eon_settings -> viewModel.onGetEonSettingsClicked()
            R.id.device_menu_set_eon_settings -> viewModel.onSetEonSettingsClicked()
            R.id.device_menu_wifi_networks -> viewModel.onWifiNetworksClicked()
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            BatteryOptimizationUtils.REQUEST_BATTERY_OPTIMIZATION_MODE -> {
                // Regardless of the outcome, continue flow and check for notification access
                showNotificationDialogConditionally()

                datahubAnalyticsTracker.trackEvent(
                    AnalyticsEvent.DOZE_MODE,
                    AnalyticsProperties()
                        .putYesNo(
                            AnalyticsEvent.DOZE_MODE_DISABLED,
                            batteryOptimizationUtils.isIgnoringBatteryOptimizations()
                        )
                )
            }

            REQUEST_MANAGE_CONNECTION -> {
                val device = data?.getParcelableExtra<SuuntoBtDevice>(
                    ManageConnectionFragment.EXTRA_DEVICE_UNPAIRED
                )
                if (device != null) {
                    viewModel.onUnpairCompleted(device)
                    companionAssociationHelperOptional.toNullable()?.removeCompanionAssociation()
                    viewModel.clearNeedForCompanionAssociationAsked()
                    viewModel.setNotificationAccessDialogAsked(false)
                    viewModel.setNotificationAccessAlertDismissed(false)
                }
            }

            REQUEST_FIRMAWARE_FILE -> {
                if (resultCode == RESULT_OK) {
                    data?.data?.let {
                        viewModel.startFirmwareFileTransfer(it)
                    }
                }
            }

            REQUEST_IMPORT_WORKOUT -> {
                if (resultCode == RESULT_OK) {
                    data?.data?.let {
                        viewModel.importWorkoutFromFile(it)
                    }
                }
            }

            REQUEST_EON_SETTINGS_FILE -> {
                if (resultCode == RESULT_OK) {
                    data?.data?.let {
                        viewModel.setEonSettings(it)
                    }
                }
            }

            REQUEST_FIRMAWARE_UPDATE_ACTIVITY -> {
                if (resultCode != RESULT_OK) {
                    val snackbar = Snackbar.make(
                        viewDataBinding.root,
                        R.string.watch_updates_failed_to_check_for_updates,
                        Snackbar.LENGTH_LONG
                    )
                    snackbar.show()
                }
            }

            DEVICE_MANAGER_ASSOCIATION_REQUEST_CODE -> {
                if (companionAssociationHelperOptional.toNullable()
                        ?.handleOnActivityResult(this, data) == true
                ) {
                    notificationSettingsShown.set(true)
                }
                // continue flow to checking for Doze mode
                checkBatteryOptimizationState()
            }
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(
            applicationContext,
            message,
            Toast.LENGTH_LONG
        ).show()
    }

    //region Notification
    private fun wereNotificationsAllowedBefore(): Boolean {
        return PreferencesUtils.getSuuntoSharedPreferences(
            this,
            STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_NOTIFICATIONS_ALLOWED_AT_LEAST_ONCE,
            false
        )
    }

    private fun markNotificationsAllowedOnce() {
        PreferencesUtils.putSuuntoSharedPreferences(
            this,
            STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_NOTIFICATIONS_ALLOWED_AT_LEAST_ONCE,
            true
        )
    }

    private fun hasSeenExitNoWatchQuestionDialog(): Boolean {
        return suuntoSharedPrefs.getBoolean(
            STTConstants.SuuntoPreferences.KEY_HAS_SEEN_NO_WATCH_PAIRED_QUESTION,
            false
        )
    }

    private fun markHasSeenNoWatchPairedQuestionDialog() {
        suuntoSharedPrefs.edit {
            putBoolean(STTConstants.SuuntoPreferences.KEY_HAS_SEEN_NO_WATCH_PAIRED_QUESTION, true)
        }
    }

    private fun showNotificationSettings() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            val companionAssociationHelper =
                companionAssociationHelperOptional.toNullable() ?: return
            if (companionAssociationHelper.isAssociated) {
                companionAssociationHelper.requestNotificationAccess(this)
            } else {
                companionAssociationHelper.startCompanionAssociationRequest(
                    CompanionAssociationUIContext.WATCH_NOTIFICATION_PERMISSIONS_SCREEN
                )
            }
        } else {
            markNotificationsAllowedOnce()
            notificationSettingsShown.set(true)
            notificationSettingsHelper.openNotificationAccessSystemMenu(this)
            analyticsUtils.onShowNotificationSettings(
                notificationSettingsHelper.notificationsEnabled(
                    this
                )
            )
        }
    }

    private fun showNotificationsAlert() {
        val dialog = SimpleDialogFragment.newInstance(
            message = getString(R.string.watch_ui_enable_notification_access),
            title = getString(R.string.watch_ui_enable_notification_title),
            positiveButtonText = getString(R.string.allow),
            negativeButtonText = getString(R.string.dont_allow)
        )
        dialog.show(supportFragmentManager, NOTIFICATION_ACCESS_DIALOG)
        viewModel.setNotificationAccessDialogAsked(true)
    }

    /**
     * Show notifications dialog if conditions apply. If not, then do nothing.
     */
    private fun showNotificationDialogConditionally() {
        val shouldPrompt = viewModel.watchNotificationsSupported.get() &&
            !notificationSettingsHelper.notificationsEnabled(this)
        if (shouldPrompt) {
            val dialogAsked = viewModel.isNotificationAccessDialogAsked()
            when {
                dialogAsked && !viewModel.isNotificationAccessAlertDismissed() -> {
                    viewModel.setNeedNotificationAccessAlert(true)
                }

                !dialogAsked && !notificationAccessDialogShown.getAndSet(true) -> {
                    showNotificationsAlert()
                }
            }
        } else {
            viewModel.setNeedNotificationAccessAlert(false)
        }
    }

    private fun showManageConnection() {
        startActivityForResult(
            ManageConnectionActivity.newIntent(this),
            REQUEST_MANAGE_CONNECTION
        )
    }

    private fun checkBatteryOptimizationState() {
        viewModel.scheduleBatteryAndNotificationCheck()
    }

    private fun checkPhoneAndContactsPermissions() {
        val missingPermissions = getMissingNotificationsPermissions(
            notificationSettingsHelper,
            this,
            emarsysAnalytics,
            datahubAnalyticsTracker
        )
        if (missingPermissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(
                this,
                missingPermissions.toTypedArray(),
                REQUEST_READ_PHONE_AND_CONTACTS
            )
        } else {
            viewModel.trySetNotificationsCategoryEnabled(this)
        }
    }

    override fun onDialogCancel() {
        // Check is notifications dialog should be shown
        // when battery optimization dialog is canceled
        showNotificationDialogConditionally()
        datahubAnalyticsTracker.trackEvent(
            AnalyticsEvent.DOZE_MODE,
            AnalyticsProperties()
                .putYesNo(AnalyticsEvent.DOZE_MODE_DISABLED, false)
        )
    }
    //endregion

    //region Sport mode
    private fun customizeSportModes(
        fteCompleted: Boolean,
        suuntoDeviceType: SuuntoDeviceType,
        supportMode: SupportMode
    ) {
        val sharedPreferences =
            getSharedPreferences(STTConstants.SuuntoPreferences.PREFS_NAME, Context.MODE_PRIVATE)
        val watchModel = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
            ""
        )
        val watchFirmware =
            sharedPreferences.getString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_FW_VERSION,
                ""
            )
        val serial =
            sharedPreferences.getString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_SERIAL_NUMBER,
                ""
            )
        val intent = Intent(this, SportModeActivity::class.java)
            .putEnumExtra(SportModeActivity.SUPPORT_MODE, supportMode)
            .putExtra(SportModeActivity.FTE_COMPLETED, fteCompleted)
            .putExtra(SportModeActivity.WATCH_MODEL, watchModel)
            .putExtra(SportModeActivity.WATCH_FIRMWARE, watchFirmware)
            .putExtra(SportModeActivity.WATCH_SERIAL_NUMBER, serial)
        intent.putEnumExtra(SportModeActivity.SUUNTO_DEVICE_TYPE, suuntoDeviceType)
        startActivity(intent)
    }

    private fun newCustomizeSportModes() {
        startActivity(newSportModeNavigator.startNewIntent(this))
    }
    //endregion

    //region SuuntoPlus
    private fun showStructuredWorkoutPlanner() {
        startActivity(
            workoutPlannerNavigator.newListWorkoutPlansIntent(
                this,
                AnalyticsPropertyValue.WorkoutPlanner.PLANNER_SCREEN_SOURCE_WATCH_SCREEN
            )
        )
    }

    private fun showSuuntoPlusFeatures() {
        startActivity(suuntoPlusNavigator.newSuuntoPlusFeaturesIntent(this))
    }

    private fun showSuuntoPlusWatchface() {
        startActivity(suuntoPlusNavigator.newSuuntoPlusWatchfaceIntent(this))
    }

    private fun showSuuntoPlusGuides() {
        startActivity(suuntoPlusNavigator.newSuuntoPlusGuideIntent(this))
    }
    //endregion

    //region Widget customization
    private fun showWidgetCustomization() {
        startActivity(
            watchWidgetCustomizationNavigator.newWatchWidgetCustomizationIntent(
                this,
                AnalyticsPropertyValue.WatchEditWidgetScreenSource.WATCH_SCREEN
            )
        )
    }
    //endregion

    //region Connection action
    private fun showConnectionInstabilityDialog() {
        if (!connectionInstability) {
            connectionInstability = true

            val dialog = SimpleDialogFragment.newInstance(
                message = getString(R.string.connection_instability_dialog_message),
                title = getString(R.string.connection_instability_dialog_title),
                positiveButtonText = getString(R.string.connection_instability_dialog_show_trouble_shooting),
                negativeButtonText = getString(R.string.connection_instability_dialog_close),
                cancellable = false
            )
            dialog.show(supportFragmentManager, CONNECTION_INSTABILITY_DIALOG)
        }
    }
    //endregion

    //region Firmware
    private fun showFirmwareUpdateRequired(deviceType: SuuntoDeviceType) {
        val productName = getString(WatchHelper.getStringResIdForSuuntoDeviceType(deviceType))
        val messageResId = when {
            deviceType.isEon -> {
                R.string.watch_update_required_for_model_message_dm5
            }

            else -> {
                R.string.watch_update_required_for_model_message_suuntolink
            }
        }

        val message = getString(messageResId, productName)
        val title = getString(R.string.watch_update_required_for_model_title, productName)

        val dialog = SimpleDialogFragment.newInstance(
            message = message,
            title = title,
            positiveButtonText = getString(R.string.close),
            negativeButtonText = getString(R.string.watch_update_required_see_whats_new)
        )
        dialog.show(supportFragmentManager, FIRMWARE_UPDATE_DIALOG)
    }

    private fun openUpdateFirmware(supportsOta: Boolean, suuntoDeviceType: SuuntoDeviceType?) {
        if (suuntoDeviceType != null && supportsOta) {
            startActivityForResult(
                WatchUpdatesActivity.newStartIntent(
                    this@DeviceActivity,
                    suuntoDeviceType
                ),
                WatchDetailActivity.REQUEST_FIRMAWARE_UPDATE_ACTIVITY
            )
        } else {
            try {
                val uri = getString(R.string.watch_fw_update_available_link).toUri()
                @Suppress("UnsafeImplicitIntentLaunch")
                startActivity(Intent(Intent.ACTION_VIEW).setData(uri))
            } catch (e: ActivityNotFoundException) {
                // there are devices that can't find activity to handle this intent
                // https://fabric.io/sporst-tracker/android/apps/com.stt
                // .android/issues/56481976f5d3a7f76b9616ba
                val dialog = SimpleDialogFragment.newInstance(message = getString(R.string.error_0))
                dialog.show(supportFragmentManager, GENERIC_ERROR_DIALOG)
            }
        }
    }

    //endregion

    //region SimpleDialogFragment callbacks

    /**
     * Handle button presses from SimpleDialogFragment instances
     */
    override fun onDialogButtonPressed(tag: String?, which: Int) {
        when (tag) {
            NOTIFICATION_ACCESS_DIALOG -> {
                when (which) {
                    DialogInterface.BUTTON_POSITIVE -> showNotificationSettings()
                    DialogInterface.BUTTON_NEGATIVE -> {
                        viewModel.setNeedNotificationAccessAlert(
                            viewModel.checkNotificationAccessNeedAlert(this)
                        )
                    }
                }
            }

            CONNECTION_INSTABILITY_DIALOG -> {
                when (which) {
                    DialogInterface.BUTTON_POSITIVE ->
                        viewModel.clearConnectionInstability {
                            connectionInstability = false
                            val deviceStateUpdate =
                                viewModel.deviceStateEvent.value as? DeviceStateUpdate
                            val deviceType =
                                deviceStateUpdate?.deviceType ?: SuuntoDeviceType.Unrecognized
                            val publishId = if (deviceType.isEon) {
                                STTConstants.HelpShiftPublishId.SUUNTO_WATCH_SYNCING_OR_PAIRING_ISSUE_DIVE
                            } else {
                                STTConstants.HelpShiftPublishId.SUUNTO_WATCH_SYNCING_OR_PAIRING_ISSUE
                            }
                            supportHelper.showFAQ(this, publishId, appVersionNumberForSupport)
                        }

                    DialogInterface.BUTTON_NEGATIVE ->
                        viewModel.clearConnectionInstability {
                            connectionInstability = false
                        }
                }
            }

            FIRMWARE_UPDATE_DIALOG -> {
                when (which) {
                    DialogInterface.BUTTON_POSITIVE -> finish()
                    DialogInterface.BUTTON_NEGATIVE -> {
                        val deviceStateUpdate =
                            viewModel.deviceStateEvent.value as? DeviceStateUpdate
                        val deviceType =
                            deviceStateUpdate?.deviceType ?: SuuntoDeviceType.Unrecognized

                        val updateLink = getString(
                            when {
                                deviceType.isSuunto3Family ->
                                    R.string.updates_info_url_suunto3fitness

                                deviceType == SuuntoDeviceType.Suunto9 || deviceType == SuuntoDeviceType.Suunto9Lima ->
                                    R.string.updates_info_url_suunto9

                                deviceType.isSpartan ->
                                    R.string.updates_info_url_spartan

                                deviceType.isEon ->
                                    R.string.updates_info_url_eon

                                else ->
                                    R.string.updates_info_url_generic
                            }
                        )
                        try {
                            @Suppress("UnsafeImplicitIntentLaunch")
                            startActivity(Intent(Intent.ACTION_VIEW, updateLink.toUri()))
                            finish()
                        } catch (e: ActivityNotFoundException) {
                            val dialog =
                                SimpleDialogFragment.newInstance(message = getString(R.string.error_0))
                            dialog.show(supportFragmentManager, GENERIC_ERROR_DIALOG)
                        }
                    }
                }
            }
        }
    }

    override fun onDialogItemSelected(tag: String?, index: Int) {
        when (tag) {
            NO_WATCH_PAIRED_DIALOG -> {
                val analyticsEvent = AnalyticsEvent.SUUNTO_NO_WATCH_PAIRED_DIALOG_RESPONSE
                val analyticsProperties =
                    AnalyticsProperties().put(
                        AnalyticsEventProperty.SUUNTO_NO_WATCH_PAIRED_DIALOG_RESPONSE,
                        AnalyticsPropertyValue.NoWatchPairedDialogResponse.ANSWERED
                    ).put(
                        AnalyticsEventProperty.SUUNTO_NO_WATCH_PAIRED_DIALOG_REASON,
                        when (index) {
                            0 -> AnalyticsPropertyValue.NoWatchPairedDialogReason.ALREADY_PAIRED_TO_ANOTHER_DEVICE
                            1 -> AnalyticsPropertyValue.NoWatchPairedDialogReason.NO_SUUNTO_YET
                            2 -> AnalyticsPropertyValue.NoWatchPairedDialogReason.DEVICE_NOT_ARRIVED
                            3 -> AnalyticsPropertyValue.NoWatchPairedDialogReason.HAD_PROBLEMS_PAIRING
                            4 -> AnalyticsPropertyValue.NoWatchPairedDialogReason.LATER
                            else -> null
                        }
                    )

                datahubAnalyticsTracker.trackEvent(analyticsEvent, analyticsProperties)
                emarsysAnalytics.trackEventWithProperties(
                    analyticsEvent,
                    analyticsProperties.map
                )
                // Finish the activity because the dialog has been shown on onBackPressed and home navigation
                finish()
            }
        }
    }

    /**
     * Handle SimpleDialogFragment dismissals
     */
    override fun onDialogDismissed(tag: String?) {
        when (tag) {
            FIRMWARE_UPDATE_DIALOG -> finish()
        }
    }

    override fun onSingleChoiceDialogCanceled(tag: String?) {
        when (tag) {
            NO_WATCH_PAIRED_DIALOG -> {
                datahubAnalyticsTracker.trackEvent(
                    AnalyticsEvent.SUUNTO_NO_WATCH_PAIRED_DIALOG_RESPONSE,
                    AnalyticsProperties().put(
                        AnalyticsEventProperty.SUUNTO_NO_WATCH_PAIRED_DIALOG_RESPONSE,
                        AnalyticsPropertyValue.NoWatchPairedDialogResponse.CANCEL
                    )
                )
            }
        }
    }

    override fun onSingleChoiceDialogDismissed(tag: String?) {
        // do nothing
    }
    //endregion

    private fun showMovescountUninstallDialog() {
        val message = getString(R.string.movescount_uninstall_dialog_content)
        val title = getString(R.string.movescount_uninstall_dialog_header)

        val dialog = SimpleDialogFragment.newInstance(
            message = message,
            title = title,
            positiveButtonText = getString(R.string.movescount_uninstall_dialog_button),
            negativeButtonText = ""
        )
        dialog.show(supportFragmentManager, MOVESCOUNT_UNINSTALL_DIALOG)
    }

    private fun requestBackgroundLocationPermission() {
        if (backgroundLocationRequestDisposable?.isDisposed == false) {
            return
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            backgroundLocationRequestDisposable =
                backgroundLocationRequestHelper.checkAndRequestBackgroundLocationPermission(
                    this,
                    requestFineLocationPermission = {
                        ActivityCompat.requestPermissions(
                            this,
                            arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
                            BackgroundLocationRequestHelper.REQUEST_FINE_LOCATION
                        )
                    },
                    requestBackgroundLocationPermission = {
                        ActivityCompat.requestPermissions(
                            this,
                            arrayOf(Manifest.permission.ACCESS_BACKGROUND_LOCATION),
                            BackgroundLocationRequestHelper.REQUEST_BACKGROUND_LOCATION
                        )
                    }
                )
                    .subscribeOn(AndroidSchedulers.mainThread())
                    .subscribeBy(
                        onComplete = {
                            Timber.d("Background location request successful")
                            setBackgroundLocationUserProperty()
                        },
                        onError = {
                            Timber.w(it, "Background location request unsuccessful")
                            setBackgroundLocationUserProperty()
                        }
                    )
        }
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private fun setBackgroundLocationUserProperty() {
        datahubAnalyticsTracker.trackUserProperty(
            AnalyticsUserProperty.ANDROID_BACKGROUND_LOCATION_ENABLED,
            if (isBackgroundLocationPermissionGranted()) AnalyticsPropertyValue.YES else AnalyticsPropertyValue.NO
        )
    }

    override fun onDestroy() {
        backgroundLocationRequestDisposable?.dispose()
        companionAssociationHelperOptional.toNullable()?.destroy()
        super.onDestroy()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            backgroundLocationRequestHelper.onRequestPermissionsResult(
                activity = this,
                requestCode = requestCode,
                grantResults = grantResults
            )
        }
        if (requestCode == REQUEST_READ_PHONE_AND_CONTACTS &&
            getMissingNotificationsPermissions(
                notificationSettingsHelper,
                this,
                emarsysAnalytics,
                datahubAnalyticsTracker
            ).isEmpty()
        ) {
            viewModel.trySetNotificationsCategoryEnabled(this)
        }
    }

    private fun shareEonSettingsFile(eonSettingsFile: File) {
        val intentShareFile = Intent(Intent.ACTION_SEND)
        if (eonSettingsFile.exists()) {
            val uri: Uri = FileProvider.getUriForFile(
                this,
                FILE_PROVIDER_AUTHORITY,
                eonSettingsFile
            )
            intentShareFile.type = "application/xml"
            intentShareFile.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            intentShareFile.putExtra(
                Intent.EXTRA_STREAM,
                uri
            )
            intentShareFile.putExtra(
                Intent.EXTRA_SUBJECT,
                "Sharing EON settings file..."
            )
            intentShareFile.putExtra(Intent.EXTRA_TEXT, "Sharing EON settings file...")
            startActivity(
                Intent.createChooser(
                    intentShareFile,
                    "Sharing EON settings file"
                )
            )
        }
    }

    companion object {
        private const val REQUEST_READ_PHONE_AND_CONTACTS = 1003
        private const val REQUEST_MANAGE_CONNECTION = 901
        private const val NOTIFICATION_ACCESS_DIALOG = "NotificationAccessDialog"
        private const val PRIVACY_SETTING_DIALOG = "PrivacySettingDialog"
        private const val CONNECTION_INSTABILITY_DIALOG = "ConnectionInstabilityDialog"
        private const val FIRMWARE_UPDATE_DIALOG = "FirmwareUpdateDialog"
        private const val GENERIC_ERROR_DIALOG = "GenericErrorDialog"
        private const val POST_UNPAIR_WARNING_DIALOG = "PostUnpairWarningDialog"
        private const val SEND_LOGS_TO_FILE_INTENT_ACTION = "SendLogsToFile"
        private const val FOR_PAIRING_DEVICE_INTENT_ACTION = "ForPairingDevice"
        private const val FOR_PARING_DEVICE_MAC_ADDRESS = "ForPairingDeviceAddress"
        private const val REQUEST_FIRMAWARE_FILE = 1051
        private const val REQUEST_FIRMAWARE_UPDATE_ACTIVITY = 1052
        private const val REQUEST_IMPORT_WORKOUT = 1053
        private const val REQUEST_EON_SETTINGS_FILE = 1054
        private const val DEVICE_MANAGER_ASSOCIATION_REQUEST_CODE = 1456
        private const val NO_WATCH_PAIRED_DIALOG = "NoWatchPairedDialog"
        private const val MOVESCOUNT_UNINSTALL_DIALOG = "MovescountUninstallDialog"
        private const val FILE_PROVIDER_AUTHORITY = AppConfig.APPLICATION_ID + ".FileProvider"

        @JvmStatic
        fun newStartIntent(context: Context) = Intent(context, DeviceActivity::class.java)

        @JvmStatic
        fun newIntentForPairing(context: Context, macAddress: String? = null) =
            Intent(context, DeviceActivity::class.java).apply {
                action = FOR_PAIRING_DEVICE_INTENT_ACTION
                putExtra(FOR_PARING_DEVICE_MAC_ADDRESS, macAddress)
            }

        @JvmStatic
        fun newSendLogsToFileIntent(context: Context): Intent {
            val intent = Intent(context, DeviceActivity::class.java)
            intent.action = SEND_LOGS_TO_FILE_INTENT_ACTION
            return intent
        }
    }
}
