package com.stt.android.watch.sportmodes.create.multisport.composable

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalResources
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodySmallBold
import com.stt.android.compose.theme.material3.bodyXLarge
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.domain.sportmodes.ActivityHeader
import com.stt.android.domain.sportmodes.SportModeHeader
import com.stt.android.domain.workout.ActivityType
import com.stt.android.watch.sportmodes.create.multisport.MultisportChild
import com.stt.android.watch.sportmodes.create.multisport.MultisportModeEditIntent
import com.stt.android.watch.sportmodes.create.multisport.MultisportModeEditViewState

@Composable
internal fun SelectParentContent(
    state: MultisportModeEditViewState.SelectParent,
    onIntent: (MultisportModeEditIntent) -> Unit,
    modifier: Modifier = Modifier,
) {
    if (state.loading) {
        LoadingContent(
            isLoading = true,
            modifier = modifier,
            color = MaterialTheme.colorScheme.primary,
        )
        return
    }
    LazyColumn(modifier = modifier.fillMaxSize()) {
        items(state.multisportActivityTypes) { activityType ->
            SelectParentItem(activityType, onIntent)
            HorizontalDivider()
        }
    }
}

@Composable
internal fun SelectChildrenContent(
    state: MultisportModeEditViewState.SelectChildren,
    onIntent: (MultisportModeEditIntent) -> Unit,
    modifier: Modifier = Modifier,
    scrollToChild: MultisportChild? = null,
    onScrollToChildConsumed: () -> Unit = {},
) {
    val children = state.children
    when {
        state.loading -> {
            LoadingContent(
                isLoading = true,
                modifier = modifier,
                color = MaterialTheme.colorScheme.primary,
            )
        }

        children.none() -> {
            Box(
                contentAlignment = Alignment.Center,
                modifier = modifier.fillMaxSize(),
            ) {
                Text(
                    text = stringResource(R.string.no_enough_child_sport_modes),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.secondary,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.large),
                )
            }
        }

        else -> {
            val reachedMaxSelectCount = state.reachedMaxSelectedCount
            val reachedMinSelectCount = state.reachedMinSelectedCount
            val listState = rememberLazyListState()

            // Handle scrolling to specific child
            LaunchedEffect(scrollToChild) {
                scrollToChild?.let { targetChild ->
                    val targetIndex = children.indexOf(targetChild)
                    if (targetIndex >= 0) {
                        listState.animateScrollToItem(targetIndex + 1)
                    }
                    onScrollToChildConsumed()
                }
            }

            Column(modifier = modifier.fillMaxSize()) {
                if (reachedMaxSelectCount) {
                    Text(
                        stringResource(R.string.reach_max_sport_modes),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.surface,
                        modifier = Modifier
                            .background(Color.Red)
                            .padding(MaterialTheme.spacing.medium),
                    )
                }

                Text(
                    stringResource(R.string.not_reach_min_sport_modes),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier
                        .padding(MaterialTheme.spacing.medium),
                )

                LazyColumn(
                    state = listState,
                    modifier = Modifier.weight(1f)
                ) {
                    if (children.any()) {
                        item {
                            Text(
                                stringResource(R.string.modes_in_short_list).uppercase(),
                                style = MaterialTheme.typography.bodySmallBold,
                                color = MaterialTheme.colorScheme.onSurface,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(MaterialTheme.colorScheme.secondaryContainer)
                                    .padding(MaterialTheme.spacing.medium),
                            )
                        }

                        items(children) { multisportChild ->
                            SelectChildItem(
                                multisportChild = multisportChild,
                                onIntent = onIntent,
                                selected = multisportChild in state.selectedChildren,
                                reachedMaxSelectCount = reachedMaxSelectCount,
                            )
                            HorizontalDivider()
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun SelectParentItem(
    activityType: ActivityType,
    onIntent: (MultisportModeEditIntent) -> Unit,
    modifier: Modifier = Modifier,
) {
    ActivityTypeItem(
        activityType = activityType,
        onClick = { onIntent(MultisportModeEditIntent.SelectParent(activityType)) },
        modifier = modifier,
    )
}

@Composable
private fun ActivityTypeItem(
    activityType: ActivityType,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val contentColor = MaterialTheme.colorScheme.onSurface
    val resources = LocalResources.current
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        modifier = modifier
            .fillMaxWidth()
            .clickableThrottleFirst(onClick = onClick)
            .padding(
                start = MaterialTheme.spacing.small,
                end = MaterialTheme.spacing.medium,
            )
            .padding(
                vertical = MaterialTheme.spacing.small,
            ),
    ) {
        SuuntoActivityIcon(
            iconRes = activityType.iconId,
            tint = contentColor,
            background = Color.Transparent,
            iconSize = MaterialTheme.iconSizes.large,
        )

        Text(
            text = activityType.getLocalizedName(resources),
            style = MaterialTheme.typography.bodyXLarge,
            color = contentColor,
        )
    }
}

@Composable
private fun SelectChildItem(
    multisportChild: MultisportChild,
    onIntent: (MultisportModeEditIntent) -> Unit,
    modifier: Modifier = Modifier,
    selected: Boolean = false,
    reachedMaxSelectCount: Boolean = false,
) {
    val contentColor = MaterialTheme.colorScheme.onSurface
    val resources = LocalResources.current
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        modifier = modifier
            .fillMaxWidth()
            .alpha(if (selected || !reachedMaxSelectCount) 1f else 0.5f)
            .clickableThrottleFirst(selected || !reachedMaxSelectCount) {
                onIntent(MultisportModeEditIntent.SelectChild(multisportChild))
            }
            .padding(
                start = MaterialTheme.spacing.small,
                end = MaterialTheme.spacing.medium,
            )
            .padding(
                vertical = MaterialTheme.spacing.small,
            ),
    ) {
        multisportChild.iconId?.let { iconId ->
            SuuntoActivityIcon(
                iconRes = iconId,
                tint = contentColor,
                background = Color.Transparent,
                iconSize = MaterialTheme.iconSizes.large,
            )
        }

        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
            modifier = Modifier.weight(1f),
        ) {
            Text(
                text = multisportChild.getLocalizedName(resources),
                style = MaterialTheme.typography.bodyLarge,
                color = contentColor,
            )
            Text(
                text = multisportChild.modeType ?: stringResource(R.string.custom_sport_mode),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }

        Checkbox(
            checked = selected,
            onCheckedChange = null,
            colors = CheckboxDefaults.colors(
                uncheckedColor = MaterialTheme.colorScheme.mediumGrey,
            ),
        )
    }
}

@Preview
@Composable
private fun SelectParentContentPreview() {
    M3AppTheme {
        SelectParentContent(
            state = MultisportModeEditViewState.SelectParent(
                multisportActivityTypes = listOf(
                    ActivityType.MULTISPORT,
                    ActivityType.RUNNING,
                    ActivityType.CYCLING,
                ),
                loading = false,
            ),
            onIntent = {},
        )
    }
}

internal val previewChildren = listOf(
    MultisportChild.CustomSportMode(
        activityType = ActivityType.RUNNING,
        sportMode = SportModeHeader(
            id = 1,
            name = "My Running",
            factoryMode = false,
            activityHeader = ActivityHeader(id = 3, type = null),
            modeIds = listOf(1),
        )
    ),
    MultisportChild.CustomSportMode(
        activityType = ActivityType.CYCLING,
        sportMode = SportModeHeader(
            id = 2,
            name = "My Cycling",
            factoryMode = false,
            activityHeader = ActivityHeader(id = 4, type = null),
            modeIds = listOf(2),
        )
    ),
    MultisportChild.CustomSportMode(
        activityType = ActivityType.RUNNING,
        sportMode = SportModeHeader(
            id = 3,
            name = null,
            factoryMode = false,
            activityHeader = ActivityHeader(id = 3, type = "Basic"),
            modeIds = listOf(3),
        )
    ),
    MultisportChild.CustomSportMode(
        activityType = ActivityType.CYCLING,
        sportMode = SportModeHeader(
            id = 4,
            name = null,
            factoryMode = false,
            activityHeader = ActivityHeader(id = 4, type = "Power"),
            modeIds = listOf(4),
        )
    ),
)

@Preview
@Composable
private fun SelectChildContentPreview() {
    M3AppTheme {
        SelectChildrenContent(
            state = MultisportModeEditViewState.SelectChildren(
                parentActivityType = ActivityType.MULTISPORT,
                children = previewChildren.take(0),
                selectedChildren = previewChildren.take(3),
                loading = false,
                enableTransitions = true,
            ),
            onIntent = {},
        )
    }
}
