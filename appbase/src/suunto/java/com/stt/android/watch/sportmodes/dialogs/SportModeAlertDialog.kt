package com.stt.android.watch.sportmodes.dialogs

import android.app.Dialog
import android.os.Bundle
import androidx.annotation.StringRes
import androidx.fragment.app.DialogFragment
import com.stt.android.ui.utils.DialogHelper

class SportModeAlertDialog : DialogFragment() {
    private lateinit var listener: SportModeDialogCallback

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        try {
            listener = parentFragment as SportModeDialogCallback
        } catch (e: ClassCastException) {
            throw ClassCastException("Calling fragment must implement SportModeDialogCallback interface")
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        arguments?.let {
            val title = it.getInt(TITLE)
            val description = it.getInt(DESCRIPTION)
            val okText = it.getInt(OK_TEXT)
            val cancelText = it.getInt(CANCEL_TEXT)

            activity?.let {
                return DialogHelper.createDialog(it, title, description, okText, { _, _ ->
                    listener.onOkClicked(tag)
                }, cancelText, { _, _ ->
                    listener.onDialogCanceled()
                }, {
                    listener.onDialogCanceled()
                })
            }
        }
        throw IllegalStateException("Cannot create dialog with null activity or empty args")
    }

    companion object {
        private const val TITLE = "title"
        private const val DESCRIPTION = "description"
        private const val OK_TEXT = "ok_text"
        private const val CANCEL_TEXT = "cancel_text"
        const val NO_VALUE = 0
        const val TAG_SAVE = "SportModeAlertDialogSave"
        const val TAG_DELETE_DISPLAY = "SportModeAlertDialogDeleteDisplay"
        const val TAG_NON_SAVED_CHANGES = "SportModeAlertDialogNonSavedChanges"
        const val TAG_DELETE_ALL = "SportModeAlertDialogDeleteAll"
        fun newInstance(
            @StringRes title: Int = NO_VALUE,
            @StringRes description: Int,
            @StringRes buttonOkText: Int,
            @StringRes buttonCancelText: Int
        ): SportModeAlertDialog {
            val dialogFragment = SportModeAlertDialog()
            val bundle = Bundle()
            bundle.apply {
                putInt(TITLE, title)
                putInt(DESCRIPTION, description)
                putInt(OK_TEXT, buttonOkText)
                putInt(CANCEL_TEXT, buttonCancelText)
            }
            dialogFragment.arguments = bundle
            return dialogFragment
        }
    }
}
