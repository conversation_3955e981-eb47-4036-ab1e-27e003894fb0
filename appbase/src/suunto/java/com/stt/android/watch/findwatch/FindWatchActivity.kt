package com.stt.android.watch.findwatch

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.BackHandler
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.material3.header
import com.stt.android.compose.util.setContentWithM3Theme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class FindWatchActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithM3Theme {
            FindWatchScreen()
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun FindWatchScreen(
        modifier: Modifier = Modifier,
        viewModel: FindWatchViewModel = hiltViewModel(),
    ) {
        val findWatchState by viewModel.findWatchState.collectAsState()
        val imageResource by viewModel.imageResource.collectAsState()
        val coroutineScope = rememberCoroutineScope()
        fun onBack() {
            coroutineScope.launch {
                viewModel.onBackPressed()
                finish()
            }
        }

        BackHandler {
            onBack()
        }

        Scaffold(
            modifier = modifier,
            topBar = {
                TopAppBar(
                    title = {
                        Text(
                            stringResource(R.string.find_watch_title).uppercase(),
                            style = MaterialTheme.typography.header,
                        )
                    },
                    navigationIcon = {
                        SuuntoIconButton(
                            icon = SuuntoIcons.ActionBack,
                            onClick = { onBack() },
                            contentDescription = stringResource(R.string.back),
                        )
                    },
                )
            },
        ) { paddingValues ->
            FindWatchContent(
                findWatchState = findWatchState,
                imageResource = imageResource,
                onFindWatchToggle = viewModel::toggleFindWatch,
                modifier = Modifier
                    .padding(paddingValues)
                    .narrowContentWithBgColors(
                        backgroundColor = MaterialTheme.colorScheme.surface,
                        outerBackgroundColor = MaterialTheme.colorScheme.background,
                    )
            )
        }
    }

    companion object {
        @JvmStatic
        fun newStartIntent(context: Context): Intent {
            return Intent(context, FindWatchActivity::class.java)
        }
    }
}
