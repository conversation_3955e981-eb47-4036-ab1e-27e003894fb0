package com.stt.android.home.diary.diarycalendar

import android.content.SharedPreferences
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.RelativeSizeSpan
import android.text.style.SuperscriptSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.edit
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavDirections
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayout.OnTabSelectedListener
import com.stt.android.R
import com.stt.android.common.navigation.findNavControllerByFragmentIdUsingChildFragmentManager
import com.stt.android.databinding.FragmentNewDiaryCalendarBinding
import com.stt.android.di.DiaryPagePreferences
import com.stt.android.home.diary.DiaryPagePreferencesKeys.DIARY_PROGRAMS_LAST_USED_SCREEN
import com.stt.android.home.diary.diarycalendar.BaseDiaryCalendarFragment.Companion.ARG_FORCED_DIARY_TAB
import com.stt.android.home.diary.diarycalendar.BaseDiaryCalendarFragment.Companion.ARG_SHOW_ACTIVITIES_LIST
import com.stt.android.home.diary.diarycalendar.planner.DiaryCalendarPlannerFragmentDirections
import com.stt.android.menstrualcycle.MenstrualCycleOnboardingNavigator
import com.stt.android.menstrualcycle.regularity.MenstrualCycleRegularitySheetCreator
import com.stt.android.remoteconfig.api.RemoteConfig
import com.stt.android.ui.utils.setOnClickListenerThrottled
import dagger.hilt.android.AndroidEntryPoint
import java.lang.ref.WeakReference
import javax.inject.Inject

@AndroidEntryPoint
class NewDiaryCalendarContainerFragment : Fragment() {
    private var binding: FragmentNewDiaryCalendarBinding? = null

    @Inject
    @DiaryPagePreferences
    lateinit var diaryPagePreferences: SharedPreferences

    @Inject
    lateinit var menstrualCycleOnboardingNavigator: MenstrualCycleOnboardingNavigator

    @Inject
    lateinit var menstrualCycleRegularitySheetCreator: MenstrualCycleRegularitySheetCreator

    @Inject
    lateinit var remoteConfig: RemoteConfig

    private val viewModel by viewModels<CalendarContainerViewModel>()

    private val navController get() = findNavControllerByFragmentIdUsingChildFragmentManager(R.id.new_diary_calendar_nav_host_fragment)

    private var navigationListener: NavController.OnDestinationChangedListener? = null

    private var onTabSelectedListener: OnTabSelectedListener? = null

    private val menuDelegate by lazy {
        CalendarMenuDelegate(
            this,
            menstrualCycleOnboardingNavigator,
            menstrualCycleRegularitySheetCreator,
        )
    }

    private fun openTab(position: Int?) {
        val navWeakRef = WeakReference(navController)
        navWeakRef.get()?.let {
            val directions = when (position) {
                CalendarTab.CALENDAR_TAB_POSITION.ordinal -> {
                    saveLastUsedPageBy(CalendarTab.CALENDAR_TAB_POSITION)
                    openCalendarTab(it)
                }

                CalendarTab.PROGRAMS_TAB_POSITION.ordinal -> {
                    saveLastUsedPageBy(CalendarTab.PROGRAMS_TAB_POSITION)
                    openProgramsTab(it)
                }

                else -> null
            }
            if (directions != null) {
                it.navigate(directions)
            }
        }
    }

    private fun openCalendarTab(navController: NavController): NavDirections? {
        return when (navController.currentDestination?.id) {
            R.id.programsFragment -> {
                DiaryCalendarPlannerFragmentDirections.actionProgramsFragmentToCalendarFragment()
            }

            else -> null
        }
    }

    private fun openProgramsTab(navController: NavController): NavDirections? {
        return when (navController.currentDestination?.id) {
            R.id.calendarFragment -> {
                DiaryCalendarContainerFragmentDirections.actionCalendarFragmentToProgramsFragment()
            }

            else -> null
        }
    }

    private fun destinationChanged(destination: NavDestination) {
        val selectedTabPosition = binding?.newTabLayout?.selectedTabPosition
        when (destination.id) {
            R.id.calendarFragment -> {
                if (selectedTabPosition != CalendarTab.CALENDAR_TAB_POSITION.ordinal) {
                    binding?.newTabLayout?.getTabAt(CalendarTab.CALENDAR_TAB_POSITION.ordinal)
                        ?.select()
                }
            }

            R.id.programsFragment -> {
                if (selectedTabPosition != CalendarTab.PROGRAMS_TAB_POSITION.ordinal) {
                    binding?.newTabLayout?.getTabAt(CalendarTab.PROGRAMS_TAB_POSITION.ordinal)
                        ?.select()
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = FragmentNewDiaryCalendarBinding.inflate(inflater, container, false).also {
        binding = it
    }.root

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setBetaTag()
        // Set navGraph dynamically to pass args to the start destination
        val showActivitiesList = arguments?.getBoolean(ARG_SHOW_ACTIVITIES_LIST, false) ?: false
        val forcedDiaryTabOrdinal = arguments?.getInt(ARG_FORCED_DIARY_TAB, -1) ?: -1
        val navGraph = navController.navInflater.inflate(R.navigation.new_diary_calendar_nav_graph)
        val lastUsedScreenByPosition =
            if (forcedDiaryTabOrdinal != -1 && forcedDiaryTabOrdinal in CalendarTab.entries.indices) forcedDiaryTabOrdinal else getLaseUsedPage()

        val (startDestination, startDestinationArgs) = if (lastUsedScreenByPosition == CalendarTab.PROGRAMS_TAB_POSITION.ordinal) {
            R.id.programsFragment to bundleOf()
        } else {
            R.id.calendarFragment to DiaryCalendarContainerFragmentArgs.Builder()
                .setShowActivitiesList(showActivitiesList)
                .build()
                .toBundle()
        }

        navGraph.setStartDestination(startDestination)

        navController.setGraph(
            navGraph,
            startDestinationArgs
        )

        binding?.addButton?.setOnClickListenerThrottled {
            val date = viewModel.initPickerDate()
            menuDelegate.showAddMenu(
                it,
                date
            )
        }

        menuDelegate.onViewCreated()
    }

    private fun setBetaTag() {
        binding?.newTabLayout?.post {
            // Only if the AI planner tab exists
            binding?.newTabLayout?.getTabAt(1)?.let { tab ->
                val originalLabel = getString(R.string.calendar_tab_title_my_plan)
                val betaLabel = getString(R.string.beta_feature_tag)
                val fullText = "$originalLabel $betaLabel"
                val spannable = SpannableString(fullText)

                // Apply superscript and size span to "BETA"
                val start = originalLabel.length + 1 // after the space
                val end = fullText.length
                spannable.setSpan(SuperscriptSpan(), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                spannable.setSpan(
                    RelativeSizeSpan(0.75f),
                    start,
                    end,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )

                tab.text = spannable
            }
        }
    }

    override fun onStart() {
        super.onStart()
        navigationListener = NavController.OnDestinationChangedListener { _, destination, _ ->
            destinationChanged(destination)
        }
        navController.addOnDestinationChangedListener(navigationListener!!)
        onTabSelectedListener = object : OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                openTab(tab.position)
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {}
        }
        binding?.newTabLayout?.addOnTabSelectedListener(onTabSelectedListener)
    }

    override fun onStop() {
        super.onStop()
        if (navigationListener != null) {
            navController.removeOnDestinationChangedListener(navigationListener!!)
        }
        binding?.newTabLayout?.removeOnTabSelectedListener(onTabSelectedListener)
        onTabSelectedListener = null
        navigationListener = null
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun saveLastUsedPageBy(calendarTab: CalendarTab) {
        diaryPagePreferences.edit {
            putInt(
                DIARY_PROGRAMS_LAST_USED_SCREEN,
                calendarTab.ordinal
            )
        }
    }

    private fun getLaseUsedPage(): Int = if (remoteConfig.isAiPlannerEnabled()) {
        diaryPagePreferences.getInt(DIARY_PROGRAMS_LAST_USED_SCREEN, CalendarTab.CALENDAR_TAB_POSITION.ordinal)
    } else {
        CalendarTab.CALENDAR_TAB_POSITION.ordinal
    }

    /**
     * Switches to the specified tab programmatically
     */
    fun switchToTab(calendarTab: CalendarTab) {
        binding?.newTabLayout?.getTabAt(calendarTab.ordinal)?.select()
    }

    companion object {
        const val FRAGMENT_TAG = "NewCalendarContainerFragment"

        @JvmStatic
        fun newInstance(): NewDiaryCalendarContainerFragment {
            return NewDiaryCalendarContainerFragment()
        }

        @JvmStatic
        fun newInstance(
            showActivitiesList: Boolean,
            isProgramsEnabled: Boolean,
            forcedCalendarTab: CalendarTab? = null
        ): Fragment {
            return if (isProgramsEnabled) {
                NewDiaryCalendarContainerFragment().apply {
                    arguments = Bundle().apply {
                        putBoolean(ARG_SHOW_ACTIVITIES_LIST, showActivitiesList)
                        if (forcedCalendarTab != null) {
                            putInt(ARG_FORCED_DIARY_TAB, forcedCalendarTab.ordinal)
                        }
                    }
                }
            } else {
                DiaryCalendarContainerFragment.newInstance(showActivitiesList)
            }
        }
    }
}
