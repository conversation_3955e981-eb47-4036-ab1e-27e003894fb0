package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.home.dashboardv2.widgets.Last7DaysHeartRateWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Period
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.utils.iterator
import com.stt.android.utils.toEpochMilli
import com.suunto.algorithms.data.HeartRate
import com.suunto.algorithms.data.HeartRate.Companion.bpm
import com.suunto.algorithms.data.HeartRate.Companion.hz
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import timber.log.Timber
import javax.inject.Inject
import kotlin.collections.map

internal class MinimumHeartRateWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val trendDataRepository: TrendDataRepository,
    private val fetchSleepUseCase: FetchSleepUseCase,
) : WidgetDataLoader<Last7DaysHeartRateWidgetInfo>() {

    override suspend fun mockedWidgetInfo(type: WidgetType): Last7DaysHeartRateWidgetInfo? {
        val dailyHrs = if (type == WidgetType.MINIMUM_HEART_RATE) {
            listOf(61, 61, 64, 56, 61, 59, 62).map { it.bpm }
        } else {
            listOf(50, 49, 50, 51, 51, 51, 55).map { it.bpm }
        }
        return generateLast7DaysHeartRateInfo(context, dailyHrs, WidgetType.MINIMUM_HEART_RATE)
    }

    override suspend fun realLoad(param: Param): WidgetData<Last7DaysHeartRateWidgetInfo> {
        val period = Period.Last7Days
        val firstDay = period.beginDate
        val lastDay = period.endDate
        val firstDayMillis = firstDay.atStartOfDay().toEpochMilli()
        val lastDayMillis = lastDay.atStartOfDay().toEpochMilli()

        val trendDataFlow = trendDataRepository.fetchTrendDataForDateRange(
            fromTimestamp = firstDayMillis,
            toTimestamp = lastDayMillis,
            aggregated = false
        )
        val sleepFlow = fetchSleepUseCase.fetchSleeps(
            from = firstDay,
            to = lastDay,
        )

        return WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = combine(trendDataFlow, sleepFlow) { pastWeekTrendData, pastWeekSleep ->
                mapMinimumHeartRateWidgetInfo(pastWeekTrendData, pastWeekSleep, period, param)
            }.catch { e ->
                Timber.w(e, "Error loading minimum heart rate widget data")
            }
        )
    }

    private fun mapMinimumHeartRateWidgetInfo(
        pastWeekTrendData: List<TrendData>,
        pastWeekSleep: List<Sleep>,
        period: Period,
        param: Param,
    ): Last7DaysHeartRateWidgetInfo {
        val forSleep = param.type == WidgetType.MINIMUM_SLEEP_HEART_RATE
        val firstDay = period.beginDate
        val lastDay = period.endDate
        val pastWeekDataByDate = pastWeekTrendData.groupBy { it.timeISO8601.toLocalDate() }
        val sleepTimeRanges =
            pastWeekSleep.mapNotNull { it.longSleep }.map { it.fellAsleep..it.wokeUp }
        val dailyMinHrs = mutableListOf<HeartRate>()
        for (day in firstDay..lastDay) {
            val dataForDay = pastWeekDataByDate[day] ?: emptyList()
            val minHrForDay = dataForDay
                .filter { trendData ->
                    val isInSleep = trendData.isInSleepTime(sleepTimeRanges)
                    forSleep == isInSleep
                }
                // fallback to hr if separate hrMin is empty
                .mapNotNull { trendData ->
                    val hr = trendData.hr?.takeIf { trendData.hasHr }
                    val minHr = trendData.hrMin
                    if (minHr != null && hr != null) {
                        minOf(minHr, hr)
                    } else {
                        minHr ?: hr
                    }
                }.minOrNull() ?: 0f
            if (minHrForDay > 0f) {
                dailyMinHrs.add(minHrForDay.hz)
            } else {
                dailyMinHrs.add(HeartRate.ZERO)
            }
        }

        return generateLast7DaysHeartRateInfo(context, dailyMinHrs, param.type)
    }

    private fun TrendData.isInSleepTime(sleepTimeRanges: List<LongRange>): Boolean {
        sleepTimeRanges.forEach { range ->
            if (timestamp in range) {
                return true
            }
        }
        return false
    }
}
