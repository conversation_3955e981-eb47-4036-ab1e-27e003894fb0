package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import com.stt.android.R
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sleep.FetchSleepHrvUseCase
import com.stt.android.domain.sleep.HrvGrade
import com.stt.android.domain.sleep.SleepHrv
import com.stt.android.domain.sleep.todayHrvGrade
import com.stt.android.home.dashboardv2.ui.widgets.common.NO_DATA_VALUE
import com.stt.android.home.dashboardv2.ui.widgets.common.generateWidgetTitle
import com.stt.android.home.dashboardv2.widgets.HrvWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Period
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.watch.GetWatchCapabilities
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.core.R as CR

internal class HrvWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val watchCapabilities: GetWatchCapabilities,
    private val fetchSleepHrvUseCase: FetchSleepHrvUseCase,
) : WidgetDataLoader<HrvWidgetInfo>() {

    override suspend fun mockedWidgetInfo(type: WidgetType): HrvWidgetInfo? = HrvWidgetInfo(
        supported = true,
        period = Period.Last7Days,
        progresses = generateDailyLineProgresses(listOf(55, 60, 59, 61, 59, 61, 65).map { it.toDouble() }),
        title = generateWidgetTitle("60", context.getString(CR.string.ms)),
        subtitle = context.getString(R.string.widget_hrv_normal),
        subtitleIconRes = R.drawable.widget_normal,
    )

    override suspend fun realLoad(param: Param): WidgetData<HrvWidgetInfo> {
        val period = Period.Last7Days
        val flow = fetchSleepHrvUseCase.fetchAvgHrv(from = period.beginDate, to = period.endDate)
            .map { hrvs ->
                val hasDataWithin60Days = hrvs.any { it.hasDataWithin60Days }
                if (!hasDataWithin60Days && !isHrvSupported()) {
                    HrvWidgetInfo(
                        supported = false,
                        period = period,
                        progresses = emptyList(),
                        title = AnnotatedString(""),
                        subtitle = "",
                        subtitleIconRes = null,
                    )
                } else {
                    hrvs.toHrvWidgetInfo(period)
                }
            }

        return WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = flow,
        )
    }

    private fun List<SleepHrv>.toHrvWidgetInfo(period: Period): HrvWidgetInfo {
        val todayHrv = last()

        val title = todayHrv.avgHrv
            ?.let { generateWidgetTitle("${it.roundToInt()}", context.getString(CR.string.ms)) }
            ?: AnnotatedString(context.getString(R.string.widget_no_data_title))

        val subtitleTextToIconRes = when {
            todayHrv.avgHrv == null -> R.string.widget_no_data_subtitle to null
            todayHrv.todayHrvGrade == HrvGrade.NO_GRADE -> R.string.widget_hrv_no_baseline to null
            todayHrv.todayHrvGrade == HrvGrade.LOW -> R.string.widget_hrv_below_normal to R.drawable.widget_down_arrow
            todayHrv.todayHrvGrade == HrvGrade.HIGH -> R.string.widget_hrv_above_normal to R.drawable.widget_up_arrow
            todayHrv.todayHrvGrade == HrvGrade.IN_NORMAL_RANGE -> R.string.widget_hrv_normal to R.drawable.widget_normal
            else -> R.string.widget_no_data_subtitle to null
        }
        val subtitle = context.getString(subtitleTextToIconRes.first)
        val subtitleIconRes: Int? = subtitleTextToIconRes.second

        val hrvValueList = map { it.avgHrv?.toDouble() ?: NO_DATA_VALUE.toDouble() }
        val progresses = generateDailyLineProgresses(hrvValueList)

        return HrvWidgetInfo(
            supported = true,
            period = period,
            progresses = progresses,
            title = title,
            subtitle = subtitle,
            subtitleIconRes = subtitleIconRes,
        )
    }

    private suspend fun isHrvSupported(): Boolean = runSuspendCatching {
        watchCapabilities.getCapabilitiesForAllWatches().let { it ->
            it.none() || it.any { it.capabilities?.supportsHrv == true }
        }
    }.getOrElse { false }
}
