package com.stt.android.home.dayviewv2.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.activityCycling
import com.stt.android.compose.theme.activitySleep
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.DualProgressBar
import com.stt.android.home.dayviewv2.SleepDurationData
import java.util.Locale
import kotlin.math.abs
import kotlin.time.Duration
import com.stt.android.R as BR

@Composable
internal fun SleepDurationView(
    data: SleepDurationData,
    date: String,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                modifier = Modifier.weight(1f),
                text = data.totalDuration.inWholeSeconds.formatDuration(context),
                style = MaterialTheme.typography.bodyLargeBold,
                color = MaterialTheme.colorScheme.onSurface,
            )
            Text(
                text = stringResource(
                    BR.string.sleep_time_duration_of_target,
                    formatSleepTarget(data.totalDuration, data.goal),
                ),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colorScheme.activitySleep,
            )
        }
        DualProgressBar(
            progress = (data.longSleepDuration / data.goal).toFloat(),
            progressBarColor = MaterialTheme.colorScheme.activitySleep,
            secondaryProgress = (data.napDuration / data.goal).toFloat(),
            secondaryProgressBarColor = MaterialTheme.colorScheme.activityCycling,
            backgroundColor = MaterialTheme.colorScheme.secondaryContainer,
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        ) {
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
            ) {
                val showDuration = listOfNotNull(data.longSleep, *data.naps.toTypedArray()).size > 1
                data.longSleep?.let {
                    SleepLegend(
                        color = MaterialTheme.colorScheme.activitySleep,
                        text = stringResource(
                            BR.string.sleep_time_duration_of_sleep,
                            "${it.fellAsleep} - ${it.wokeUp}",
                        ),
                        duration = it.totalDuration.inWholeSeconds.formatDuration(context),
                        showDuration = showDuration,
                    )
                }
                data.naps.forEach {
                    SleepLegend(
                        color = MaterialTheme.colorScheme.activityCycling,
                        text = stringResource(
                            BR.string.sleep_time_duration_of_nap,
                            "${it.fellAsleep} - ${it.wokeUp}",
                        ),
                        duration = it.totalDuration.inWholeSeconds.formatDuration(context),
                        showDuration = showDuration,
                    )
                }
            }
            Text(
                text = date,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface,
            )
        }
    }
}

@Composable
private fun SleepLegend(
    color: Color,
    text: String,
    duration: String,
    showDuration: Boolean,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box(
            modifier = Modifier
                .clip(CircleShape)
                .background(color = color)
                .size(6.dp),
        )
        Text(
            text = if (showDuration) "$text  $duration" else text,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface,
        )
    }
}

private fun formatSleepTarget(totalDuration: Duration, goal: Duration): String {
    val diff = (totalDuration - goal).inWholeSeconds
    val sign = if (diff < 0L) "-" else "+"
    return abs(diff).secondsToHourMinute().let { (hours, minutes) ->
        String.format(Locale.US, "%s%d:%02d", sign, hours, minutes)
    }
}
