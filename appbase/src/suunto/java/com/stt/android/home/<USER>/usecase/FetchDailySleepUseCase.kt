package com.stt.android.home.dayviewv2.usecase

import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.activitydata.goals.FetchSleepGoalUseCase
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.recovery.FetchRecoveryDataUseCase
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.sleep.SleepStage
import com.stt.android.domain.sleep.SleepStageInterval
import com.stt.android.home.dayviewv2.DayViewExpandable
import com.stt.android.home.dayviewv2.SleepDurationData
import com.stt.android.home.dayviewv2.SleepPeriod
import com.stt.android.home.dayviewv2.SleepResourcesData
import com.stt.android.home.dayviewv2.SleepStageChartData
import com.stt.android.home.dayviewv2.SleepStageSummary
import com.stt.android.home.dayviewv2.SleepViewData
import com.stt.android.home.dayviewv2.SleepVitalsData
import com.stt.android.home.dayviewv2.TrendChartAxisRange
import com.stt.android.ui.components.charts.model.ExtendedSleepStage
import com.stt.android.ui.components.charts.model.SleepRegion
import com.stt.android.ui.components.charts.model.SleepStageEntry
import com.stt.android.ui.components.charts.model.extended
import com.stt.android.ui.utils.DateFormatter
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.firstRecoveryDataForSleep
import com.stt.android.utils.lastRecoverDataForSleep
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.time.Duration

internal class FetchDailySleepUseCase @Inject constructor(
    private val fetchSleepUseCase: FetchSleepUseCase,
    private val fetchSleepGoalUseCase: FetchSleepGoalUseCase,
    private val fetchRecoveryDataUseCase: FetchRecoveryDataUseCase,
    private val fetchExpandedStateUseCase: FetchExpandedStateUseCase,
    private val generateSleepQualityUseCase: GenerateSleepQualityUseCase,
    private val dispatchers: CoroutinesDispatchers,
    private val userSettingsController: UserSettingsController,
    private val dateFormatter: DateFormatter,
) {
    operator fun invoke(date: LocalDate): Flow<SleepViewData?> {
        return combine(
            fetchSleepUseCase.fetchSleeps(date.minusDays(2), date),
            fetchSleepUseCase.fetchSleepStages(date, date).map { it.values.flatten() },
            fetchSleepGoalUseCase.fetchSleepGoal(),
            fetchRecoveryDataUseCase.fetchRecoveryData(date, date),
            fetchExpandedStateUseCase(DayViewExpandable.Sleep),
        ) { sleeps, sleepStages, sleepGoal, recoveryList, expanded ->
            val sleep = sleeps.firstOrNull {
                it.timestamp.toLocalDate() == date
            } ?: return@combine null

            val quality = generateSleepQualityUseCase(sleep, sleeps)

            val adjustedSleepStages = sleepStages.dropLastWhile { it.stage == SleepStage.AWAKE }
            val chartData = if (adjustedSleepStages.isNotEmpty() || sleep.hasNap) {
                createSleepStageChartData(date, sleep, adjustedSleepStages)
            } else null

            SleepViewData(
                date = dateFormatter.formatRelativeDate(date),
                totalSleepSeconds = sleep.totalSleepDuration.inWholeSeconds,
                goal = sleepGoal.inWholeSeconds.toInt(),
                quality = quality?.first,
                qualityDescRes = quality?.second,
                stageSummaries = generateSleepStageSummaries(sleep),
                vitalsData = sleep.longSleep?.let { longSleep ->
                    val measurementUnit = userSettingsController.settings.measurementUnit
                    SleepVitalsData(
                        avgHr = longSleep.avgHr,
                        minHr = longSleep.minHr,
                        avgHrv = longSleep.avgHrv?.roundToInt(),
                        maxSpO2 = longSleep.maxSpO2,
                        altitude = longSleep.altitude?.let { measurementUnit.toAltitudeUnit(it.toDouble()) },
                        altitudeUnitRes = measurementUnit.altitudeUnit,
                    )
                },
                sleepDuration = generateSleepDurationData(sleep, sleepGoal),
                resources = generateResourcesData(sleep, recoveryList),
                expanded = expanded,
                chartData = chartData,
            )
        }.flowOn(dispatchers.io)
    }

    private fun createSleepStageChartData(
        date: LocalDate,
        sleep: Sleep,
        sleepStages: List<SleepStageInterval>,
    ): SleepStageChartData {
        val sleepRegions = calcDailySleepRegions(sleep, sleepStages)
        val offset = ZonedDateTime.now().offset
        val (minX, maxX) = if (sleepRegions.any { !it.nap }) {
            sleepRegions.minOf { it.xStart } to sleepRegions.maxOf { it.xEnd }
        } else {
            date.atStartOfDay().toEpochSecond(offset) to date.atEndOfDay().toEpochSecond(offset)
        }
        // keep nap entries at the beginning of the list to draw them first
        val entries = listOf(
            *sleepRegions.filter { it.nap }.map {
                SleepStageEntry(
                    xStart = it.xStart,
                    xEnd = it.xEnd,
                    stage = ExtendedSleepStage.NAP,
                )
            }.toTypedArray(),
            *sleepStages.mapNotNull { interval ->
                // The stage minimum time is 30 seconds.
                // Since the precision is lost after long is converted to float, xStart equals xEnd will occur.
                // We just ignore such stages, or sleep stages chart may not be drawn correctly.
                interval.duration.inWholeSeconds.takeIf { it > 0L }?.let { duration ->
                    SleepStageEntry(
                        xStart = interval.timeISO8601.toEpochSecond(),
                        xEnd = interval.timeISO8601.plusSeconds(duration).toEpochSecond(),
                        stage = interval.stage.extended,
                    )
                }
            }.toTypedArray(),
        )

        return SleepStageChartData(
            entries = entries,
            regions = sleepRegions,
            axisRange = TrendChartAxisRange(
                minX = minX,
                maxX = maxX,
                minY = 0.0,
                maxY = 4.0,
            )
        )
    }

    private fun calcDailySleepRegions(
        sleep: Sleep,
        sleepStages: List<SleepStageInterval>,
    ): List<SleepRegion> {
        val longSleepFellAsleep = listOfNotNull(
            sleepStages.firstOrNull()?.timeISO8601?.toEpochSecond(),
            sleep.longSleep?.fellAsleep?.toZonedDateTime()?.toEpochSecond(),
        ).minOrNull()
        val longSleepWokeUp = listOfNotNull(
            sleepStages.lastOrNull()?.let {
                it.timeISO8601.plusSeconds(it.duration.inWholeSeconds).toEpochSecond()
            },
            sleep.longSleep?.wokeUp?.toZonedDateTime()?.toEpochSecond(),
        ).maxOrNull()
        return buildList {
            if (longSleepFellAsleep != null && longSleepWokeUp != null && longSleepFellAsleep < longSleepWokeUp) {
                add(SleepRegion(longSleepFellAsleep, longSleepWokeUp, false))
            }
            sleep.naps.forEach { nap ->
                val napFellAsleep = nap.fellAsleep.toZonedDateTime().toEpochSecond()
                val napWokeUp = nap.wokeUp.toZonedDateTime().toEpochSecond()
                if (napFellAsleep < napWokeUp) {
                    add(SleepRegion(napFellAsleep, napWokeUp, true))
                }
            }
        }.sortedBy { it.xStart }
    }

    private fun generateSleepStageSummaries(sleep: Sleep): List<SleepStageSummary> {
        val longSleep = sleep.longSleep ?: return emptyList()
        val awakeSeconds = longSleep.awakeDuration
            ?.takeIf { it > Duration.ZERO }
            ?.inWholeSeconds ?: 0L
        val remSeconds = longSleep.remSleepDuration
            ?.takeIf { it > Duration.ZERO }
            ?.inWholeSeconds ?: 0L
        val lightSeconds = longSleep.lightSleepDuration
            ?.takeIf { it > Duration.ZERO }
            ?.inWholeSeconds ?: 0L
        val deepSeconds = longSleep.deepSleepDuration
            ?.takeIf { it > Duration.ZERO }
            ?.inWholeSeconds ?: 0L
        val totalSeconds = awakeSeconds + remSeconds + lightSeconds + deepSeconds
        fun Float.roundToTwoDecimals() = (this * 100f).roundToInt() / 100f
        fun calcPercent(seconds: Long) = (seconds / totalSeconds.toFloat()).roundToTwoDecimals()
        return listOf(
            SleepStageSummary(
                SleepStage.REM,
                R.string.sleep_stages_rem,
                R.color.sleep_rem,
                remSeconds,
                calcPercent(remSeconds),
            ),
            SleepStageSummary(
                SleepStage.LIGHT,
                R.string.sleep_stages_light,
                R.color.sleep_core,
                lightSeconds,
                calcPercent(lightSeconds),
            ),
            SleepStageSummary(
                SleepStage.DEEP,
                R.string.sleep_stages_deep,
                R.color.sleep_deep,
                deepSeconds,
                calcPercent(deepSeconds),
            ),
        ).toMutableList().apply {
            add(
                0,
                SleepStageSummary(
                    SleepStage.AWAKE,
                    R.string.sleep_stages_awake,
                    R.color.sleep_awake,
                    awakeSeconds,
                    (1f - map { it.percent }.sum()).roundToTwoDecimals(),
                )
            )
        }
    }

    private fun generateSleepDurationData(sleep: Sleep, sleepGoal: Duration): SleepDurationData? {
        if (sleep.totalSleepDuration <= Duration.ZERO) return null

        return SleepDurationData(
            longSleep = sleep.longSleep?.let {
                SleepPeriod(
                    fellAsleep = it.fellAsleep.toZonedDateTime().formatTime(),
                    wokeUp = it.wokeUp.toZonedDateTime().formatTime(),
                    totalDuration = it.sleepDuration,
                )
            },
            naps = sleep.naps.map {
                SleepPeriod(
                    fellAsleep = it.fellAsleep.toZonedDateTime().formatTime(),
                    wokeUp = it.wokeUp.toZonedDateTime().formatTime(),
                    totalDuration = it.duration,
                )
            },
            longSleepDuration = sleep.longSleep?.sleepDuration ?: Duration.ZERO,
            napDuration = sleep.getMergedNap()?.duration ?: Duration.ZERO,
            totalDuration = sleep.totalSleepDuration,
            goal = sleepGoal,
        )
    }

    private fun generateResourcesData(
        sleep: Sleep,
        recoveryList: List<RecoveryData>,
    ): SleepResourcesData? {
        val longSleep = sleep.longSleep ?: return null
        val lastRecovery = recoveryList.lastRecoverDataForSleep(longSleep.wokeUp) ?: return null
        val firstRecovery = recoveryList.firstRecoveryDataForSleep(longSleep.fellAsleep)
        val gained = if (firstRecovery != null && firstRecovery != lastRecovery) {
            lastRecovery.balance - firstRecovery.balance
        } else null
        return SleepResourcesData(lastRecovery.balance, gained)
    }

    private fun Long.toZonedDateTime() = Instant.ofEpochMilli(this).atZone(ZoneId.systemDefault())

    private fun ZonedDateTime.formatTime() = DateTimeFormatter.ofPattern("HH:mm").format(this)
}
