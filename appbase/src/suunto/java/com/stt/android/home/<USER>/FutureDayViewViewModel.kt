package com.stt.android.home.futuredayview

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.home.diary.diarycalendar.planner.TrainingPlannerUiStateMapper
import com.stt.android.home.diary.diarycalendar.planner.models.PlannedWorkoutUiState
import com.stt.android.home.diary.diarycalendar.planner.usercases.PlannedWorkoutEnhancer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import javax.inject.Inject

@HiltViewModel
class FutureDayViewViewModel @Inject constructor(
    private val plannedWorkoutEnhancer: PlannedWorkoutEnhancer,
    private val trainingPlannerUiStateMapper: TrainingPlannerUiStateMapper,
    private val dispatchers: CoroutinesDispatchers,
    savedStateHandle: SavedStateHandle,
) : ViewModel() {
    private val initialDate = savedStateHandle.get<LocalDate>(EXTRA_INITIAL_DATE) ?: LocalDate.now()
    private val trainingDayFormatter = DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT)

    private val _uiState = MutableStateFlow<FutureDayViewUiState>(FutureDayViewUiState.Initial)
    val uiState: StateFlow<FutureDayViewUiState> = _uiState

    init {
        loadPlannedWorkouts(initialDate)
    }

    private fun loadPlannedWorkouts(date: LocalDate) {
        viewModelScope.launch(dispatchers.io) {
            val plannedWorkouts = plannedWorkoutEnhancer.getPlannedWorkouts(date)
            _uiState.value = FutureDayViewUiState.Loaded(
                date = date,
                plannedWorkouts = plannedWorkouts.map {
                    trainingPlannerUiStateMapper.toWorkoutUiState(
                        date = date.format(trainingDayFormatter),
                        workout = it
                    )
                }
            )
        }
    }

    companion object {
        const val EXTRA_INITIAL_DATE = "initial_date"
    }
}

sealed class FutureDayViewUiState {
    data object Initial : FutureDayViewUiState()
    data class Loaded(
        val date: LocalDate,
        val plannedWorkouts: List<PlannedWorkoutUiState>,
    ) : FutureDayViewUiState()
}
