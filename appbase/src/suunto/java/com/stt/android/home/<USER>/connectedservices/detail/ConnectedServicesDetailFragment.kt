package com.stt.android.home.settings.connectedservices.detail

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.net.toUri
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import coil3.load
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.serviceNameForAnalytics
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.databinding.FragmentConnectedServicesDetailBinding
import com.stt.android.home.settings.connectedservices.ConnectedServicesActivity
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class ConnectedServicesDetailFragment : ViewModelFragment2() {

    private val args: ConnectedServicesDetailFragmentArgs by navArgs()

    override val viewModel: ConnectedServicesDetailViewModel by viewModels()

    private val viewDataBinding: FragmentConnectedServicesDetailBinding get() = requireBinding()

    override fun getLayoutResId() = R.layout.fragment_connected_services_detail

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        viewModel.connectEvent.observeNotNull(viewLifecycleOwner) {
            @Suppress("UnsafeImplicitIntentLaunch")
            val browserIntent = Intent(Intent.ACTION_VIEW, it.partnerUrl.toUri())
            startActivity(browserIntent)
        }
        viewModel.serviceDisconnectEvent.observeK(viewLifecycleOwner) {
            if (args.showSingleService) {
                requireActivity().finish()
            } else {
                NavHostFragment.findNavController(this).navigateUp()
            }
        }
        viewModel.readMoreEvent.observeK(viewLifecycleOwner) {
            openReadMore()
        }
        viewModel.errorEventLiveData.observeNotNull(viewLifecycleOwner) { errorEvent ->
            showErrorSnackbar(errorEvent)
        }

        findNavController().previousBackStackEntry?.savedStateHandle?.set(
            SOURCE_FROM_DETAILS_VIEW,
            true
        )

        val serviceMetadata = viewModel.serviceMetadata
        (activity as AppCompatActivity).supportActionBar?.title =
            if (serviceMetadata.localization.viewTitle.isNotEmpty()) {
                serviceMetadata.localization.viewTitle
            } else {
                serviceMetadata.name
            }

        context?.let {
            // Loading icon image
            if (serviceMetadata.iconImageUrl.isNotBlank()) {
                viewDataBinding.connectedServiceIcon.load(serviceMetadata.iconImageUrl)
            }

            // Show connection button image if it exists, connect button otherwise
            if (serviceMetadata.additionalImageUrls.isNotEmpty() && !serviceMetadata.additionalImageUrls[0].isBlank()) {
                viewDataBinding.connectedServiceImage.load(serviceMetadata.additionalImageUrls[0])
            }

            if (serviceMetadata.connectImageUrl.isNotBlank()) {
                viewDataBinding.connectButtonImage.load(serviceMetadata.connectImageUrl)
            }
        }
        sendDetailsScreenEvent()
    }

    private fun openReadMore() {
        @Suppress("UnsafeImplicitIntentLaunch")
        startActivity(Intent(Intent.ACTION_VIEW, viewModel.serviceMetadata.readMoreUrl.toUri()))
    }

    private fun sendDetailsScreenEvent() {
        val properties = AnalyticsProperties().apply {
            put(
                AnalyticsEventProperty.SUUNTO_PLUS_ITEM_NAME,
                serviceNameForAnalytics(viewModel.serviceMetadata.name)
            )
            put(
                AnalyticsEventProperty.SUUNTO_PLUS_ITEM_TYPE,
                AnalyticsPropertyValue.SuuntoPlusItemTypeValue.PARTNER
            )
            putTrueFalse(
                AnalyticsEventProperty.SUUNTO_CURRENTLY_CONNECTED,
                viewModel.serviceMetadata.isConnected
            )
            put(
                AnalyticsEventProperty.SUUNTO_PLUS_DETAIL_SOURCE,
                requireActivity().intent.getStringExtra(ConnectedServicesActivity.SOURCE) ?: ""

            )
        }
        viewModel.sendDetailsScreenEvent(properties)
    }

    private fun showErrorSnackbar(errorEvent: ErrorEvent) {
        val snackbar = Snackbar.make(
            viewDataBinding.root,
            errorEvent.errorStringRes,
            if (errorEvent.canRetry) Snackbar.LENGTH_INDEFINITE else Snackbar.LENGTH_LONG
        )
        if (errorEvent.canRetry) {
            snackbar.setAction(R.string.retry_action) {
                Timber.v("Retry button clicked")
                viewModel.retryLoading()
            }
        }
        snackbar.show()
    }

    companion object {
        const val SOURCE_FROM_DETAILS_VIEW = "source_from_details_view"
    }
}
