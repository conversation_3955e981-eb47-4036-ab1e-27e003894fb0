package com.stt.android.home.settings.zones.hr

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.content.res.Configuration
import android.os.Bundle
import android.view.Gravity
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.fragment.app.DialogFragment
import com.stt.android.BR
import com.stt.android.R
import com.stt.android.common.ui.ViewModelActivity2
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.databinding.ActivityHrZonesTypeBinding
import com.stt.android.databinding.TitleSummaryActivityIconBinding
import com.stt.android.domain.user.HrType
import com.stt.android.domain.user.IntensityZones
import com.stt.android.home.settings.zones.ZoneType
import com.stt.android.home.settings.zones.ZonesActivity
import com.stt.android.home.settings.zones.ZonesDescriptionActivity
import com.stt.android.home.settings.zones.ZonesDescriptionType
import dagger.hilt.android.AndroidEntryPoint
import com.stt.android.core.R as CR

@AndroidEntryPoint
class HrZonesTypeActivity : ViewModelActivity2() {

    override val viewModel: HrZonesTypeViewModel by viewModels()
    private val viewDataBinding: ActivityHrZonesTypeBinding get() = requireBinding()

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                return true
            }

            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun getLayoutResId() = R.layout.activity_hr_zones_type

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setSupportActionBar(findViewById<Toolbar>(R.id.toolbar))

        supportActionBar?.let {
            it.setDisplayShowHomeEnabled(false)
            it.setDisplayHomeAsUpEnabled(true)
            title = getString(R.string.hr_zones)
        }
        initView()
        initObserver()
        viewModel.loadData()
    }

    private fun initObserver() {
        viewModel.intensityZonesLiveData.observe(this) {
            setSwitchPreference(it)
        }
        viewModel.hrTypeLiveData.observe(this) { hrType ->
            val operation = when (hrType) {
                HrType.MAX -> getString(R.string.max_hr_zones)
                HrType.RESERVE -> getString(R.string.hr_reserve_zones)
                HrType.AET -> getString(R.string.hr_threshold_zones)
                else -> getString(R.string.max_hr_zones)
            }
            setTitleSummaryPreference(
                viewDataBinding.hrZonesType,
                title = getString(R.string.hr_zones_type),
                operation = operation,
                null
            )
        }
    }

    private fun setSwitchPreference(intensityZones: IntensityZones) {
        setTitleSummaryPreference(
            viewDataBinding.runningHrZones,
            title = getString(R.string.running_hr_zones),
            operation = if (intensityZones.runningHrZonesEnable?.enable == true) getString(R.string.on) else getString(
                R.string.off
            ),
            CoreActivityType.RUNNING
        )
        setTitleSummaryPreference(
            viewDataBinding.cyclingHrZones,
            title = getString(R.string.cycling_hr_zones),
            operation = if (intensityZones.cyclingHrZonesEnable?.enable == true) getString(R.string.on) else getString(
                R.string.off
            ),
            CoreActivityType.CYCLING
        )
    }

    private fun initView() {
        viewDataBinding.defaultHrZones.apply {
            val context = root.context
            title.text = getString(R.string.default_hr_zones)
            summary.text = getString(R.string.for_all_sports)
            summary.setTextColor(ContextCompat.getColor(context, R.color.suunto_dark_gray))
            ivArrows.visibility = View.VISIBLE
            ivArrows.imageTintList =
                ColorStateList.valueOf(ContextCompat.getColor(context, CR.color.near_black))
            root.setOnClickListener {
                startActivity(
                    ZonesActivity.newStartIntent(
                        this@HrZonesTypeActivity,
                        getString(R.string.default_hr_zones),
                        ZoneType.HR_ALL_SPORTS,
                        viewModel.hrTypeLiveData.value
                    )
                )
            }
        }
        viewDataBinding.hrZonesType.root.setOnClickListener {
            showSelectHrTypeDialog()
        }
        viewDataBinding.runningHrZones.root.setOnClickListener {
            startActivity(
                ZonesActivity.newStartIntent(
                    this,
                    getString(R.string.running_hr_zones),
                    ZoneType.HR_RUNNING,
                    null
                )
            )
        }
        viewDataBinding.cyclingHrZones.root.setOnClickListener {
            startActivity(
                ZonesActivity.newStartIntent(
                    this,
                    getString(R.string.cycling_hr_zones),
                    ZoneType.HR_CYCLING,
                    null
                )
            )
        }
    }

    private fun showSelectHrTypeDialog() {
        val entriesId = this.resources.getStringArray(R.array.entries_settings_hr_zones_values)
        val entriesValue = this.resources.getStringArray(R.array.entries_settings_hr_zones_list)
        val index = entriesId.indexOf(viewModel.hrTypeLiveData.value?.type)
        HrZonesDialogFragment.newInstance(index, entriesValue).apply {
            onSelectedClick = { viewModel.setHrType(HrType.entries[it]) }
        }.show(supportFragmentManager, HrZonesDialogFragment.TAG)
    }

    private fun setTitleSummaryPreference(
        titleSummaryPreferenceBinding: TitleSummaryActivityIconBinding,
        title: String,
        operation: String?,
        coreActivityType: CoreActivityType?
    ) {
        titleSummaryPreferenceBinding.titletext = title
        if (operation != null) {
            titleSummaryPreferenceBinding.setVariable(BR.operationText, operation)
            titleSummaryPreferenceBinding.operation.visibility = View.VISIBLE
        }
        coreActivityType?.let {
            titleSummaryPreferenceBinding.activityGroupBackground.setColorFilter(
                getColor(
                    coreActivityType.color
                )
            )
            titleSummaryPreferenceBinding.activityIcon.setImageResource(coreActivityType.icon)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_trim_video, menu)
        menu?.findItem(R.id.save)?.apply {
            setIcon(R.drawable.ic_help_round_outline)
            setOnMenuItemClickListener {
                startActivity(
                    ZonesDescriptionActivity.newIntent(
                        this@HrZonesTypeActivity,
                        ZonesDescriptionType.HR_ZONES.name
                    )
                )
                true
            }
        }
        return true
    }

    companion object {
        fun newStartIntent(context: Context?): Intent {
            return Intent(context, HrZonesTypeActivity::class.java)
        }
    }
}

class HrZonesDialogFragment : DialogFragment() {

    var onSelectedClick: ((index: Int) -> Unit)? = null

    companion object {
        const val TAG = "HrZonesDialogFragment"
        private const val SELECT_INDEX = "SELECT_INDEX"
        private const val ENTRIES_VALUE = "ENTRIES_VALUE"
        fun newInstance(
            selectIndex: Int,
            entriesValue: Array<String>
        ): HrZonesDialogFragment {
            val args = Bundle()
            val frag = HrZonesDialogFragment()
            args.putInt(SELECT_INDEX, selectIndex)
            args.putCharSequenceArray(ENTRIES_VALUE, entriesValue)
            frag.arguments = args
            return frag
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val selectIndex = arguments?.getInt(SELECT_INDEX)
        val entriesValue = arguments?.getCharSequenceArray(ENTRIES_VALUE)
        return AlertDialog.Builder(requireActivity())
            .setTitle(R.string.hr_zones_type)
            .setSingleChoiceItems(
                entriesValue,
                selectIndex?.let { if (it >= 0) it else 0 } ?: 0
            ) { p0, p1 ->
                p0?.dismiss()
                onSelectedClick?.invoke(p1)
            }
            .create()
            .apply {
                setCanceledOnTouchOutside(true)
                show()
                window?.run {
                    attributes = attributes.apply {
                        val widthPixels = resources.displayMetrics.widthPixels
                        width =
                            if (resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT)
                                (widthPixels * 0.95).toInt() else (widthPixels * 0.65).toInt()
                        gravity = Gravity.CENTER
                    }
                }
            }
    }

    override fun onDestroy() {
        super.onDestroy()
        onSelectedClick = null
    }
}
