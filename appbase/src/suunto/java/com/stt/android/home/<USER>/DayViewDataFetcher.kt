package com.stt.android.home.dayview

import android.content.SharedPreferences
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.data.TimeUtils
import com.stt.android.data.toEpochMilli
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyEnergyUseCase
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyRecoveryDataUseCase
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyStepsUseCase
import com.stt.android.domain.activitydata.dailyvalues.StressState
import com.stt.android.domain.activitydata.goals.FetchEnergyGoalUseCase
import com.stt.android.domain.activitydata.goals.FetchSleepGoalUseCase
import com.stt.android.domain.activitydata.goals.FetchStepsGoalUseCase
import com.stt.android.domain.recovery.FetchRecoveryDataUseCase
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.sleep.SleepStage
import com.stt.android.domain.sleep.SleepStageInterval
import com.stt.android.domain.trenddata.FetchTrendDataUseCase
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.diary.RecoveryBarGraphItem.Companion.DATA_FREQUENCY_SECONDS
import com.stt.android.home.diary.diarycalendar.workoutstats.WorkoutStat
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.menstrualcycle.ShowCycleDayHelper
import com.stt.android.menstrualcycle.domain.MenstrualCycle
import com.stt.android.menstrualcycle.domain.MenstrualCycleType
import com.stt.android.menstrualcycle.domain.ObservableMenstrualCycleListUseCase
import com.stt.android.utils.STTConstants
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG
import com.stt.android.utils.firstRecoveryDataForSleep
import com.stt.android.utils.lastRecoverDataForSleep
import com.stt.android.utils.takeIfNotNaN
import com.suunto.algorithms.data.Energy
import com.suunto.algorithms.data.Energy.Companion.cal
import io.reactivex.Flowable
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.rx2.asFlowable
import timber.log.Timber
import java.time.Clock
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.time.Duration
import kotlin.time.Duration.Companion.days

/**
 * Class for fetching workout data and 247 data for day view
 */
class DayViewDataFetcher @Inject constructor(
    private val fetchTrendDataUseCase: FetchTrendDataUseCase,
    private val fetchRecoveryDataUseCase: FetchRecoveryDataUseCase,
    private val fetchDailyEnergyUseCase: FetchDailyEnergyUseCase,
    private val fetchEnergyGoalUseCase: FetchEnergyGoalUseCase,
    private val fetchStepsGoalUseCase: FetchStepsGoalUseCase,
    private val fetchDailyStepsUseCase: FetchDailyStepsUseCase,
    private val fetchSleepUseCase: FetchSleepUseCase,
    private val fetchDailyRecoveryDataUseCase: FetchDailyRecoveryDataUseCase,
    private val fetchSleepGoalUseCase: FetchSleepGoalUseCase,
    private val observableMenstrualCycleListUseCase: ObservableMenstrualCycleListUseCase,
    private val fetchWorkoutsController: WorkoutHeaderController,
    private val currentUserController: CurrentUserController,
    private val rewriteNavigator: WorkoutDetailsRewriteNavigator,
    private val infoModelFormatter: InfoModelFormatter,
    private val clock: Clock,
    @SuuntoSharedPrefs private val suuntoPreferences: SharedPreferences,
    @FeatureTogglePreferences private val featureTogglePreferences: SharedPreferences,
    private val showCycleDayHelper: ShowCycleDayHelper,
    private val unitConverter: JScienceUnitConverter,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {

    private data class CombinedDayData(
        val bmr: Energy,
        val stepsGoal: Int,
        val sleepGoal: Duration,
        val energyGoal: Energy,
        val trendData: List<TrendData>,
        val recoveryData: List<RecoveryData>,
        val stepsForToday: Int,
        val energyForToday: Energy,
        val sleep: List<Sleep>,
        val currentBalance: Float, // 0..1
        val currentStressState: StressState,
        val workouts: List<WorkoutHeader>,
        val sleepStageIntervalMaps: Map<Long, List<SleepStageInterval>>,
        val menstrualCycles: List<MenstrualCycle>
    )

    /**
     * Fetch workout headers, trend data, sleep data for given date range and also activity goals. Emits updates if
     * data changes while subscribed.
     *
     * @param firstDay First date to fetch data
     * @param lastDay Last date to fetch data. To fetch data for a single day, this should be the same as [firstDay]
     * @param today The date for the current date. This is used to calculate how many pages of data to fetch
     *              from [FetchTrendDataUseCase] and [FetchSleepUseCase]. When fetching data for today, steps and
     *              calories data will be fetched using [FetchDailyEnergyUseCase] and [FetchDailyStepsUseCase].
     * @return Flowable of lists of [DayViewData]. All requested days are included in the list. The
     *                 DayViewData will be empty if there is no data for that particular day.
     */
    fun fetchDataForDateRange(
        firstDay: LocalDate,
        lastDay: LocalDate,
        today: LocalDate = LocalDate.now()
    ): Flowable<List<DayViewData>> {
        val zoneId = ZoneId.systemDefault()

        // Timestamps as start-of-day (i.e. midnight) in local time zone
        val startOfFirstDay = firstDay.atStartOfDay(zoneId).toEpochMilli()
        val startOfLastDay = lastDay.atStartOfDay(zoneId).toEpochMilli()

        // Data sources
        val getBmr = fetchDailyEnergyUseCase.fetchMetabolicEnergy()
        val getStepsGoal = fetchStepsGoalUseCase.fetchStepsGoal()
        val getSleepGoal = fetchSleepGoalUseCase.fetchSleepGoal()
        val getEnergyGoal = fetchEnergyGoalUseCase.fetchEnergyGoal()
        val getWorkouts = getWorkoutHeaders(startOfFirstDay, startOfLastDay)

        // Get steps for today from watch directly
        val getStepsForToday = if (today in firstDay..lastDay) {
            fetchDailyStepsUseCase.fetchSteps()
                .catch { emit(0) }
        } else {
            flowOf(0)
        }

        // Get steps for today from watch directly
        val getEnergyForToday = if (today in firstDay..lastDay) {
            fetchDailyEnergyUseCase.fetchEnergy()
                .catch { emit(Energy.ZERO) }
        } else {
            flowOf(Energy.ZERO)
        }

        // Get balance 0..1 for today from watch directly using NO_BALANCE_VALUE to indicate that the value is not ready
        val getCurrentBalance = if (today in firstDay..lastDay) {
            fetchDailyRecoveryDataUseCase.fetchCurrentBalance(NO_BALANCE_VALUE)
                .catch { emit(NO_BALANCE_VALUE) }
        } else {
            flowOf(NO_BALANCE_VALUE)
        }

        // Get stress state for today from watch directly
        val getCurrentStressState = if (today in firstDay..lastDay) {
            fetchDailyRecoveryDataUseCase.fetchCurrentStressState(StressState.INVALID)
                .catch { emit(StressState.INVALID) }
        } else {
            flowOf(StressState.INVALID)
        }

        // Actual number of days we're interested in
        val numberOfDays = ChronoUnit.DAYS.between(firstDay, lastDay).toInt() + 1

        // Number of days to fetch using the paged use cases. This assumes the use cases return all data starting from
        // the oldest day (i.e. largest page number).
        val numberOfPages = ChronoUnit.DAYS.between(firstDay, today).toInt() + 1

        if (numberOfPages <= 0 || numberOfDays <= 0) {
            Timber.w("fetchDataForDateRange: bad timestamps: first=$firstDay last=$lastDay today=$today")
            return Flowable.just(listOf())
        }

        val getTrendData = fetchTrendDataUseCase.fetchTrendData(
            from = firstDay,
            to = lastDay,
            aggregated = false,
        )

        val getRecoveryData = fetchRecoveryDataUseCase.fetchRecoveryData(
            from = firstDay,
            to = lastDay,
        )

        val getSleep = fetchSleepUseCase.fetchSleeps(
            from = firstDay,
            to = lastDay,
        )

        val getSleepStagesInterval = fetchSleepUseCase.fetchSleepStages(
            from = firstDay,
            to = lastDay,
        )

        val firstbeatSleepThresholds = suuntoPreferences.getBoolean(
            STTConstants.SuuntoPreferences.KEY_FIRSTBEAT_SLEEP_THRESHOLD,
            true
        )

        val getMenstrualCycles =
            observableMenstrualCycleListUseCase(MenstrualCycleType.HISTORICAL)

        @Suppress("UNCHECKED_CAST")
        return combine(
            getBmr,
            getStepsGoal,
            getSleepGoal,
            getEnergyGoal,
            getTrendData,
            getRecoveryData,
            getStepsForToday,
            getEnergyForToday,
            getSleep,
            getCurrentBalance,
            getCurrentStressState,
            getWorkouts,
            getSleepStagesInterval,
            getMenstrualCycles,
        ) { valueArray ->
            CombinedDayData(
                bmr = valueArray[0] as Energy,
                stepsGoal = valueArray[1] as Int,
                sleepGoal = valueArray[2] as Duration,
                energyGoal = valueArray[3] as Energy,
                trendData = valueArray[4] as List<TrendData>,
                recoveryData = valueArray[5] as List<RecoveryData>,
                stepsForToday = valueArray[6] as Int,
                energyForToday = valueArray[7] as Energy,
                sleep = valueArray[8] as List<Sleep>,
                currentBalance = valueArray[9] as Float,
                currentStressState = (valueArray[10] as StressState),
                workouts = valueArray[11] as List<WorkoutHeader>,
                sleepStageIntervalMaps = valueArray[12] as Map<Long, List<SleepStageInterval>>,
                menstrualCycles = valueArray[13] as List<MenstrualCycle>
            )
        }
            .map { data ->
                // Aggregate the data into a list with a DayViewData for each day

                val res = mutableListOf<DayViewData>()
                var date = firstDay
                var goodDaysInRow = 0
                var poorDaysInRow = 0
                repeat(numberOfDays) {
                    val nextDay = date.plusDays(1L)
                    val startOfDay = date.atStartOfDay(zoneId)
                    val startOfNextDay = nextDay.atStartOfDay(zoneId)
                    val dateRangeInMillis =
                        startOfDay.toEpochMilli() until startOfNextDay.toEpochMilli()

                    // Filter out data not included in this day
                    val workouts = data.workouts.filter { it.startTime in dateRangeInMillis }
                    val sleep = data.sleep.filter { it.timestamp in dateRangeInMillis }
                    val trendDataSamples = data.trendData
                        .filter { it.timestamp in dateRangeInMillis }
                        .sortedBy { it.timestamp }
                        .toMutableList()

                    val validHRSamples = trendDataSamples
                        .filter { it.hasHr }
                        .mapNotNull { it.hr }

                    val avgHR = if (validHRSamples.isNotEmpty()) {
                        validHRSamples.average().toFloat()
                    } else {
                        0f
                    }
                    // we calculate min as min of all available hrMin and hrs
                    val hrMin = listOfNotNull(
                        trendDataSamples.mapNotNull { it.hrMin }.minOrNull(),
                        validHRSamples.minOrNull()
                    ).minOrNull() ?: 0f
                    // we calculate max as max of all available hrMax and hrs
                    val hrMax = listOfNotNull(
                        trendDataSamples.mapNotNull { it.hrMax }.maxOrNull(),
                        validHRSamples.maxOrNull()
                    ).maxOrNull() ?: 0f
                    val spo2 = trendDataSamples.mapNotNull { it.spo2 }.average().toFloat()
                    val altitude = trendDataSamples.mapNotNull { it.altitude }.average().toFloat()
                    val hrv = trendDataSamples.mapNotNull { it.hrv }.average().takeIfNotNaN()
                        ?.roundToInt()

                    // for the chart to be consistent we make sure that it contains the hrMin and hrMax
                    // (introducing a bit of an error but making it more clear for the user)
                    var indexOfHRMin = -1
                    if (hrMin > 0) {
                        val hrMinNotInChart = trendDataSamples.none { it.hr == hrMin }
                        indexOfHRMin = trendDataSamples.indexOfFirst { it.hrMin == hrMin }
                        if (hrMinNotInChart && indexOfHRMin >= 0) {
                            trendDataSamples[indexOfHRMin] =
                                trendDataSamples[indexOfHRMin].copy(hr = hrMin)
                        }
                    }
                    if (hrMax > 0) {
                        val hrMaxNotInChart = trendDataSamples.none { it.hr == hrMax }
                        val indexOfHRMax = trendDataSamples.indexOfFirst { it.hrMax == hrMax }
                        if (hrMaxNotInChart && indexOfHRMax >= 0 && indexOfHRMax != indexOfHRMin) {
                            trendDataSamples[indexOfHRMax] =
                                trendDataSamples[indexOfHRMax].copy(hr = hrMax)
                        }
                    }

                    if (sleep.isNotEmpty() &&
                        sleep.mapNotNull { it.longSleep?.quality }
                            .all { it > STTConstants.SleepQuality.GOOD_TREND_THRESHOLD }
                    ) {
                        goodDaysInRow++
                    } else {
                        goodDaysInRow = 0
                    }
                    val poorThresHold = if (firstbeatSleepThresholds) {
                        STTConstants.SleepQuality.NEW_POOR_TREND_THRESHOLD
                    } else {
                        STTConstants.SleepQuality.OLD_POOR_TREND_THRESHOLD
                    }
                    if (sleep.isNotEmpty() &&
                        sleep.mapNotNull { it.longSleep?.quality }
                            .all { it <= poorThresHold }
                    ) {
                        poorDaysInRow++
                    } else {
                        poorDaysInRow = 0
                    }
                    val trendDataAggregated = if (date == today) {
                        // Get data for today using the daily use cases
                        listOf(
                            TrendData(
                                serial = "",
                                timestamp = startOfDay.toEpochMilli(),
                                energy = data.energyForToday,
                                steps = data.stepsForToday,
                                hr = avgHR,
                                hrMin = hrMin,
                                hrMax = hrMax,
                                spo2 = if (spo2.isNaN()) null else spo2,
                                altitude = if (altitude.isNaN()) null else altitude,
                                hrv = hrv
                            )
                        )
                    } else {
                        // For older dates, collect all trendDataAggregated values with timestamp in the requested window
                        listOf(
                            TrendData(
                                serial = "",
                                timestamp = startOfDay.toEpochMilli(),
                                energy = trendDataSamples.sumOf { it.energy.inCal }.cal,
                                steps = trendDataSamples.sumOf { it.steps },
                                hr = avgHR,
                                hrMin = hrMin,
                                hrMax = hrMax,
                                spo2 = if (spo2.isNaN()) null else spo2,
                                altitude = if (altitude.isNaN()) null else altitude,
                                hrv = hrv
                            )
                        )
                    }

                    // Get the recovery data for our date range
                    var recoveryData =
                        data.recoveryData.filter { it.timestamp in dateRangeInMillis }
                    var currentRecoveryData: RecoveryData? = null
                    // If its today and we have a balance value... (-1 = no current balance value see above)
                    if (date == today && data.currentBalance != NO_BALANCE_VALUE) {
                        // Construct the current recovery data with a 30min resolution (squash to previous)
                        val time = TimeUtils.getNow(clock)
                        val dataFrequencyMinutes = DATA_FREQUENCY_SECONDS.toInt() / 60
                        val truncatedTime = time.truncatedTo(ChronoUnit.HOURS)
                            .plusMinutes((dataFrequencyMinutes * (time.minute / dataFrequencyMinutes)).toLong())
                        currentRecoveryData = RecoveryData(
                            "",
                            truncatedTime.toEpochMilli(),
                            data.currentBalance,
                            data.currentStressState,
                            truncatedTime
                        )
                        // Linearly fill to the current value
                        recoveryData = fillRecoveryDataLinearly(currentRecoveryData, recoveryData)
                    }

                    val fromMenstrualCycleDays = showCycleDayHelper.getFromMenstrualCycleDays(data.menstrualCycles, date)
                    val isPeriodDay = data.menstrualCycles.any { it.includedDates.contains(date) }

                    res.add(
                        DayViewData(
                            startOfDayISO8601 = startOfDay,
                            workouts = workouts,
                            trendDataAggregated = trendDataAggregated,
                            trendDataSamples = trendDataSamples,
                            recoveryData = recoveryData,
                            currentRecoveryData = currentRecoveryData,
                            sleep = sleep.map {
                                createDayViewSleep(
                                    it,
                                    data.recoveryData,
                                    data.sleepStageIntervalMaps[it.timestamp]
                                )
                            },
                            bmr = data.bmr,
                            sleepGoal = data.sleepGoal,
                            stepsGoal = data.stepsGoal,
                            energyGoal = data.energyGoal,
                            showBubble = false,
                            firstbeatSleepThresholds = firstbeatSleepThresholds,
                            sleepTrendGood = goodDaysInRow >= STTConstants.SleepQuality.TREND_DAYS_THRESHOLD,
                            sleepTrendPoor = poorDaysInRow >= STTConstants.SleepQuality.TREND_DAYS_THRESHOLD,
                            rewriteNavigator = rewriteNavigator,
                            infoModelFormatter = infoModelFormatter,
                            showHRVGraph = featureTogglePreferences.getBoolean(
                                KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG,
                                false,
                            ),
                            stats = buildList {
                                add(WorkoutStat.createActivityCountStat(workouts.size))
                                add(
                                    WorkoutStat.createActivityDurationCountStat(
                                        infoModelFormatter,
                                        workouts.sumOf { it.totalTime }
                                    )
                                )
                                add(
                                    WorkoutStat.createEnergyStat(
                                        infoModelFormatter,
                                        unitConverter,
                                        workouts.sumOf { it.energyConsumption },
                                    )
                                )
                                fromMenstrualCycleDays?.let {
                                    add(WorkoutStat.createFromMenstrualCycleStartDateStat(it))
                                }
                            }.toPersistentList(),
                            isPeriodDay = isPeriodDay
                        )
                    )
                    date = nextDay
                }

                res.toList()
            }
            .asFlowable()
    }

    /**
     * Get workout headers for given date range. New lists are emitted if there are new workouts or changes to existing
     * ones as long as the Flowable is subscribed.
     *
     * @param startOfFirstDay The UTC timestamp of the start of the first day in milliseconds
     * @param startOfLastDay The UTC timestamp of the start of the last day in milliseconds
     * @return Flowable of lists of [WorkoutHeader].
     */
    private fun getWorkoutHeaders(
        startOfFirstDay: Long,
        startOfLastDay: Long,
    ): Flow<List<WorkoutHeader>> = fetchWorkoutsController.currentUserWorkoutUpdated
        .onStart { emit(Unit) }
        .map {
            fetchWorkoutsController.findNotDeletedWorkoutHeaders(
                currentUserController.username,
                null,
                startOfFirstDay,
                startOfLastDay + MILLIS_IN_DAY - 1L,
            )
        }
        .distinctUntilChanged()
        .flowOn(coroutinesDispatchers.io)

    /**
     * Fill missing recovery data from the last item of the list to the current linearly
     * - If the [recoveryData] list is empty then it's filled with the [currentRecoveryData]
     * - If the last item of [recoveryData] is close to the [currentRecoveryData]
     *   then return [recoveryData] but the first element has the values of [currentRecoveryData]
     * - Else fill the remaining space between [recoveryData] and [currentRecoveryData] linearly
     *
     * [currentRecoveryData] is what the watch reports now
     * [recoveryData] is a list from last complete sync
     */
    private fun fillRecoveryDataLinearly(
        currentRecoveryData: RecoveryData,
        recoveryData: List<RecoveryData>
    ): List<RecoveryData> {
        // 0. If list is empty return a list of the current
        if (recoveryData.isEmpty()) {
            return listOf(currentRecoveryData)
        }
        // 1. Get first item
        val firstItem = recoveryData.first()
        // 2. Compare the time diff it's it's smaller than the frequency then noop
        val milliSeconds = DATA_FREQUENCY_SECONDS * 1000
        if (currentRecoveryData.timestamp - firstItem.timestamp < milliSeconds) {
            return listOf(currentRecoveryData) + recoveryData.drop(1)
        }
        // 3. Find how many items we should fill
        val timeStep = milliSeconds.toInt()
        val fillCount = (currentRecoveryData.timestamp - firstItem.timestamp) / timeStep
        // 4. Find the step of each item and if its positive then the status should be relaxing
        val valueStep = (currentRecoveryData.balance - firstItem.balance) / fillCount.toFloat()
        val stressState = if (valueStep > 0) StressState.RELAXING else StressState.ACTIVE
        // 5. Start filling the data prepending to the list
        val filledRecoveryData = recoveryData.toMutableList()
        for (i in 1 until fillCount) {
            filledRecoveryData.add(
                0,
                RecoveryData(
                    "",
                    firstItem.timestamp + (timeStep * i),
                    firstItem.balance + valueStep * i,
                    stressState,
                    TimeUtils.epochToLocalZonedDateTime(firstItem.timestamp + (timeStep * i))
                )
            )
        }
        // 6. Use the current as the last to avoid rounding issues to the current value
        filledRecoveryData.add(0, currentRecoveryData)
        return filledRecoveryData
    }

    // TODO: Create a new use case for this when refactoring DayViewDataFetcher
    private fun createDayViewSleep(
        sleep: Sleep,
        allRecoveryData: List<RecoveryData>, // Recovery data for the whole fetched range
        sleepStageIntervals: List<SleepStageInterval>?
    ): DayViewSleep {
        val fellAsleep = sleep.longSleep?.fellAsleep
        val wokeUp = sleep.longSleep?.wokeUp
        val firstRecovery = fellAsleep?.let { allRecoveryData.firstRecoveryDataForSleep(it) }
        val lastRecovery = wokeUp?.let { allRecoveryData.lastRecoverDataForSleep(it) }
        val gained =
            if (firstRecovery != null && lastRecovery != null && firstRecovery != lastRecovery) {
                lastRecovery.balance - firstRecovery.balance
            } else {
                null
            }
        return DayViewSleep(
            sleep = sleep,
            balanceGained = gained,
            balanceOnWakeUp = lastRecovery?.balance,
            sleepStageIntervals = sleepStageIntervals?.dropLastWhile {
                it.stage == SleepStage.AWAKE
            } ?: emptyList()
        )
    }

    companion object {
        private val MILLIS_IN_DAY = 1.days.inWholeMilliseconds
        const val NO_BALANCE_VALUE = -1f
    }
}
