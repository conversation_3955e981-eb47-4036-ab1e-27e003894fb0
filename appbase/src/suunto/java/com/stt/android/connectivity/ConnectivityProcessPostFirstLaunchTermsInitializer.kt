package com.stt.android.connectivity

import android.app.Application
import com.google.firebase.FirebaseApp
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.stt.android.BuildConfig
import com.stt.android.LoggingInitializer
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.SuuntoAnalyticsRuntimeHook
import com.stt.android.di.initializer.SecondaryProcessInitializerPostTermsApproval
import com.stt.android.remoteconfig.api.RemoteConfig
import com.suunto.connectivity.repository.AnalyticsUtils
import javax.inject.Inject

class ConnectivityProcessPostFirstLaunchTermsInitializer @Inject constructor(
    private val analyticsRuntimeHook: SuuntoAnalyticsRuntimeHook,
    private val datahubAnalyticsTracker: DatahubAnalyticsTracker,
    private val remoteConfig: RemoteConfig,
) : SecondaryProcessInitializerPostTermsApproval {
    override fun init(app: Application) {
        // Firebase is initialized manually using a OnFirstLaunchTermsAcceptedAppInitializer
        // Other processes need manual initialization separately
        FirebaseApp.initializeApp(app)
        FirebaseCrashlytics.getInstance().isCrashlyticsCollectionEnabled = !BuildConfig.DEBUG

        datahubAnalyticsTracker.initialize(app)
        remoteConfig.refresh()
        LoggingInitializer().apply {
            initializeTimberPostFirstLaunchTermsCheck()
            initializeForBuildType(app)
        }

        // setting analytics hook in connectivity module
        AnalyticsUtils.setAnalyticsHook(
            analyticsRuntimeHook
        )
    }
}
