package com.stt.android.social.badges.badgesbase

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.material3.bodyXLarge
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberEventThrottler
import com.stt.android.core.utils.EventThrottler

@Composable
internal fun BadgesLoadedScreen(
    viewData: BadgesViewData.Loaded,
    onEvent: (BadgesViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    Scaffold(
        modifier = modifier,
        contentWindowInsets = WindowInsets.navigationBars
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .narrowContentWithBgColors(
                    backgroundColor = MaterialTheme.colorScheme.surface,
                    outerBackgroundColor = MaterialTheme.colorScheme.background
                )
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
        ) {
            MyBadgesSection(
                numberOfBadges = viewData.myBadgesList.numberOfBadges,
                recentBadgeImage = viewData.myBadgesList.recentBadgeImage,
                recentBadgeId = viewData.myBadgesList.recentBadgeId,
                newBadges = viewData.myBadgesList.hasNewBadges,
                onEvent = onEvent,
            )
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
            viewData.activityBadgesList.forEach { (moduleName, badgeList) ->
                ModuleBadgeRow(
                    moduleName = moduleName,
                    badges = badgeList,
                    onEvent = onEvent,
                )
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
            }
        }
    }
}

@Composable
private fun MyBadgesSection(
    numberOfBadges: Int,
    recentBadgeImage: String?,
    newBadges: Boolean,
    recentBadgeId: String?,
    onEvent: (BadgesViewEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(246.dp),
        contentAlignment = Alignment.TopCenter
    ) {
        Image(
            painter = painterResource(R.drawable.bg_my_badge),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .windowInsetsPadding(WindowInsets.statusBars)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                SuuntoIconButton(
                    icon = SuuntoIcons.ActionBack,
                    onClick = { onEvent(BadgesViewEvent.Close) },
                )
                Text(
                    text = stringResource(id = R.string.main_badges_screen_title).uppercase(),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.Bold
                )
            }
            Box(modifier = Modifier.weight(1f), contentAlignment = Alignment.BottomCenter) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(IntrinsicSize.Max),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Column(
                        modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    ) {
                        Row(
                            modifier = Modifier
                                .padding(bottom = MaterialTheme.spacing.small)
                                .clickable(
                                    indication = null,
                                    interactionSource = remember { MutableInteractionSource() },
                                    onClick = { onEvent(BadgesViewEvent.OnMyListClick) }
                                ),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(id = R.string.my_badges_title),
                                style = MaterialTheme.typography.bodyMegaBold,
                            )
                            Icon(
                                painter = SuuntoIcons.ActionRight.asPainter(),
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onSurface
                            )
                        }
                        Text(
                            text = pluralStringResource(
                                R.plurals.badges_earned,
                                numberOfBadges,
                                numberOfBadges
                            ),
                            style = MaterialTheme.typography.bodyXLarge
                        )
                    }
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .clickable(
                                indication = null,
                                interactionSource = remember { MutableInteractionSource() },
                                onClick = {
                                    if (recentBadgeId.isNullOrEmpty()) {
                                        return@clickable
                                    } else {
                                        onEvent(BadgesViewEvent.OnBadgesClick(recentBadgeId))
                                    }
                                }
                            ),
                        contentAlignment = Alignment.BottomEnd
                    ) {
                        if (!recentBadgeImage.isNullOrBlank()) {
                            AsyncImage(
                                model = recentBadgeImage,
                                contentDescription = "recent badge",
                                modifier = Modifier
                                    .padding(MaterialTheme.spacing.medium)
                                    .size(108.dp)
                            )
                        } else {
                            Image(
                                painter = painterResource(id = R.drawable.empty_my_badge_image),
                                contentDescription = "recent badge",
                                modifier = Modifier
                                    .padding(MaterialTheme.spacing.medium)
                                    .size(108.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ModuleBadgeRow(
    moduleName: String,
    badges: List<BadgesDisplayItem>,
    onEvent: (BadgesViewEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    val eventThrottler = rememberEventThrottler()
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = MaterialTheme.spacing.medium)
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() },
                    onClick = { onEvent(BadgesViewEvent.OnListClick(moduleName)) }
                ),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = moduleName,
                style = MaterialTheme.typography.bodyLargeBold,
                modifier = Modifier
                    .padding(
                        horizontal = MaterialTheme.spacing.medium,
                        vertical = MaterialTheme.spacing.small
                    )
            )
            Icon(
                painter = SuuntoIcons.ActionRight.asPainter(),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier
                    .padding(
                        horizontal = MaterialTheme.spacing.medium
                    )
            )
        }
        Row(
            modifier = Modifier
                .horizontalScroll(rememberScrollState())
                .padding(horizontal = MaterialTheme.spacing.large),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xlarge)
        ) {
            badges.forEach { badge ->
                BadgeItem(
                    badge = badge,
                    eventThrottler = eventThrottler,
                    onEvent = onEvent
                )
            }
        }
    }
}

@Composable
private fun BadgeItem(
    badge: BadgesDisplayItem,
    onEvent: (BadgesViewEvent) -> Unit,
    modifier: Modifier = Modifier,
    eventThrottler: EventThrottler? = null,
) {
    Column(
        modifier = modifier
            .width(80.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val imageUrl = if (badge.isAcquired) {
            badge.acquiredBadgeIconUrl
        } else {
            badge.badgeIconUrl
        }
        Box(
            modifier = Modifier
                .size(80.dp)
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() },
                    onClick = {
                        eventThrottler?.processEvent {
                            onEvent(
                                BadgesViewEvent.OnBadgesClick(
                                    badge.badgeConfigId
                                )
                            )
                        } ?: onEvent(BadgesViewEvent.OnBadgesClick(badge.badgeConfigId))
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            if (!imageUrl.isNullOrBlank()) {
                AsyncImage(
                    model = imageUrl,
                    contentDescription = badge.badgeName,
                    modifier = Modifier.size(80.dp)
                )
            }
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = badge.badgeName ?: "",
            style = MaterialTheme.typography.body,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )
    }
}
