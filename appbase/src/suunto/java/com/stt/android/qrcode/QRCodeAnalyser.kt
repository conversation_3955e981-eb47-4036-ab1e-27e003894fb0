package com.stt.android.qrcode

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.graphics.Rect
import android.graphics.RectF
import androidx.annotation.OptIn
import androidx.appcompat.app.AlertDialog
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.core.graphics.toRect
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import com.stt.android.R
import timber.log.Timber

class QRCodeAnalyser(
    private val context: Context,
    private val overlay: ScanOverlay,
    private val isValidResult: (String) -> Boolean,
    private val onResult: (Result<Barcode>) -> Unit
) : ImageAnalysis.Analyzer {

    private var scaleX = 0f
    private var scaleY = 0f

    private var invalidDialog: AlertDialog? = null

    private val detector by lazy {
        BarcodeScanning.getClient(
            BarcodeScannerOptions.Builder()
                .setBarcodeFormats(
                    Barcode.FORMAT_QR_CODE,
                    Barcode.FORMAT_AZTEC
                )
                .build()
        )
    }

    @OptIn(ExperimentalGetImage::class)
    @SuppressLint("UnsafeExperimentalUsageError")
    override fun analyze(imageProxy: ImageProxy) {
        val mediaImage = imageProxy.image ?: run {
            imageProxy.close()
            return
        }
        val image = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)
        detector.process(image)
            .addOnSuccessListener { barCodes ->
                if (barCodes.any()) {
                    initScale(imageProxy.width, imageProxy.height)
                    barCodes.forEach { barCode ->
                        val originRect = barCode.boundingBox ?: return@forEach
                        val translatedRect = translateRect(originRect)
                        val rect = overlay.scanningRect.toRect()
                        val inAvailableRange =
                            translatedRect.centerX().toInt() in rect.left..rect.right &&
                                translatedRect.centerY().toInt() in rect.top..rect.bottom
                        if (!inAvailableRange) return@forEach
                        if (!checkIfValid(barCode)) return@forEach
                        onResult.invoke(Result.success(barCode))
                        overlay.addRect(translatedRect)
                        detector.close()
                        return@addOnSuccessListener
                    }
                }
            }
            .addOnFailureListener {
                Timber.w(it, "Analyze qrcode error.")
                onResult.invoke(Result.failure(it))
            }
            .addOnCompleteListener { imageProxy.close() }
    }

    private fun checkIfValid(barcode: Barcode): Boolean {
        val isValid = isValidResult(barcode.rawValue ?: "")
        if (!isValid) {
            showInvalidDialog()
        } else {
            hideInvalidDialog()
        }
        return isValid
    }

    private fun showInvalidDialog() {
        if (invalidDialog?.isShowing != true) {
            invalidDialog = AlertDialog.Builder(context)
                .setTitle(R.string.device_scan_qrcode_invalid_title)
                .setMessage(R.string.device_scan_qrcode_invalid_message)
                .setPositiveButton(R.string.ok, null)
                .show()
        }
    }

    private fun hideInvalidDialog() {
        invalidDialog?.dismiss()
        invalidDialog = null
    }

    private fun initScale(imageWidth: Int, imageHeight: Int) {
        if (isPortraitMode(context)) {
            scaleY = overlay.height / imageWidth.toFloat()
            scaleX = overlay.width / imageHeight.toFloat()
        } else {
            scaleY = overlay.height / imageHeight.toFloat()
            scaleX = overlay.width / imageWidth.toFloat()
        }
    }

    private fun translateX(x: Float): Float = x * scaleX
    private fun translateY(y: Float): Float = y * scaleY

    private fun translateRect(rect: Rect) = RectF(
        translateX(rect.left.toFloat()),
        translateY(rect.top.toFloat()),
        translateX(rect.right.toFloat()),
        translateY(rect.bottom.toFloat())
    )

    private fun isPortraitMode(context: Context): Boolean {
        val mConfiguration: Configuration = context.resources.configuration
        return mConfiguration.orientation == Configuration.ORIENTATION_PORTRAIT
    }
}
