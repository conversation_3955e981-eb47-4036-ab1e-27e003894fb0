package com.stt.android.login.newsletter

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.coroutineScope
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.ui.observeK
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateFragment2
import com.stt.android.domain.user.UserSettingsDataSource
import com.stt.android.questionnaire.QuestionnaireMode.MOTIVATION_QUESTIONNAIRE
import com.stt.android.questionnaire.QuestionnaireMode.SPORTS_AND_MOTIVATION_QUESTIONNAIRE
import com.stt.android.questionnaire.QuestionnaireMode.SPORTS_QUESTIONNAIRE
import com.stt.android.questionnaire.QuestionnaireNavigator
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
abstract class NewsletterSubscriptionFragment :
    ViewStateFragment2<NewsletterSubscriptionViewState, NewsletterSubscriptionViewModel>() {
    @Inject
    lateinit var questionnaireNavigator: QuestionnaireNavigator

    @Inject
    lateinit var userSettingsDataSource: UserSettingsDataSource

    override val viewModel: NewsletterSubscriptionViewModel by viewModels()
    override val layoutId: Int = R.layout.fragment_newsletter_subscription

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.autoNavigateEvent.observeK(viewLifecycleOwner) {
            navigateForward()
        }
    }

    protected open fun navigateForward() {
        if (viewModel.viewState.value?.data?.subscribed == true) {
            lifecycle.coroutineScope.launch {
                delay(500)
                navigate()
            }
        } else {
            navigate()
        }
    }

    abstract fun navigate()

    protected fun launchQuestionnaire() {
        val settings = userSettingsDataSource.getUserSettings()
        val showSports = settings.favoriteSports.isEmpty()
        val showMotivations = settings.motivations.isEmpty()

        if (showSports || showMotivations) {
            val mode = when {
                showSports && showMotivations -> SPORTS_AND_MOTIVATION_QUESTIONNAIRE
                showSports -> SPORTS_QUESTIONNAIRE
                else -> MOTIVATION_QUESTIONNAIRE
            }
            startActivity(
                questionnaireNavigator.newStartIntent(
                    requireContext(),
                    mode,
                    R.anim.fade_in to R.anim.slide_out_down,
                    AnalyticsPropertyValue.SurveySkippedContext.NEW_USER_FLOW
                ),
            )
            activity?.overridePendingTransition(R.anim.slide_in_up, R.anim.fade_out)
        }
    }

    override fun onStateChanged(state: ViewState<NewsletterSubscriptionViewState?>) {
        // do nothing
    }

    companion object {
        const val ARG_IS_SIGNUP = "isSignUp" // Must match parameter name in navigation xml
    }
}
