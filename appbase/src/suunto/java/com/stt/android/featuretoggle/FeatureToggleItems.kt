package com.stt.android.featuretoggle

import com.stt.android.BuildConfig
import com.stt.android.utils.FlavorUtils
import com.stt.android.utils.STTConstants.FeatureTogglePreferences
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_HEADSET_RUNNING_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_SHOW_SET_WATCH_LOCATION_FOR_DEBUG_DEFAULT

fun featureKeys(isFieldTester: <PERSON><PERSON>an) = listOfNotNull(
    FeatureTogglePreferences.KEY_ENABLE_FIELD_TESTER_FOR_DEBUG.takeIf { BuildConfig.DEBUG }, // We want to make sure this is only available for Debug build for obvious reasons.
    FeatureTogglePreferences.KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG.takeIf { BuildConfig.DEBUG },
    FeatureTogglePreferences.KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG.takeIf { BuildConfig.DEBUG },
    FeatureTogglePreferences.KEY_SHOW_SET_WATCH_LOCATION_FOR_DEBUG.takeIf { BuildConfig.DEBUG },
    FeatureTogglePreferences.KEY_ENABLE_ZONE_SENSE_DEBUG_INFO.takeIf { isFieldTester || BuildConfig.DEBUG },
    FeatureTogglePreferences.KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES,
    FeatureTogglePreferences.KEY_ENABLE_HEADSET_RUNNING,
    FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS,
    FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES,
    FeatureTogglePreferences.KEY_ENABLE_WORKOUT_VALUE_GROUPS,
    FeatureTogglePreferences.KEY_GOOGLE_LOGIN,
    FeatureTogglePreferences.KEY_ENABLE_SUUNTO_PLUS_FEEDBACK,
    FeatureTogglePreferences.KEY_ENABLE_DAY_VIEW_V2,
    FeatureTogglePreferences.KEY_OFFLINE_MAP_FOR_MOBILE,
    FeatureTogglePreferences.KEY_ENABLE_WORKOUT_PAGING,
    FeatureTogglePreferences.KEY_ENABLE_HOME_SEARCH,
    FeatureTogglePreferences.KEY_ENABLE_SYNC_WIDGETS,
    FeatureTogglePreferences.KEY_ENABLE_TRANSIT_CARD.takeIf { FlavorUtils.isSuuntoAppChina },
    FeatureTogglePreferences.KEY_ENABLE_SU10,
    FeatureTogglePreferences.KEY_ENABLE_MOCK_WIDGET_INFO,
)

// Suunto app specific FeatureItems
fun getStaticFeatures(key: String): FeatureItem {
    return when (key) {
        FeatureTogglePreferences.KEY_ENABLE_FIELD_TESTER_FOR_DEBUG ->
            FeatureItem(
                name = "Enable field tester role (for debugging only)",
                key = key,
                enabled = FeatureTogglePreferences.KEY_ENABLE_FIELD_TESTER_FOR_DEBUG_DEFAULT,
                requireProcessKill = true,
            )

        FeatureTogglePreferences.KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG ->
            FeatureItem(
                name = "Show HRV graph (for debugging only)",
                key = key,
                enabled = KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG_DEFAULT,
                requireProcessKill = false
            )

        FeatureTogglePreferences.KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG ->
            FeatureItem(
                "Show headset MAC addresses (for debugging only)",
                key,
                KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG_DEFAULT
            )

        FeatureTogglePreferences.KEY_SHOW_SET_WATCH_LOCATION_FOR_DEBUG ->
            FeatureItem(
                "Show 'set debug location' menu option (for debugging only)",
                key,
                KEY_SHOW_SET_WATCH_LOCATION_FOR_DEBUG_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_ZONE_SENSE_DEBUG_INFO ->
            FeatureItem(
                "Enable ZoneSense debug info",
                key,
                FeatureTogglePreferences.KEY_ENABLE_ZONE_SENSE_DEBUG_INFO_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES ->
            FeatureItem(
                "Enable DiLu onboarding watch faces",
                key,
                KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_HEADSET_RUNNING ->
            FeatureItem("Enable Headset Running", key, KEY_ENABLE_HEADSET_RUNNING_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS ->
            FeatureItem(
                "Enable new widgets",
                key,
                FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES ->
            FeatureItem(
                "Enable TopRoutes features",
                key,
                FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_WORKOUT_VALUE_GROUPS ->
            FeatureItem(
                "Enable workout value groups",
                key,
                FeatureTogglePreferences.KEY_ENABLE_WORKOUT_VALUE_GROUPS_DEFAULT
            )

        FeatureTogglePreferences.KEY_GOOGLE_LOGIN ->
            FeatureItem(
                "Enable Google login",
                key,
                FeatureTogglePreferences.KEY_GOOGLE_LOGIN_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_SUUNTO_PLUS_FEEDBACK ->
            FeatureItem(
                "Enable SuuntoPlus feedback",
                key,
                FeatureTogglePreferences.KEY_ENABLE_SUUNTO_PLUS_FEEDBACK_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_DAY_VIEW_V2 ->
            FeatureItem(
                "Enable Day View V2",
                key,
                FeatureTogglePreferences.KEY_ENABLE_DAY_VIEW_V2_DEFAULT
            )

        FeatureTogglePreferences.KEY_OFFLINE_MAP_FOR_MOBILE ->
            FeatureItem(
                "Enable offline map for mobile",
                key,
                FeatureTogglePreferences.KEY_OFFLINE_MAP_FOR_MOBILE_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_WORKOUT_PAGING ->
            FeatureItem(
                "Enable workout paging",
                key,
                FeatureTogglePreferences.KEY_ENABLE_WORKOUT_PAGING_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_HOME_SEARCH ->
            FeatureItem(
                "Enable home search",
                key,
                FeatureTogglePreferences.KEY_ENABLE_HOME_SEARCH_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_SYNC_WIDGETS ->
            FeatureItem(
                "Enable syncing widgets",
                key,
                FeatureTogglePreferences.KEY_ENABLE_SYNC_WIDGETS_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_TRANSIT_CARD ->
            FeatureItem(
                "Enable transit card",
                key,
                FeatureTogglePreferences.KEY_ENABLE_TRANSIT_CARD_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_SU10 ->
            FeatureItem(
                "Enable SU10",
                key,
                FeatureTogglePreferences.KEY_ENABLE_SU10_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_MOCK_WIDGET_INFO ->
            FeatureItem(
                "Enable mocking widget info",
                key,
                FeatureTogglePreferences.KEY_ENABLE_MOCK_WIDGET_INFO_DEFAULT
            )

        else -> throw IllegalArgumentException("Unknown key")
    }
}
