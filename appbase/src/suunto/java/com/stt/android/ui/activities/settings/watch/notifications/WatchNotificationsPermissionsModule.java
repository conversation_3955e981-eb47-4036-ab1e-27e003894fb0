package com.stt.android.ui.activities.settings.watch.notifications;

import com.stt.android.common.viewstate.ViewStateEpoxyController;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.android.components.FragmentComponent;

@Module
@InstallIn(FragmentComponent.class)
public abstract class WatchNotificationsPermissionsModule {

    @Binds
    public abstract ViewStateEpoxyController<WatchNotificationsPermissionsContainer> bindWatchNotificationsPermissionsController(
        WatchNotificationsPermissionsController controller);

    @Binds
    public abstract ViewStateEpoxyController<WatchAppNotificationsPermissionsContainer> WatchAppNotificationsPermissionsContainer(
        WatchAppNotificationsPermissionsController controller);
}
