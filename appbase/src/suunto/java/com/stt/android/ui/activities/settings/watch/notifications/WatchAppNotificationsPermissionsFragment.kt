package com.stt.android.ui.activities.settings.watch.notifications

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.coroutines.launchOnLifecycle
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class WatchAppNotificationsPermissionsFragment : BaseWatchAppNotificationsPermissionsFragment<WatchAppNotificationsPermissionsViewModel>() {
    override val viewModel: WatchAppNotificationsPermissionsViewModel by viewModels()

    override val layoutId = R.layout.fragment_watch_app_notifications_permissions

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        var notConnectedSnackBar: Snackbar? = null
        launchOnLifecycle(Lifecycle.State.RESUMED) {
            viewModel.isWatchConnected.collect { isConnected ->
                if (isConnected) {
                    notConnectedSnackBar?.dismiss()
                    notConnectedSnackBar = null
                } else {
                    if (notConnectedSnackBar == null) {
                        notConnectedSnackBar = Snackbar.make(
                            binding.root,
                            R.string.notification_watch_not_connected,
                            Snackbar.LENGTH_INDEFINITE
                        ).apply {
                            show()
                        }
                    }
                }
            }
        }
    }
}
