package com.stt.android.ui.components.charts

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Typeface
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.renderer.XAxisRenderer
import com.github.mikephil.charting.utils.MPPointF
import com.github.mikephil.charting.utils.Transformer
import com.github.mikephil.charting.utils.Utils
import com.github.mikephil.charting.utils.ViewPortHandler
import com.stt.android.FontRefs
import com.stt.android.R
import com.stt.android.ui.components.charts.model.SleepRegion
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.max
import kotlin.math.min
import kotlin.time.Duration.Companion.hours
import com.stt.android.core.R as CR

internal class SleepStagesXAxisRenderer(
    private val context: Context,
    viewPortHandler: ViewPortHandler,
    xAxis: XAxis,
    transformer: Transformer,
) : XAxisRenderer(viewPortHandler, xAxis, transformer) {

    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")
    private val zoneOffset = ZonedDateTime.now().offset
    private val axisHeight = Utils.convertDpToPixel(1f)
    private val tickWidth = Utils.convertDpToPixel(1f)
    private val tickHeight = Utils.convertDpToPixel(4f)
    private val labelOffset = Utils.convertDpToPixel(2f)

    // To keep the timestamp accurate, use long as min/max range
    private var minTimestamp = 0L
    private var maxTimestamp = 0L

    private var sleepRegions: List<SleepRegion> = emptyList()

    private val textBounds = Rect()

    private val tickPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = tickWidth
        color = ContextCompat.getColor(context, R.color.cloudy_grey)
    }

    fun setXRange(min: Long, max: Long) {
        minTimestamp = min
        maxTimestamp = max
    }

    fun setSleepRanges(sleepRegions: List<SleepRegion>) {
        this.sleepRegions = sleepRegions
    }

    override fun drawLabels(c: Canvas, pos: Float, anchor: MPPointF) {
        val longSleepRegions = sleepRegions.filter { !it.nap }
        val napRegions = sleepRegions.filter { it.nap }
        val points = if (longSleepRegions.isNotEmpty() && napRegions.isNotEmpty()) {
            buildSet {
                longSleepRegions.forEach {
                    addAll(splitTimeRange(it.xStart, it.xEnd))
                }
                napRegions.forEach {
                    add(it.xStart)
                    add(it.xEnd)
                }
            }.sorted()
        } else splitTimeRange(minTimestamp, maxTimestamp)
        // no idea why the calculation of the x position of the label is wrong in some cases,
        // so we use the content right to limit the x position of the label
        val contentLeft = mViewPortHandler.contentLeft()
        val contentRight = mViewPortHandler.contentRight()
        var lastTextXEnd = -Float.MAX_VALUE
        mAxisLabelPaint.color = ContextCompat.getColor(context, CR.color.near_black)
        mAxisLabelPaint.textSize = Utils.convertDpToPixel(12f)
        mAxisLabelPaint.typeface = ResourcesCompat.getFont(context, FontRefs.DEFAULT_FONT_REF)
            ?: Typeface.DEFAULT
        points.forEachIndexed { index, point ->
            val x = transformer.getPixelForValues(point.toFloat(), pos).x.toFloat()
            val y = pos + axisHeight / 2f + tickHeight

            val tickX = min(
                max(x, contentLeft + tickWidth / 2f),
                contentRight - tickWidth / 2f,
            )
            c.drawLine(tickX, pos + axisHeight / 2f, tickX, y, tickPaint)

            val isLastPoint = index == points.lastIndex
            val is24Hours = point - points.first() == 24.hours.inWholeSeconds
            val text = LocalDateTime.ofEpochSecond(
                if (isLastPoint && is24Hours) point - 60 else point + 30,
                0,
                zoneOffset,
            ).format(timeFormatter)
            mAxisLabelPaint.getTextBounds(text, 0, text.length, textBounds)
            val textWidth = textBounds.width()
            val textX = min(
                max(x, contentLeft + textWidth / 2f + tickWidth),
                contentRight - textWidth / 2f - tickWidth,
            )
            if (textX - textWidth / 2f >= lastTextXEnd) {
                lastTextXEnd = textX + textWidth / 2f
                Utils.drawXAxisValue(c, text, textX, y + labelOffset, mAxisLabelPaint, anchor, 0f)
            }
        }
    }

    private fun splitTimeRange(start: Long, end: Long, count: Int = mAxis.labelCount): List<Long> {
        val step = (end - start) / (count.coerceAtLeast(2) - 1)
        return (0 until count).map { index ->
            if (index != count - 1) {
                start + index * step
            } else end
        }
    }
}
