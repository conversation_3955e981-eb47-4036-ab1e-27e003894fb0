package com.stt.android.ui.activities.settings.watch.notifications

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import com.stt.android.R
import com.stt.android.databinding.ActivityWatchNotificationsPermissionsBinding
import com.stt.android.di.AppVersionNumberForSupport
import com.stt.android.help.BaseSupportHelper
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class WatchNotificationsPermissionsActivity : AppCompatActivity() {
    @Inject
    @AppVersionNumberForSupport
    internal lateinit var appVersionNumberForSupport: String

    @Inject
    internal lateinit var supportHelper: BaseSupportHelper

    private val binding: ActivityWatchNotificationsPermissionsBinding by lazy {
        ActivityWatchNotificationsPermissionsBinding.inflate(layoutInflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(binding.root)
        setSupportActionBar(binding.toolbar)
        supportActionBar?.let {
            it.setDisplayShowHomeEnabled(false)
            it.setDisplayHomeAsUpEnabled(true)
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean = when (item.itemId) {
        android.R.id.home -> {
            onBackPressedDispatcher.onBackPressed()
            true
        }

        R.id.notification_help -> {
            supportHelper.showFAQ(
                this,
                getString(R.string.notifications_helpshift_article_id),
                appVersionNumberForSupport
            )
            true
        }

        else -> super.onOptionsItemSelected(item)
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.notifications_menu, menu)
        return super.onCreateOptionsMenu(menu)
    }

    companion object {
        fun newStartIntent(context: Context): Intent =
            Intent(context, WatchNotificationsPermissionsActivity::class.java)
    }
}
