package com.stt.android.ui.activities.settings

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import com.gojuno.koptional.Optional
import com.squareup.moshi.Moshi
import com.stt.android.R
import com.stt.android.common.ui.observeNotNull
import com.stt.android.coroutines.launchOnLifecycle
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.domain.android.IsLocationPermissionGrantedUseCase
import com.stt.android.utils.BatteryOptimizationUtils
import com.stt.android.utils.getEnumExtra
import com.stt.android.utils.permissions.backgroundLocation.BackgroundLocationRequestHelper
import com.stt.android.utils.putEnumExtra
import com.stt.android.watch.SuuntoWatchModel
import com.stt.android.watch.companionAssociation.CompanionAssociationHelper
import com.stt.android.watch.companionAssociation.CompanionAssociationUIContext
import com.stt.android.watch.manage.ManageConnectionFragment
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChangedBy
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class PowerManagementSettingsActivity :
    PowerManagementSettingsActivityBase() {

    @Inject
    lateinit var batteryOptimizationUtils: BatteryOptimizationUtils

    @Inject
    lateinit var suuntoWatchModel: SuuntoWatchModel

    @Inject
    @SuuntoSharedPrefs
    lateinit var suuntoSharedPrefs: SharedPreferences

    @Inject
    lateinit var moshi: Moshi

    @Inject
    lateinit var companionAssociationHelperOptional: Optional<CompanionAssociationHelper>

    @Inject
    lateinit var locationPermissionUseCase: IsLocationPermissionGrantedUseCase

    private var companionAssociationUIContext: CompanionAssociationUIContext =
        CompanionAssociationUIContext.UNKNOWN
    private val backgroundLocationRequestHelper = BackgroundLocationRequestHelper()
    private var backgroundLocationRequestDisposable: Disposable? = null

    private val viewModel: PowerManagementSettingsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.title = getString(R.string.keep_connected_settings_title)
        powerManagementActions.powerManagementBatteryOptimisation.root.visibility = View.VISIBLE
        powerManagementActions.powerManagementBatteryOptimisation.root.setOnClickListener { onBatteryOptimisationClicked() }
        if (intent.getBooleanExtra(KEY_SHOW_CONNECTION_MANAGEMENT, false)) {
            title = getString(R.string.connection_handling_view_title)
            if (supportFragmentManager.findFragmentByTag(ManageConnectionFragment.FRAGMENT_TAG) == null) {
                val manageConnectionFragment = ManageConnectionFragment.newInstance()
                findViewById<View>(R.id.extraPowerManagementContentContainer).visibility =
                    View.VISIBLE
                supportFragmentManager.beginTransaction()
                    .replace(
                        R.id.extraPowerManagementContentContainer,
                        manageConnectionFragment,
                        ManageConnectionFragment.FRAGMENT_TAG
                    )
                    .commit()
            }
        }
        companionAssociationUIContext = if (intent.hasExtra(KEY_UI_CONTEXT)) {
            intent.getEnumExtra(
                KEY_UI_CONTEXT,
                CompanionAssociationUIContext::class.java,
                CompanionAssociationUIContext.UNKNOWN
            )
        } else {
            // UI context is not set when PowerManagementSettingsActivity is launched from settings.
            // The other launchers do set the explicit context.
            CompanionAssociationUIContext.SETTINGS_POWER_MANAGEMENT
        }
        configureCompanionAssociation()

        viewModel.currentDeviceType.observeNotNull(this) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                checkNeedForBackgroundLocationPermission(it)
            } else {
                checkNeedForLocationPermission(it)
            }
        }
    }

    private fun configureCompanionAssociation() {
        val companionAssociationHelper = companionAssociationHelperOptional.toNullable()

        if (companionAssociationHelper == null) {
            // companion API is not available, returning
            hideCompanionAssociationSettings()
            return
        }

        launchOnLifecycle {
            @Suppress("NewApi")
            companionAssociationHelper.pairingRequestState.collect {
                if (it is CompanionAssociationHelper.PairingRequestState.Pending && !it.consumed &&
                    it.intentSender != null
                ) {
                    startIntentSenderForResult(
                        it.intentSender,
                        DEVICE_MANAGER_ASSOCIATION_REQUEST_CODE,
                        null,
                        0,
                        0,
                        0,
                        null
                    )
                    it.consumed = true
                }
            }
        }
        launchOnLifecycle {
            @Suppress("NewApi")
            companionAssociationHelper.companionState
                .distinctUntilChangedBy { it.javaClass }
                .collect {
                    when (it) {
                        is CompanionAssociationHelper.CompanionState.Associated -> {
                            showCompanionAssociationSettings(false)
                        }

                        is CompanionAssociationHelper.CompanionState.NotAssociated -> {
                            showCompanionAssociationSettings(true)
                        }

                        else -> {
                            hideCompanionAssociationSettings()
                        }
                    }
                }
        }
    }

    private fun showCompanionAssociationSettings(needsAssociation: Boolean) {
        powerManagementActions.companionDeviceAssociation.root.visibility = View.VISIBLE
        powerManagementActions.companionDeviceAssociationHeader.visibility = View.VISIBLE
        powerManagementActions.companionDeviceAssociation.titletext =
            getString(R.string.trusted_companion_setting_title)
        if (needsAssociation) {
            powerManagementActions.companionDeviceAssociation.operation.visibility = View.VISIBLE
            powerManagementActions.companionDeviceAssociation.operationText =
                getString(R.string.trusted_companion_link_button)
        } else {
            powerManagementActions.companionDeviceAssociation.enabledCheckbox.visibility =
                View.VISIBLE
            powerManagementActions.companionDeviceAssociation.operation.visibility = View.GONE
        }
        powerManagementActions.companionDeviceAssociation.summarytext =
            getString(R.string.trusted_companion_setting_summary)

        powerManagementActions.companionDeviceAssociation.operation.setOnClickListener {
            onCompanionAssociationLinkClicked()
        }
    }

    private fun hideCompanionAssociationSettings() {
        powerManagementActions.companionDeviceAssociation.root.visibility = View.GONE
        powerManagementActions.companionDeviceAssociationHeader.visibility = View.GONE
        powerManagementActions.companionDeviceAssociation.operation.visibility = View.GONE
    }

    override fun onResume() {
        super.onResume()

        if (batteryOptimizationUtils.isIgnoringBatteryOptimizations()) {
            with(powerManagementActions.powerManagementBatteryOptimisation) {
                root.isEnabled = false
                disabledCheckbox.visibility = View.VISIBLE
                summarytext = getString(R.string.battery_optimisation_setting_summary_all_good)
            }
        } else {
            powerManagementActions.powerManagementBatteryOptimisation.summarytext =
                getString(R.string.battery_optimisation_setting_summary_action_required)
        }
    }

    private fun checkNeedForLocationPermission(deviceType: SuuntoDeviceType) {
        if (deviceType.isSuunto3Family) {
            val description =
                BackgroundLocationRequestHelper.injectBackgroundOptionLabelIntoText(
                    this@PowerManagementSettingsActivity,
                    R.string.mobile_connected_gpd_settings_description
                )
            powerManagementActions.mobileConnectedGpsSettingsDescription.text = description
            powerManagementActions.mobileConnectedGpsSettings.visibility = View.VISIBLE

            updateMobileConnectedGpsPreference(
                locationPermissionUseCase.foregroundLocationPermissionGranted()
            ) { onLocationPermissionClicked() }
        }
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private fun checkNeedForBackgroundLocationPermission(deviceType: SuuntoDeviceType) {
        if (deviceType.isSuunto3Family) {
            val description =
                BackgroundLocationRequestHelper.injectBackgroundOptionLabelIntoText(
                    this@PowerManagementSettingsActivity,
                    R.string.mobile_connected_gpd_settings_description
                )
            powerManagementActions.mobileConnectedGpsSettingsDescription.text = description
            powerManagementActions.mobileConnectedGpsSettings.visibility = View.VISIBLE
            updateMobileConnectedGpsPreference(
                BackgroundLocationRequestHelper.isPermissionGranted(
                    this@PowerManagementSettingsActivity
                )
            ) { onBackgroundLocationPermissionClicked() }
        }
    }

    private fun updateMobileConnectedGpsPreference(
        permissionGranted: Boolean,
        onClickListener: View.OnClickListener
    ) {
        with(powerManagementActions.mobileConnectedGps) {
            root.visibility = View.VISIBLE
            if (permissionGranted) {
                summarytext =
                    getString(R.string.battery_optimisation_setting_summary_all_good)
                alertIcon.visibility = View.GONE
                disabledCheckbox.visibility = View.VISIBLE
                root.setOnClickListener(null)
                root.isClickable = false
            } else {
                summarytext =
                    BackgroundLocationRequestHelper.injectBackgroundOptionLabelIntoText(
                        this@PowerManagementSettingsActivity,
                        R.string.mobile_connected_gps_location_permission_not_good
                    )
                disabledCheckbox.visibility = View.GONE
                alertIcon.visibility = View.VISIBLE
                root.setOnClickListener(onClickListener)
                root.isClickable = true
            }
            titletext =
                getString(R.string.mobile_connected_gps_location_permission_title)
        }
    }

    private fun onLocationPermissionClicked() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
            BackgroundLocationRequestHelper.REQUEST_FINE_LOCATION
        )
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private fun onBackgroundLocationPermissionClicked() {
        if (backgroundLocationRequestDisposable?.isDisposed == false) {
            return
        }
        backgroundLocationRequestDisposable =
            backgroundLocationRequestHelper.checkAndRequestBackgroundLocationPermission(
                this,
                requestFineLocationPermission = {
                    ActivityCompat.requestPermissions(
                        this,
                        arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
                        BackgroundLocationRequestHelper.REQUEST_FINE_LOCATION
                    )
                },
                requestBackgroundLocationPermission = {
                    ActivityCompat.requestPermissions(
                        this,
                        arrayOf(Manifest.permission.ACCESS_BACKGROUND_LOCATION),
                        BackgroundLocationRequestHelper.REQUEST_BACKGROUND_LOCATION
                    )
                }
            )
                .subscribeOn(AndroidSchedulers.mainThread())
                .subscribeBy(
                    onComplete = {
                        with(powerManagementActions.mobileConnectedGps) {
                            summarytext =
                                getString(R.string.battery_optimisation_setting_summary_all_good)
                            disabledCheckbox.visibility = View.VISIBLE
                            alertIcon.visibility = View.GONE
                        }
                    },
                    onError = {
                        Timber.w(it, "Background location request unsuccessful")
                    }
                )
    }

    private fun onBatteryOptimisationClicked() {
        batteryOptimizationUtils.checkBatteryOptimizationState(this)
    }

    @SuppressLint("CheckResult")
    private fun onCompanionAssociationLinkClicked() {
        val companionAssociationHelper = companionAssociationHelperOptional.toNullable()
        when (val companionState = companionAssociationHelper?.companionState?.value) {
            is CompanionAssociationHelper.CompanionState.Associated -> {
                companionAssociationHelper.removeCompanionAssociation()
            }

            is CompanionAssociationHelper.CompanionState.NotAssociated -> {
                companionAssociationHelper.startCompanionAssociationRequest(
                    companionAssociationUIContext
                )
            }

            else -> {
                Timber.w("Cannot trigger companion association in state: ${companionState?.javaClass?.name}")
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressedDispatcher.onBackPressed()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onDestroy() {
        super.onDestroy()
        backgroundLocationRequestDisposable?.dispose()
        companionAssociationHelperOptional.toNullable()?.destroy()
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == DEVICE_MANAGER_ASSOCIATION_REQUEST_CODE) {
            companionAssociationHelperOptional.toNullable()?.handleOnActivityResult(this, data)
            // After handleOnActivityResult, it may not be possible to get the expected latest state. Retry three times.
            launchOnLifecycle {
                repeat(3) {
                    delay(100)
                    companionAssociationHelperOptional.toNullable()?.refreshCompanionState()
                }
            }
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            backgroundLocationRequestHelper.onRequestPermissionsResult(
                activity = this,
                requestCode = requestCode,
                grantResults = grantResults
            )
        }
    }

    companion object {
        const val DEVICE_MANAGER_ASSOCIATION_REQUEST_CODE = 1456
        const val KEY_SHOW_CONNECTION_MANAGEMENT = "show_connection_management"
        const val KEY_UI_CONTEXT = "ui_context"

        @JvmStatic
        fun newStartIntent(
            context: Context,
            showConnectionManagement: Boolean,
            uiContext: CompanionAssociationUIContext
        ): Intent {
            return Intent(context, PowerManagementSettingsActivity::class.java)
                .putExtra(KEY_SHOW_CONNECTION_MANAGEMENT, showConnectionManagement)
                .putEnumExtra(KEY_UI_CONTEXT, uiContext)
        }

        @JvmStatic
        fun newStartIntent(
            context: Context,
            uiContext: CompanionAssociationUIContext
        ): Intent {
            return newStartIntent(context, false, uiContext)
        }

        @JvmStatic
        fun newStartIntentForDeepLink(
            context: Context
        ): Intent {
            return newStartIntent(
                context,
                false,
                CompanionAssociationUIContext.POWER_MANAGEMENT_SETTINGS_DEEPLINK
            )
        }

        @JvmStatic
        fun newStartIntentForGpsTrackingInterruptedPopUp(
            context: Context
        ): Intent {
            return newStartIntent(
                context,
                false,
                CompanionAssociationUIContext.GPS_TRACKING_INTERRUPTED_POPUP
            )
        }
    }
}
