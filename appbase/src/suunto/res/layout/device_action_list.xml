<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View"/>

        <variable
            name="sharedViewModel"
            type="com.stt.android.watch.DeviceHolderViewModel"/>
    </data>

    <!-- TODO: Refactor to use RecyclerView or Compose -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <include
            layout="@layout/viewholder_suunto_plus_store_link_item"
            app:onClick="@{() -> sharedViewModel.showSuuntoPlusStore()}"
            app:title="@{sharedViewModel.suuntoPlusStoreTitle}"
            app:description="@{sharedViewModel.suuntoPlusStoreText}"
            app:iconBgRes="@{sharedViewModel.backgroundImageRes}"
            app:visible="@{sharedViewModel.suuntoPlusStoreSupported}"/>

        <include
            layout="@layout/device_action_item_with_subtitle"
            app:title="@{@string/suunto_plus_guides_title}"
            app:description="@{@string/suunto_plus_guides_device_action_subtitle}"
            app:onClick="@{() -> sharedViewModel.showSuuntoPlusGuides()}"
            app:visible="@{sharedViewModel.suuntoPlusGuidesSupported}"/>

        <include
            layout="@layout/device_action_item_with_subtitle_divider"
            app:title="@{@string/suunto_plus_features}"
            app:description="@{@string/suunto_plus_features_device_action_subtitle}"
            app:onClick="@{() -> sharedViewModel.showSuuntoPlusFeatures()}"
            app:enabled="@{true}"
            app:visible="@{sharedViewModel.suuntoPlusFeatureSelectionEnabled}"/>

        <include
            layout="@layout/device_action_item_with_subtitle_divider"
            app:title="@{@string/watch_faces_features}"
            app:description="@{@string/suunto_plus_watch_faces_action_subtitle}"
            app:onClick="@{() -> sharedViewModel.showSuuntoPlusWatchface()}"
            app:enabled="@{true}"
            app:visible="@{sharedViewModel.suuntoPlusWatchfaceEnabled}"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:background="@color/near_white" />

        <include
            layout="@layout/device_action_item_with_subtitle"
            app:title="@{@string/interval_workouts_title}"
            app:description="@{@string/structured_workout_planner_watch_view_action_subtitle}"
            app:onClick="@{() -> sharedViewModel.showStructuredWorkoutPlanner()}"
            app:visible="@{sharedViewModel.structuredWorkoutPlannerSupported}"/>

        <LinearLayout
            android:id="@+id/device_action_sport_mode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:onClick="@{() -> sharedViewModel.showSportModesCustomization()}"
            android:orientation="vertical"
            android:enabled="@{sharedViewModel.sportModeEnabled}"
            app:visible="@{sharedViewModel.sportModeSupported}"
            style="@style/Button.Action">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/near_white" />

            <TextView
                android:id="@+id/device_action_sport_mode_header"
                android:layout_marginTop="@dimen/size_spacing_medium"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:enabled="@{sharedViewModel.sportModeEnabled}"
                android:text="@string/sport_modes_customization"
                style="@style/Body.Action.Larger"/>

            <TextView
                android:id="@+id/device_action_sport_mode_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:layout_marginBottom="@dimen/size_spacing_medium"
                android:background="@null"
                android:enabled="@{sharedViewModel.sportModeEnabled}"
                android:lineSpacingMultiplier="1.3"
                app:textFlow="@{sharedViewModel.sportModeDescriptionFlow}"
                style="@style/Body.Action"/>
        </LinearLayout>

        <include
            layout="@layout/device_action_item_with_subtitle_divider"
            app:title="@{@string/watch_offline_music_title}"
            app:description="@{@string/watch_setting_item_offline_music_content}"
            app:onClick="@{() -> sharedViewModel.showOfflineMusic()}"
            app:enabled="@{!sharedViewModel.isSyncingOrBusy()}"
            app:visible="@{sharedViewModel.offlineMusicSupported}"/>

        <include
            layout="@layout/device_action_item_with_subtitle_divider"
            app:title="@{@string/offline_maps_device_action_title}"
            app:description="@{@string/offline_maps_device_action_subtitle}"
            app:onClick="@{() -> sharedViewModel.showOfflineMaps()}"
            app:enabled="@{true}"
            app:showUnseenBadge="@{sharedViewModel.showOfflineMapsBadge}"
            app:visible="@{sharedViewModel.offlineMapsSupported}"/>

        <include
            layout="@layout/device_action_item_with_subtitle_divider"
            app:title="@{@string/widget_customization_device_action_title}"
            app:description="@{@string/widget_customization_device_action_subtitle}"
            app:onClick="@{() -> sharedViewModel.showWidgetCustomization()}"
            app:enabled="@{!sharedViewModel.isSyncingOrBusy()}"
            app:showUnseenBadge="@{sharedViewModel.showWidgetCustomizationBadge}"
            app:visible="@{sharedViewModel.widgetCustomizationSupported}"/>

        <include
            layout="@layout/device_action_item_with_subtitle_divider"
            app:title="@{@string/dive_modes_customization}"
            app:description="@{@string/dive_modes_header_text}"
            app:onClick="@{() -> sharedViewModel.showDiveModesCustomization()}"
            app:enabled="@{sharedViewModel.diveModeEnabled}"
            app:visible="@{sharedViewModel.diveModeSupported}"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:background="@color/near_white" />

        <include
            layout="@layout/device_action_item_with_icon"
            app:description="@{sharedViewModel.watchNotificationSummary}"
            app:enabled="@{!sharedViewModel.isSyncingOrBusy()}"
            app:onClick="@{() -> sharedViewModel.onNotificationsClick()}"
            app:selected="@{!sharedViewModel.notificationsOffIconEnabled}"
            app:title="@{@string/watch_notifications_settings_action}"
            app:visible="@{sharedViewModel.watchNotificationsSupported}" />

        <include
            layout="@layout/device_action_item_with_icon_divider"
            app:description="@{@string/sync_we_run_content}"
            app:enabled="@{true}"
            app:onClick="@{() -> sharedViewModel.onSyncWechatRunClick()}"
            app:selected="@{sharedViewModel.syncWechatRunConnected}"
            app:showAlert="@{false}"
            app:title="@{@string/sync_we_run_title}"
            app:visible="@{sharedViewModel.syncWechatRunSupported}" />

        <include
            layout="@layout/device_action_item_with_subtitle_divider"
            app:description="@{@string/transit_card_content}"
            app:enabled="@{!sharedViewModel.isSyncingOrBusy()}"
            app:onClick="@{() -> sharedViewModel.onTransitCardClick()}"
            app:title="@{@string/transit_card_title}"
            app:visible="@{sharedViewModel.supportsTransitCard}" />

        <include
            layout="@layout/device_action_item_with_subtitle_divider"
            app:description="@{@string/find_watch_content}"
            app:enabled="@{!sharedViewModel.isSyncingOrBusy()}"
            app:onClick="@{() -> sharedViewModel.onFindWatchClick()}"
            app:title="@{@string/find_watch_title}"
            app:visible="@{sharedViewModel.supportsFindWatch}" />

        <include
            layout="@layout/device_action_item_with_icon_divider"
            app:description="@{@string/keep_connected_settings_content}"
            app:enabled="@{true}"
            app:onClick="@{() -> sharedViewModel.onKeepConnectedSettingsClick()}"
            app:selected="@{sharedViewModel.keepConnectedSettingConnected}"
            app:showAlert="@{sharedViewModel.connectionAlertAvailable}"
            app:title="@{@string/keep_connected_settings_title}" />

        <LinearLayout
            style="@style/Button.Action"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:onClick="@{() -> sharedViewModel.onAutoLocationClick()}"
            android:orientation="vertical"
            android:padding="@dimen/size_spacing_medium"
            android:background="@drawable/background_for_device_action_list"
            app:visible="@{sharedViewModel.autoLocationSettingAvailable}">

            <TextView
                style="@style/Body.Action.Larger"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/automatic_location" />

            <Switch
                android:id="@+id/onboardingSwitch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checked="@{sharedViewModel.autoLocationSettingEnabled}"
                android:clickable="false"
                android:focusable="false"
                android:fontFamily="@font/proximanova_regular"
                android:text="@string/automatic_location_header_text"
                android:textColor="@color/color_body_action_text_disabled"
                android:textSize="@dimen/text_size_medium"
                android:theme="@style/SwitchTheme" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:background="@color/near_white"/>

        <include
            layout="@layout/device_action_item"
            app:onClick="@{() -> sharedViewModel.onIntroductionClick()}"
            app:title="@{@string/onboarding_overflow_menu_introduction}"
            app:visible="@{sharedViewModel.introductionAvailable}" />

        <include
            layout="@layout/device_action_item_with_divider"
            app:onClick="@{() -> sharedViewModel.onUserGuideClick()}"
            app:title="@{@string/onboarding_overflow_menu_user_guide}"
            app:visible="@{sharedViewModel.userGuideAvailable}" />

        <include
            layout="@layout/device_action_item_with_divider"
            app:onClick="@{() -> sharedViewModel.onHelpClick()}"
            app:title="@{@string/settings_help}"
            app:visible="@{true}" />

        <include
            layout="@layout/device_action_item_with_divider"
            app:onClick="@{() -> sharedViewModel.onConnectionClick()}"
            app:title="@{@string/watch_menu_manage_connection}"
            app:visible="@{true}" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/device_switch_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/viewholder_device_switch"
            tools:itemCount="1"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:onClick="@{() -> sharedViewModel.onPairAnotherDeviceClick()}"
            style="@style/Button.Action">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/near_white"
                app:layout_constraintTop_toTopOf="parent"/>

            <TextView
                android:id="@+id/device_action_switch_device_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_spacing_medium"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:background="@null"
                android:text="@string/device_switch_pair_another"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                style="@style/Body.Action.Larger"/>

            <TextView
                android:id="@+id/device_action_switch_device_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/size_spacing_medium"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:background="@null"
                android:lineSpacingMultiplier="1.3"
                app:deviceSwitchHeader="@{sharedViewModel.deviceStateEvent}"
                app:layout_constraintTop_toBottomOf="@id/device_action_switch_device_header"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:text="This disconnects Suunto 9"
                style="@style/Body.Action"/>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/size_spacing_small"
                android:contentDescription="@string/device_switch_pair_another"
                android:scaleType="centerInside"
                android:src="@drawable/ic_plus"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/very_light_gray"/>

    </LinearLayout>
</layout>
