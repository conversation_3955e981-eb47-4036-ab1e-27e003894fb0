package com.stt.android.core.domain.workouts

import com.google.common.truth.Truth.assertThat
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.infomodel.getActivitySummaryForActivity
import org.junit.Test

class CoreActivityTypeTest {

    @Test
    fun customValueOfMustContainsAllEnumEntries() {
        CoreActivityType.entries.forEach { type ->
            assertThat(CoreActivityType.valueOf(type.id)).isEqualTo(type)
        }
    }

    @Test
    fun hasDistanceInCoreActivityTypeMatchesTheActivitySummary() {
        CoreActivityType.entries.forEach { type ->
            ActivityMapping.entries
                .firstOrNull { mapping ->
                    mapping.stId == type.id
                }?.let { activityMapping ->
                    getActivitySummaryForActivity(
                        activityMapping = activityMapping
                    ).let { activitySummary ->
                        // Activities empty -> fallback
                        // We don't want to check the fallback
                        if (activitySummary.activities.isNotEmpty()) {
                            assertThat(activitySummary.hasDistance).isEqualTo(type.hasDistance)
                        }
                    }
                }
        }
    }
}
