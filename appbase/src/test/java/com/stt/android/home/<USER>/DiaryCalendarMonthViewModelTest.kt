package com.stt.android.home.diarycalendar

import android.content.Context
import android.content.SharedPreferences
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.Observer
import androidx.lifecycle.SavedStateHandle
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.R
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.ViewState
import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.diarycalendar.DailyWorkoutStatisticsWithSummary
import com.stt.android.domain.diarycalendar.DiaryCalendarDailyData
import com.stt.android.domain.diarycalendar.DiaryCalendarTotalValues
import com.stt.android.domain.diarycalendar.GetWorkoutStatisticsWithSummaryUseCase
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.User
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.domain.workouts.ActivityGroupMapper
import com.stt.android.domain.workouts.GetWorkoutHeaderByIdUseCase
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainerBuilder
import com.stt.android.home.diary.diarycalendar.TotalValueItem
import com.stt.android.home.diary.diarycalendar.TotalValues
import com.stt.android.home.diary.diarycalendar.month.DiaryCalendarMonthViewModel
import com.stt.android.home.diary.diarycalendar.planner.usercases.GetLastPlanUseCase
import com.stt.android.home.diary.diarycalendar.planner.usercases.PlannedWorkoutEnhancer
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.menstrualcycle.domain.MenstrualDateType
import com.stt.android.menstrualcycle.domain.ObservableMenstrualCycleUpdateUseCase
import com.stt.android.remoteconfig.api.RemoteConfig
import com.stt.android.testutils.CoroutinesTestRule
import com.stt.android.ui.utils.DateFormatter
import com.stt.android.utils.FixedFirstDayOfTheWeekCalendarProvider
import com.stt.android.workouts.details.values.WorkoutValue
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.anyBoolean
import org.mockito.ArgumentMatchers.anyDouble
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDate
import java.time.YearMonth
import java.util.Locale
import com.stt.android.core.R as CR

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(MockitoJUnitRunner::class)
class DiaryCalendarMonthViewModelTest {
    @Rule
    @JvmField
    val coroutinesTestRule = CoroutinesTestRule()

    @JvmField
    @Rule
    val archTaskExecutorRule = InstantTaskExecutorRule()

    @Mock
    private lateinit var context: Context

    @Mock
    private lateinit var dateFormatter: DateFormatter

    @Mock
    private lateinit var getWorkoutStatisticsWithSummaryUseCase: GetWorkoutStatisticsWithSummaryUseCase

    @Mock
    private lateinit var currentUserController: CurrentUserController

    @Mock
    private lateinit var observer: Observer<ViewState<DiaryCalendarListContainer?>?>

    @Mock
    private lateinit var infoModelFormatter: InfoModelFormatter

    @Mock
    private lateinit var measurementUnit: MeasurementUnit

    @Mock
    private lateinit var sharedPreferences: SharedPreferences

    @Mock
    private lateinit var emarsysAnalytics: EmarsysAnalytics

    @Mock
    private lateinit var datahubAnalyticsTracker: DatahubAnalyticsTracker

    @Mock
    private lateinit var getWorkoutHeaderByIdUseCase: GetWorkoutHeaderByIdUseCase

    @Mock
    private lateinit var observableMenstrualCycleUpdateUseCase: ObservableMenstrualCycleUpdateUseCase

    @Mock
    private lateinit var getLastPlanUseCase: GetLastPlanUseCase

    @Mock
    private lateinit var activityGroupMapper: ActivityGroupMapper

    @Mock
    private lateinit var remoteConfig: RemoteConfig

    private lateinit var plannedWorkoutEnhancer: PlannedWorkoutEnhancer

    private val calendarProvider = FixedFirstDayOfTheWeekCalendarProvider(Locale.getDefault())

    private val testDispatchers = object : CoroutinesDispatchers {
        override val main = Dispatchers.Unconfined
        override val computation = Dispatchers.Unconfined
        override val io = Dispatchers.Unconfined
    }

    private val username = User.ANONYMOUS_USERNAME

    private val totalValues = DiaryCalendarTotalValues(
        duration = 12.5,
        distance = 20.0,
        ascent = 123.4,
        energy = 1500.0,
        activityCount = 3,
        workoutIdsToKeys = emptyMap(),
        diveCount = 1,
        maxDepth = 10f
    )

    private lateinit var viewModel: DiaryCalendarMonthViewModel

    @Before
    fun setup() {
        runBlocking {
            Mockito.lenient().whenever(observableMenstrualCycleUpdateUseCase())
                .thenReturn(emptyFlow())
        }
        whenever(currentUserController.username).thenReturn(username)
        whenever(infoModelFormatter.unit).thenReturn(measurementUnit)
        whenever(measurementUnit.distanceUnit).thenReturn(CR.string.km)

        // Set up PlannedWorkoutEnhancer with mocked dependencies
        plannedWorkoutEnhancer =
            PlannedWorkoutEnhancer(getLastPlanUseCase, activityGroupMapper, remoteConfig)

        val diaryCalendarListContainerBuilder = DiaryCalendarListContainerBuilder(
            infoModelFormatter,
            JScienceUnitConverter()
        )

        val savedStateHandle = SavedStateHandle()
        viewModel = DiaryCalendarMonthViewModel(
            appContext = context,
            dateFormatter = dateFormatter,
            savedStateHandle = savedStateHandle,
            getWorkoutStatisticsWithSummaryUseCase = getWorkoutStatisticsWithSummaryUseCase,
            currentUserController = currentUserController,
            diaryPagePreferences = sharedPreferences,
            getWorkoutHeaderByIdUseCase = getWorkoutHeaderByIdUseCase,
            diaryCalendarListContainerBuilder = diaryCalendarListContainerBuilder,
            emarsysAnalytics = emarsysAnalytics,
            datahubAnalyticsTracker = datahubAnalyticsTracker,
            exploreMapPreferences = sharedPreferences,
            calendarProvider = calendarProvider,
            coroutinesDispatchers = testDispatchers,
            observableMenstrualCycleUpdateUseCase = observableMenstrualCycleUpdateUseCase,
            sharedPreferences = sharedPreferences,
            plannedWorkoutEnhancer = plannedWorkoutEnhancer,
            ioThread = Schedulers.trampoline(),
            mainThread = Schedulers.trampoline()
        )
    }

    @Test
    fun `Data is loaded for the selected month`() {
        runTest {
            whenever(getWorkoutStatisticsWithSummaryUseCase.invoke(any()))
                .thenReturn(
                    DailyWorkoutStatisticsWithSummary(
                        dailyData = emptyMap(),
                        totalValuesByActivityType = emptyMap(),
                        totalValues = totalValues,
                        locations = emptyList()
                    )
                )

            whenever(infoModelFormatter.formatAccumulatedTotalDuration(anyDouble()))
                .thenReturn("0")

            whenever(
                infoModelFormatter.formatAccumulatedTotalDistance(
                    anyDouble(),
                    anyBoolean(),
                    anyBoolean(),
                    anyBoolean()
                )
            )
                .thenReturn("1234")

            viewModel.load(YearMonth.of(2019, 12))

            val data = viewModel.viewState.value?.data
            var startDate: LocalDate = LocalDate.of(0, 1, 1)
            var endDate: LocalDate = LocalDate.of(0, 1, 1)
            if (data != null && data.bubbleData.isNotEmpty()) {
                startDate = data.bubbleData.first().startDate
                endDate = data.bubbleData.last().endDate
            }
            assertThat(startDate).isEqualTo(LocalDate.of(2019, 12, 1))
            assertThat(endDate).isEqualTo(LocalDate.of(2019, 12, 31))
        }
    }

    @Test
    fun `Start and end dates for bubble data`() {
        runTest {
            whenever(getWorkoutStatisticsWithSummaryUseCase.invoke(any()))
                .thenReturn(
                    DailyWorkoutStatisticsWithSummary(
                        dailyData = emptyMap(),
                        totalValuesByActivityType = emptyMap(),
                        totalValues = totalValues,
                        locations = emptyList()
                    )
                )

            whenever(infoModelFormatter.formatAccumulatedTotalDuration(anyDouble()))
                .thenReturn("0")

            whenever(
                infoModelFormatter.formatAccumulatedTotalDistance(
                    anyDouble(),
                    anyBoolean(),
                    anyBoolean(),
                    anyBoolean()
                )
            )
                .thenReturn("1234")

            viewModel.load(YearMonth.of(2019, 12))

            val data = viewModel.viewState.value?.data
            assertThat(data?.bubbleData?.first()?.startDate).isEqualTo(LocalDate.of(2019, 12, 1))
            assertThat(data?.bubbleData?.last()?.endDate).isEqualTo(LocalDate.of(2019, 12, 31))
        }
    }

    @Test
    fun `Bubble data is available for days with workouts`() {
        runTest {
            whenever(getWorkoutStatisticsWithSummaryUseCase.invoke(any()))
                .thenReturn(
                    DailyWorkoutStatisticsWithSummary(
                        dailyData = mapOf(
                            LocalDate.of(2019, 12, 9) to DiaryCalendarDailyData(
                                durationByActivityGroup = mapOf(ActivityGroup.Running to 3_600_000L),
                                workoutIds = listOf(1),
                                menstrualDateType = MenstrualDateType.NOTHING
                            )
                        ),
                        totalValuesByActivityType = mapOf(
                            ActivityType.RUNNING.id to totalValues
                        ),
                        totalValues = totalValues,
                        locations = emptyList()
                    )
                )

            whenever(infoModelFormatter.formatAccumulatedTotalDuration(anyDouble()))
                .thenReturn("1:00")

            whenever(
                infoModelFormatter.formatAccumulatedTotalDistance(
                    anyDouble(),
                    anyBoolean(),
                    anyBoolean(),
                    anyBoolean()
                )
            )
                .thenReturn("1234")

            viewModel.viewState.observeForever(observer)
            viewModel.load(YearMonth.of(2019, 12))

            val captor = ArgumentCaptor.forClass(ViewState::class.java)
            captor.run {
                verify(
                    observer,
                    times(2)
                ).onChanged(capture() as ViewState<DiaryCalendarListContainer?>?)
                val loadingState = allValues[0].data as DiaryCalendarListContainer
                val loadedState = allValues[1].data as DiaryCalendarListContainer

                // Empty data during loading
                assertThat(loadingState.bubbleData.first().bubbles.first().dayData)
                    .isEqualTo(
                        DiaryCalendarDailyData(
                            durationByActivityGroup = emptyMap(),
                            workoutIds = emptyList(),
                            menstrualDateType = MenstrualDateType.NOTHING
                        )
                    )

                // Fully loaded bubble data
                assertThat(
                    loadedState.bubbleData.first().bubbles.find {
                        it.startDate == LocalDate.of(
                            2019,
                            12,
                            9
                        )
                    }?.dayData
                        ?.durationByActivityGroup
                        ?.get(ActivityGroup.Running)
                )
                    .isEqualTo(3_600_000L)
            }
        }
    }

    @Test
    fun `Totals data per activity type is loaded`() {
        runTest {
            whenever(getWorkoutStatisticsWithSummaryUseCase.invoke(any()))
                .thenReturn(
                    DailyWorkoutStatisticsWithSummary(
                        dailyData = mapOf(
                            LocalDate.of(2019, 12, 1) to DiaryCalendarDailyData(
                                durationByActivityGroup = mapOf(ActivityGroup.Running to 3_600_000L),
                                workoutIds = listOf(1),
                                menstrualDateType = MenstrualDateType.NOTHING
                            ),
                            LocalDate.of(2019, 12, 31) to DiaryCalendarDailyData(
                                durationByActivityGroup = mapOf(ActivityGroup.Watersports to 1_800_000L),
                                workoutIds = listOf(2),
                                menstrualDateType = MenstrualDateType.NOTHING
                            )
                        ),
                        totalValuesByActivityType = mapOf(
                            ActivityType.RUNNING.id to totalValues,
                            ActivityType.SWIMMING.id to totalValues
                        ),
                        totalValues = totalValues,
                        locations = emptyList()
                    )
                )

            whenever(infoModelFormatter.formatAccumulatedTotalDuration(anyDouble()))
                .thenReturn("1:30")

            whenever(
                infoModelFormatter.formatAccumulatedTotalDistance(
                    anyDouble(),
                    anyBoolean(),
                    anyBoolean(),
                    anyBoolean()
                )
            )
                .thenReturn("1234")

            whenever(infoModelFormatter.unit.swimDistanceUnit).thenReturn(CR.string.meters)

            whenever(
                infoModelFormatter.formatValue(
                    eq(SummaryItem.ENERGY),
                    anyDouble(),
                    anyOrNull(),
                    any(),
                    anyBoolean()
                )
            ).thenReturn(
                WorkoutValue(
                    item = SummaryItem.ENERGY,
                    value = "0",
                    unit = CR.string.kcal
                )
            )

            viewModel.viewState.observeForever(observer)
            viewModel.load(YearMonth.of(2019, 12))

            val captor = ArgumentCaptor.forClass(ViewState::class.java)
            captor.run {
                verify(
                    observer,
                    times(2)
                ).onChanged(capture() as ViewState<DiaryCalendarListContainer?>?)
                val loadingState = allValues[0].data as DiaryCalendarListContainer
                val loadedState = allValues[1].data as DiaryCalendarListContainer

                // Empty data during loading
                assertThat(loadingState.activityStatsWithTotals)
                    .isEqualTo(emptyList<Pair<ActivityType, Long>>())

                // Fully loaded monthly totals for running
                assertThat(loadedState.activityStatsWithTotals[0])
                    .isEqualTo(
                        ActivityType.RUNNING to TotalValues(
                            duration = 12.5,
                            distance = 20.0,
                            values = listOf(
                                TotalValueItem(
                                    value = "1:30",
                                    unitRes = CR.string.hour,
                                    labelRes = R.string.duration
                                ),
                                TotalValueItem(
                                    value = "1234",
                                    unitRes = CR.string.km,
                                    labelRes = R.string.distance
                                ),
                                TotalValueItem(
                                    value = "0",
                                    unitRes = CR.string.kcal,
                                    labelRes = R.string.energy
                                )
                            ),
                            workoutIds = emptyList()
                        )
                    )

                // Fully loaded monthly totals for swimming
                assertThat(loadedState.activityStatsWithTotals[1])
                    .isEqualTo(
                        ActivityType.SWIMMING to TotalValues(
                            duration = 12.5,
                            distance = 20.0,
                            values = listOf(
                                TotalValueItem(
                                    value = "1:30",
                                    unitRes = CR.string.hour,
                                    labelRes = R.string.duration
                                ),
                                TotalValueItem(
                                    value = "1234",
                                    unitRes = CR.string.meters,
                                    labelRes = R.string.distance
                                ),
                                TotalValueItem(
                                    value = "0",
                                    unitRes = CR.string.kcal,
                                    labelRes = R.string.energy
                                )
                            ),
                            workoutIds = emptyList()
                        )
                    )
            }
        }
    }

    @Test
    fun `Megacalories are formatted as K kcal`() {
        runTest {
            whenever(getWorkoutStatisticsWithSummaryUseCase.invoke(any()))
                .thenReturn(
                    DailyWorkoutStatisticsWithSummary(
                        dailyData = mapOf(
                            LocalDate.of(2019, 12, 1) to DiaryCalendarDailyData(
                                durationByActivityGroup = mapOf(ActivityGroup.Running to 3_600_000L),
                                workoutIds = listOf(1),
                                menstrualDateType = MenstrualDateType.NOTHING
                            )
                        ),
                        totalValuesByActivityType = mapOf(
                            ActivityType.RUNNING.id to totalValues.copy(energy = 2_000_000.0)
                        ),
                        totalValues = totalValues.copy(energy = 2_000_000.0),
                        locations = emptyList()
                    )
                )

            whenever(infoModelFormatter.formatAccumulatedTotalDuration(anyDouble()))
                .thenReturn("1:33")

            whenever(
                infoModelFormatter.formatAccumulatedTotalDistance(
                    anyDouble(),
                    anyBoolean(),
                    anyBoolean(),
                    anyBoolean()
                )
            )
                .thenReturn("1234")

            whenever(
                infoModelFormatter.formatValue(
                    eq(SummaryItem.ENERGY),
                    anyDouble(),
                    anyOrNull(),
                    any(),
                    anyBoolean()
                )
            ).thenReturn(
                WorkoutValue(
                    item = SummaryItem.ENERGY,
                    value = "2000",
                    unit = CR.string.kcal
                )
            )

            viewModel.viewState.observeForever(observer)
            viewModel.load(YearMonth.of(2019, 12))

            val captor = ArgumentCaptor.forClass(ViewState::class.java)
            captor.run {
                verify(
                    observer,
                    times(2)
                ).onChanged(capture() as ViewState<DiaryCalendarListContainer?>?)
                val loadedState = allValues[1].data as DiaryCalendarListContainer

                // Fully loaded monthly totals for running with K kcal
                assertThat(loadedState.activityStatsWithTotals[0].second.values[2])
                    .isEqualTo(
                        TotalValueItem(
                            value = "2000 K",
                            unitRes = CR.string.kcal,
                            labelRes = R.string.energy
                        )
                    )
            }
        }
    }
}
