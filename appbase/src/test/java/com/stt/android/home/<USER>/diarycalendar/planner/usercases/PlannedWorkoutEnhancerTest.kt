package com.stt.android.home.diary.diarycalendar.planner.usercases

import com.stt.android.domain.diarycalendar.DailyWorkoutStatisticsWithSummary
import com.stt.android.domain.diarycalendar.DiaryCalendarTotalValues
import com.stt.android.domain.workouts.ActivityGroupMapper
import com.stt.android.remoteconfig.api.RemoteConfig
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDate

@RunWith(MockitoJUnitRunner::class)
class PlannedWorkoutEnhancerTest {

    @Mock
    private lateinit var getLastPlanUseCase: GetLastPlanUseCase

    @Mock
    private lateinit var activityGroupMapper: ActivityGroupMapper

    @Mock
    private lateinit var totalValues: DiaryCalendarTotalValues

    @Mock
    private lateinit var remoteConfig: RemoteConfig

    private lateinit var testData: DailyWorkoutStatisticsWithSummary
    private val startDate = LocalDate.now()
    private val endDate = LocalDate.now().plusDays(7)

    @Before
    fun setup() {
        // Clear any cached data before each test
        PlannedWorkoutEnhancer.clearCache()

        // Initialize test data after mocks are created
        testData = DailyWorkoutStatisticsWithSummary(
            dailyData = emptyMap(),
            totalValuesByActivityType = emptyMap(),
            totalValues = totalValues,
            locations = emptyList()
        )

        whenever(remoteConfig.isAiPlannerEnabled()).thenReturn(true)
    }

    @Test
    fun `caching works across different PlannedWorkoutEnhancer instances`() = runTest {
        println("[DEBUG_LOG] Testing PlannedWorkoutEnhancer caching across instances...")

        // Setup mock to return NoLastPlan
        whenever(getLastPlanUseCase.invoke()).thenReturn(GetLastPlanUseCaseResult.NoLastPlan)

        // Create first instance and call enhanceWithPlannedWorkouts
        val enhancer1 = PlannedWorkoutEnhancer(getLastPlanUseCase, activityGroupMapper, remoteConfig)
        enhancer1.enhanceWithPlannedWorkouts(testData, startDate, endDate)
        println("[DEBUG_LOG] First call completed")

        // Create second instance and call enhanceWithPlannedWorkouts
        val enhancer2 = PlannedWorkoutEnhancer(getLastPlanUseCase, activityGroupMapper, remoteConfig)
        enhancer2.enhanceWithPlannedWorkouts(testData, startDate, endDate)
        println("[DEBUG_LOG] Second call completed")

        // Create third instance and call enhanceWithPlannedWorkouts
        val enhancer3 = PlannedWorkoutEnhancer(getLastPlanUseCase, activityGroupMapper, remoteConfig)
        enhancer3.enhanceWithPlannedWorkouts(testData, startDate, endDate)
        println("[DEBUG_LOG] Third call completed")

        // Verify that getLastPlanUseCase was only called once due to caching
        verify(getLastPlanUseCase, times(1)).invoke()
        println("[DEBUG_LOG] SUCCESS: getLastPlanUseCase was called only once, caching is working!")
    }

    @Test
    fun `multiple calls on same instance use cache`() = runTest {
        println("[DEBUG_LOG] Testing PlannedWorkoutEnhancer caching on same instance...")

        // Setup mock to return NoLastPlan
        whenever(getLastPlanUseCase.invoke()).thenReturn(GetLastPlanUseCaseResult.NoLastPlan)

        // Create instance and call enhanceWithPlannedWorkouts multiple times
        val enhancer = PlannedWorkoutEnhancer(getLastPlanUseCase, activityGroupMapper, remoteConfig)

        enhancer.enhanceWithPlannedWorkouts(testData, startDate, endDate)
        println("[DEBUG_LOG] First call completed")

        enhancer.enhanceWithPlannedWorkouts(testData, startDate, endDate)
        println("[DEBUG_LOG] Second call completed")

        enhancer.enhanceWithPlannedWorkouts(testData, startDate, endDate)
        println("[DEBUG_LOG] Third call completed")

        // Verify that getLastPlanUseCase was only called once due to caching
        verify(getLastPlanUseCase, times(1)).invoke()
        println("[DEBUG_LOG] SUCCESS: getLastPlanUseCase was called only once, caching is working!")
    }
}
