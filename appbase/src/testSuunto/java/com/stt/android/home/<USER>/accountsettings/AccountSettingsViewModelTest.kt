package com.stt.android.home.settings.accountsettings

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.user.UserSettings
import com.stt.android.testutils.CoroutinesTestRule
import io.reactivex.schedulers.Schedulers
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.mockito.Mock
import org.mockito.junit.MockitoJUnit
import org.mockito.junit.MockitoRule
import org.mockito.kotlin.given

class AccountSettingsViewModelTest {

    @Rule
    @JvmField
    val mockitoRule: MockitoRule = MockitoJUnit.rule()

    @Rule
    @JvmField
    val coroutinesTestRule = CoroutinesTestRule()

    @Rule
    @JvmField
    val archTaskExecutorRule = InstantTaskExecutorRule()

    @Mock
    private lateinit var currentUserController: CurrentUserController

    @Mock
    private lateinit var userSettingsController: UserSettingsController

    @Mock
    private lateinit var settingsAnalyticsTracker: SettingsAnalyticsTracker

    private lateinit var accountSettingsViewModel: AccountSettingsViewModel

    @Before
    fun setup() {
        given(userSettingsController.settings).willReturn(UserSettings())
        given(currentUserController.username).willReturn("")
        accountSettingsViewModel = AccountSettingsViewModel(
            currentUserController,
            userSettingsController,
            settingsAnalyticsTracker,
            Schedulers.trampoline(),
            Schedulers.trampoline(),
        )
    }

    @Test
    fun `should return false when isEmailAddressValid called wit an invalid email address`() {
        assertThat(accountSettingsViewModel.isEmailAddressValid("invalidEmailAddress@")).isFalse()
    }

    @Test
    fun `should return true when isEmailAddressValid called with a valid email address`() {
        assertThat(accountSettingsViewModel.isEmailAddressValid("<EMAIL>")).isTrue()
    }
}
