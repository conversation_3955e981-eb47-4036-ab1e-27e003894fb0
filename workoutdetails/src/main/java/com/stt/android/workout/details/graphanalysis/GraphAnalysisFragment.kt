package com.stt.android.workout.details.graphanalysis

import android.animation.ValueAnimator
import android.content.SharedPreferences
import android.graphics.Matrix
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import androidx.core.content.edit
import androidx.core.os.BundleCompat
import androidx.core.os.bundleOf
import androidx.core.view.doOnDetach
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.github.mikephil.charting.data.Entry
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.core.domain.GraphType
import com.stt.android.core.utils.EventThrottler
import com.stt.android.di.MapPreferences
import com.stt.android.extensions.combineLatest
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.infomodel.SummaryItem
import com.stt.android.intensityzone.ZoneRangeWithColor
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.tooltips.TooltipModal
import com.stt.android.ui.fragments.workout.analysis.WorkoutAnalysisHelper
import com.stt.android.utils.STTConstants.MapPreferences.KEY_WORKOUT_ANALYSIS_CHART_GUIDE
import com.stt.android.workout.details.R
import com.stt.android.workout.details.graphanalysis.highlight.GraphAnalysisHighlightInfoView
import com.stt.android.workout.details.graphanalysis.laps.AnalysisLapMarkerSelectionDialogFragment
import com.stt.android.workout.details.graphanalysis.laps.AnalysisLapSelectionDialogFragment
import com.stt.android.workout.details.graphanalysis.map.MainGraphTypeChangeViewModel
import com.stt.android.workout.details.graphanalysis.typeselection.GraphTypeSelectionDialogFragment
import com.stt.android.workout.details.intensity.IntensityZoneUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject
import kotlin.coroutines.resume
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@AndroidEntryPoint
class GraphAnalysisFragment : Fragment() {
    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    @MapPreferences
    @Inject
    lateinit var mapPreferences: SharedPreferences

    var listener: Listener? = null

    private lateinit var binding: GraphAnalysisFragmentBindingWrapper

    private var typeSelectionDialogFragment: GraphTypeSelectionDialogFragment? = null

    private val mainGraphTypeChangeViewModel: MainGraphTypeChangeViewModel by activityViewModels()

    /**
     * Most of the time the chart doesn't need to be notified about custom lap selections,
     * as the chart is the primary source for those selections and changing the chart's zoom
     * through code can cancel ongoing touch interactions.
     * If a custom lap is selected by other means (initial selection, [setGraphAnalysisSelections]),
     * then this flag should be raised to make sure the chart is updated.
     */
    private var shouldApplyCustomLapToChart = true

    private val dialogOpeningThrottler = EventThrottler()

    private val viewModel: GraphAnalysisViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (savedInstanceState != null) {
            (childFragmentManager.findFragmentByTag(GraphTypeSelectionDialogFragment.FRAGMENT_TAG) as? GraphTypeSelectionDialogFragment)?.let {
                setupTypeSelectionDialogFragment(it)
            }

            (childFragmentManager.findFragmentByTag(AnalysisLapMarkerSelectionDialogFragment.FRAGMENT_TAG) as? AnalysisLapMarkerSelectionDialogFragment)?.let {
                setupLapMarkerSelectionDialogFragment(it)
            }

            (childFragmentManager.findFragmentByTag(AnalysisLapSelectionDialogFragment.FRAGMENT_TAG) as? AnalysisLapSelectionDialogFragment)?.let {
                setupLapSelectionDialogFragment(it)
            }
        }
    }

    /**
     * Set the height of the contents meant to be initially visible in a scrollable parent.
     *
     * For the minimal versions of the layout this includes the chart, highlight bars,
     * laps selection and control buttons. The workout value grid will be below those
     * and is meant to be scrolled visible.
     *
     * For the full versions this sets height of the whole View
     */
    fun setInitiallyVisibleContentHeight(height: Int) {
        if (binding.initiallyVisibleContentBottomGuide != null) {
            binding.initiallyVisibleContentBottomGuide!!.setGuidelineBegin(height)
            // Force re-drawing the chart to properly apply the height update
            binding.graphAnalysisChart.requestLayout()
        } else {
            binding.root.updateLayoutParams {
                this.height = height
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val displayMode = arguments?.let {
            BundleCompat.getSerializable(it, ARG_DISPLAY_MODE, DisplayMode::class.java)
        } ?: DisplayMode.MINIMAL

        binding = GraphAnalysisFragmentBindingWrapper.inflate(
            inflater,
            container,
            false,
            this,
            displayMode,
        )

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        var chartReadyCalledForView = false

        updateLoadingVisibility(true)

        viewModel.viewState.observeNotNull(viewLifecycleOwner) { viewState ->
            viewState.data?.let {
                mainGraphTypeChangeViewModel.setMainGraphType(it.graphTypeInfo.mainGraphType)
                updateLoadingVisibility(false)
                updatePlayButtonEnabled(it)

                binding.graphAnalysisChart.resetZones()
                it.mainGraphZoneLimits?.let innerLet@{ intensityZoneLimits ->
                    val isInverted = it.mainGraphData?.isGraphInverted ?: false
                    val graphType = it.mainGraphData?.graphType ?: return@innerLet

                    binding.graphAnalysisChart.setIntensityZoneLimits(
                        intensityZoneLimits,
                        isInverted,
                        graphType
                    )
                }
                binding.graphAnalysisChart.setWorkoutDurationSeconds(it.durationSeconds)
                binding.graphAnalysisChart.setChartData(
                    it.mainGraphData,
                    getComparisonDataIfNeeded(it),
                    getBackgroundDataIfNeeded(it)
                )
                val isSwimming = viewState.data?.isSwimming ?: false
                updateHighlightInfoGraphType(
                    binding.mainGraphHighlightInfo,
                    viewState.data?.mainGraphData,
                    isSwimming
                )
                if (isRecoverySummary(it)) {
                    binding.comparisonGraphHighlightInfo.isInvisible = true
                    binding.backgroundGraphHighlightInfo.isInvisible = true
                } else {
                    binding.comparisonGraphHighlightInfo.isVisible = true
                    binding.backgroundGraphHighlightInfo.isVisible = true
                    updateHighlightInfoGraphType(
                        binding.comparisonGraphHighlightInfo,
                        viewState.data?.comparisonGraphData,
                        isSwimming
                    )
                    updateHighlightInfoGraphType(
                        binding.backgroundGraphHighlightInfo,
                        viewState.data?.backgroundGraphData,
                        isSwimming
                    )
                }

                typeSelectionDialogFragment?.setGraphTypeInfo(it.graphTypeInfo)

                if (!chartReadyCalledForView) {
                    chartReadyCalledForView = true
                    binding.graphAnalysisChart.post {
                        if (listener?.onGraphAnalysisChartReady() == Pair(true, true)) {
                            showAnalysisChartGuideIfNeeded()
                        }
                    }
                }
            }
        }

        combineLatest(
            viewModel.viewState,
            viewModel.lapsData,
        ).observeNotNull(viewLifecycleOwner) { (viewState, lapsData) ->
            val entryEnabled = (viewState.data?.canSelectLap ?: false) && lapsData.hasLapsData
            binding.selectedLapView.canSelectLap = entryEnabled
            binding.selectLapButton?.isVisible = entryEnabled
            if (entryEnabled) {
                binding.tvSelectLapLabel?.apply {
                    val autoLapLength = lapsData.lapsTables.firstOrNull {
                        it.lapsType == lapsData.lapsTableType
                    }?.autoLapLength?.takeIf { it > 0f }
                    val lapLabel = infoModelFormatter.formatLapTitleAbbr(
                        lapsData.lapsTableType,
                        lapsData.selectedLap,
                        autoLapLength,
                    )
                    isVisible = !lapLabel.isNullOrBlank()
                    text = lapLabel?.replace(" ", "")
                }
            } else {
                binding.tvSelectLapLabel?.isVisible = false
            }
        }

        viewModel.highlightedSecond.observeNotNull(viewLifecycleOwner) { second ->
            binding.graphAnalysisChart.highlightAtXValue(second)
        }

        viewModel.playbackResumed.observeNotNull(viewLifecycleOwner) { resumed ->
            val iconRes =
                if (resumed) BaseR.drawable.ic_pause_outline else BaseR.drawable.ic_play_white_triangle
            binding.playButton.setImageResource(iconRes)
        }

        viewModel.lapsData.observeNotNull(viewLifecycleOwner) { lapsData ->
            binding.selectedLapView.lapsData = lapsData
            if (!lapsData.isCustomLapSelected || shouldApplyCustomLapToChart) {
                shouldApplyCustomLapToChart = false
                binding.graphAnalysisChart.showTimeRange(
                    lapsData.lapStartSecondsInWorkout,
                    lapsData.lapEndSecondsInWorkout
                )
            }
        }

        viewModel.workoutValuesContainer.observeK(viewLifecycleOwner) {
            binding.workoutValuesGrid?.workoutValuesContainer = it
        }

        viewModel.workoutValuesAreOutdated.observeNotNull(viewLifecycleOwner) {
            binding.workoutValuesGrid?.setWorkoutValuesAreOutdated(it)
        }

        binding.selectGraphTypesButton?.setOnClickListener {
            openGraphTypeSelectionDialog()
        }

        binding.mainGraphHighlightInfo.setOnClickListener {
            openGraphTypeSelectionDialog()
        }

        binding.comparisonGraphHighlightInfo.setOnClickListener {
            openGraphTypeSelectionDialog()
        }

        binding.backgroundGraphHighlightInfo.setOnClickListener {
            openGraphTypeSelectionDialog()
        }

        binding.playButton.setOnClickListener {
            if (listener?.onPlaybackClicked() != true) {
                viewModel.onPlaybackStateTogglePressed()
            }
        }

        binding.showFullscreenGraphAnalysisButton?.setOnClickListener {
            listener?.showFullscreenGraphAnalysis(
                viewModel.viewState.value?.data?.graphTypeInfo?.mainGraphType
            )
        }

        binding.closeFullscreenGraphAnalysisButton?.setOnClickListener {
            listener?.closeFullscreenGraphAnalysis()
        }

        binding.selectedLapView.onClearLapClickListener = {
            viewModel.clearSelectedLap()
        }

        binding.selectedLapView.onOpenLapSelectionDialogClickListener = {
            openLapSelectionDialog()
        }
        binding.selectedLapView.onNextLapClickListener = {
            viewModel.setSelectNextLap()
        }
        binding.selectedLapView.onPreviousLapClickListener = {
            viewModel.setSelectPreviousLap()
        }
        binding.selectLapButton?.setOnClickListener {
            openLapMarkerSelectionDialog()
        }
        binding.graphAnalysisChart.highlightListener =
            object : GraphAnalysisChart.HighlightListener {
                override fun onHighlight(data: GraphAnalysisChartHighlightData) {
                    updateHighlightInfoCurrentValue(
                        binding.mainGraphHighlightInfo,
                        data.mainChartData,
                        data.mainEntry,
                        data.mainGraphZoneRanges
                    )
                    updateHighlightInfoCurrentValue(
                        binding.comparisonGraphHighlightInfo,
                        data.comparisonChartData,
                        data.comparisonEntry,
                        null
                    )
                    updateHighlightInfoCurrentValue(
                        binding.backgroundGraphHighlightInfo,
                        data.backgroundChartData,
                        data.backgroundEntry,
                        null
                    )

                    binding.highlightedDurationLabel.text = infoModelFormatter
                        .formatValue(SummaryItem.DURATION, data.closestEntry.x)
                        .value
                        .orEmpty()

                    val viewStateData = viewModel.viewState.value?.data
                    if (viewStateData != null) {
                        val distance = data.closestEntry.x.takeIf { it > 0f }?.let {
                            viewStateData.timeInWorkoutToDistance.invoke(it)
                        } ?: 0f
                        binding.highlightedDistanceLabel.text =
                            infoModelFormatter.formatAccumulatedTotalDistance(
                                distance = distance.toDouble(),
                                usesNauticalUnits = viewStateData.usesNauticalUnits,
                                isSwimming = viewStateData.isSwimming,
                                fourDigitsRegularDistance = true
                            )

                        val distanceUnitRes = if (viewStateData.usesNauticalUnits) {
                            CR.string.nautical_mile
                        } else if (viewStateData.isSwimming) {
                            infoModelFormatter.unit.swimDistanceUnit
                        } else {
                            infoModelFormatter.unit.distanceUnit
                        }
                        binding.highlightedDistanceUnit.text = getString(distanceUnitRes)
                    } else {
                        binding.highlightedDistanceLabel.text = ""
                        binding.highlightedDistanceUnit.text = ""
                    }

                    if (!data.programmaticallyHighlighted) {
                        viewModel.onChartTouchXValueHighlighted(data.closestEntry.x)
                    }
                }

                override fun onHighlightCleared() {
                    updateHighlightInfoCurrentValue(
                        binding.mainGraphHighlightInfo,
                        null,
                        null,
                        null
                    )
                    updateHighlightInfoCurrentValue(
                        binding.comparisonGraphHighlightInfo,
                        null,
                        null,
                        null
                    )
                    updateHighlightInfoCurrentValue(
                        binding.backgroundGraphHighlightInfo,
                        null,
                        null,
                        null
                    )
                }
            }

        binding.graphAnalysisChart.visibleChartRangeChangedListener =
            object : GraphAnalysisChart.VisibleChartRangeChangedListener {
                override fun onVisibleRangeChanged(
                    startSeconds: Float,
                    endSeconds: Float,
                    isFullyZoomedOut: Boolean
                ) {
                    if (isFullyZoomedOut) {
                        viewModel.clearSelectedLap()
                    } else {
                        viewModel.setSelectedTimeWindow(startSeconds, endSeconds)
                    }
                }
            }
    }

    private fun getComparisonDataIfNeeded(data: GraphAnalysisData) =
        if (isRecoverySummary(data)) null else data.comparisonGraphData

    private fun getBackgroundDataIfNeeded(data: GraphAnalysisData) =
        if (isRecoverySummary(data)) null else data.backgroundGraphData

    private fun isRecoverySummary(data: GraphAnalysisData): Boolean {
        val summary = (data.graphTypeInfo.mainGraphType as? GraphType.Summary)?.summaryGraph
        return summary == SummaryGraph.RECOVERYHRINTHREEMINS
    }

    override fun onDestroyView() {
        super.onDestroyView()
        shouldApplyCustomLapToChart = true
    }

    private fun setupTypeSelectionDialogFragment(dialogFragment: GraphTypeSelectionDialogFragment) {
        typeSelectionDialogFragment = dialogFragment
        val viewStateData = viewModel.viewState.value?.data ?: return
        dialogFragment.setGraphTypeInfo(viewStateData.graphTypeInfo)
        dialogFragment.setOnSelectionsChangedListener { selectedMainGraph, selectedComparisonGraph, selectedBackgroundGraph ->
            typeSelectionDialogFragment = null
            viewModel.setSelectedGraphTypes(
                viewStateData.graphTypeInfo.activityTypeInfo.activityTypeId,
                selectedMainGraph,
                selectedComparisonGraph,
                selectedBackgroundGraph
            )
        }
    }

    private fun setupLapMarkerSelectionDialogFragment(dialogFragment: AnalysisLapMarkerSelectionDialogFragment) {
        dialogFragment.setOnAnalysisLapsTableTypeSelectedListener { tableType ->
            viewModel.setSelectedLapsTableType(tableType)
        }
        val analysisLapsData = viewModel.lapsData.value
        dialogFragment.setSelectedLap(
            analysisLapsData?.lapsTableType,
            analysisLapsData?.selectedLap
        )
    }

    private fun setupLapSelectionDialogFragment(dialogFragment: AnalysisLapSelectionDialogFragment) {
        dialogFragment.setOnAnalysisLapSelectedListener { tableType, lap ->
            viewModel.setSelectedLap(tableType, lap)
        }
        val analysisLapsData = viewModel.lapsData.value
        dialogFragment.setSelectedLap(
            analysisLapsData?.lapsTableType,
            analysisLapsData?.selectedLap
        )
    }

    private fun updateHighlightInfoGraphType(
        infoView: GraphAnalysisHighlightInfoView,
        data: GraphAnalysisChartData?,
        isSwimming: Boolean
    ) {
        if (data != null) {
            infoView.setGraphNameString(
                WorkoutAnalysisHelper.getGraphNameTitle(infoView.context, data.graphType)
            )
            infoView.setGraphUnitStringRes(
                stringRes = WorkoutAnalysisHelper.getGraphUnitStringRes(
                    infoModelFormatter,
                    data.graphType,
                    data.measurementUnit,
                    isSwimming
                ) ?: 0,
                defaultTextForZeroStringRes = ""
            )
        } else {
            infoView.setGraphNameString(null)
            infoView.setGraphUnitStringRes(0)
        }
    }

    private fun getColorForValue(
        value: Float,
        zoneRanges: List<ZoneRangeWithColor>,
        isInverted: Boolean,
        graphType: GraphType
    ): Int? {
        return IntensityZoneUtils.getColorForValue(value, isInverted, zoneRanges, graphType)
    }

    private fun updateHighlightInfoCurrentValue(
        infoView: GraphAnalysisHighlightInfoView,
        data: GraphAnalysisChartData?,
        entry: Entry?,
        zoneRangeWithColors: List<ZoneRangeWithColor>?
    ) {
        if (data != null && entry != null) {
            infoView.setCurrentValueText(data.highlightedInfoValueFormatter(entry.y))

            val zoneColor = zoneRangeWithColors?.let {
                getColorForValue(
                    entry.y,
                    it,
                    data.isGraphInverted,
                    data.graphType
                )
            }
            zoneColor?.let { infoView.setCurrentValueTextColor(it) } ?: infoView.setDefaultColor()
        } else {
            infoView.setCurrentValueText(null)
        }
    }

    private fun openGraphTypeSelectionDialog() {
        if (!dialogOpeningThrottler.checkAcceptEvent()) return

        GraphTypeSelectionDialogFragment().apply {
            setupTypeSelectionDialogFragment(this)
            show(
                <EMAIL>,
                GraphTypeSelectionDialogFragment.FRAGMENT_TAG
            )
        }
    }

    private fun openLapMarkerSelectionDialog() {
        if (!dialogOpeningThrottler.checkAcceptEvent()) return

        AnalysisLapMarkerSelectionDialogFragment().apply {
            setupLapMarkerSelectionDialogFragment(this)
            show(
                <EMAIL>,
                AnalysisLapMarkerSelectionDialogFragment.FRAGMENT_TAG
            )
        }
    }

    private fun openLapSelectionDialog() {
        if (!dialogOpeningThrottler.checkAcceptEvent()) return

        AnalysisLapSelectionDialogFragment().apply {
            setupLapSelectionDialogFragment(this)
            show(
                <EMAIL>,
                AnalysisLapSelectionDialogFragment.FRAGMENT_TAG
            )
        }
    }

    private fun updatePlayButtonEnabled(data: GraphAnalysisData) {
        val dataSizes = listOfNotNull(
            data.mainGraphData?.data?.maxOf { it.entries.size },
            data.comparisonGraphData?.data?.maxOf { it.entries.size },
            data.backgroundGraphData?.data?.maxOf { it.entries.size },
        )
        val hasMoreThanOneDataPoint = dataSizes.any { it > 1 }
        binding.playButton.isEnabled = hasMoreThanOneDataPoint
    }

    private fun updateLoadingVisibility(loading: Boolean) = with(binding) {
        if (loading) {
            invisibleWhileLoading.isInvisible = true
            loadingIndicator.isVisible = true
            mainGraphHighlightInfo.isInvisible = true
            comparisonGraphHighlightInfo.isInvisible = true
            backgroundGraphHighlightInfo.isInvisible = true
        } else {
            invisibleWhileLoading.isVisible = true
            loadingIndicator.isVisible = false
            mainGraphHighlightInfo.isVisible = true
            comparisonGraphHighlightInfo.isVisible = true
            backgroundGraphHighlightInfo.isVisible = true
        }
    }

    fun setGraphAnalysisSelections(selections: GraphAnalysisSelections) {
        when (val lapSelection = selections.lapSelection) {
            is GraphAnalysisSelections.LapsTableRowSelection -> {
                viewModel.setSelectedLap(lapSelection.lapsTableType, lapSelection.lapsTableRow)
            }

            is GraphAnalysisSelections.TimeWindowLapSelection -> {
                shouldApplyCustomLapToChart = true
                viewModel.setSelectedTimeWindow(lapSelection.startSeconds, lapSelection.endSeconds)
            }

            null -> {
                viewModel.clearSelectedLap()
            }
        }
        viewModel.onChartTouchXValueHighlighted(selections.workoutTimeInMillis.toFloat() / 1000f)
    }

    fun getGraphAnalysisSelections(): GraphAnalysisSelections {
        val lapSelection = viewModel.lapsData.value?.let {
            if (it.isCustomLapSelected && it.lapStartSecondsInWorkout != null && it.lapEndSecondsInWorkout != null) {
                GraphAnalysisSelections.TimeWindowLapSelection(
                    it.lapStartSecondsInWorkout,
                    it.lapEndSecondsInWorkout
                )
            } else if (it.lapsTableType != null && it.selectedLap != null) {
                GraphAnalysisSelections.LapsTableRowSelection(it.lapsTableType, it.selectedLap)
            } else {
                null
            }
        }

        return GraphAnalysisSelections(
            viewModel.playbackProgressMillisInWorkout,
            lapSelection
        )
    }

    private fun showAnalysisChartGuideIfNeeded() {
        lifecycleScope.launch {
            binding.graphAnalysisChart.animateZoomIn()
            val showGuide = mapPreferences.getBoolean(KEY_WORKOUT_ANALYSIS_CHART_GUIDE, true)
            if (showGuide) {
                TooltipModal.show(
                    requireActivity(),
                    binding.graphAnalysisChart,
                    R.string.workout_analysis_chart_tooltip,
                    Gravity.START or Gravity.BOTTOM,
                )
                TooltipModal.show(
                    requireActivity(),
                    binding.selectedLapView,
                    R.string.workout_analysis_select_lap_tooltip,
                    Gravity.START or Gravity.BOTTOM,
                )
                mapPreferences.edit { putBoolean(KEY_WORKOUT_ANALYSIS_CHART_GUIDE, false) }
            }
        }
    }

    private suspend fun GraphAnalysisChart.animateZoomIn() =
        suspendCancellableCoroutine { continuation ->
            animateZoomIn(
                onEnd = { continuation.resume(Unit) },
                onCancel = { continuation.cancel() },
            )
        }

    private fun GraphAnalysisChart.animateZoomIn(onEnd: () -> Unit, onCancel: () -> Unit) {
        var animator: ValueAnimator?
        animator = ValueAnimator.ofFloat(1.25f, 1f).apply {
            val matrix = Matrix()
            val center = centerOffsets
            addUpdateListener {
                val sx = it.animatedValue as Float
                matrix.setScale(sx, 1f, center.x, -center.y)
                viewPortHandler.refresh(matrix, this@animateZoomIn, true)
            }
            doOnEnd {
                onEnd()
                animator = null
            }
            doOnCancel {
                onCancel()
                animator = null
            }
            duration = 400
            start()
        }

        doOnDetach {
            animator?.let {
                onCancel()
                it.cancel()
            }
            animator = null
        }
    }

    interface Listener {
        /**
         * Called once per Fragment View's lifecycle after the chart
         * has been fully drawn for the first time.
         *
         * @return whether chart is visible & should show animation
         */
        fun onGraphAnalysisChartReady(): Pair<Boolean, Boolean>
        fun showFullscreenGraphAnalysis(initialMainGraphType: GraphType?)
        fun closeFullscreenGraphAnalysis()

        /**
         * @return whether consumed
         */
        fun onPlaybackClicked(): Boolean
    }

    enum class DisplayMode {
        FULL,
        MINIMAL,
    }

    companion object {
        private const val ARG_DISPLAY_MODE = "ARG_DISPLAY_MODE"

        fun createArguments(
            displayMode: DisplayMode,
            initialMainGraphType: GraphType? = null,
            initialSelections: GraphAnalysisSelections? = null,
        ): Bundle = bundleOf(
            ARG_DISPLAY_MODE to displayMode,
            GraphAnalysisViewModel.ARG_INITIAL_MAIN_GRAPH_TYPE to initialMainGraphType,
            GraphAnalysisViewModel.ARG_INITIAL_SELECTIONS to initialSelections
        )
    }
}
