package com.stt.android.workout.details.workoutvalues

import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutDetailsContext.WORKOUT_DETAILS_SCREEN
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutDetailsContext.WORKOUT_MULTISPORT_DETAILS_SCREEN
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.failure
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.dive.NgDiveDataBuilder
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workouts.DomainWindow
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.GetExtensionsUseCase
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.isMultisport
import com.stt.android.domain.workouts.toDomainWindow
import com.stt.android.extensions.getResId
import com.stt.android.infomodel.ActivitySummaryGroup
import com.stt.android.infomodel.SummaryGroup
import com.stt.android.infomodel.SummaryGroupHighlightMap
import com.stt.android.infomodel.SummaryGroupItemMap
import com.stt.android.infomodel.SummaryItem
import com.stt.android.infomodel.getStIdForMcId
import com.stt.android.logbook.NgDiveData
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.mapping.getActivitySummaryForActivityId
import com.stt.android.ui.controllers.WorkoutDataLoaderController
import com.stt.android.ui.controllers.loadWorkout
import com.stt.android.usecases.CalculateDFAUseCase
import com.stt.android.utils.ZoneSenseUtils
import com.stt.android.utils.firstOfType
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.SuuntoPlusGroup
import com.stt.android.workout.details.WorkoutDetailsMultisportDetailsNavEvent
import com.stt.android.workout.details.WorkoutDetailsValueDescriptionNavEvent
import com.stt.android.workout.details.WorkoutValueClickListener
import com.stt.android.workout.details.WorkoutValues
import com.stt.android.workout.details.WorkoutValuesContainer
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workouts.details.values.WorkoutValue
import com.stt.android.workouts.details.values.WorkoutValueFactory
import com.stt.android.workouts.details.values.applyScubaDiveFilteringRules
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.supervisorScope
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

interface WorkoutValuesLoader {
    suspend fun loadWorkoutValuesData(workoutHeader: WorkoutHeader): Flow<ViewState<WorkoutValues?>>

    suspend fun update(workoutHeader: WorkoutHeader)
}

@FlowPreview
@ActivityRetainedScoped
class DefaultWorkoutValuesLoader @Inject constructor(
    private val getExtensionsUseCase: GetExtensionsUseCase,
    private val infoModelFormatter: InfoModelFormatter,
    private val smlDataLoader: SmlDataLoader,
    private val navigationEventDispatcher: NavigationEventDispatcher,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope,
    private val calculateDFAUseCase: CalculateDFAUseCase,
    private val workoutDataLoaderController: WorkoutDataLoaderController,
    private val zoneSenseUtils: ZoneSenseUtils,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WorkoutValuesLoader {
    private data class ActivityTypeValues(
        val workoutValues: List<WorkoutValue>,
        val suuntoPlusValues: List<WorkoutValue>,
        val suuntoPlusGroups: List<SuuntoPlusGroup>,
        val gasData: List<com.stt.android.logbook.LogbookGasData> = emptyList(),
    )

    private val workoutValuesStateFlow = MutableStateFlow<ViewState<WorkoutValues?>>(loading())
    private lateinit var workoutHeader: WorkoutHeader

    private val workoutValueClickListener = object : WorkoutValueClickListener {
        override fun onWorkoutValueClicked(workoutValue: WorkoutValue) {
            val multisportStId =
                multisportPartActivityLoader.multisportPartActivityFlow.value.data
            navigationEventDispatcher.dispatchEvent(
                WorkoutDetailsValueDescriptionNavEvent(
                    workoutValue,
                    if (multisportStId == null) WORKOUT_DETAILS_SCREEN else WORKOUT_MULTISPORT_DETAILS_SCREEN,
                    workoutId = workoutHeader.id,
                    workoutKey = workoutHeader.key,
                )
            )
        }
    }

    override suspend fun loadWorkoutValuesData(workoutHeader: WorkoutHeader): Flow<ViewState<WorkoutValues?>> {
        update(workoutHeader)
        return workoutValuesStateFlow
    }

    override suspend fun update(workoutHeader: WorkoutHeader) {
        Timber.d("Loading workout values data")
        this.workoutHeader = workoutHeader
        activityRetainedCoroutineScope.launch {
            runSuspendCatching {
                loadSmlForActivityValues()
            }.onFailure { e ->
                Timber.w(e, "Failed to load workout values")
                workoutValuesStateFlow.value = failure<WorkoutValues>(ErrorEvent.get(e::class))
            }
        }
    }

    private suspend fun loadSmlForActivityValues() = withContext(coroutinesDispatchers.io) {
        val workoutDataAsync = async {
            runSuspendCatching {
                workoutDataLoaderController.loadWorkout(workoutHeader)
            }.getOrNull()
        }
        val sml = smlDataLoader.smlStateFlow
            .firstOrNull { it.isLoaded() }
            ?.data
        val workoutData = workoutDataAsync.await()

        // All activity windows but not the move or transition ones (filtering happens on prop getter)
        val activityWindows = sml?.summary
            ?.activityWindows
            ?.mapNotNull { it.toDomainWindow() }
            ?: emptyList()
        // When multi-sport we load the move window that contains basically the main window (other windows are activity specific etc)
        // Else we load the first window if possible (non multi-sport activities have only one window).
        val mainActivityWindow = if (workoutHeader.isMultisport) {
            sml?.summary?.moveWindow.toDomainWindow()
        } else {
            activityWindows.firstOrNull()
        }

        val activityTypeValues = extractValuesForMainActivity(
            sml = sml,
            workoutData = workoutData,
            activityWindow = mainActivityWindow,
        )

        val workoutValues = WorkoutValues(
            onValueSelectedListener = workoutValueClickListener,
            workoutValuesContainer = WorkoutValuesContainer(
                activityType = workoutHeader.activityTypeId,
                multisportPartActivity = null,
                workoutValues = activityTypeValues.workoutValues + activityTypeValues.suuntoPlusValues,
                summaryGroups = createSummaryGroups(activityTypeValues.workoutValues.mapNotNull { it.item }),
                suuntoPlusGroups = activityTypeValues.suuntoPlusGroups,
                gasData = activityTypeValues.gasData,
            ),
            multisportValuesContainers = extractValuesForMultisportParts(
                sml = sml,
                activityWindows = activityWindows,
            ),
            onMultisportDetailsClicked = ::handleMultisportDetailsClick,
            onViewMoreClicked = ::onViewMoreClicked,
        )
        workoutValuesStateFlow.value = loaded(workoutValues)

        Timber.d("Workout values data loaded from activity window")
    }

    private suspend fun extractValuesForMultisportParts(
        sml: Sml?,
        activityWindows: List<DomainWindow>,
    ): List<WorkoutValuesContainer> {
        if (sml == null || !workoutHeader.isMultisport) {
            return emptyList()
        }

        val ddfaResults = calculateDFAUseCase(
            workoutId = workoutHeader.id,
            sml = sml,
        )

        return activityWindows.mapIndexed { index, windows ->
            val stId = getStIdForMcId(windows.activityId)
            val summaryItems = getActivitySummaryForActivityId(stId).items
            val ddfaResult = ddfaResults.getOrNull(index)
            WorkoutValueFactory(
                workoutExtensions = emptyList(),
                workoutHeader = null,
                infoModelFormatter = infoModelFormatter,
                activityWindow = windows,
                vo2MaxDuration = ddfaResult?.zones?.timeInVo2MaxZone,
                anaerobicDuration = ddfaResult?.zones?.timeInAnaerobicZone,
                aerobicDuration = ddfaResult?.zones?.timeInAerobicZone,
                aerobicHrThreshold = ddfaResult?.zones?.aerobicThreshold,
                anaerobicHrThreshold = ddfaResult?.zones?.anaerobicThreshold,
                zoneSenseUtils = zoneSenseUtils,
                zoneSenseBaseline = ddfaResult?.baseline,
                zoneSenseCumulativeBaseline = ddfaResult?.cumulativeBaseline,
            ).getValueForItems(summaryItems)
        }.mapIndexed { index, workoutValues ->
            sml.streamData
                .multisportPartActivities[index]
                .let { multisportPartActivity ->
                    WorkoutValuesContainer(
                        activityType = multisportPartActivity.activityType,
                        multisportPartActivity = multisportPartActivity,
                        workoutValues = workoutValues,
                        summaryGroups = createSummaryGroups(workoutValues.mapNotNull { it.item }),
                        gasData = emptyList(), // Multisport parts don't have gas data
                    )
                }
        }
    }

    private suspend fun createSummaryGroups(
        summaryItems: List<SummaryItem>,
    ): List<ActivitySummaryGroup> = supervisorScope {
        SummaryGroup.entries.mapNotNull { summaryGroup ->
            val intersect = SummaryGroupItemMap[summaryGroup]?.intersect(summaryItems)
            if (intersect.isNullOrEmpty()) {
                null
            } else {
                ActivitySummaryGroup(
                    name = summaryGroup.getResId,
                    items = intersect.toList(),
                    highlight = SummaryGroupHighlightMap[summaryGroup]
                        ?.intersect(intersect)?.toList() ?: emptyList()
                )
            }
        }
    }

    private suspend fun extractValuesForMainActivity(
        sml: Sml?,
        workoutData: WorkoutData?,
        activityWindow: DomainWindow?,
    ): ActivityTypeValues = supervisorScope {
        val extensionsListAsync = async { getExtensionsUseCase.getExtensions(workoutHeader) }
        val activityTypeId = workoutHeader.activityTypeId
        val ddfaResult = if (workoutHeader.isMultisport) {
            null
        } else {
            calculateDFAUseCase(
                workoutId = workoutHeader.id,
                sml = sml,
            ).firstOrNull()
        }
        val extensionsList = extensionsListAsync.await()

        val ngDiveData = sml.getNgDiveData(activityWindow)
        val workoutValueFactory = WorkoutValueFactory(
            workoutExtensions = extensionsList,
            workoutHeader = workoutHeader,
            infoModelFormatter = infoModelFormatter,
            activityWindow = activityWindow,
            sml = sml,
            workoutData = workoutData,
            ngDiveData = ngDiveData,
            vo2MaxDuration = ddfaResult?.zones?.timeInVo2MaxZone,
            anaerobicDuration = ddfaResult?.zones?.timeInAnaerobicZone,
            aerobicDuration = ddfaResult?.zones?.timeInAerobicZone,
            aerobicHrThreshold = ddfaResult?.zones?.aerobicThreshold,
            anaerobicHrThreshold = ddfaResult?.zones?.anaerobicThreshold,
            zoneSenseUtils = zoneSenseUtils,
            zoneSenseBaseline = ddfaResult?.baseline?.takeIf { zoneSenseUtils.isZoneSenseDebugInfoEnabled() },
            zoneSenseCumulativeBaseline = ddfaResult?.cumulativeBaseline?.takeIf { zoneSenseUtils.isZoneSenseDebugInfoEnabled() },
        )

        val activityItems = if (activityTypeId == ActivityType.SCUBADIVING.id) {
            val divingAlgorithm = extensionsList.firstOfType<DiveExtension>()
                ?.algorithm
                ?: ngDiveData?.algorithm
            applyScubaDiveFilteringRules(
                algorithm = divingAlgorithm,
                items = getActivitySummaryForActivityId(activityTypeId).items,
            )
        } else {
            getActivitySummaryForActivityId(activityTypeId).items
        }
        val productType = extensionsList.firstOfType<SummaryExtension>()?.productType
        // Extract gas data from NgDiveData
        val gasData = ngDiveData?.gasQuantities?.values?.filterNotNull() ?: emptyList()
        
        // Filter out the items that are not relevant for the current activity type if this activity is from headphone
        ActivityTypeValues(
            workoutValues = workoutValueFactory.getValueForItems(
                filterSummaryItems(
                    activityItems,
                    activityTypeId,
                    productType
                )
            ),
            suuntoPlusValues = workoutValueFactory.getSuuntoPlusValues(),
            suuntoPlusGroups = workoutValueFactory.getSuuntoPlusValuesGrouped()
                .map { (name, values) ->
                    SuuntoPlusGroup(name = name, workoutValues = values)
                },
            gasData = gasData
        )
    }

    private fun Sml?.getNgDiveData(activityWindow: DomainWindow?): NgDiveData? {
        if (this == null || !workoutHeader.activityType.isDiving) {
            return null
        }
        if (!SuuntoDeviceType.fromVariantName(summary.header?.deviceName).isNgDiveDevice) {
            return null
        }
        return NgDiveDataBuilder.build(
            diveFooter = summary.diveFooter,
            diveHeader = summary.diveHeader,
            activityWindow = activityWindow,
            moveWindow = summary.moveWindow.toDomainWindow(),
            activityType = workoutHeader.activityType,
        )
    }

    private fun handleMultisportDetailsClick(multisportPartActivity: MultisportPartActivity) {
        if (!this::workoutHeader.isInitialized) return

        navigationEventDispatcher.dispatchEvent(
            WorkoutDetailsMultisportDetailsNavEvent(
                workoutHeader.id,
                multisportPartActivity,
                workoutDetailsAnalytics,
                smlDataLoader.smlStateFlow.value.data,
            )
        )
    }

    private fun onViewMoreClicked() {
        activityRetainedCoroutineScope.launch {
            workoutDetailsAnalytics.trackWorkoutValuesGridExpandedEvent()
        }
    }
}
