package com.stt.android.workout.details.share.recentsummary

import android.content.Context
import android.view.View
import android.widget.TextView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.domain.user.workout.RecentWorkoutSummary
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.components.charts.RecentWorkoutSummaryChart
import com.stt.android.workout.details.R
import com.stt.android.workoutdetail.recentsummary.RecentWorkoutItemHelper

@EpoxyModelClass
abstract class LongScreenshotRecentWorkoutSummaryItemModel :
    EpoxyModelWithHolder<RecentWorkoutSummaryItemViewHolder>() {

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var referenceWorkout: WorkoutHeader

    @EpoxyAttribute
    var position: Int = 0

    @EpoxyAttribute
    lateinit var context: Context

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var recentWorkoutSummary: RecentWorkoutSummary

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var startDateText: String

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var endDateText: String

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var dateType: String

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var infoModelFormatter: InfoModelFormatter

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var unitConverter: JScienceUnitConverter

    override fun getDefaultLayout(): Int = R.layout.model_item_recent_summary

    override fun bind(holder: RecentWorkoutSummaryItemViewHolder) {
        super.bind(holder)
        holder.startDate.text = startDateText
        holder.endDate.text = endDateText
        holder.dataType.text = dateType
        RecentWorkoutItemHelper.initChart(
            context,
            referenceWorkout,
            recentWorkoutSummary,
            holder.chart,
            position,
            infoModelFormatter,
            unitConverter,
        )
    }
}

class RecentWorkoutSummaryItemViewHolder : KotlinEpoxyHolder() {
    val root by bind<View>(R.id.root)
    val dataType by bind<TextView>(R.id.dataType)
    val startDate by bind<TextView>(R.id.startDate)
    val endDate by bind<TextView>(R.id.endDate)
    val chart by bind<RecentWorkoutSummaryChart>(R.id.recentWorkoutSummaryChart)
}
