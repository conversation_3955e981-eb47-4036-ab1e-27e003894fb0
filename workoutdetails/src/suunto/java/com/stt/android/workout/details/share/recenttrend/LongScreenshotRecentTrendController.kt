package com.stt.android.workout.details.share.recenttrend

import android.content.Context
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.domain.user.workout.RecentWorkoutTrend
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.mapping.InfoModelFormatter
import javax.inject.Inject
import com.stt.android.R as BR
import com.stt.android.core.R as CR

class LongScreenshotRecentTrendController @Inject constructor(
    private val infoModelFormatter: InfoModelFormatter,
    private val unitConverter: JScienceUnitConverter,
) : ViewStateEpoxyController<RecentWorkoutTrend>() {
    companion object {
        private val PAGE_COUNT_INDOOR = 3
        private val PAGE_COUNT_SNORKELING = 4
        private val PAGE_COUNT_CYCLING = 7
        private val PAGE_COUNT = 6
    }

    private var workoutHeader: WorkoutHeader? = null

    private var count = 0

    private var titles: Array<String?>? = null

    private var context: Context? = null

    private var similarWorkout = false

    fun initData(
        context: Context,
        workoutHeader: WorkoutHeader,
        similarWorkout: Boolean,
    ) {
        this.context = context
        this.similarWorkout = similarWorkout
        this.workoutHeader = workoutHeader
        setCount()
        setTitles()
    }

    override fun buildModels(viewState: ViewState<RecentWorkoutTrend?>) {
        super.buildModels(viewState)
        if (viewState.isLoaded()) {
            viewState.data?.let {
                for (index in 0 until count) {
                    longScreenshotRecentTrendItemData {
                        id("longScreenshotRecentTrendItemData")
                        recentWorkoutTrend(it)
                        context(context)
                        position(index)
                        similarWorkout(similarWorkout)
                        dataType(titles?.get(index) ?: "")
                        infoModelFormatter(infoModelFormatter)
                        unitConverter(unitConverter)
                    }
                }
            }
        }
    }

    private fun setTitles() {
        val activityType: ActivityType = workoutHeader?.activityType ?: ActivityType.DEFAULT
        val isIndoor = activityType.isIndoor
        val isSnorkeling = ActivityType.SNORKELING == activityType
        val isCycling =
            !isIndoor && (ActivityType.CYCLING == activityType || ActivityType.MOUNTAIN_BIKING == activityType || ActivityType.GRAVEL_CYCLING == activityType)
        titles = arrayOfNulls(count)
        titles?.let {
            var index = 0
            it[index++] = context?.resources?.getString(BR.string.duration)
            if (!isIndoor && !isSnorkeling) {
                it[index++] = context?.resources?.getString(BR.string.distance)
                it[index++] = context?.resources?.getString(CR.string.speed)
                it[index++] = context?.resources?.getString(BR.string.pace)
            }
            if (isSnorkeling) {
                it[index++] = context?.resources?.getString(BR.string.distance)
            }
            it[index++] = context?.resources?.getString(BR.string.energy)
            it[index++] = context?.resources?.getString(BR.string.avg_hr)
            if (isCycling) {
                it[index] = context?.resources?.getString(BR.string.avg_cadence)
            }
        }
    }

    private fun setCount(): Int {
        // refer to positionToPage() for details
        val activityType: ActivityType = workoutHeader?.activityType ?: ActivityType.DEFAULT
        val isIndoor = activityType.isIndoor
        val isSnorkeling = ActivityType.SNORKELING == activityType
        val isCycling =
            !isIndoor && (ActivityType.CYCLING == activityType || ActivityType.MOUNTAIN_BIKING == activityType || ActivityType.GRAVEL_CYCLING == activityType)
        count = if (isIndoor) {
            PAGE_COUNT_INDOOR
        } else if (isCycling) {
            PAGE_COUNT_CYCLING
        } else if (isSnorkeling) {
            PAGE_COUNT_SNORKELING
        } else {
            PAGE_COUNT
        }
        return count
    }
}
