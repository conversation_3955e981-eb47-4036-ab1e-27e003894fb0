package com.stt.android.divecustomization.customization.entities.gases

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.stt.android.divecustomization.R
import com.stt.android.divecustomization.customization.getGasNameFromComposition

data class DiveAddGasTitle(
    private val helium: Double,
    private val oxygen: Double,
    private val newGas: <PERSON>olean
) {
    val gasName: String
        @Composable get() {
            return if (newGas) {
                stringResource(R.string.dive_modes_add_gas)
            } else {
                getGasNameFromComposition(oxygen, helium)
            }
        }
}
