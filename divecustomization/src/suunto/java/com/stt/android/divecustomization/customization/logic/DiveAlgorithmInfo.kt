package com.stt.android.divecustomization.customization.logic

import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.flatMap
import com.stt.android.divecustomization.customization.entities.CustomizationModeWithAvailableOptions
import com.stt.android.divecustomization.customization.entities.DiveAlgorithmType
import com.stt.android.divecustomization.customization.entities.settings.DiveAlgorithmInfoContent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

interface DiveAlgorithmInfo : CustomizationModeParent {

    fun getDiveAlgorithmInfoFlow(): Flow<ViewState<DiveAlgorithmInfoContent>> {
        return currentModeFlow.map { value ->
            value.flatMap { modeWithAvailableOptions ->
                ViewState.Loaded(
                    getDiveAlgorithmInfoContent(modeWithAvailableOptions)
                )
            }
        }
    }

    fun getDiveAlgorithmInfoContent(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveAlgorithmInfoContent {
        val selectedAlgorithm = DiveAlgorithmType.from(modeWithAvailableOptions.mode.diving.algorithm.value)
        return DiveAlgorithmInfoContent(
            selectedAlgorithm = selectedAlgorithm
        )
    }
}
