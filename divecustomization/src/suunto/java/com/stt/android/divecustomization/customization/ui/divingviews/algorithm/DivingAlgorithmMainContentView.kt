package com.stt.android.divecustomization.customization.ui.divingviews.algorithm

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import com.stt.android.common.viewstate.ViewState
import com.stt.android.divecustomization.R
import com.stt.android.divecustomization.customization.entities.settings.DiveCustomizationAlgorithmContent
import com.stt.android.divecustomization.customization.logic.DivingAlgorithm
import com.stt.android.divecustomization.customization.ui.common.DiveNumberPickerDialog
import com.stt.android.divecustomization.customization.ui.common.DiveSingleSelectionDialog
import com.stt.android.divecustomization.customization.ui.common.LoadingView
import com.stt.android.divecustomization.customization.ui.common.ShowMessage
import com.stt.android.divecustomization.customization.ui.divingviews.algorithm.DivingAlgorithmListItemType.DIVE_ALGORITHM
import com.stt.android.divecustomization.customization.ui.divingviews.algorithm.DivingAlgorithmListItemType.DIVE_ALTITUDE
import com.stt.android.divecustomization.customization.ui.divingviews.algorithm.DivingAlgorithmListItemType.DIVE_CONSERVATISM
import com.stt.android.divecustomization.customization.ui.divingviews.algorithm.DivingAlgorithmListItemType.DIVE_MAXGF
import com.stt.android.divecustomization.customization.ui.divingviews.algorithm.DivingAlgorithmListItemType.DIVE_MINGF

@Composable
fun DiveAlgorithmsMainContentView(
    diveSettingsAlgorithmContent: ViewState<DiveCustomizationAlgorithmContent>,
    diveAlgorithmsController: DivingAlgorithm,
    onAlgorithmInfoClicked: () -> Unit
) {
    var clickedListItem by rememberSaveable { mutableStateOf<DivingAlgorithmListItemType?>(null) }

    when (diveSettingsAlgorithmContent) {
        is ViewState.Error -> {
            ShowMessage(
                messageStringResource = diveSettingsAlgorithmContent.errorEvent.errorStringRes
            )
        }
        is ViewState.Loaded -> {
            val data = diveSettingsAlgorithmContent.data!!
            DiveSettingsAlgorithmList(
                content = data,
                onListItemClicked = { itemType ->
                    clickedListItem = itemType
                },
                onAlgorithmInfoClicked = onAlgorithmInfoClicked
            )

            data.diveAlgorithm?.run {
                DiveSingleSelectionDialog(
                    dialogTitle = stringResource(R.string.dive_modes_algorithm_title),
                    availableOptions = optionsList,
                    showDialog = clickedListItem == DIVE_ALGORITHM,
                    onValueChanged = {
                        diveAlgorithmsController.setDiveAlgorithm(it)
                        clickedListItem = null
                    },
                    onDismiss = {
                        clickedListItem = null
                    }
                )
            }

            data.diveMinGF?.run {
                DiveNumberPickerDialog(
                    showDialog = clickedListItem == DIVE_MINGF,
                    title = stringResource(R.string.dive_modes_min_gf_title),
                    possibleValues = values,
                    selectedValue = selectedValue,
                    units = stringResource(units),
                    onDoneClicked = {
                        diveAlgorithmsController.setMinGF(it)
                        clickedListItem = null
                    },
                    onDismiss = {
                        clickedListItem = null
                    }
                )
            }

            data.diveMaxGF?.run {
                DiveNumberPickerDialog(
                    showDialog = clickedListItem == DIVE_MAXGF,
                    title = stringResource(R.string.dive_modes_max_gf_title),
                    possibleValues = values,
                    selectedValue = selectedValue,
                    units = stringResource(units),
                    onDoneClicked = {
                        diveAlgorithmsController.setMaxGF(it)
                        clickedListItem = null
                    },
                    onDismiss = {
                        clickedListItem = null
                    }
                )
            }

            data.diveConservatism?.run {
                DiveSingleSelectionDialog(
                    dialogTitle = stringResource(R.string.dive_modes_conservatism_title),
                    availableOptions = optionsList,
                    showDialog = clickedListItem == DIVE_CONSERVATISM,
                    onValueChanged = {
                        diveAlgorithmsController.setDiveConservatism(it)
                        // showDiveConservatismDialog = false
                        clickedListItem = null
                    },
                    onDismiss = {
                        clickedListItem = null
                    }
                )
            }

            data.diveAltitude?.run {
                DiveSingleSelectionDialog(
                    dialogTitle = stringResource(R.string.dive_modes_altitude_title),
                    availableOptions = diveAltitudeList,
                    showDialog = clickedListItem == DIVE_ALTITUDE,
                    onValueChanged = {
                        diveAlgorithmsController.setDiveAltitude(it)
                        clickedListItem = null
                    },
                    onDismiss = {
                        clickedListItem = null
                    }
                )
            }
        }
        is ViewState.Loading -> {
            LoadingView()
        }
    }
}
