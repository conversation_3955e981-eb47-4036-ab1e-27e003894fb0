plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.compose"
    alias libs.plugins.androidx.navigation.safeargs.kotlin
}

android {
    namespace 'com.stt.android.divecustomization'
    buildFeatures.buildConfig = true
}

dependencies {
    implementation project(Deps.core)
    implementation project(Deps.appBase)
    implementation project(Deps.domain)
    implementation project(Deps.datasource)
    implementation project(Deps.diveCustomizationDomain)
    suuntoImplementation project(Deps.connectivity)
    implementation libs.soy.algorithms
    implementation libs.androidx.swiperefreshlayout
    implementation libs.gson
}
