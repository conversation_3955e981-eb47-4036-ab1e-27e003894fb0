[{"Activities": ["Triathlon", "Duathlon", "SwimRun", "Aquathlon", "Multisport"], "Items": ["Duration", "Distance", "AvgHeartRate", "TrainingStressScore", "RecoveryTime", "AvgPower", "NormalizedPower", "AvgPowerWithZero", "Steps", "AvgStepCadence", "MaxStepCadence", "AvgCadence", "MaxCadence", "AvgStepLength", "AvgStrideLength", "Feeling", "Energy", "FatConsumption", "CarbohydrateConsumption", "AscentAltitude", "AvgAscentSpeed", "DescentAltitude", "AvgDescentSpeed", "TotalTime", "PauseTime", "MovingTime", "EstVO2peak", "MaxHeartRate", "MinHeartRate", "AvgSpeed", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "PeakPower30S", "PeakPower1M", "PeakPower3M", "PeakPower5M", "Pte", "MoveType", "AvgTemperature", "PeakEpoc", "Climbs", "ClimbsCategory1", "ClimbsCategory2", "ClimbsCategory3", "ClimbsCategory4", "ClimbsCategoryHC", "ClimbAscentCategory1", "ClimbAscentCategory2", "ClimbAscentCategory3", "ClimbAscentCategory4", "ClimbAscentCategoryHC", "ClimbDistanceCategory1", "ClimbDistanceCategory2", "ClimbDistanceCategory3", "ClimbDistanceCategory4", "ClimbDistanceCategoryHC", "ClimbDurationCategory1", "ClimbDurationCategory2", "ClimbDurationCategory3", "ClimbDurationCategory4", "ClimbDurationCategoryHC"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Speed", "Power", "Altitude", "Temperature", "Epoc", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "Pace", "<PERSON><PERSON>", "SwimPace", "SwimStrokeRate", "<PERSON>wolf", "VerticalSpeed", "VerticalOscillation", "GroundContactTime", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Speed", "Power", "Altitude", "Temperature", "Epoc", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "Pace", "<PERSON><PERSON>", "SwimPace", "SwimStrokeRate", "<PERSON>wolf", "VerticalSpeed", "VerticalOscillation", "GroundContactTime", "RecoveryHRInThreeMins"], "IntervalLaps": [], "ManualLaps": [], "DistanceAutoLaps": [], "DurationAutoLaps": [], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Running"], "Items": ["Duration", "Distance", "AvgPace", "NormalizedGradedPace", "AvgHeartRate", "AvgPower", "NormalizedPower", "Feeling", "TrainingStressScore", "RecoveryTime", "CO2EmissionsReduced", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "AscentAltitude", "DescentAltitude", "Energy", "FatConsumption", "CarbohydrateConsumption", "EstVO2peak", "MaxHeartRate", "MinHeartRate", "Pte", "Steps", "AvgStepCadence", "MaxStepCadence", "AvgCadence", "MaxCadence", "AvgStepLength", "AvgStrideLength", "AvgGroundContactTime", "AvgVerticalOscillation", "AvgGroundContactBalance", "TotalTime", "PauseTime", "MovingTime", "RestTime", "MovingPace", "Max<PERSON>ace", "PeakPace30S", "PeakPace1M", "PeakPace3M", "PeakPace5M", "PeakPower30S", "PeakPower1M", "PeakPower3M", "PeakPower5M", "MoveType", "AvgSpeed", "MaxSpeed", "AscentTime", "AvgAscentSpeed", "DescentTime", "AvgDescentSpeed", "HighAltitude", "LowAltitude", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "PerformanceLevel", "AvgTemperature", "PeakEpoc"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Pace", "Power", "Altitude", "<PERSON><PERSON>", "Speed", "Epoc", "VerticalSpeed", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "VerticalOscillation", "GroundContactTime", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Pace", "Power", "Altitude", "<PERSON><PERSON>", "Speed", "Epoc", "VerticalSpeed", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "VerticalOscillation", "GroundContactTime", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "Distance", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps", "AvgGroundContactTime", "AvgVerticalOscillation"], "ManualLaps": ["Duration", "Distance", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps", "AvgGroundContactTime", "AvgVerticalOscillation"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps", "AvgGroundContactTime", "AvgVerticalOscillation"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps", "Energy", "PeakEpoc", "AvgTemperature", "MaxTemperature", "AvgGroundContactTime", "AvgVerticalOscillation"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["RollerSkiing"], "Items": ["Duration", "Distance", "AvgHeartRate", "TrainingStressScore", "RecoveryTime", "Feeling", "Energy", "Pte", "AvgCadence", "MaxCadence", "Steps", "AvgSkiSpeed", "MaxSkiSpeed", "AscentAltitude", "DescentAltitude", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "FatConsumption", "CarbohydrateConsumption", "TotalTime", "PauseTime", "MovingTime", "RestTime", "AvgSpeed", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "MaxHeartRate", "MinHeartRate", "MoveType", "AscentTime", "AvgAscentSpeed", "MaxAscentSpeed", "DescentTime", "AvgDescentSpeed", "MaxDescentSpeed", "HighAltitude", "LowAltitude", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "PerformanceLevel", "AvgTemperature", "PeakEpoc", "Climbs", "ClimbsCategory1", "ClimbsCategory2", "ClimbsCategory3", "ClimbsCategory4", "ClimbsCategoryHC", "ClimbAscentCategory1", "ClimbAscentCategory2", "ClimbAscentCategory3", "ClimbAscentCategory4", "ClimbAscentCategoryHC", "ClimbDistanceCategory1", "ClimbDistanceCategory2", "ClimbDistanceCategory3", "ClimbDistanceCategory4", "ClimbDistanceCategoryHC", "ClimbDurationCategory1", "ClimbDurationCategory2", "ClimbDurationCategory3", "ClimbDurationCategory4", "ClimbDurationCategoryHC"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Speed", "Altitude", "Heartrate", "Power", "Epoc", "VerticalSpeed", "<PERSON><PERSON>", "Pace", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Speed", "Altitude", "Heartrate", "Power", "Epoc", "VerticalSpeed", "<PERSON><PERSON>", "Pace", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "ManualLaps": ["Duration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps", "Energy", "PeakEpoc", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["NordicSkiing", "Biathlon", "SkateSkiing", "ClassicSkiing"], "Items": ["Duration", "Distance", "AvgHeartRate", "TrainingStressScore", "RecoveryTime", "Feeling", "Energy", "Pte", "AvgCadence", "MaxCadence", "Steps", "AvgSkiSpeed", "MaxSkiSpeed", "AscentAltitude", "DescentAltitude", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "FatConsumption", "CarbohydrateConsumption", "TotalTime", "PauseTime", "MovingTime", "RestTime", "AvgPace", "AvgSpeed", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "MaxHeartRate", "MinHeartRate", "MoveType", "AscentTime", "AvgAscentSpeed", "MaxAscentSpeed", "DescentTime", "AvgDescentSpeed", "MaxDescentSpeed", "HighAltitude", "LowAltitude", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "PerformanceLevel", "AvgTemperature", "PeakEpoc", "Climbs", "ClimbsCategory1", "ClimbsCategory2", "ClimbsCategory3", "ClimbsCategory4", "ClimbsCategoryHC", "ClimbAscentCategory1", "ClimbAscentCategory2", "ClimbAscentCategory3", "ClimbAscentCategory4", "ClimbAscentCategoryHC", "ClimbDistanceCategory1", "ClimbDistanceCategory2", "ClimbDistanceCategory3", "ClimbDistanceCategory4", "ClimbDistanceCategoryHC", "ClimbDurationCategory1", "ClimbDurationCategory2", "ClimbDurationCategory3", "ClimbDurationCategory4", "ClimbDurationCategoryHC"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Altitude", "Heartrate", "Power", "Speed", "Epoc", "VerticalSpeed", "<PERSON><PERSON>", "Pace", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Altitude", "Heartrate", "Power", "Speed", "Epoc", "VerticalSpeed", "<PERSON><PERSON>", "Pace", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "Distance", "AvgSpeed", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "ManualLaps": ["Duration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps", "Energy", "PeakEpoc", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Treadmill", "Chores"], "Items": ["Duration", "Distance", "AvgPace", "AvgSpeed", "AvgHeartRate", "TrainingStressScore", "RecoveryTime", "Energy", "Feeling", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "FatConsumption", "CarbohydrateConsumption", "EstVO2peak", "Pte", "MaxHeartRate", "MinHeartRate", "Steps", "AvgStepCadence", "MaxStepCadence", "AvgCadence", "MaxCadence", "AvgGroundContactTime", "AvgVerticalOscillation", "AvgGroundContactBalance", "TotalTime", "PauseTime", "MovingTime", "RestTime", "MovingPace", "Max<PERSON>ace", "PeakPace30S", "PeakPace1M", "PeakPace3M", "PeakPace5M", "AvgPower", "NormalizedPower", "PeakPower30S", "PeakPower1M", "PeakPower3M", "PeakPower5M", "MoveType", "PeakEpoc", "PerformanceLevel", "AvgTemperature"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Pace", "Power", "<PERSON><PERSON>", "Speed", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "GroundContactTime", "VerticalOscillation", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Pace", "Power", "<PERSON><PERSON>", "Speed", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "GroundContactTime", "VerticalOscillation", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "Distance", "AvgPace", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "AvgCadence", "Steps", "AvgGroundContactTime", "AvgVerticalOscillation"], "ManualLaps": ["Duration", "Distance", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AscentAltitude", "AvgCadence", "Steps", "AvgGroundContactTime", "AvgVerticalOscillation"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AscentAltitude", "AvgCadence", "Steps", "AvgGroundContactTime", "AvgVerticalOscillation"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AscentAltitude", "AvgCadence", "Steps", "Energy", "PeakEpoc", "AvgTemperature", "MaxTemperature", "AvgGroundContactTime", "AvgVerticalOscillation"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["TrailRunning", "Orienteering", "VerticalRun"], "Items": ["Duration", "Distance", "AvgHeartRate", "AvgPace", "NormalizedGradedPace", "EstimatedFloorsClimbed", "AvgPower", "NormalizedPower", "AscentAltitude", "DescentAltitude", "TrainingStressScore", "RecoveryTime", "Energy", "Feeling", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "EstVO2peak", "FatConsumption", "CarbohydrateConsumption", "AscentTime", "AvgAscentSpeed", "MaxAscentSpeed", "DescentTime", "AvgDescentSpeed", "MaxDescentSpeed", "HighAltitude", "LowAltitude", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "MaxHeartRate", "MinHeartRate", "TotalTime", "PauseTime", "MovingTime", "RestTime", "MovingPace", "Max<PERSON>ace", "PeakPace30S", "PeakPace1M", "PeakPace3M", "PeakPace5M", "AvgSpeed", "MaxSpeed", "MoveType", "Pte", "AvgTemperature", "PeakEpoc", "Steps", "AvgStepCadence", "MaxStepCadence", "AvgCadence", "MaxCadence", "AvgStepLength", "AvgStrideLength", "AvgGroundContactTime", "AvgVerticalOscillation", "PeakPower30S", "PeakPower1M", "PeakPower3M", "PeakPower5M", "Climbs", "ClimbsCategory1", "ClimbsCategory2", "ClimbsCategory3", "ClimbsCategory4", "ClimbsCategoryHC", "ClimbAscentCategory1", "ClimbAscentCategory2", "ClimbAscentCategory3", "ClimbAscentCategory4", "ClimbAscentCategoryHC", "ClimbDistanceCategory1", "ClimbDistanceCategory2", "ClimbDistanceCategory3", "ClimbDistanceCategory4", "ClimbDistanceCategoryHC", "ClimbDurationCategory1", "ClimbDurationCategory2", "ClimbDurationCategory3", "ClimbDurationCategory4", "ClimbDurationCategoryHC"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Pace", "Power", "Altitude", "Heartrate", "VerticalSpeed", "<PERSON><PERSON>", "Speed", "Epoc", "Temperature", "GroundContactTime", "VerticalOscillation", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Pace", "Power", "Altitude", "Heartrate", "VerticalSpeed", "<PERSON><PERSON>", "Speed", "Epoc", "Temperature", "GroundContactTime", "VerticalOscillation", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "Distance", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgHeartRate", "AvgPace", "MinHeartRate", "MaxHeartRate", "AvgCadence", "Steps", "AvgGroundContactTime", "AvgVerticalOscillation"], "ManualLaps": ["Duration", "Distance", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgHeartRate", "AvgPace", "MinHeartRate", "MaxHeartRate", "AvgCadence", "Steps", "AvgGroundContactTime", "AvgVerticalOscillation"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgPace", "AvgCadence", "Steps", "AvgGroundContactTime", "AvgVerticalOscillation"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "AvgPace", "AvgPower", "Max<PERSON><PERSON><PERSON>", "MinHeartRate", "MaxHeartRate", "AvgCadence", "Steps", "Energy", "PeakEpoc", "AvgTemperature", "MaxTemperature", "AvgGroundContactTime", "AvgVerticalOscillation"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["SkiTouring", "Mountaineering"], "Items": ["Duration", "Distance", "AscentAltitude", "AscentTime", "DescentAltitude", "DescentTime", "AvgHeartRate", "Energy", "TrainingStressScore", "RecoveryTime", "Feeling", "TotalTime", "PauseTime", "MovingTime", "RestTime", "AvgAscentSpeed", "MaxAscentSpeed", "AvgDescentSpeed", "MaxDescentSpeed", "HighAltitude", "LowAltitude", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "Climbs", "ClimbsCategory1", "ClimbsCategory2", "ClimbsCategory3", "ClimbsCategory4", "ClimbsCategoryHC", "ClimbAscentCategory1", "ClimbAscentCategory2", "ClimbAscentCategory3", "ClimbAscentCategory4", "ClimbAscentCategoryHC", "ClimbDistanceCategory1", "ClimbDistanceCategory2", "ClimbDistanceCategory3", "ClimbDistanceCategory4", "ClimbDistanceCategoryHC", "ClimbDurationCategory1", "ClimbDurationCategory2", "ClimbDurationCategory3", "ClimbDurationCategory4", "ClimbDurationCategoryHC", "AvgPace", "Max<PERSON>ace", "AvgSpeed", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "AvgCadence", "FatConsumption", "CarbohydrateConsumption", "MaxHeartRate", "MinHeartRate", "EstVO2peak", "MoveType", "Pte", "PeakEpoc", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "Steps", "AvgTemperature"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Altitude", "Pace", "Power", "VerticalSpeed", "Speed", "<PERSON><PERSON>", "Temperature", "Epoc", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Altitude", "Pace", "Power", "VerticalSpeed", "Speed", "<PERSON><PERSON>", "Temperature", "Epoc", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "Distance", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "AvgPace", "MinHeartRate", "MaxHeartRate", "AvgCadence", "Steps"], "ManualLaps": ["Duration", "Distance", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "AvgPace", "MinHeartRate", "MaxHeartRate", "AvgCadence", "Steps"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPace", "AvgCadence", "Steps"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "AvgPace", "MinHeartRate", "MaxHeartRate", "AvgCadence", "Steps", "Energy", "PeakEpoc", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Paragliding"], "Items": ["Duration", "TotalTime", "PauseTime", "MovingTime", "RestTime", "Distance", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "AscentAltitude", "AscentTime", "AvgAscentSpeed", "DescentAltitude", "DescentTime", "AvgDescentSpeed", "HighAltitude", "LowAltitude", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "Feeling", "MoveType", "Energy", "TrainingStressScore", "RecoveryTime", "Pte", "AvgSpeed", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "EstVO2peak", "AvgTemperature", "PeakEpoc", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "AerobicPowerThreshold", "AnaerobicPowerThreshold", "AerobicPaceThreshold", "AnaerobicPaceThreshold", "AerobicDuration", "AnaerobicDuration", "Vo2MaxDuration"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "Altitude", "VerticalSpeed", "Speed", "Temperature", "Epoc", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "Altitude", "VerticalSpeed", "Speed", "Temperature", "Epoc", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["Duration", "Type", "Distance", "DescentAltitude", "AvgVerticalSpeed", "AscentAltitude", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "AvgTemperature", "MaxTemperature"], "ManualLaps": ["Duration", "Distance", "DescentAltitude", "AvgVerticalSpeed", "AscentAltitude", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "DescentAltitude", "AvgVerticalSpeed", "AscentAltitude", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "DescentAltitude", "AvgVerticalSpeed", "AscentAltitude", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Walking", "NordicWalking"], "Items": ["Duration", "Distance", "Energy", "Steps", "Feeling", "AvgHeartRate", "TrainingStressScore", "RecoveryTime", "CO2EmissionsReduced", "AscentAltitude", "DescentAltitude", "HighAltitude", "LowAltitude", "AscentTime", "AvgAscentSpeed", "MaxAscentSpeed", "DescentTime", "TotalTime", "PauseTime", "MovingTime", "RestTime", "AvgSpeed", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "MoveType", "EstVO2peak", "Pte", "MaxHeartRate", "MinHeartRate", "AvgDescentSpeed", "MaxDescentSpeed", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "AvgCadence", "AvgTemperature", "AvgPace", "Max<PERSON>ace", "AvgPower", "PeakEpoc", "Climbs", "ClimbsCategory1", "ClimbsCategory2", "ClimbsCategory3", "ClimbsCategory4", "ClimbsCategoryHC", "ClimbAscentCategory1", "ClimbAscentCategory2", "ClimbAscentCategory3", "ClimbAscentCategory4", "ClimbAscentCategoryHC", "ClimbDistanceCategory1", "ClimbDistanceCategory2", "ClimbDistanceCategory3", "ClimbDistanceCategory4", "ClimbDistanceCategoryHC", "ClimbDurationCategory1", "ClimbDurationCategory2", "ClimbDurationCategory3", "ClimbDurationCategory4", "ClimbDurationCategoryHC", "FatConsumption", "CarbohydrateConsumption"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Pace", "Altitude", "Power", "VerticalSpeed", "Speed", "Temperature", "<PERSON><PERSON>", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Pace", "Altitude", "Power", "VerticalSpeed", "Speed", "Temperature", "<PERSON><PERSON>", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "Distance", "AvgPace", "AvgPower", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "ManualLaps": ["Duration", "Distance", "AvgPace", "AvgPower", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgPower", "AvgPace", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgPower", "AvgPace", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps", "Energy", "PeakEpoc", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Trekking", "Hiking"], "Items": ["Distance", "Duration", "TrainingStressScore", "RecoveryTime", "AvgHeartRate", "Feeling", "Energy", "FatConsumption", "CarbohydrateConsumption", "HighAltitude", "LowAltitude", "AscentAltitude", "AscentTime", "DescentAltitude", "DescentTime", "TotalTime", "PauseTime", "MovingTime", "RestTime", "AvgAscentSpeed", "AvgDescentSpeed", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "AvgSpeed", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "MoveType", "MaxHeartRate", "MinHeartRate", "Steps", "Pte", "PeakEpoc", "AvgPace", "AvgTemperature", "Climbs", "ClimbsCategory1", "ClimbsCategory2", "ClimbsCategory3", "ClimbsCategory4", "ClimbsCategoryHC", "ClimbAscentCategory1", "ClimbAscentCategory2", "ClimbAscentCategory3", "ClimbAscentCategory4", "ClimbAscentCategoryHC", "ClimbDistanceCategory1", "ClimbDistanceCategory2", "ClimbDistanceCategory3", "ClimbDistanceCategory4", "ClimbDistanceCategoryHC", "ClimbDurationCategory1", "ClimbDurationCategory2", "ClimbDurationCategory3", "ClimbDurationCategory4", "ClimbDurationCategoryHC", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Altitude", "Speed", "VerticalSpeed", "Temperature", "Pace", "Power", "<PERSON><PERSON>", "Epoc", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Altitude", "Speed", "VerticalSpeed", "Temperature", "Pace", "Power", "<PERSON><PERSON>", "Epoc", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "Distance", "AvgPace", "AvgPower", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "ManualLaps": ["Duration", "Distance", "AvgPace", "AvgPower", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgPower", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgPower", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps", "Energy", "PeakEpoc", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Fishing"], "Items": ["Duration", "Catch:Fish", "Distance", "HighAltitude", "AscentAltitude", "AvgAscentSpeed", "DescentAltitude", "AvgDescentSpeed", "TotalTime", "PauseTime", "MovingTime", "RestTime", "TrainingStressScore", "RecoveryTime", "Feeling", "Energy", "FatConsumption", "CarbohydrateConsumption", "AvgTemperature", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "AvgSpeed", "MoveType"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "Altitude", "SeaLevelPressure", "Speed", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "Altitude", "SeaLevelPressure", "Speed", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["Duration", "Type", "Distance", "AvgPace", "AvgPower", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "AvgCadence", "Steps"], "ManualLaps": ["Duration", "Distance", "AvgPace", "AvgPower", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "AvgCadence", "Steps"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgPower", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "AvgCadence", "Steps"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgPower", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "AvgCadence", "Steps", "Energy", "PeakEpoc", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Hunting"], "Items": ["Duration", "Distance", "Catch:<PERSON><PERSON><PERSON>", "Catch:<PERSON><PERSON>ame", "Catch:<PERSON>", "Catch:<PERSON><PERSON><PERSON>nt", "TotalTime", "PauseTime", "AscentAltitude", "AvgAscentSpeed", "DescentAltitude", "AvgDescentSpeed", "HighAltitude", "LowAltitude", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "AvgTemperature", "Energy", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "TrainingStressScore", "RecoveryTime", "Feeling", "MoveType", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "Climbs", "ClimbsCategory1", "ClimbsCategory2", "ClimbsCategory3", "ClimbsCategory4", "ClimbsCategoryHC", "ClimbAscentCategory1", "ClimbAscentCategory2", "ClimbAscentCategory3", "ClimbAscentCategory4", "ClimbAscentCategoryHC", "ClimbDistanceCategory1", "ClimbDistanceCategory2", "ClimbDistanceCategory3", "ClimbDistanceCategory4", "ClimbDistanceCategoryHC", "ClimbDurationCategory1", "ClimbDurationCategory2", "ClimbDurationCategory3", "ClimbDurationCategory4", "ClimbDurationCategoryHC"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "Altitude", "Speed", "Pace", "Temperature", "SeaLevelPressure", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "Altitude", "Speed", "Pace", "Temperature", "SeaLevelPressure", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["Duration", "Type", "Distance", "AvgPace", "AvgPower", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "AvgCadence", "Steps"], "ManualLaps": ["Duration", "Distance", "AvgPace", "AvgPower", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "AvgCadence", "Steps"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgPower", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "AvgCadence", "Steps"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgPower", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "AvgCadence", "Steps", "Energy", "PeakEpoc", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Cycling", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "E_Biking", "WheelChairing", "HandCycling", "Cyclocross"], "Items": ["Duration", "Distance", "AvgSpeed", "AvgPace", "AvgHeartRate", "AvgPower", "NormalizedPower", "AvgPowerWithZero", "Feeling", "Energy", "FatConsumption", "CarbohydrateConsumption", "TrainingStressScore", "RecoveryTime", "CO2EmissionsReduced", "Pte", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "AscentAltitude", "AscentTime", "AvgAscentSpeed", "MaxAscentSpeed", "DescentAltitude", "DescentTime", "AvgDescentSpeed", "MaxDescentSpeed", "HighAltitude", "LowAltitude", "PeakPower30S", "PeakPower1M", "PeakPower3M", "PeakPower5M", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "AvgCadence", "TotalTime", "PauseTime", "MovingTime", "RestTime", "MoveType", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "AvgTemperature", "MaxHeartRate", "MinHeartRate", "PeakEpoc", "Climbs", "ClimbsCategory1", "ClimbsCategory2", "ClimbsCategory3", "ClimbsCategory4", "ClimbsCategoryHC", "ClimbAscentCategory1", "ClimbAscentCategory2", "ClimbAscentCategory3", "ClimbAscentCategory4", "ClimbAscentCategoryHC", "ClimbDistanceCategory1", "ClimbDistanceCategory2", "ClimbDistanceCategory3", "ClimbDistanceCategory4", "ClimbDistanceCategoryHC", "ClimbDurationCategory1", "ClimbDurationCategory2", "ClimbDurationCategory3", "ClimbDurationCategory4", "ClimbDurationCategoryHC"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Power", "Speed", "Altitude", "VerticalSpeed", "<PERSON><PERSON>", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Power", "Speed", "Altitude", "VerticalSpeed", "<PERSON><PERSON>", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgSpeed", "Distance", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence"], "ManualLaps": ["Duration", "Distance", "AvgSpeed", "AvgHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgCadence"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgSpeed", "AvgHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgCadence"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgSpeed", "AvgHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgCadence"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["IndoorCycling"], "Items": ["Duration", "Distance", "AvgPower", "NormalizedPower", "AvgPowerWithZero", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "TrainingStressScore", "RecoveryTime", "Feeling", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "MoveType", "Energy", "FatConsumption", "CarbohydrateConsumption", "Pte", "AvgCadence", "MaxCadence", "PeakEpoc", "TotalTime", "PauseTime", "MovingTime", "RestTime", "AvgSpeed", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "PeakPower30S", "PeakPower1M", "PeakPower3M", "PeakPower5M", "AvgTemperature"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Power", "<PERSON><PERSON>", "Speed", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Power", "<PERSON><PERSON>", "Speed", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgSpeed", "Distance", "AvgCadence", "AvgTemperature", "MaxTemperature"], "ManualLaps": ["Duration", "Distance", "AvgSpeed", "AvgHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgTemperature", "MaxTemperature"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgSpeed", "AvgHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgTemperature", "MaxTemperature"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgSpeed", "AvgHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["MountainBiking", "E_MTB"], "Items": ["Duration", "AvgHeartRate", "AvgPace", "AvgSpeed", "MaxSpeed", "Distance", "MaxDownhillGrade", "SkiDistance", "SkiTime", "MaxSkiSpeed", "AvgSkiSpeed", "AvgPowerWithZero", "AvgPower", "NormalizedPower", "AvgCadence", "Feeling", "TrainingStressScore", "RecoveryTime", "MaxHeartRate", "MinHeartRate", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "Energy", "FatConsumption", "CarbohydrateConsumption", "PeakPower30S", "PeakPower1M", "PeakPower3M", "PeakPower5M", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "TotalTime", "PauseTime", "MovingTime", "RestTime", "AscentAltitude", "AscentTime", "AvgAscentSpeed", "DescentAltitude", "DescentTime", "AvgDescentSpeed", "HighAltitude", "LowAltitude", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "AvgTemperature", "Pte", "PeakEpoc", "MoveType", "Climbs", "ClimbsCategory1", "ClimbsCategory2", "ClimbsCategory3", "ClimbsCategory4", "ClimbsCategoryHC", "ClimbAscentCategory1", "ClimbAscentCategory2", "ClimbAscentCategory3", "ClimbAscentCategory4", "ClimbAscentCategoryHC", "ClimbDistanceCategory1", "ClimbDistanceCategory2", "ClimbDistanceCategory3", "ClimbDistanceCategory4", "ClimbDistanceCategoryHC", "ClimbDurationCategory1", "ClimbDurationCategory2", "ClimbDurationCategory3", "ClimbDurationCategory4", "ClimbDurationCategoryHC"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "Speed", "Altitude", "VerticalSpeed", "Power", "<PERSON><PERSON>", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Speed", "Altitude", "VerticalSpeed", "Power", "<PERSON><PERSON>", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "AvgHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgPower", "Max<PERSON><PERSON><PERSON>", "MinHeartRate", "MaxHeartRate", "AvgSpeed", "Distance", "AvgCadence", "AvgTemperature", "MaxTemperature"], "ManualLaps": ["Duration", "Distance", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgSpeed", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgTemperature", "MaxTemperature"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgSpeed", "MaxHeartRate", "MinHeartRate", "AvgCadence", "AvgTemperature", "MaxTemperature"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgSpeed", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgTemperature", "MaxTemperature"], "DownhillLaps": ["Duration", "DescentAltitude", "Distance", "AvgSpeed", "AscentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgTemperature", "MaxTemperature"], "DiveAutoLaps": []}, {"Activities": ["Swimming"], "Items": ["Duration", "SwimDistance", "AvgSwimPace", "AvgSwimStrokeRate", "SwimStrokeDistance", "SwimStrokeCount", "AvgSWOLF", "TrainingStressScore", "RecoveryTime", "Feeling", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "Energy", "FatConsumption", "CarbohydrateConsumption", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "MovingTime", "RestTime", "TotalTime", "PauseTime", "Pte", "AvgTemperature", "PeakEpoc", "MoveType", "BreaststrokeDuration", "BreaststrokePercent", "AvgBreaststrokeBreathAngle", "BreaststrokeHeadAngle", "MaxBreaststrokeBreathAngle", "BreaststrokeGlideTime", "FreestyleDuration", "FreestylePercent", "AvgFreestyleBreathAngle", "FreestylePitchAngle", "MaxFreestyleBreathAngle", "BreathingRate"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "SwimPace", "SwimStrokeRate", "<PERSON>wolf", "Temperature", "Epoc", "RecoveryHRInThreeMins", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "BreathingRate", "BreaststrokeGlideTime", "AvgBreaststrokeBreathAngle", "BreaststrokeHeadAngle", "AvgFreestyleBreathAngle", "FreestylePitchAngle"], "AnalysisGraphs": ["Heartrate", "SwimPace", "SwimStrokeRate", "<PERSON>wolf", "Temperature", "Epoc", "RecoveryHRInThreeMins", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "BreathingRate", "BreaststrokeGlideTime", "AvgBreaststrokeBreathAngle", "BreaststrokeHeadAngle", "AvgFreestyleBreathAngle", "FreestylePitchAngle"], "IntervalLaps": ["SwimDistance", "SwimStyle", "Duration", "AvgSwimPace", "AvgSwimStrokeRate", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "AvgSWOLF", "BreathingRate", "BreaststrokeGlideTime", "AvgFreestyleBreathAngle", "AvgBreaststrokeBreathAngle", "FreestylePitchAngle", "BreaststrokeHeadAngle"], "ManualLaps": ["Duration", "SwimDistance", "AvgSwimPace", "AvgSwimStrokeRate", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "AvgSWOLF", "AvgFreestyleBreathAngle", "AvgBreaststrokeBreathAngle", "BreaststrokeGlideTime", "BreaststrokeHeadAngle"], "DistanceAutoLaps": ["CumulatedSwimDistance", "Duration", "AvgSwimPace", "AvgSwimStrokeRate", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "AvgSWOLF", "AvgFreestyleBreathAngle", "AvgBreaststrokeBreathAngle", "BreaststrokeGlideTime", "BreaststrokeHeadAngle"], "DurationAutoLaps": ["CumulatedDuration", "SwimDistance", "AvgSwimPace", "AvgSwimStrokeRate", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "AvgSWOLF", "AvgFreestyleBreathAngle", "AvgBreaststrokeBreathAngle", "BreaststrokeGlideTime", "BreaststrokeHeadAngle"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Sailing"], "Items": ["Duration", "TotalTime", "PauseTime", "NauticalDistance", "AvgNauticalSpeed", "MaxNauticalSpeed", "Energy", "FatConsumption", "CarbohydrateConsumption", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "TrainingStressScore", "RecoveryTime", "Pte", "AvgTemperature", "MaxTemperature", "PeakEpoc", "Feeling", "MoveType", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "SpeedKnots", "Temperature", "SeaLevelPressure", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "SpeedKnots", "Temperature", "SeaLevelPressure", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["Duration", "Type", "NauticalDistance", "AvgNauticalSpeed", "AvgHeartRate", "MaxHeartRate", "MinHeartRate"], "ManualLaps": ["Duration", "NauticalDistance", "AvgNauticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "AvgSeaLevelPressure"], "DistanceAutoLaps": ["NauticalDistance", "Duration", "AvgNauticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "AvgSeaLevelPressure"], "DurationAutoLaps": ["CumulatedDuration", "NauticalDistance", "AvgNauticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "AvgSeaLevelPressure"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["OpenwaterSwimming"], "Items": ["Duration", "AvgSwimPace", "AvgSwimStrokeRate", "SwimDistance", "SwimStrokeCount", "SwimStrokeDistance", "AvgDistancePerStroke", "AvgHeartRate", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "TrainingStressScore", "RecoveryTime", "Feeling", "Energy", "FatConsumption", "CarbohydrateConsumption", "MoveType", "TotalTime", "PauseTime", "AvgSpeed", "MaxSpeed", "MaxHeartRate", "MinHeartRate", "Pte", "AvgTemperature", "PeakEpoc", "BreaststrokeDuration", "BreaststrokePercent", "AvgBreaststrokeBreathAngle", "BreaststrokeHeadAngle", "MaxBreaststrokeBreathAngle", "BreaststrokeGlideTime", "FreestyleDuration", "FreestylePercent", "AvgFreestyleBreathAngle", "FreestylePitchAngle", "MaxFreestyleBreathAngle", "BreathingRate"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "SwimPace", "SwimStrokeRate", "Speed", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "SwimPace", "SwimStrokeRate", "Speed", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["SwimDistance", "Type", "Duration", "AvgSwimPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "AvgSwimStrokeRate"], "ManualLaps": ["Duration", "SwimDistance", "AvgSwimPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "AvgSwimStrokeRate"], "DistanceAutoLaps": ["CumulatedSwimDistance", "Duration", "AvgSwimPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "AvgSwimStrokeRate"], "DurationAutoLaps": ["CumulatedDuration", "SwimDistance", "AvgSwimPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "AvgSwimStrokeRate"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["StandupPaddling", "Surfing", "Canoeing", "IceSkating"], "Items": ["Duration", "Distance", "AvgSpeed", "AvgHeartRate", "TrainingStressScore", "RecoveryTime", "Energy", "FatConsumption", "CarbohydrateConsumption", "Feeling", "MoveType", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "AvgCadence", "TotalTime", "PauseTime", "MovingTime", "RestTime", "MaxHeartRate", "MinHeartRate", "Pte", "AvgTemperature", "PeakEpoc"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "Speed", "Epoc", "<PERSON><PERSON>", "SeaLevelPressure", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "Speed", "Epoc", "<PERSON><PERSON>", "SeaLevelPressure", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["Distance", "Type", "Duration", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "ManualLaps": ["Duration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["KitesurfingKiting", "Windsurfing"], "Items": ["Duration", "Distance", "AvgSpeed", "MaxSpeed", "NauticalDistance", "AvgNauticalSpeed", "MaxNauticalSpeed", "Energy", "TrainingStressScore", "FatConsumption", "CarbohydrateConsumption", "RecoveryTime", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "TotalTime", "PauseTime", "MovingTime", "RestTime", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "HRAerobicThreshold", "HRAnaerobicThreshold", "Pte", "AvgTemperature", "PeakEpoc", "Feeling", "MoveType"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "SpeedKnots", "<PERSON><PERSON>", "SeaLevelPressure", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "SpeedKnots", "<PERSON><PERSON>", "SeaLevelPressure", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["Distance", "NauticalDistance", "Type", "Duration", "AvgSpeed", "AvgNauticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "ManualLaps": ["Duration", "Distance", "AvgSpeed", "NauticalDistance", "AvgNauticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "DistanceAutoLaps": ["Distance", "NauticalDistance", "Duration", "AvgSpeed", "AvgNauticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "NauticalDistance", "AvgSpeed", "AvgNauticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Rowing"], "Items": ["Duration", "Distance", "AvgSpeed", "MaxSpeed", "AvgHeartRate", "MaxHeartRate", "RecoveryTime", "Energy", "FatConsumption", "CarbohydrateConsumption", "Feeling", "MoveType", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "TotalTime", "PauseTime", "MovingTime", "RestTime", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "TrainingStressScore", "AvgCadence", "Pte", "AvgTemperature", "PeakEpoc", "AerobicPowerThreshold", "AnaerobicPowerThreshold", "AerobicPaceThreshold", "AnaerobicPaceThreshold", "AerobicDuration", "AnaerobicDuration", "Vo2MaxDuration"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "Speed", "<PERSON><PERSON>", "Epoc", "SeaLevelPressure", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "Speed", "<PERSON><PERSON>", "Epoc", "SeaLevelPressure", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["Distance", "Type", "Duration", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "ManualLaps": ["Duration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["SnowShoeing"], "Items": ["Duration", "Distance", "AscentAltitude", "AscentTime", "AvgAscentSpeed", "DescentAltitude", "DescentTime", "AvgDescentSpeed", "MaxAscentSpeed", "MaxDescentSpeed", "HighAltitude", "LowAltitude", "TotalTime", "PauseTime", "SkiRunCount", "SkiTime", "SkiDistance", "AvgSkiSpeed", "MaxSkiSpeed", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "Energy", "TrainingStressScore", "RecoveryTime", "Feeling", "MovingTime", "RestTime", "MoveType", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "AvgSpeed", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "AvgPace", "Max<PERSON>ace", "AvgCadence", "FatConsumption", "CarbohydrateConsumption", "Pte", "PeakEpoc", "EstVO2peak", "Steps", "AvgTemperature", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "AerobicPowerThreshold", "AnaerobicPowerThreshold", "AerobicPaceThreshold", "AnaerobicPaceThreshold", "AerobicDuration", "AnaerobicDuration", "Vo2MaxDuration", "Climbs", "ClimbsCategory1", "ClimbsCategory2", "ClimbsCategory3", "ClimbsCategory4", "ClimbsCategoryHC", "ClimbAscentCategory1", "ClimbAscentCategory2", "ClimbAscentCategory3", "ClimbAscentCategory4", "ClimbAscentCategoryHC", "ClimbDistanceCategory1", "ClimbDistanceCategory2", "ClimbDistanceCategory3", "ClimbDistanceCategory4", "ClimbDistanceCategoryHC", "ClimbDurationCategory1", "ClimbDurationCategory2", "ClimbDurationCategory3", "ClimbDurationCategory4", "ClimbDurationCategoryHC"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "Altitude", "VerticalSpeed", "Speed", "<PERSON><PERSON>", "Pace", "Temperature", "Epoc", "RecoveryHRInThreeMins", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "Altitude", "VerticalSpeed", "Speed", "<PERSON><PERSON>", "Pace", "Temperature", "Epoc", "RecoveryHRInThreeMins", "AerobicZone", "AerobicHrThresholds"], "IntervalLaps": ["Distance", "Type", "Duration", "AvgSpeed", "AvgPace", "AvgCadence", "AscentAltitude", "DescentAltitude", "DescentTime", "AvgVerticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "ManualLaps": ["Duration", "Distance", "AvgSpeed", "MaxSpeed", "AvgPace", "AvgCadence", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "MaxSpeed", "AvgSpeed", "AvgPace", "AvgCadence", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "MaxSpeed", "AvgSpeed", "AvgPace", "AvgCadence", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["DownhillSkiing", "Snowboarding", "TelemarkSkiing", "BackcountrySkiing", "SplitBoarding", "SkiMountaineering"], "Items": ["SkiTime", "SkiDistance", "AvgSkiSpeed", "SkiRunCount", "MaxSkiSpeed", "Duration", "AvgPace", "AvgSpeed", "MaxSpeed", "DownhillDescent", "HighAltitude", "LowAltitude", "AscentAltitude", "AscentTime", "DescentAltitude", "DescentTime", "MaxDownhillGrade", "AvgAscentSpeed", "AvgDescentSpeed", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "Distance", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "Steps", "AvgStepCadence", "MaxStepCadence", "AvgCadence", "MaxCadence", "TrainingStressScore", "RecoveryTime", "Feeling", "Pte", "Energy", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "FatConsumption", "CarbohydrateConsumption", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "EstVO2peak", "PeakEpoc", "TotalTime", "PauseTime", "MovingTime", "RestTime", "MoveType", "AvgTemperature"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Altitude", "Speed", "VerticalSpeed", "<PERSON><PERSON>", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Altitude", "Speed", "VerticalSpeed", "<PERSON><PERSON>", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Distance", "Type", "Duration", "AvgSpeed", "MaxSpeed", "DescentAltitude", "DescentTime", "AvgVerticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgTemperature", "MaxTemperature", "Energy"], "ManualLaps": ["Duration", "Distance", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "MaxSpeed", "AvgSpeed", "AvgTemperature", "MaxTemperature", "Energy"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "MaxSpeed", "AvgSpeed", "AvgTemperature", "MaxTemperature", "Energy"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "MaxSpeed", "AvgSpeed", "AvgTemperature", "MaxTemperature", "Energy"], "DownhillLaps": ["Duration", "Distance", "DescentAltitude", "AvgVerticalSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "MaxSpeed", "AvgSpeed", "AvgTemperature", "MaxTemperature", "Energy"], "DiveAutoLaps": []}, {"Activities": ["Crosstrainer"], "Items": ["Distance", "Duration", "RevolutionCount", "TrainingStressScore", "RecoveryTime", "AvgHeartRate", "MaxHeartRate", "AvgCadence", "AvgSpeed", "Energy", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "FatConsumption", "CarbohydrateConsumption", "Pte", "AvgTemperature", "PeakEpoc", "Feeling", "MoveType"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Speed", "<PERSON><PERSON>", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "Speed", "<PERSON><PERSON>", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["Distance", "Type", "Duration", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgSpeed", "Energy", "AvgTemperature", "MaxTemperature", "RevolutionCount"], "ManualLaps": ["Duration", "Distance", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgSpeed", "Energy", "AvgTemperature", "MaxTemperature", "RevolutionCount"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgSpeed", "Energy", "AvgTemperature", "MaxTemperature", "RevolutionCount"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgSpeed", "Energy", "AvgTemperature", "MaxTemperature", "RevolutionCount"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["IndoorRowing"], "Items": ["Distance", "Duration", "RowingStrokeCount", "AvgSwimStrokeRate", "AvgPace", "AvgCadence", "AvgSpeed", "AvgHeartRate", "MaxHeartRate", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "TrainingStressScore", "RecoveryTime", "Energy", "FatConsumption", "CarbohydrateConsumption", "Pte", "AvgTemperature", "PeakEpoc", "Feeling", "MoveType"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Speed", "<PERSON><PERSON>", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Speed", "<PERSON><PERSON>", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Distance", "Type", "Duration", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgSpeed", "Energy", "AvgTemperature", "MaxTemperature", "RowingStrokeCount"], "ManualLaps": ["Duration", "Distance", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgSpeed", "Energy", "AvgTemperature", "MaxTemperature", "RowingStrokeCount"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgSpeed", "Energy", "AvgTemperature", "MaxTemperature", "RowingStrokeCount"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgCadence", "AvgSpeed", "Energy", "AvgTemperature", "MaxTemperature", "RowingStrokeCount"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["TrackAndField"], "Items": ["Duration", "Distance", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "AvgSpeed", "MaxSpeed", "RecoveryTime", "Energy", "FatConsumption", "CarbohydrateConsumption", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "TotalTime", "PauseTime", "MovingTime", "RestTime", "HRAerobicThreshold", "HRAnaerobicThreshold", "TrainingStressScore", "Pte", "PeakEpoc", "MoveType", "AvgTemperature", "Feeling", "Steps"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "Speed", "Pace", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Speed", "Pace", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Distance", "AvgSpeed", "Type", "Duration", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "Energy", "AvgTemperature", "MaxTemperature"], "ManualLaps": ["Duration", "AvgSpeed", "Distance", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "Energy", "AvgTemperature", "MaxTemperature"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgSpeed", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "Energy", "AvgTemperature", "MaxTemperature"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Aerobics", "Yoga", "CircuitTraining", "Stretching", "Gym", "Cheerleading", "CombatSport", "Boxing", "Bowling", "Dancing", "Gymnastics", "<PERSON><PERSON><PERSON>", "Crossfit", "Calisthenics", "Meditation", "Pilates", "NewYoga", "Fu<PERSON>al", "Skateboarding", "<PERSON>our"], "Items": ["Duration", "AvgHeartRate", "Feeling", "RecoveryTime", "Energy", "FatConsumption", "CarbohydrateConsumption", "TotalTime", "PauseTime", "HRAerobicThreshold", "HRAnaerobicThreshold", "MaxHeartRate", "MinHeartRate", "TrainingStressScore", "Pte", "AvgTemperature", "PeakEpoc", "MoveType", "Steps", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "AerobicPowerThreshold", "AnaerobicPowerThreshold", "AerobicPaceThreshold", "AnaerobicPaceThreshold", "AerobicDuration", "AnaerobicDuration", "Vo2MaxDuration"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Distance", "Type", "Duration", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature"], "ManualLaps": ["Duration", "Distance", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "Energy", "AvgTemperature", "MaxTemperature"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "Energy", "AvgTemperature", "MaxTemperature"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Softball", "Floorball", "Handball", "Basketball", "Soccer", "IceHockey", "Volleyball", "AmericanFootball", "Baseball", "Rugby", "FieldHockey"], "Items": ["Duration", "TotalTime", "PauseTime", "RecoveryTime", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "Energy", "TrainingStressScore", "FatConsumption", "CarbohydrateConsumption", "Pte", "AvgTemperature", "PeakEpoc", "Feeling", "MoveType", "Steps", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["Distance", "Type", "Duration", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature", "Steps"], "ManualLaps": ["Duration", "Distance", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature", "Steps"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "Energy", "AvgTemperature", "MaxTemperature", "Steps"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature", "Steps"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Tennis", "Bad<PERSON>ton", "TableTennis", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Squash", "Cricket", "Pa<PERSON>"], "Items": ["Duration", "TotalTime", "PauseTime", "AvgHeartRate", "HRAerobicThreshold", "HRAnaerobicThreshold", "MaxHeartRate", "MinHeartRate", "Energy", "TrainingStressScore", "RecoveryTime", "FatConsumption", "CarbohydrateConsumption", "Pte", "AvgTemperature", "PeakEpoc", "Feeling", "MoveType", "Steps"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "Energy", "AvgTemperature", "MaxTemperature", "Steps"], "ManualLaps": ["Duration", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature", "Steps"], "DistanceAutoLaps": ["CumulatedDistance", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature", "Steps"], "DurationAutoLaps": ["CumulatedDuration", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature", "Steps"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Golf", "Frisbee"], "Items": ["Duration", "TotalTime", "PauseTime", "MovingTime", "RestTime", "Distance", "Energy", "TrainingStressScore", "RecoveryTime", "Pte", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "AvgTemperature", "HRAerobicThreshold", "HRAnaerobicThreshold", "PeakEpoc", "AvgSpeed", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "Feeling", "MoveType", "Steps", "AscentAltitude", "AscentTime", "AvgAscentSpeed", "DescentAltitude", "DescentTime", "AvgDescentSpeed", "HighAltitude", "LowAltitude", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "AerobicPowerThreshold", "AnaerobicPowerThreshold", "AerobicPaceThreshold", "AnaerobicPaceThreshold", "AerobicDuration", "AnaerobicDuration", "Vo2MaxDuration"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "Pace", "Altitude", "Speed", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "Pace", "Altitude", "Speed", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["Duration", "Type", "Distance", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature", "Steps"], "ManualLaps": ["Duration", "Distance", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature", "Steps"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature", "Steps"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "Energy", "AvgTemperature", "MaxTemperature", "Steps"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["ScubaDiving"], "Items": ["DiveTime", "<PERSON><PERSON><PERSON><PERSON>", "AvgTemperature", "DiveMaxDepthTemperature", "A<PERSON>g<PERSON><PERSON><PERSON>", "DiveSurfaceTime", "DiveGases", "DiveGasPressure", "DiveGasEndPressure", "DiveGasUsedPressure", "GasConsumption", "Algorithm", "AlgorithmLock", "GradientFactors", "AltitudeSetting", "DiveCNS", "DiveOTU", "DiveNumberInSeries", "DiveMode", "Personal", "DiveVisibility", "Feeling", "TrainingStressScore", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "AerobicPowerThreshold", "AnaerobicPowerThreshold", "AerobicPaceThreshold", "AnaerobicPaceThreshold", "AerobicDuration", "AnaerobicDuration", "Vo2MaxDuration"], "ZoneGraphs": [], "Graphs": ["Heartrate", "De<PERSON><PERSON>", "Temperature", "GasConsumption", "TankPressure", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "De<PERSON><PERSON>", "Temperature", "GasConsumption", "TankPressure", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": [], "ManualLaps": [], "DistanceAutoLaps": [], "DurationAutoLaps": [], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["FreeDiving"], "Items": ["Duration", "TotalTime", "PauseTime", "DiveInWorkout", "DiveSurfaceTime", "DiveTime", "A<PERSON>g<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "AvgTemperature", "DiveTimeMax", "DiveMaxDepthTemperature", "Distance", "DiveNumberInSeries", "Feeling", "HRAerobicThreshold", "HRAnaerobicThreshold", "TrainingStressScore"], "ZoneGraphs": ["Heartrate"], "Graphs": ["De<PERSON><PERSON>", "Heartrate", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["De<PERSON><PERSON>", "Heartrate", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["DiveTime", "DiveSurfaceTime", "<PERSON><PERSON><PERSON><PERSON>"], "ManualLaps": [], "DistanceAutoLaps": [], "DurationAutoLaps": [], "DownhillLaps": [], "DiveAutoLaps": ["<PERSON><PERSON><PERSON><PERSON>", "DiveTime", "DiveRecoveryTime", "CumulatedDuration", "AvgTemperature", "MaxTemperature"]}, {"Activities": ["Snorkeling"], "Items": ["Duration", "TotalTime", "PauseTime", "SwimDistance", "DiveTime", "DiveTimeMax", "DiveRecoveryTime", "DiveInWorkout", "<PERSON><PERSON><PERSON><PERSON>", "A<PERSON>g<PERSON><PERSON><PERSON>", "Energy", "Pte", "PeakEpoc", "AvgSwimPace", "TrainingStressScore", "RecoveryTime", "Feeling", "HRAerobicThreshold", "HRAnaerobicThreshold"], "ZoneGraphs": ["Heartrate"], "Graphs": ["De<PERSON><PERSON>", "Temperature", "Heartrate", "Speed", "Pace", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["De<PERSON><PERSON>", "Temperature", "Heartrate", "Speed", "Pace", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["<PERSON><PERSON><PERSON><PERSON>", "DiveSurfaceTime", "CumulatedDuration"], "ManualLaps": [], "DistanceAutoLaps": [], "DurationAutoLaps": [], "DownhillLaps": [], "DiveAutoLaps": ["<PERSON><PERSON><PERSON><PERSON>", "DiveTime", "DiveRecoveryTime", "CumulatedDuration", "AvgTemperature", "MaxTemperature"]}, {"Activities": ["Mermaiding"], "Items": ["Duration", "TotalTime", "PauseTime", "SwimDistance", "DiveTime", "DiveTimeMax", "DiveRecoveryTime", "DiveInWorkout", "<PERSON><PERSON><PERSON><PERSON>", "A<PERSON>g<PERSON><PERSON><PERSON>", "AvgHeartRate", "Energy", "Pte", "PeakEpoc", "TrainingStressScore", "RecoveryTime", "Feeling", "HRAerobicThreshold", "HRAnaerobicThreshold"], "ZoneGraphs": ["Heartrate"], "Graphs": ["De<PERSON><PERSON>", "Temperature", "Heartrate", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["De<PERSON><PERSON>", "Temperature", "Heartrate", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["<PERSON><PERSON><PERSON><PERSON>", "DiveSurfaceTime", "CumulatedDuration"], "ManualLaps": [], "DistanceAutoLaps": [], "DurationAutoLaps": [], "DownhillLaps": [], "DiveAutoLaps": ["<PERSON><PERSON><PERSON><PERSON>", "DiveTime", "DiveRecoveryTime", "CumulatedDuration", "AvgTemperature", "MaxTemperature"]}, {"Activities": ["UnspecifiedSport"], "Items": ["Duration", "TotalTime", "PauseTime", "MovingTime", "RestTime", "Distance", "AvgPace", "NormalizedGradedPace", "Max<PERSON>ace", "AvgPower", "AvgSpeed", "MaxSpeed", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "TrainingStressScore", "Energy", "FatConsumption", "CarbohydrateConsumption", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "RecoveryTime", "Pte", "AvgTemperature", "PeakEpoc", "AscentAltitude", "AscentTime", "AvgAscentSpeed", "DescentAltitude", "DescentTime", "AvgDescentSpeed", "HighAltitude", "LowAltitude", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "Feeling", "MoveType", "Steps", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "AerobicPowerThreshold", "AnaerobicPowerThreshold", "AerobicPaceThreshold", "AnaerobicPaceThreshold", "AerobicDuration", "AnaerobicDuration", "Vo2MaxDuration", "Climbs", "ClimbsCategory1", "ClimbsCategory2", "ClimbsCategory3", "ClimbsCategory4", "ClimbsCategoryHC", "ClimbAscentCategory1", "ClimbAscentCategory2", "ClimbAscentCategory3", "ClimbAscentCategory4", "ClimbAscentCategoryHC", "ClimbDistanceCategory1", "ClimbDistanceCategory2", "ClimbDistanceCategory3", "ClimbDistanceCategory4", "ClimbDistanceCategoryHC", "ClimbDurationCategory1", "ClimbDurationCategory2", "ClimbDurationCategory3", "ClimbDurationCategory4", "ClimbDurationCategoryHC"], "ZoneGraphs": ["Heartrate"], "Graphs": ["Heartrate", "Altitude", "Pace", "Speed", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Altitude", "Pace", "Speed", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "ManualLaps": ["Duration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps", "Energy", "PeakEpoc", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Transition"], "Items": ["Duration", "TotalTime", "PauseTime", "HRAerobicThreshold", "HRAnaerobicThreshold"], "ZoneGraphs": [], "Graphs": [], "AnalysisGraphs": [], "IntervalLaps": [], "ManualLaps": [], "DistanceAutoLaps": [], "DurationAutoLaps": [], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["Fallback"], "Items": ["Duration", "TotalTime", "PauseTime", "MovingTime", "RestTime", "Distance", "AvgSpeed", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "AvgHeartRate", "MaxHeartRate", "MinHeartRate", "Energy", "TrainingStressScore", "RecoveryTime", "Pte", "AvgTemperature", "PeakEpoc", "AscentAltitude", "AscentTime", "AvgAscentSpeed", "MaxAscentSpeed", "DescentAltitude", "DescentTime", "AvgDescentSpeed", "MaxDescentSpeed", "HighAltitude", "LowAltitude", "PeakVerticalSpeed30S", "PeakVerticalSpeed1M", "PeakVerticalSpeed3M", "PeakVerticalSpeed5M", "Feeling", "MoveType", "Steps", "HRAerobicThreshold", "HRAnaerobicThreshold", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "AerobicPowerThreshold", "AnaerobicPowerThreshold", "AerobicPaceThreshold", "AnaerobicPaceThreshold", "AerobicDuration", "AnaerobicDuration", "Vo2MaxDuration", "FatConsumption", "CarbohydrateConsumption"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Altitude", "Power", "Pace", "Speed", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Altitude", "Power", "Pace", "Speed", "Epoc", "Temperature", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "ManualLaps": ["Duration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgSpeed", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AscentAltitude", "DescentAltitude", "AvgVerticalSpeed", "AvgCadence", "Steps", "Energy", "PeakEpoc", "AvgTemperature", "MaxTemperature"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["JumpRope"], "Items": ["SkipCount", "Duration", "TotalTime", "PauseTime", "AvgHeartRate", "MaxHeartRate", "RecoveryTime", "Energy", "FatConsumption", "CarbohydrateConsumption", "Feeling", "HRAerobicThreshold", "HRAnaerobicThreshold", "TrainingStressScore", "Pte", "AvgTemperature", "PeakEpoc", "MoveType", "Steps", "Rounds", "AvgSkipsRate", "MaxAvgSkipsRate", "AvgSkipsPerRound", "MaxConsecutiveSkips", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "AerobicPowerThreshold", "AnaerobicPowerThreshold", "AerobicPaceThreshold", "AnaerobicPaceThreshold", "AerobicDuration", "AnaerobicDuration", "Vo2MaxDuration"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Temperature", "Speed", "Epoc", "AvgSkipsPerRound", "AvgSkipsRate", "RecoveryHRInThreeMins", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "AnalysisGraphs": ["Heartrate", "Temperature", "Speed", "Epoc", "AvgSkipsPerRound", "AvgSkipsRate", "RecoveryHRInThreeMins", "AerobicZone", "AerobicHrThresholds", "AerobicPowerThresholds"], "IntervalLaps": ["Type", "SkipCount", "AvgTemperature", "MaxTemperature", "MaxHeartRate", "AvgHeartRate", "MinHeartRate", "AvgSkipsRate", "Distance", "Duration", "Energy", "AvgSpeed"], "ManualLaps": ["SkipCount", "AvgTemperature", "MaxTemperature", "MaxHeartRate", "AvgHeartRate", "MinHeartRate", "AvgSkipsRate", "Energy", "Duration", "Distance", "AvgSpeed"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgSpeed", "SkipCount", "AvgTemperature", "MaxTemperature", "MaxHeartRate", "AvgHeartRate", "MinHeartRate", "AvgSkipsRate", "Energy"], "DurationAutoLaps": ["SkipCount", "CumulatedDuration", "Distance", "AvgTemperature", "MaxTemperature", "MaxHeartRate", "AvgHeartRate", "MinHeartRate", "Energy", "AvgSpeed", "AvgSkipsRate"], "DownhillLaps": [], "DiveAutoLaps": []}, {"Activities": ["TrackRunning"], "Items": ["Duration", "Distance", "AvgPace", "Max<PERSON>ace", "NormalizedGradedPace", "AvgHeartRate", "MaxHeartRate", "HRAerobicThreshold", "HRAnaerobicThreshold", "RecoveryTime", "Feeling", "AvgPower", "NormalizedPower", "TrainingStressScore", "Energy", "FatConsumption", "CarbohydrateConsumption", "ZoneSenseBaseline", "ZoneSenseCumulativeBaseline", "EstVO2peak", "MinHeartRate", "Pte", "Steps", "AvgStepCadence", "MaxStepCadence", "AvgCadence", "MaxCadence", "AvgStepLength", "AvgStrideLength", "AvgGroundContactTime", "AvgVerticalOscillation", "AvgGroundContactBalance", "TotalTime", "PauseTime", "MovingTime", "RestTime", "MovingPace", "PeakPace30S", "PeakPace1M", "PeakPace3M", "PeakPace5M", "PeakPower30S", "PeakPower1M", "PeakPower3M", "PeakPower5M", "AvgSpeed", "MaxSpeed", "PeakSpeed30S", "PeakSpeed1M", "PeakSpeed3M", "PeakSpeed5M", "MoveType", "PerformanceLevel", "PeakEpoc", "AvgTemperature"], "ZoneGraphs": ["Heartrate", "RecoveryHRInThreeMins"], "Graphs": ["Heartrate", "Pace", "Power", "Speed", "<PERSON><PERSON>", "Temperature", "VerticalOscillation", "GroundContactTime", "RecoveryHRInThreeMins"], "AnalysisGraphs": ["Heartrate", "Pace", "Power", "Speed", "<PERSON><PERSON>", "Temperature", "VerticalOscillation", "GroundContactTime", "RecoveryHRInThreeMins"], "IntervalLaps": ["Duration", "Type", "Distance", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgCadence", "AvgVerticalOscillation", "Steps", "AvgGroundContactTime"], "ManualLaps": ["Duration", "Distance", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgCadence", "AvgVerticalOscillation", "Steps", "AvgGroundContactTime"], "DistanceAutoLaps": ["CumulatedDistance", "Duration", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgCadence", "AvgVerticalOscillation", "AvgGroundContactTime"], "DurationAutoLaps": ["CumulatedDuration", "Distance", "AvgPace", "AvgHeartRate", "MinHeartRate", "MaxHeartRate", "AvgPower", "Max<PERSON><PERSON><PERSON>", "AvgCadence", "Steps", "Energy", "AvgTemperature", "MaxTemperature", "AvgVerticalOscillation", "AvgGroundContactTime"], "DownhillLaps": [], "DiveAutoLaps": []}]