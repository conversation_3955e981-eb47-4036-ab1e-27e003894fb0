package com.stt.android.data.marketing

import com.stt.android.remote.BrandForAsko
import com.stt.android.remote.marketing.MarketingInboxRemoteApi
import javax.inject.Inject

class MarketingInboxRemoteDataSource @Inject constructor(
    private val marketingInboxRemoteApi: MarketingInboxRemoteApi,
    @BrandForAsko private val brand: String,
) {
    suspend fun getMarketingInboxes() =
        marketingInboxRemoteApi.getMarketingInboxes(brand.uppercase())

    suspend fun updateTag(messageId: String, tag: String) =
        marketingInboxRemoteApi.updateTag(messageId, brand.uppercase(), tag)
}
