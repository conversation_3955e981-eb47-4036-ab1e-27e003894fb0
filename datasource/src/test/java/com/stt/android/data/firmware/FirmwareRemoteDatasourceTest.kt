package com.stt.android.data.firmware

import com.stt.android.remote.firmware.FirmwareRestApi
import io.reactivex.Single
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class FirmwareRemoteDatasourceTest {

    @Mock
    lateinit var firmwareRestApi: FirmwareRestApi

    private lateinit var firmwareRemoteDataSource: FirmwareRemoteDataSource

    @Before
    fun setup() {
        firmwareRemoteDataSource = FirmwareRemoteDataSource(firmwareRestApi)
    }

    @Test
    fun `should access firmware remote api when fetching firmware`() {
        // Prepare
        whenever(firmwareRestApi.fetchFirmware(any(), any(), anyOrNull()))
            .thenReturn(Single.just(FirmwareEntityFactory.fakeRemoteWatchFirmwareInfo()))

        // Verify
        firmwareRemoteDataSource.fetchFirmware(
            variant = "A",
            hwCompatibilityIdentifier = "B",
            isOtaUpdateFirmware = false,
        )
            .test()
            .assertComplete()

        verify(firmwareRestApi).fetchFirmware("A", "B", null)
    }
}
