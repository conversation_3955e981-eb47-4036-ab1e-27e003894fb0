package com.stt.android.data.badges

import com.stt.android.remote.badges.UserBadgeApi
import com.stt.android.remote.response.AskoResponse
import timber.log.Timber
import javax.inject.Inject

class BadgesRemoteDataSource @Inject constructor(
    private val api: UserBadgeApi,
) {
    suspend fun getUserBadgeDetail(badgeConfigId: String?): UserBadge {
        val response = api.getUserBadgeDetail(badgeConfigId)
        return response.payloadOrThrow().toDomain()
    }

    suspend fun getBadgeConfigs(module: String?): List<BadgeModuleConfig> {
        val response = api.getBadgeConfigs(module)
        return response.payloadOrThrow().map { it.toDomain() }
    }

    suspend fun getUserBadgeList(): UserBadgeList {
        val response = api.getUserBadgeList()
        return response.payloadOrThrow().toDomain()
    }

    suspend fun getFriendBadgesList(userName: String): UserBadgeList {
        val response = api.getFriendBadgesList(userName)
        return response.payloadOrThrow().toDomain()
    }

    suspend fun getMoreBadgesInDetail(badgeConfigId: String?): List<ExploreMore> {
        val response = api.getUserBadgeDetailOtherBadges(badgeConfigId)
        return response.payloadOrEmptyList().toDomainList()
    }

    suspend fun deleteRedPointOfBadge(): BadgesNotification {
        val response = api.deleteRedPoint()
        return response.payloadOrThrow().toDomain()
    }
}

fun <T> AskoResponse<List<T>>.payloadOrEmptyList(): List<T> {
    return try {
        this.payloadNullableOrThrow() ?: emptyList()
    } catch (e: Exception) {
        Timber.w(e, "Error from BadgesRemoteDataSource")
        emptyList()
    }
}
