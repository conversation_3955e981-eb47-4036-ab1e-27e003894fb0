package com.stt.android.data.badges

data class UserBadge(
    val badgeIconUrl: String?,
    val acquiredBadgeIconUrl: String?,
    val badgeBackgroundImageUrl: String?,
    val badgeType: BadgeType?,
    val badgeName: String?,
    val badgeDesc: String?,
    val startTime: Long?,
    val endTime: Long?,
    val acquisitionWayType: String?,
    val acquisitionCondition: BadgeAcquisitionCondition?,
    val acquisitionTime: Long?,
    val participationTime: Long?,
    val acquisitionRanking: Int?,
    val transcriptDataFields: List<BadgesFields>?,
    val maxSpeed: Float?,
    val maxDistance: Double?,
    val totalDistance: Double?,
    val activitySessions: Int?,
    val totalDuration: Double?,
    val energy: Int?,
    val totalWorkoutDays: Int?,
    val totalAscent: Int?,
    val maxPace: Double?,
    val maxCyclingSpeed: Float?,
    val maxDivingDepth: Double?,
    val maxDuration: Double?,
    val activityIds: List<Int>?,
    val totalUTMBDistance: Double?,
    val createTime: Long?,
    val badgeStatus: BadgeStatus?,
)

data class ExploreMore(
    val badgeConfigId: String?,
    val badgeType: BadgeType?,
    val badgeName: String?,
    val badgeIconUrl: String?,
    val acquiredBadgeIconUrl: String?,
    val badgeBackgroundImageUrl: String?,
    val badgeDesc: String?,
    val acquisitionCondition: BadgeAcquisitionCondition?,
    val relatedActivityIds: List<Int>?,
    val startTime: Long?,
    val endTime: Long?,
)

data class BadgeModuleConfig(
    val moduleName: String,
    val badgeConfigs: List<BadgeConfigSimple>,
)

data class BadgeConfigSimple(
    val badgeConfigId: String,
    val badgeType: BadgeType?,
    val badgeName: String?,
    val badgeIconUrl: String?,
    val acquiredBadgeIconUrl: String?,
    val badgeBackgroundImageUrl: String?,
    val badgeDesc: String?,
    val acquisitionCondition: BadgeAcquisitionCondition?,
    val relatedActivityIds: List<Int>?,
    val startTime: Long?,
    val endTime: Long?,
)

data class UserBadgeList(
    val userHasWonBadges: List<UserBadgeModule>?,
    val hasNewBadge: Boolean?,
)

data class UserBadgeModule(
    val moduleName: String,
    val userBadges: List<UserBadgeSimple>,
)

data class UserBadgeSimple(
    val badgeName: String,
    val badgeId: String,
    val badgeConfigId: String,
    val acquisitionTime: Long,
    val badgeIconUrl: String,
    val acquiredBadgeIconUrl: String?,
    val badgeBackgroundImageUrl: String?,
)

enum class BadgeType {
    ACTIVITY_TYPE,
    TOP_ACTIVITY,
    CITY_TRACK,
    ANNUAL_ACHIEVEMENT,
    SUUNTO_MILESTONE,
    LIMITED_EDITION,
    ONLINE_CHALLENGE,
}

data class BadgeAcquisitionCondition(
    val type: BadgeConditionType?,
    val logicalOperator: BadgeLogicalOperator?,
    val conditionName: BadgeConditionName?,
    val conditionalOperator: BadgeConditionalOperator?,
    val targetVal: String?,
    val childConditions: List<BadgeAcquisitionCondition>?,
)

data class BadgesNotification(
    val isHaveNewBadge: Boolean
)

enum class BadgeConditionType { LEAF, COMPOSITE }
enum class BadgeLogicalOperator { AND, OR }
enum class BadgeConditionName { DISTANCE, DURATION, ASCENT, ENERGY, WORKOUT_NUMBER, TOTAL_UTMB_DISTANCE }
enum class BadgeConditionalOperator { GREATER, LESS, EQUAL, GREATER_EQUAL, LESS_EQUAL }
enum class BadgeStatus { NOT_STARTED, IN_PROGRESS, ACQUIRED }

enum class BadgesFields { TOTAL_ACTIVITIES, TOTAL_CALORIES, TOTAL_WORKOUT_DAYS, TOTAL_ASCENT, TOTAL_WORKOUT_DURATION, MAX_PACE, MAX_DISTANCE, MAX_CYCLING_SPEED, MAX_DIVING_DEPTH, MAX_SPEED, MAX_DURATION, TOTAL_DISTANCE }
