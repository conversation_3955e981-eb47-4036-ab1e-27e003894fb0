package com.stt.android.data.sleep

import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.sleep.SleepRepository
import com.stt.android.domain.sleep.SleepSegment
import com.stt.android.domain.sleep.SleepStageInterval
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.toEpochMilli
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import javax.inject.Inject
import kotlin.time.Duration
import kotlin.time.Duration.Companion.hours

@Module
@InstallIn(SingletonComponent::class)
abstract class SleepRepositoryModule {

    @Binds
    abstract fun bindSleepRepositoryImpl(
        repositoryImpl: SleepRepositoryImpl
    ): SleepRepository
}

/**
 * Encapsulates the persistence of [Sleep] entities in the data source.
 */
class SleepRepositoryImpl
@Inject constructor(
    private val sleepLocalDataSource: RoomSleepLocalDataSource
) : SleepRepository {
    override fun fetchSleeps(
        from: LocalDate,
        to: LocalDate,
    ): Flow<List<Sleep>> {
        // For long sleeps, the valid range should be from noon of previous day, till noon of last day.
        // For naps, the valid range should be from start of the first day, till end of the last day.
        val fromTimestamp = from.minusDays(1L).atTime(12, 0).toEpochMilli()
        val toTimestamp = to.atEndOfDay().toEpochMilli()

        val startOfFirstDay = fromTimestamp + 12.hours.inWholeMilliseconds
        val noonOfLastDay = toTimestamp - 12.hours.inWholeMilliseconds
        return sleepLocalDataSource.fetchSleeps(
            fromTimestamp = fromTimestamp,
            toTimestamp = toTimestamp,
        ).map { sleeps ->
            sleeps.mapNotNull { sleep ->
                sleep.copy(
                    // If long sleep starts after noon of last day, remove it.
                    longSleep = sleep.longSleep
                        ?.takeUnless { longSleep -> longSleep.fellAsleep > noonOfLastDay },
                    // If nap ends before start of first day, remove it.
                    // If nap starts at the day before the first day, but ends after the start of first day,
                    // it should belong to the first day.
                    naps = sleep.naps
                        .filterNot { nap -> nap.wokeUp < startOfFirstDay },
                ).takeIf { it.totalSleepDuration > Duration.ZERO }
            }.groupBy {
                it.timestamp
            }.flatMap { (_, sleepGroup) ->
                when (sleepGroup.size) {
                    1 -> sleepGroup

                    else -> {
                        // Merge long sleep with naps on the same day
                        val longSleeps = sleepGroup.filter { it.hasLongSleep }
                        if (longSleeps.size == 1) {
                            listOf(longSleeps.first().copy(naps = sleepGroup.flatMap { it.naps }))
                        } else {
                            sleepGroup
                        }
                    }
                }
            }.sortedBy {
                it.timestamp
            }
        }
    }

    /**
     * Save a list of [SleepSegment] entities in the local data source
     * @param sleepList list of [SleepSegment] entities to save in the data source
     * @param requireBackendSync true if data needs to be synced to backend in the future
     * @return If any SleepSegments were stored in the local database. Can be false if the
     * list is empty or all of Segments had been inserted already.
     */
    override suspend fun saveSleep(
        sleepList: List<SleepSegment>,
        requireBackendSync: Boolean
    ): Boolean =
        sleepLocalDataSource.saveSleep(sleepList, requireBackendSync)

    override suspend fun saveSleepStages(
        sleepStageList: List<SleepStageInterval>,
        replaceConflicts: Boolean,
        requireBackendSync: Boolean
    ): Boolean =
        sleepLocalDataSource.saveSleepStages(sleepStageList, replaceConflicts, requireBackendSync)

    override suspend fun fetchSleepDataExists(maxDaysLoaded: Int): Boolean =
        sleepLocalDataSource.sleepDataExists(maxDaysLoaded)

    override suspend fun fetchOldestSleepTimestamp(): Long? =
        sleepLocalDataSource.oldestDataTimestamp()

    override suspend fun fetchNumDaysWithSleepData(checkLimit: Int): Int =
        sleepLocalDataSource.fetchNumDaysWithSleepData(checkLimit)

    override fun fetchSleepStagesForDateRange(
        fromTimestamp: Long,
        toTimestamp: Long,
    ): Flow<List<SleepStageInterval>> = sleepLocalDataSource.fetchSleepStagesForDateRange(
        fromTimestamp = fromTimestamp,
        toTimestamp = toTimestamp,
    )
}
