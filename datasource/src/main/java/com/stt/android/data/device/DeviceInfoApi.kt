package com.stt.android.data.device

import com.stt.android.domain.device.ConnectedWatchState
import com.stt.android.domain.device.DeviceInfoWear
import io.reactivex.Flowable
import io.reactivex.Single
import kotlinx.coroutines.flow.Flow

interface DeviceInfoApi {
    suspend fun macAddress(): String

    suspend fun variant(): String

    suspend fun version(): String

    fun isConnected(): Flow<Boolean>

    fun isSyncing(): Flow<Boolean>

    fun isBusy(): Flow<Boolean>

    fun connectedWatchState(): Flowable<ConnectedWatchState>

    suspend fun versionHash(): String

    suspend fun serial(): String

    fun sku(): Single<String>

    suspend fun wearInfo(): DeviceInfoWear

    suspend fun otaUpdateSupported(): Boolean
}
