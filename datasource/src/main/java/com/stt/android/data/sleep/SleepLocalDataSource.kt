package com.stt.android.data.sleep

import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.sleep.SleepSegment
import com.stt.android.domain.sleep.SleepStageInterval
import com.stt.android.domain.sleep.SleepTrackingMode
import kotlinx.coroutines.flow.Flow
import java.time.ZonedDateTime

interface SleepLocalDataSource {

    /**
     * Get list of [SleepSegment] entities that require sync to backend.
     * @return List of [SleepSegment] ordered by timestamp
     */
    suspend fun fetchSleepSegmentsForBackendSync(): List<SleepSegment>

    /**
     * Save a list of [SleepSegment] entities in the data source
     * @param sleepList list of [SleepSegment] entities to save in the data source
     * @param requireBackendSync true if data comes from watch, false if it comes from backend
     * @return If any rows were inserted or not.
     */
    suspend fun saveSleep(
        sleepList: List<SleepSegment>,
        requireBackendSync: Boolean
    ): Boolean

    /**
     * Save a list of [SleepStageInterval] entities in the data source
     * @param sleepStages list of [SleepStageInterval] entities to save in the data source
     * @param replaceConflicts true if data needs to be replaced in case of conflict
     * @param requireBackendSync true if data comes from watch, false if it comes from backend
     * @return If any rows were inserted or not. With [replaceConflicts] false this can be used
     * check if all of the SleepSegments were stored previously
     */
    suspend fun saveSleepStages(
        sleepStages: List<SleepStageInterval>,
        replaceConflicts: Boolean,
        requireBackendSync: Boolean
    ): Boolean

    /**
     * Get [Flow] that emits a list of [Sleep] entities for the given time range.
     * The returned [Sleep] objects can be incomplete if the query endpoints are in middle
     * of the sleep period.
     *
     * @param fromTimestamp UTC timestamp in ms of the start time to query from
     * @param toTimestamp UTC timestamp in ms of the end time to query to
     * @return Flow that emits a list of [Sleep] entities whenever data changes
     */
    fun fetchSleeps(
        fromTimestamp: Long,
        toTimestamp: Long,
    ): Flow<List<Sleep>>

    /**
     * Get list of [SleepStageInterval] entities that require sync to backend.
     * @return List of [SleepStageInterval] ordered by timestamp
     */
    suspend fun fetchSleepStagesForBackendSync(): List<SleepStageInterval>

    suspend fun fetchSleepTrackingMode(): SleepTrackingMode

    suspend fun saveSleepTrackingMode(sleepTrackingMode: SleepTrackingMode)

    suspend fun sleepDataExists(maxDaysLoaded: Int): Boolean

    suspend fun oldestDataTimestamp(): Long?

    suspend fun getLatestSleepSyncedTimestamp(): ZonedDateTime?

    suspend fun getLatestSleepStagesSyncedTimestamp(): ZonedDateTime?

    /**
     * @param checkLimit - Stop the count at threshold value. If user has data for longer periods
     *   of time and we just want to see if some threshold is met, using this optimizes the
     *   fetching to stop at the threshold value and not go through all of the data.
     */
    suspend fun fetchNumDaysWithSleepData(checkLimit: Int = Int.MAX_VALUE): Int

    /**
     * Get [Flow] that emits a list of [SleepStageInterval] entities for the given date range.
     *
     * @param fromTimestamp UTC timestamp in ms of the start of the day to query from
     * @param toTimestamp UTC timestamp in ms of the start of the day to query to
     * @return Flow that emits a list of [SleepStageInterval] entities whenever data changes
     */
    fun fetchSleepStagesForDateRange(
        fromTimestamp: Long,
        toTimestamp: Long
    ): Flow<List<SleepStageInterval>>
}
