package com.stt.android.data.sleep

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.activitydata.ActivityDataSharedPrefStorage
import com.stt.android.data.source.local.sleep.LocalSleepSegment
import com.stt.android.data.source.local.sleep.LocalSleepStageInterval
import com.stt.android.data.source.local.sleep.SleepSegmentDao
import com.stt.android.data.source.local.sleep.SleepStagesDao
import com.stt.android.data.source.local.trenddata.TrendDataDao
import com.stt.android.data.toEpochMilli
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.sleep.SleepSegment
import com.stt.android.domain.sleep.SleepStageInterval
import com.stt.android.domain.sleep.SleepTrackingMode
import com.stt.android.domain.sleep.aggregateToSleepNights
import com.stt.android.utils.distinctBy
import com.suunto.algorithms.data.HeartRate
import com.suunto.algorithms.data.HeartRate.Companion.hz
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import timber.log.Timber
import java.time.Duration
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.roundToInt

class RoomSleepLocalDataSource
@Inject constructor(
    private val sleepDao: SleepSegmentDao,
    private val sleepStagesDao: SleepStagesDao,
    private val trendDataDao: TrendDataDao,
    private val sleepSegmentLocalMapper: SleepSegmentLocalMapper,
    private val sleepStagesLocalMapper: SleepStagesLocalMapper,
    private val activityDataSharedPrefStorage: ActivityDataSharedPrefStorage
) : SleepLocalDataSource {

    override suspend fun fetchSleepSegmentsForBackendSync(): List<SleepSegment> =
        sleepSegmentLocalMapper.toDomainEntityList()(
            sleepDao.fetchSleepSegmentsByStatus(LocalSleepSegment.STATUS_NOT_SYNCED)
        )

    override fun fetchSleeps(
        fromTimestamp: Long,
        toTimestamp: Long,
    ): Flow<List<Sleep>> {
        val fromTimestampSeconds = TimeUnit.MILLISECONDS.toSeconds(fromTimestamp)
        val toTimestampSeconds = TimeUnit.MILLISECONDS.toSeconds(toTimestamp)
        Timber.v(
            "fetchSleepForDateRange: %s - %s",
            Instant.ofEpochSecond(fromTimestampSeconds),
            Instant.ofEpochSecond(toTimestampSeconds),
        )

        return sleepDao.fetchSleepSegments(fromTimestampSeconds, toTimestampSeconds)
            .map(sleepSegmentLocalMapper.toDomainEntityList())
            .map { it.aggregateToSleepNights() }
            .aggregateWithHrIfMissing()
    }

    override suspend fun fetchSleepStagesForBackendSync(): List<SleepStageInterval> =
        sleepStagesLocalMapper.toDomainEntityList()(
            sleepStagesDao.fetchSleepStagesWithSyncedStatus(LocalSleepSegment.STATUS_NOT_SYNCED)
        )

    override suspend fun saveSleep(
        sleepList: List<SleepSegment>,
        requireBackendSync: Boolean
    ): Boolean {
        val syncedStatus = if (requireBackendSync) {
            LocalSleepSegment.STATUS_NOT_SYNCED
        } else {
            LocalSleepSegment.STATUS_SYNCED
        }
        /*
        Sleep segments conflict resolution is a bit complicated due to Gomore algorithm
        potentially generating duplicate segments with same startTimestamp.
        1. First we try to remove duplicates from `sleepList` and insert them with a single DB
        transaction.
        2. If insert fails, we process segments one by one and replace conflicts with the segment
        with longest duration
         */
        val distinctLocalSleepList = sleepList
            .distinctBy(
                comparator = { s1, s2 -> s1.duration.compareTo(s2.duration) },
                selector = { it.timestamp }
            ).let {
                sleepSegmentLocalMapper.toDataEntityList(syncedStatus)(it)
            }
        var success = false
        runSuspendCatching {
            sleepDao.insertSleepSegments(distinctLocalSleepList)
        }.onSuccess {
            success = true
        }.onFailure {
            Timber.i(
                it,
                "Error inserting batch of sleep segments, probably duplicate sleep segment"
            )

            // batch failed, processing 1 by 1
            for (segment in distinctLocalSleepList) {
                runSuspendCatching {
                    sleepDao.insertSleepSegment(segment)
                }.recoverCatching {
                    // possible conflict
                    val previousDuration =
                        sleepDao.fetchSleepSegment(segment.timestampSeconds)?.durationSeconds ?: 0f
                    // duration of new segment is longer, we replace existing one
                    if (segment.durationSeconds >= previousDuration) {
                        sleepDao.replaceSleepSegment(segment)
                    }
                }.onFailure {
                    Timber.w(it, "Error inserting sleep segment: $segment")
                }.onSuccess {
                    success = true
                }
            }
        }

        return success
    }

    override suspend fun saveSleepStages(
        sleepStages: List<SleepStageInterval>,
        replaceConflicts: Boolean,
        requireBackendSync: Boolean
    ): Boolean {
        val syncedStatus = if (requireBackendSync) {
            LocalSleepStageInterval.STATUS_NOT_SYNCED
        } else {
            LocalSleepStageInterval.STATUS_SYNCED
        }
        val localSleepStages = sleepStagesLocalMapper.toDataEntityList(syncedStatus)(sleepStages)
        return if (replaceConflicts) {
            sleepStagesDao.insertSleepStages(localSleepStages)
            localSleepStages.isNotEmpty()
        } else {
            val rowIds = sleepStagesDao.insertSleepStagesSafe(localSleepStages)
            rowIds.any { it != -1L }
        }
    }

    override suspend fun fetchSleepTrackingMode(): SleepTrackingMode =
        sleepTrackingModeFromDbValue(activityDataSharedPrefStorage.fetchSleepTrackingMode())

    override suspend fun saveSleepTrackingMode(sleepTrackingMode: SleepTrackingMode) {
        activityDataSharedPrefStorage.saveSleepTrackingMode(sleepTrackingMode.dbValue)
    }

    /**
     * We are checking for a timestamp here because the diary loads a certain amount of days
     * together at most, so it is possible some data is there but it is too old and it would not
     * be shown anyway.
     * fixme in general diary should have a smarter logic when fetching data
     */
    override suspend fun sleepDataExists(maxDaysLoaded: Int): Boolean {
        val untilTimestamp = ZonedDateTime.of(LocalDateTime.now(), ZoneOffset.systemDefault())
            .minusDays(maxDaysLoaded.toLong()).truncatedTo(ChronoUnit.DAYS).toEpochMilli()
        val oldestTimestamp = TimeUnit.MILLISECONDS.toSeconds(untilTimestamp)
        return sleepDao.sleepDataCount(oldestTimestamp) > 0
    }

    override suspend fun oldestDataTimestamp(): Long? = sleepDao.fetchOldestTimestamp()
        ?.let { TimeUnit.SECONDS.toMillis(it) }

    override suspend fun getLatestSleepSyncedTimestamp(): ZonedDateTime? =
        sleepDao.fetchLatestSynced()?.timeISO8601

    override suspend fun getLatestSleepStagesSyncedTimestamp(): ZonedDateTime? =
        sleepStagesDao.fetchLatestSynced()?.timeISO8601

    private val HeartRate.isValid: Boolean
        get() = inBpm.roundToInt() > 0

    /**
     * This is needed because S7 doesn't include minHR and avgHR in sleep data
     */
    private fun Flow<List<Sleep>>.aggregateWithHrIfMissing(): Flow<List<Sleep>> = map { sleeps ->
        sleeps.map sleepMap@{ sleep ->
            val longSleep = sleep.longSleep?.let {
                it.copy(
                    minHr = if (it.minHr?.isValid == true) it.minHr else null,
                    avgHr = if (it.avgHr?.isValid == true) it.avgHr else null,
                )
            }
            if (longSleep == null || !sleep.hasLongSleep) {
                // if there is no long sleep, we don't need to check if we have HR data
                return@sleepMap sleep
            }
            if (longSleep.minHr != null && longSleep.avgHr != null) {
                // we already have the data, no need to aggregate it with activity data
                return@sleepMap sleep
            }

            // sleep doesn't contain HR, we try to fetch it from trend data table
            val sleepWithHr = trendDataDao.fetchTrendDataBetween(
                fromTimestampSeconds = Duration.ofMillis(longSleep.fellAsleep).seconds,
                toTimestampSeconds = Duration.ofMillis(longSleep.wokeUp).seconds,
            ).map { trendDataList ->
                val hrs = trendDataList.mapNotNull { it.heartrate }.filter { it > 0 }
                val hrsMin = listOfNotNull(
                    trendDataList.mapNotNull { it.hrMin }.minOrNull(),
                    hrs.minOrNull()
                ).minOrNull()
                if (hrsMin == null && hrs.isEmpty()) {
                    sleep
                } else {
                    sleep.copy(
                        longSleep = longSleep.copy(
                            minHr = longSleep.minHr ?: hrsMin?.hz,
                            avgHr = longSleep.avgHr ?: hrs.average().hz,
                        )
                    )
                }
            }.first()

            sleepWithHr
        }
    }

    override suspend fun fetchNumDaysWithSleepData(checkLimit: Int): Int =
        sleepDao.fetchNumDaysWithSleepData(checkLimit)

    override fun fetchSleepStagesForDateRange(
        fromTimestamp: Long,
        toTimestamp: Long,
    ): Flow<List<SleepStageInterval>> = sleepStagesDao
        .flowSleepStagesBetween(fromTimestamp, toTimestamp)
        .map(sleepStagesLocalMapper.toDomainEntityList())

    companion object {
        private val SleepTrackingMode.dbValue: Int get() = when (this) {
            SleepTrackingMode.OFF -> 0
            SleepTrackingMode.AUTO -> 1
            SleepTrackingMode.MANUAL -> 2
        }

        private fun sleepTrackingModeFromDbValue(dbValue: Int): SleepTrackingMode = when (dbValue) {
            0 -> SleepTrackingMode.OFF
            1 -> SleepTrackingMode.AUTO
            2 -> SleepTrackingMode.MANUAL
            else -> throw IllegalArgumentException("Can't convert '$dbValue' to SleepTrackingMode")
        }
    }
}
