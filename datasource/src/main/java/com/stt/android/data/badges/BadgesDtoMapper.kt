package com.stt.android.data.badges

import com.stt.android.remote.badges.BadgeAcquisitionConditionRemote
import com.stt.android.remote.badges.BadgeConfigSimpleRemote
import com.stt.android.remote.badges.BadgeModuleConfigRemote
import com.stt.android.remote.badges.BadgesNotificationDeleteRemote
import com.stt.android.remote.badges.ExploreMorePayload
import com.stt.android.remote.badges.UserBadgeDetailPayloadRemote
import com.stt.android.remote.badges.UserBadgeListPayloadRemote
import com.stt.android.remote.badges.UserBadgeModuleRemote
import com.stt.android.remote.badges.UserBadgeSimpleRemote

fun BadgeModuleConfigRemote.toDomain() = BadgeModuleConfig(
    moduleName = moduleName,
    badgeConfigs = badgeConfigs.map { it.toDomain() }
)

fun BadgeAcquisitionConditionRemote.toDomain(): BadgeAcquisitionCondition {
    return BadgeAcquisitionCondition(
        type = type?.let { runCatching { BadgeConditionType.valueOf(it) }.getOrNull() },
        logicalOperator = logicalOperator?.let { runCatching { BadgeLogicalOperator.valueOf(it) }.getOrNull() },
        conditionName = conditionName.let { runCatching { BadgeConditionName.valueOf(it) }.getOrNull() },
        conditionalOperator = conditionalOperator?.let {
            runCatching {
                BadgeConditionalOperator.valueOf(
                    it
                )
            }.getOrNull()
        },
        targetVal = targetVal,
        childConditions = childConditions?.map { it.toDomain() }
    )
}

fun BadgeConfigSimpleRemote.toDomain() = BadgeConfigSimple(
    badgeConfigId = badgeConfigId,
    badgeType = BadgeType.valueOf(badgeType),
    badgeName = badgeName,
    badgeIconUrl = badgeIconUrl,
    acquiredBadgeIconUrl = acquiredBadgeIconUrl,
    badgeBackgroundImageUrl = badgeBackgroundImageUrl,
    badgeDesc = badgeDesc,
    acquisitionCondition = acquisitionCondition?.toDomain(),
    relatedActivityIds = relatedActivityIds,
    startTime = startTime,
    endTime = endTime
)

fun UserBadgeListPayloadRemote.toDomain() = UserBadgeList(
    userHasWonBadges = userHasWonBadges?.map { it.toDomain() },
    hasNewBadge = hasNewBadge
)

fun UserBadgeModuleRemote.toDomain() = UserBadgeModule(
    moduleName = moduleName,
    userBadges = userBadges.map { it.toDomain() }
)

fun UserBadgeSimpleRemote.toDomain() = UserBadgeSimple(
    badgeName = badgeName,
    badgeId = badgeId,
    badgeConfigId = badgeConfigId,
    acquisitionTime = acquisitionTime,
    badgeIconUrl = badgeIconUrl,
    acquiredBadgeIconUrl = acquiredBadgeIconUrl,
    badgeBackgroundImageUrl = badgeBackgroundImageUrl
)

fun UserBadgeDetailPayloadRemote.toDomain() = UserBadge(
    badgeIconUrl = badgeIconUrl,
    acquiredBadgeIconUrl = acquiredBadgeIconUrl,
    badgeBackgroundImageUrl = badgeBackgroundImageUrl,
    badgeType = badgeType?.let { BadgeType.valueOf(it) },
    badgeName = badgeName,
    badgeDesc = badgeDesc,
    startTime = startTime,
    endTime = endTime,
    acquisitionWayType = acquisitionWayType,
    acquisitionCondition = acquisitionCondition?.toDomain(),
    acquisitionTime = acquisitionTime,
    participationTime = participationTime,
    acquisitionRanking = acquisitionRanking,
    transcriptDataFields = transcriptDataFields?.mapNotNull { fieldName ->
        runCatching { BadgesFields.valueOf(fieldName) }.getOrNull()
    },
    maxSpeed = maxSpeed,
    maxDistance = maxDistance,
    totalDistance = totalDistance,
    activitySessions = activitySessions,
    totalDuration = totalDuration,
    energy = energy,
    totalWorkoutDays = totalWorkoutDays,
    totalAscent = totalAscent,
    maxPace = maxPace,
    maxCyclingSpeed = maxCyclingSpeed,
    maxDivingDepth = maxDivingDepth,
    maxDuration = maxDuration,
    activityIds = activityIds,
    totalUTMBDistance = totalUTMBDistance,
    createTime = createTime,
    badgeStatus = badgeStatus?.let { BadgeStatus.valueOf(it) },
)

fun ExploreMorePayload.toDomain() = ExploreMore(
    badgeConfigId = badgeConfigId,
    badgeType = badgeType?.let { BadgeType.valueOf(it) },
    badgeName = badgeName,
    badgeIconUrl = badgeIconUrl,
    acquiredBadgeIconUrl = acquiredBadgeIconUrl,
    badgeBackgroundImageUrl = badgeBackgroundImageUrl,
    badgeDesc = badgeDesc,
    acquisitionCondition = acquisitionCondition?.toDomain(),
    relatedActivityIds = relatedActivityIds,
    startTime = startTime,
    endTime = endTime
)

fun BadgesNotificationDeleteRemote.toDomain(): BadgesNotification = BadgesNotification(
    isHaveNewBadge = deleteRedPoint
)

fun List<ExploreMorePayload>.toDomainList(): List<ExploreMore> = this.map { it.toDomain() }
