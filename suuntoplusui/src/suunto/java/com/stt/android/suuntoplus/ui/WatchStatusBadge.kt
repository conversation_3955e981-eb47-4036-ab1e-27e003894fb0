package com.stt.android.suuntoplus.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Card
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.attention
import com.stt.android.compose.theme.confirmation

enum class WatchStatus {
    INSTALLED,
    INSTALLING,
    INCOMPATIBLE,
    ERROR
}

@Composable
fun WatchStatusBadge(
    status: WatchStatus,
    modifier: Modifier = Modifier,
    badgeSize: Dp = SuuntoPlusIconSizeDefaults.watchStatusBadgeSize,
    iconSize: Dp = SuuntoPlusIconSizeDefaults.watchStatusBadgeIconSize,
    elevation: Dp = SuuntoPlusIconSizeDefaults.watchStatusBadgeElevation
) {
    Card(
        modifier = modifier,
        shape = CircleShape,
        elevation = elevation
    ) {
        Box {
            if (status == WatchStatus.INSTALLING) {
                CircularProgressIndicator(
                    modifier = Modifier
                        .size(badgeSize)
                        .clip(CircleShape)
                        .background(MaterialTheme.colors.primary)
                        .wrapContentSize()
                        .size(iconSize),
                    color = MaterialTheme.colors.onPrimary,
                    strokeWidth = 2.dp
                )
            } else {
                val badgeColor: Color
                val badgeDrawableRes: Int

                @Suppress("KotlinConstantConditions")
                when (status) {
                    WatchStatus.INSTALLED -> {
                        badgeColor = MaterialTheme.colors.confirmation
                        badgeDrawableRes = R.drawable.installed_in_watch
                    }

                    WatchStatus.INCOMPATIBLE -> {
                        badgeColor = MaterialTheme.colors.attention
                        badgeDrawableRes = R.drawable.not_connected_fill
                    }

                    WatchStatus.ERROR -> {
                        badgeColor = MaterialTheme.colors.error
                        badgeDrawableRes = R.drawable.sync_failed_fill
                    }

                    WatchStatus.INSTALLING -> throw NotImplementedError("Never used")
                }

                Icon(
                    modifier = Modifier
                        .size(badgeSize)
                        .clip(CircleShape)
                        .background(badgeColor)
                        .wrapContentSize()
                        .size(iconSize),
                    painter = painterResource(id = badgeDrawableRes),
                    contentDescription = null,
                    tint = Color.Unspecified,
                )
            }
        }
    }
}

@Preview
@Composable
private fun WatchStatusBadgePreview(
    @PreviewParameter(WatchStatusPreviewParameterProvider::class) status: WatchStatus
) {
    AppTheme {
        WatchStatusBadge(status)
    }
}

private class WatchStatusPreviewParameterProvider : PreviewParameterProvider<WatchStatus> {
    override val values = WatchStatus.entries.asSequence()
}
