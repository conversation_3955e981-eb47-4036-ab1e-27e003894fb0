package com.stt.android.remote.sportmodes

import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import retrofit2.Response

@RunWith(MockitoJUnitRunner::class)
class SportModesRemoteApiTest {
    @Mock
    private lateinit var sportModesRestApi: SportModesRestApi
    private lateinit var sportModesRemoteApi: SportModesRemoteApi

    @Before
    fun setup() {
        sportModesRemoteApi = SportModesRemoteApi(sportModesRestApi)
    }

    @Test
    fun `fetch sport mode bundle should make a call to sport modes rest`() = runTest {
        // prepare
        `when`(sportModesRestApi.fetchSportModeComponent("hash", "etag"))
            .thenReturn(Response.success(null))

        sportModesRemoteApi.fetchSportModeBundle("hash", "etag")

        verify(sportModesRestApi).fetchSportModeComponent("hash", "etag")
    }
}
