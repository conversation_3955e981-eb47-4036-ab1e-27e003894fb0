package com.stt.android.remote.marketing

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class RemoteMarketingInboxMessage(
    @<PERSON>son(name = "id") val id: String,
    @<PERSON><PERSON>(name = "name") val name: String,
    @<PERSON><PERSON>(name = "title") val title: String,
    @Json(name = "description") val description: String,
    @<PERSON><PERSON>(name = "imageUrl") val imageUrl: String? = null,
    @<PERSON><PERSON>(name = "tags") val tags: List<String> = emptyList(),
    @<PERSON><PERSON>(name = "receivedAt") val receivedAt: Long,
    @<PERSON><PERSON>(name = "expiresAt") val expiresAt: Long,
    @<PERSON><PERSON>(name = "properties") val properties: Map<String, Any>? = emptyMap(),
    @<PERSON><PERSON>(name = "action") val action: RemoteAction? = null,
) {
    @JsonClass(generateAdapter = true)
    data class RemoteAction(
        @Json(name = "title") val title: String,
        @J<PERSON>(name = "linkType") val linkType: RemoteLinkType?,
        @Json(name = "link") val link: String,
    )
}
