package com.stt.android.remote.marketing

import com.stt.android.remote.response.AskoResponse
import retrofit2.http.GET
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface MarketingInboxRestApi {
    @GET("marketing/api/inboxes")
    suspend fun getMarketingInboxes(
        @Query("brand") brand: String,
    ): AskoResponse<List<RemoteMarketingInboxMessage>>

    @PUT("marketing/api/inbox/updatetag/{messageId}")
    suspend fun updateTag(
        @Path("messageId") messageId: String,
        @Query("brand") brand: String,
        @Query("tag") tag: String,
    ): AskoResponse<Boolean>
}
