package com.stt.android.remote.marketing

import javax.inject.Inject

class MarketingInboxRemoteApi @Inject constructor(
    private val marketingInboxRestApi: MarketingInboxRestApi
) {
    suspend fun getMarketingInboxes(brand: String) =
        marketingInboxRestApi.getMarketingInboxes(brand).payloadOrThrow()

    suspend fun updateTag(messageId: String, brand: String, tag: String) =
        marketingInboxRestApi.updateTag(messageId, brand, tag).payloadOrThrow()
}
