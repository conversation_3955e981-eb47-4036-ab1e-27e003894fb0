package com.stt.android.remote.badges

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class BadgeModuleConfigRemote(
    @Json(name = "moduleName")
    val moduleName: String,
    @Json(name = "badgeConfigs")
    val badgeConfigs: List<BadgeConfigSimpleRemote>
)

@JsonClass(generateAdapter = true)
data class BadgeConfigSimpleRemote(
    @<PERSON><PERSON>(name = "badgeConfigId")
    val badgeConfigId: String,
    @<PERSON><PERSON>(name = "badgeType")
    val badgeType: String,
    @<PERSON><PERSON>(name = "badgeName")
    val badgeName: String?,
    @<PERSON><PERSON>(name = "badgeIconUrl")
    val badgeIconUrl: String?,
    @<PERSON><PERSON>(name = "acquiredBadgeIconUrl")
    val acquiredBadgeIconUrl: String?,
    @<PERSON><PERSON>(name = "badgeBackgroundImageUrl")
    val badgeBackgroundImageUrl: String?,
    @Json(name = "badgeDesc")
    val badgeDesc: String?,
    @Json(name = "acquisitionCondition")
    val acquisitionCondition: BadgeAcquisitionConditionRemote?,
    @<PERSON><PERSON>(name = "relatedActivityIds")
    val relatedActivityIds: List<Int>?,
    @Json(name = "startTime")
    val startTime: Long?,
    @Json(name = "endTime")
    val endTime: Long?
)

@JsonClass(generateAdapter = true)
data class BadgeAcquisitionConditionRemote(
    @Json(name = "type")
    val type: String?,
    @Json(name = "logicalOperator")
    val logicalOperator: String?,
    @Json(name = "conditionName")
    val conditionName: String,
    @Json(name = "conditionalOperator")
    val conditionalOperator: String?,
    @Json(name = "targetVal")
    val targetVal: String?,
    @Json(name = "childConditions")
    val childConditions: List<BadgeAcquisitionConditionRemote>?,
)

@JsonClass(generateAdapter = true)
data class ExploreMorePayload(
    @Json(name = "badgeConfigId")
    val badgeConfigId: String?,
    @Json(name = "badgeType")
    val badgeType: String?,
    @Json(name = "badgeName")
    val badgeName: String?,
    @Json(name = "badgeIconUrl")
    val badgeIconUrl: String?,
    @Json(name = "acquiredBadgeIconUrl")
    val acquiredBadgeIconUrl: String?,
    @Json(name = "badgeBackgroundImageUrl")
    val badgeBackgroundImageUrl: String?,
    @Json(name = "badgeDesc")
    val badgeDesc: String?,
    @Json(name = "acquisitionCondition")
    val acquisitionCondition: BadgeAcquisitionConditionRemote?,
    @Json(name = "relatedActivityIds")
    val relatedActivityIds: List<Int>?,
    @Json(name = "startTime")
    val startTime: Long?,
    @Json(name = "endTime")
    val endTime: Long?
)

@JsonClass(generateAdapter = true)
data class UserBadgeListPayloadRemote(
    @Json(name = "userHasWonBadges") val userHasWonBadges: List<UserBadgeModuleRemote>?,
    @Json(name = "hasNewBadge") val hasNewBadge: Boolean?
)

@JsonClass(generateAdapter = true)
data class UserBadgeModuleRemote(
    @Json(name = "moduleName") val moduleName: String,
    @Json(name = "userBadges") val userBadges: List<UserBadgeSimpleRemote>
)

@JsonClass(generateAdapter = true)
data class UserBadgeSimpleRemote(
    @Json(name = "badgeName") val badgeName: String,
    @Json(name = "badgeId") val badgeId: String,
    @Json(name = "badgeConfigId") val badgeConfigId: String,
    @Json(name = "acquisitionTime") val acquisitionTime: Long,
    @Json(name = "badgeIconUrl") val badgeIconUrl: String,
    @Json(name = "acquiredBadgeIconUrl") val acquiredBadgeIconUrl: String?,
    @Json(name = "badgeBackgroundImageUrl") val badgeBackgroundImageUrl: String?
)

@JsonClass(generateAdapter = true)
data class UserBadgeDetailPayloadRemote(
    @Json(name = "badgeIconUrl")
    val badgeIconUrl: String?,
    @Json(name = "acquiredBadgeIconUrl")
    val acquiredBadgeIconUrl: String?,
    @Json(name = "badgeBackgroundImageUrl")
    val badgeBackgroundImageUrl: String?,
    @Json(name = "badgeType")
    val badgeType: String?,
    @Json(name = "badgeName")
    val badgeName: String?,
    @Json(name = "badgeDesc")
    val badgeDesc: String?,
    @Json(name = "startTime")
    val startTime: Long?,
    @Json(name = "endTime")
    val endTime: Long?,
    @Json(name = "acquisitionWayType")
    val acquisitionWayType: String?,
    @Json(name = "acquisitionCondition")
    val acquisitionCondition: BadgeAcquisitionConditionRemote?,
    @Json(name = "acquisitionTime")
    val acquisitionTime: Long?,
    @Json(name = "participationTime")
    val participationTime: Long?,
    @Json(name = "acquisitionRanking")
    val acquisitionRanking: Int?,
    @Json(name = "transcriptDataFields")
    val transcriptDataFields: List<String>?,
    @Json(name = "maxSpeed")
    val maxSpeed: Float?,
    @Json(name = "maxDistance")
    val maxDistance: Double?,
    @Json(name = "totalDistance")
    val totalDistance: Double?,
    @Json(name = "activitySessions")
    val activitySessions: Int?,
    @Json(name = "totalDuration")
    val totalDuration: Double?,
    @Json(name = "energy")
    val energy: Int?,
    @Json(name = "totalWorkoutDays")
    val totalWorkoutDays: Int?,
    @Json(name = "totalAscent")
    val totalAscent: Int?,
    @Json(name = "maxPace")
    val maxPace: Double?,
    @Json(name = "maxCyclingSpeed")
    val maxCyclingSpeed: Float?,
    @Json(name = "maxDivingDepth")
    val maxDivingDepth: Double?,
    @Json(name = "maxDuration")
    val maxDuration: Double?,
    @Json(name = "activityIds")
    val activityIds: List<Int>?,
    @Json(name = "totalUTMBDistance")
    val totalUTMBDistance: Double?,
    @Json(name = "createTime")
    val createTime: Long?,
    @Json(name = "badgeStatus")
    val badgeStatus: String?
)

@JsonClass(generateAdapter = true)
data class BadgesNotificationDeleteRemote(
    val deleteRedPoint: Boolean
)

@JsonClass(generateAdapter = true)
data class BadgeJoin(
    val joinBadgeToPlan: Boolean?
)
