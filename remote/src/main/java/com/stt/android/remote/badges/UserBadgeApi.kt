package com.stt.android.remote.badges

import com.stt.android.remote.response.AskoResponse
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface UserBadgeApi {
    @GET("user-badge/detail")
    suspend fun getUserBadgeDetail(
        @Query("badgeConfigId") badgeConfigId: String? = null
    ): AskoResponse<UserBadgeDetailPayloadRemote>

    @GET("user-badge/list")
    suspend fun getUserBadgeList(): AskoResponse<UserBadgeListPayloadRemote>

    @GET("badge-config/module/list")
    suspend fun getBadgeConfigs(
        @Query("module") module: String? = null,
    ): AskoResponse<List<BadgeModuleConfigRemote>>

    @GET("user-badge/exploreMore")
    suspend fun getUserBadgeDetailOtherBadges(
        @Query("badgeConfigId") badgeConfigId: String? = null
    ): AskoResponse<List<ExploreMorePayload>>

    @GET("user-badge/friend/list")
    suspend fun getFriendBadgesList(
        @Query("userName") userName: String? = null
    ): AskoResponse<UserBadgeListPayloadRemote>

    @POST("user-badge/join")
    suspend fun postJoinInformation(
        @Query("badgeConfigId") badgeConfigId: String? = null
    ): AskoResponse<BadgeJoin>

    @POST("user-badge/notification/delete")
    suspend fun deleteRedPoint(): AskoResponse<BadgesNotificationDeleteRemote>
}
