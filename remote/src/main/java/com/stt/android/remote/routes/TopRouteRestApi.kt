package com.stt.android.remote.routes

import com.sportstracker.apiserver.domain.route.v2.RouteEntitiesProtos
import com.stt.android.remote.response.AskoResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface TopRouteRestApi {

    @GET("toproutes/{key}")
    @Headers(X_PROTOBUF_ACCEPT_HEADER)
    suspend fun fetchTopRoute(
        @Path("key") key: String,
    ): RouteEntitiesProtos.Route?

    @GET("toproutes/userroutes/{key}")
    @Headers(X_PROTOBUF_ACCEPT_HEADER)
    suspend fun fetchTopRouteWithActivityType(
        @Path("key") key: String,
        @Query("activityType") activityType: Int,
    ): RouteEntitiesProtos.UserTopRoute?

    @POST("toproutes/metadata")
    suspend fun fetchTopRouteMetadata(
        @Body body: List<String>,
    ): AskoResponse<List<RemoteTopRouteMetadataSimple>>

    @PUT("toproutes/share/{key}")
    suspend fun shareTopRoute(
        @Path("key") key: String
    ): Response<Unit>

    @POST("toproutes/favorite")
    suspend fun saveTopRouteFavorite(
        @Query("topRouteId") topRouteId: String,
        @Query("activityType") activityType: Int,
        @Query("watchEnabled") watchEnabled: Boolean,
    ): AskoResponse<Boolean>

    @POST("toproutes/remove/favorite")
    suspend fun deleteTopRouteFavorites(
        @Body body: List<RemoteRouteFavorite>
    ): AskoResponse<Boolean>

    @GET("toproutes/favorite")
    @Headers(X_PROTOBUF_ACCEPT_HEADER)
    suspend fun fetchRoutesPage(
        @Query("since") since: Long,
        @Query("size") limit: Int,
        @Query("page") page: Int,
        @Query("pagesorting") sortOrder: String,
        @Query("includesegments") includeSegments: Boolean
    ): RouteEntitiesProtos.ListOfUserRoute

    @GET("toproutes/favorite")
    @Headers(X_PROTOBUF_ACCEPT_HEADER)
    suspend fun fetchRoutes(
        @Query("includesegments") includeSegments: Boolean
    ): RouteEntitiesProtos.ListOfUserRoute
}
