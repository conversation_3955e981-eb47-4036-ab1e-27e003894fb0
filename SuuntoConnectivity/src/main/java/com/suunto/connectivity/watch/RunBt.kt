package com.suunto.connectivity.watch

import android.bluetooth.BluetoothAdapter
import android.content.Context
import com.google.gson.Gson
import com.stt.android.eventtracking.EventTracker
import com.stt.android.timeline.TimelineResourceLocalDataSource
import com.stt.android.timeline.WeChatTimelineResourceLocalDataSource
import com.suunto.connectivity.files.LogFilesMdsResponse
import com.suunto.connectivity.gps.GpsDataResource
import com.suunto.connectivity.gps.GpsFileManager
import com.suunto.connectivity.notifications.NotificationsDevice
import com.suunto.connectivity.offlinemusic.OfflineMusicSettings
import com.suunto.connectivity.repository.AppInfo
import com.suunto.connectivity.repository.commands.SetupPreferenceContract
import com.suunto.connectivity.repository.commands.SetupPreferenceContract.GetStateInfo
import com.suunto.connectivity.repository.commands.SetupPreferenceContract.PutStateInfo
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.settings.Gender
import com.suunto.connectivity.settings.SettingsResource
import com.suunto.connectivity.settings.UnitSystem
import com.suunto.connectivity.settings.WearDirection
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_CONTRACT_EMPTY
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_SCHEME_PREFIX
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice
import com.suunto.connectivity.suuntoconnectivity.utils.BtStateMonitor
import com.suunto.connectivity.sync.SynchronizerStorage
import com.suunto.connectivity.trainingzone.BESTrainingZoneSyncContract
import com.suunto.connectivity.trainingzone.BESTrainingZoneSyncData
import com.suunto.connectivity.trainingzone.TrainingZoneResource
import com.suunto.connectivity.util.SupportedDevices
import com.suunto.connectivity.watch.userprofile.UserProfileBirthYearItem
import com.suunto.connectivity.watch.userprofile.UserProfileGenderItem
import com.suunto.connectivity.watch.userprofile.UserProfileHeightItem
import com.suunto.connectivity.watch.userprofile.UserProfileMaxHeartItem
import com.suunto.connectivity.watch.userprofile.UserProfileRestHeartItem
import com.suunto.connectivity.watch.userprofile.UserProfileWeightItem
import rx.Completable
import rx.Observable
import rx.Single
import timber.log.Timber
import java.util.Locale
import java.util.Base64

/**
 * SuuntoRun used some of Spartan's watch APIs and then added new ones that are unique to it.
 * This is for SuuntoRun, and we put its watch APIs here.
 */
class RunBt(
    context: Context,
    suuntoBtDevice: SuuntoBtDevice,
    watchConnector: WatchConnector,
    val mdsRx: MdsRx,
    gson: Gson,
    bluetoothAdapter: BluetoothAdapter,
    btStateMonitor: BtStateMonitor,
    notificationsDevice: NotificationsDevice,
    synchronizerStorage: SynchronizerStorage,
    gpsFileManager: GpsFileManager,
    gpsDataResource: GpsDataResource,
    synchronizerInjection: SpartanSynchronizer.Injection,
    supportedDevices: SupportedDevices?,
    timelineResourceLocalDataSource: TimelineResourceLocalDataSource,
    weChatTimelineResourceLocalDataSource: WeChatTimelineResourceLocalDataSource,
    trainingZoneResource: TrainingZoneResource,
    settingsResource: SettingsResource,
    eventTracker: EventTracker,
) : SpartanBt(
    context,
    suuntoBtDevice,
    watchConnector,
    mdsRx,
    gson,
    bluetoothAdapter,
    btStateMonitor,
    notificationsDevice,
    synchronizerStorage,
    gpsFileManager,
    gpsDataResource,
    synchronizerInjection,
    supportedDevices,
    timelineResourceLocalDataSource,
    weChatTimelineResourceLocalDataSource,
    trainingZoneResource,
    settingsResource,
    eventTracker,
) {
    private val offlineMusicSettings: OfflineMusicSettings =
        OfflineMusicSettings(suuntoBtDevice.getSerial(), moshi, mdsRx, gson)

    private companion object {
        private const val LOG_FILES_URI: String = "$MDS_SCHEME_PREFIX%s/logs/list"

        private const val SUBSCRIBE_SETUP_PREFERENCE_URI: String = "%s/settings/user/launcherStatus"
        private const val SETUP_PREFERENCE_URI: String =
            MDS_SCHEME_PREFIX + SUBSCRIBE_SETUP_PREFERENCE_URI

        private const val SUBSCRIBE_OFFLINE_MUSIC_UPDATE_URI: String = "%s/offline/music/update"

        private const val USER_GENDER_SUBSCRIPTION_URI: String = "%s/Settings/User/Gender"
        private const val USER_HEIGHT_SUBSCRIPTION_URI: String = "%s/Settings/User/Height"
        private const val USER_WEIGHT_SUBSCRIPTION_URI: String = "%s/Settings/User/Weight"
        private const val USER_BIRTH_YEAR_SUBSCRIPTION_URI: String = "%s/Settings/User/BirthYear"
        private const val USER_MAX_HR_SUBSCRIPTION_URI: String = "%s/Settings/User/MaxHR"
        private const val USER_REST_HR_SUBSCRIPTION_URI: String = "%s/Settings/User/RestHR"

        private const val BES_TRAINING_ZONE_SYNC_URI =
            "$MDS_SCHEME_PREFIX%s/BES/TrainingZone/Combine"
        private const val BES_TRAINING_ZONE_SYNC_SUBSCRIPTION_URI =
            "%s/BES/TrainingZone/Combine"
    }

    override fun getLogFiles(): Single<LogFilesMdsResponse> {
        val uri = String.format(LOG_FILES_URI, serial)
        return mdsRx.get(uri, MDS_CONTRACT_EMPTY)
            .flatMap { responseBody ->
                decodeMdsResponseWrappedInContent(responseBody, LogFilesMdsResponse::class.java)
            }
    }

    override fun getOfflineMusicSettings(): OfflineMusicSettings {
        return offlineMusicSettings
    }

    override fun subscribeSetupPreferenceMessage(): Observable<SetupPreferenceContract.StateInfo> {
        val uri = String.format(SUBSCRIBE_SETUP_PREFERENCE_URI, serial)
        return mdsRx.subscribe(uri, SetupPreferenceContract.StateInfo::class.java, false)
    }

    override fun getSetupPreferenceState(): Single<GetStateInfo> {
        val uri = String.format(SETUP_PREFERENCE_URI, serial)
        return mdsRx.get(uri, MDS_CONTRACT_EMPTY).map { GetStateInfo.fromJson(it) }
    }

    override fun setSetupPreferenceState(stateInfo: PutStateInfo): Completable {
        val uri = String.format(SETUP_PREFERENCE_URI, serial)
        return mdsRx.put(uri, stateInfo.toJson()).toCompletable()
    }

    override fun setAppInfo(appInfo: AppInfo): Completable {
        return deviceSettings?.setupAppInfo(appInfo) ?: Completable.complete()
    }

    override fun setupWearDirection(direction: WearDirection): Completable {
        return deviceSettings?.setWearDirection(direction) ?: Completable.complete()
    }

    override fun setUserGender(gender: Gender): Completable {
        return settings?.gender?.setValue(gender) ?: Completable.complete()
    }

    override fun setUserWeight(weight: Float): Completable {
        return settings?.weight?.setValue(weight) ?: Completable.complete()
    }

    override fun setUserBirthYear(year: Int): Completable {
        return settings?.birthYear?.setValue(year) ?: Completable.complete()
    }

    override fun setUserHeight(height: Float): Completable {
        return settings?.height?.setValue(height) ?: Completable.complete()
    }

    override fun setUserMaxHR(maxHR: Int): Completable {
        return settings?.maxHR?.setValue(maxHR) ?: Completable.complete()
    }

    override fun setUserRestHR(restHR: Int): Completable {
        val restHRItem =
            UserProfileRestHeartItem(restHR, (System.currentTimeMillis() / 1000).toInt())
        return settings?.restHRWithTimestamp?.setValue(restHRItem) ?: Completable.complete()
    }

    override fun setUserUnitSystem(unitSystem: UnitSystem): Completable {
        return settings?.unitSystem?.setValue(unitSystem) ?: Completable.complete()
    }

    override fun subscribeOfflineMusicUpdate(): Observable<Boolean> {
        val uri = String.format(SUBSCRIBE_OFFLINE_MUSIC_UPDATE_URI, serial)
        return mdsRx.subscribe(uri, Boolean::class.java, false)
    }

    override fun subscribeUserGenderSettingObservable(): Observable<UserProfileGenderItem> {
        val uri = String.format(Locale.US, USER_GENDER_SUBSCRIPTION_URI, serial)
        Timber.v("Subscribe user gender setting, uri: %s", uri)
        return mdsRx.subscribe(uri, UserProfileGenderItem::class.java, true)
    }

    override fun subscribeUserHeightSettingObservable(): Observable<UserProfileHeightItem> {
        val uri = String.format(Locale.US, USER_HEIGHT_SUBSCRIPTION_URI, serial)
        Timber.v("Subscribe user height setting, uri: %s", uri)
        return mdsRx.subscribe(uri, UserProfileHeightItem::class.java, true)
    }

    override fun subscribeUserWeightSettingObservable(): Observable<UserProfileWeightItem> {
        val uri = String.format(Locale.US, USER_WEIGHT_SUBSCRIPTION_URI, serial)
        Timber.v("Subscribe user weight setting, uri: %s", uri)
        return mdsRx.subscribe(uri, UserProfileWeightItem::class.java, true)
    }

    override fun subscribeUserBirthYearSettingObservable(): Observable<UserProfileBirthYearItem> {
        val uri = String.format(Locale.US, USER_BIRTH_YEAR_SUBSCRIPTION_URI, serial)
        Timber.v("Subscribe user birthYear setting, uri: %s", uri)
        return mdsRx.subscribe(uri, UserProfileBirthYearItem::class.java, true)
    }

    override fun subscribeUserMaxHRSettingObservable(): Observable<UserProfileMaxHeartItem> {
        val uri = String.format(Locale.US, USER_MAX_HR_SUBSCRIPTION_URI, serial)
        Timber.v("Subscribe user maxHR setting, uri: %s", uri)
        return mdsRx.subscribe(uri, UserProfileMaxHeartItem::class.java, true)
    }

    override fun subscribeUserRestHRSettingObservable(): Observable<UserProfileRestHeartItem> {
        val uri = String.format(Locale.US, USER_REST_HR_SUBSCRIPTION_URI, serial)
        Timber.v("Subscribe user restHR setting, uri: %s", uri)
        return mdsRx.subscribe(uri, UserProfileRestHeartItem::class.java, true)
    }

    override fun setBESTrainingZoneSyncData(besTrainingZoneSyncData: BESTrainingZoneSyncData): Completable {
        val uri = String.format(BES_TRAINING_ZONE_SYNC_URI, serial)
        val contract =
            moshi.adapter(BESTrainingZoneSyncData::class.java).toJson(besTrainingZoneSyncData)
        val besTrainingZoneSyncContract = BESTrainingZoneSyncContract(
            data = Base64.getEncoder().withoutPadding().encodeToString(contract.encodeToByteArray())
        )
        return mdsRx.put(
            uri,
            moshi.adapter(BESTrainingZoneSyncContract::class.java)
                .toJson(besTrainingZoneSyncContract)
        ).toCompletable()
    }

    override fun subscribeBESTrainingZoneSync(): Observable<Boolean> {
        val uri = String.format(Locale.US, BES_TRAINING_ZONE_SYNC_SUBSCRIPTION_URI, serial)
        return mdsRx.subscribe(uri, Boolean::class.java, false)
    }
}
