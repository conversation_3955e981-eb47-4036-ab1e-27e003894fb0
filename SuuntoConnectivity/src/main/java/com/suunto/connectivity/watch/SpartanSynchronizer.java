package com.suunto.connectivity.watch;

import com.google.gson.Gson;
import com.suunto.connectivity.burypoint.BuriedPointResource;
import com.suunto.connectivity.deviceid.ISuuntoDeviceCapabilityInfo;
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider;
import com.suunto.connectivity.gps.GpsDataResource;
import com.suunto.connectivity.logbook.Logbook;
import com.suunto.connectivity.poi.POIResource;
import com.suunto.connectivity.recovery.RecoveryDataResource;
import com.suunto.connectivity.repository.LogbookEntrySyncResult;
import com.suunto.connectivity.repository.LogbookSyncResult;
import com.suunto.connectivity.routes.RouteResource;
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo;
import com.suunto.connectivity.settings.SettingsResource;
import com.suunto.connectivity.sleep.SleepResource;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice;
import com.suunto.connectivity.suuntoplusguides.SuuntoPlusGuideResource;
import com.suunto.connectivity.sync.SyncState;
import com.suunto.connectivity.sync.SynchronizerStorage;
import com.suunto.connectivity.systemevents.SystemEventsResource;
import com.suunto.connectivity.trainingzone.TrainingZoneResource;
import com.suunto.connectivity.trenddata.TrendDataResource;
import com.suunto.connectivity.util.SupportedDevices;
import com.suunto.connectivity.weather.WeatherResource;
import com.suunto.connectivity.zonesense.ZoneSenseResource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import rx.Completable;
import rx.Observable;
import timber.log.Timber;

/**
 * Component responsible for synchronizing Spartan watches
 */
public class SpartanSynchronizer extends WatchSynchronizerBase {

    private static final int LOGBOOK_SYNC_RETRY_COUNT = 3;
    private static final int SYSTEM_EVENT_BACKGROUND_SYNC_MAX_DELAY_HOURS = 12;

    public static class Injection extends InjectionBase {
        @Inject
        public Injection(
            POIResource poiResource,
            RouteResource routeResource,
            SuuntoPlusGuideResource suuntoPlusGuideResource,
            WeatherResource weatherResource,
            TrainingZoneResource trainingZoneResource,
            SettingsResource settingsResource,
            ZoneSenseResource zoneSenseResource
        ) {
            super(poiResource, routeResource, suuntoPlusGuideResource, weatherResource,
                trainingZoneResource, settingsResource, zoneSenseResource);
        }
    }

    private final SpartanBt spartanBt;
    private final TrendDataResource trendDataResource;
    private final RecoveryDataResource recoveryDataResource;
    private final SleepResource sleepResource;
    private final GpsDataResource gpsDataResource;
    private final SystemEventsResource systemEventsResource;
    private final BuriedPointResource buriedPointResource;
    private final TrainingZoneResource trainingZoneResource;
    private final SettingsResource settingsResource;

    SpartanSynchronizer(
        SpartanBt spartanBt,
        Gson gson,
        SynchronizerStorage synchronizerStorage,
        GpsDataResource gpsDataResource,
        TrendDataResource trendDataResource,
        RecoveryDataResource recoveryDataResource,
        SleepResource sleepResource,
        SystemEventsResource systemEventsResource,
        BuriedPointResource buriedPointResource,
        Injection injection,
        SupportedDevices supportedDevices,
        TrainingZoneResource trainingZoneResource,
        SettingsResource settingsResource
    ) {
        super(spartanBt, gson, synchronizerStorage, injection, supportedDevices);
        this.spartanBt = spartanBt;
        this.gpsDataResource = gpsDataResource;
        this.trendDataResource = trendDataResource;
        this.recoveryDataResource = recoveryDataResource;
        this.sleepResource = sleepResource;
        this.systemEventsResource = systemEventsResource;
        this.buriedPointResource = buriedPointResource;
        this.trainingZoneResource = trainingZoneResource;
        this.settingsResource = settingsResource;
    }

    /**
     * Chain for performing activity data sync
     *
     * @param builder is builder for sync result
     * @return {@link Completable} which completes when activity data sync is done
     */
    @Override
    protected Completable activityDataSyncCompletable(SpartanSyncResult.Builder builder) {
        ISuuntoDeviceCapabilityInfo capabilities =
            SuuntoDeviceCapabilityInfoProvider.get(spartanBt.getSuuntoBtDevice().getDeviceType());
        MdsDeviceInfo mdsDeviceInfo = spartanBt.getCurrentState().getDeviceInfo();
        Completable trendDataCompletable;
        Completable recoveryDataCompletable;
        Completable sleepCompletable;

        if (mdsDeviceInfo != null && capabilities.supportsTrendData(mdsDeviceInfo)) {
            trendDataCompletable = trendDataResource
                .sync(getWatchSerial(), builder)
                .compose(checkConnectedState())
                .compose(checkBusyState())
                .doOnSubscribe(s -> setSyncState(new SyncState(SyncState.SYNCING_TREND_DATA)))
                .onErrorResumeNext(throwable -> {
                    Timber.w(throwable, "Trend data sync failed");
                    setSyncState(new SyncState(SyncState.NOT_SYNCING));
                    return Completable.complete();
                });
        } else {
            trendDataCompletable = Completable.complete();
            builder.trendDataResult(SyncStepResult.skipped());
        }

        if (mdsDeviceInfo != null && capabilities.supportsRecoveryData(mdsDeviceInfo)) {
            recoveryDataCompletable = recoveryDataResource
                .sync(getWatchSerial(), builder)
                .compose(checkConnectedState())
                .compose(checkBusyState())
                .doOnSubscribe(s -> setSyncState(new SyncState(SyncState.SYNCING_RECOVERY_DATA)))
                .onErrorResumeNext(throwable -> {
                    Timber.w(throwable, "Recovery data sync failed");
                    setSyncState(new SyncState(SyncState.NOT_SYNCING));
                    return Completable.complete();
                });
        } else {
            recoveryDataCompletable = Completable.complete();
            builder.recoveryDataResult(SyncStepResult.skipped());
        }

        if (mdsDeviceInfo != null && capabilities.supportsSleepData(mdsDeviceInfo)) {
            sleepCompletable = sleepResource
                .sync(getWatchSerial(), builder)
                .compose(checkConnectedState())
                .compose(checkBusyState())
                .doOnSubscribe(s -> setSyncState(new SyncState(SyncState.SYNCING_SLEEP)))
                .onErrorResumeNext(throwable -> {
                    Timber.w(throwable, "Sleep data sync failed");
                    setSyncState(new SyncState(SyncState.NOT_SYNCING));
                    return Completable.complete();
                });
        } else {
            sleepCompletable = Completable.complete();
            builder.sleepResult(SyncStepResult.skipped());
        }

        return trendDataCompletable
            .andThen(recoveryDataCompletable)
            .andThen(sleepCompletable);
    }

    @Override
    protected Completable trainingZoneSyncCompletable(SpartanSyncResult.Builder builder) {
        if (!supportedDevices.isSupportedTrainingZoneSync(spartanBt)) {
            Timber.d("Device doesn't support training zone sync");
            return Completable.complete();
        }
        Timber.d("Request to sync training zone");
        return trainingZoneResource
            .sync(spartanBt, builder)
            .doOnSubscribe(s -> setSyncState(trainingZoneResource.getSyncState()));
    }

    private Completable gpsSyncCompletable(SpartanSyncResult.Builder builder) {
        SuuntoBtDevice device = spartanBt.getSuuntoBtDevice();
        return supportedDevices.supportsGpsFileSync(device) ? gpsDataResource.sync(spartanBt, builder)
            .compose(checkBusyState())
            .doOnSubscribe(s -> setSyncState(new SyncState(SyncState.SYNCING_GPS)))
            : Completable.complete();
    }

    /**
     * Chain for performing a full sync
     *
     * @param builder is builder for sync result
     * @return {@link Completable} which completes when sync is fully done
     */
    @Override
    protected Completable fullSyncCompletable(SpartanSyncResult.Builder builder) {
        Completable gpsSync = gpsSyncCompletable(builder);
        return syncRoutes(builder)
            .andThen(syncPOIs(builder))
            .andThen(syncSuuntoPlusGuides(builder))
            .andThen(syncLogbook(builder)
                .andThen(syncSettings(builder)
                    .compose(checkBusyState()))
                .andThen(gpsSync)
                .andThen(syncSystemEvents(builder))
                .andThen(fetchBuryPointZip(builder))
                .andThen(activityDataSyncCompletable(builder)))
                .andThen(syncWeather(builder))
                .andThen(syncZoneSense(builder))
                .andThen(
                    // Make sure training zone sync is always the last one
                    // training zone need to have the latest data in order to calculate values correctly
                    // so, we need to ensure everything is synced on order to sync training zone
                    // The implementation in SyncLogic#requestTrainingZoneSync assumes that training zone sync is the last one
                    trainingZoneSyncCompletable(builder)
                );
    }

    private Completable fetchBuryPointZip(SpartanSyncResult.Builder builder) {
        return buriedPointResource.sync(builder)
            .compose(checkBusyState())
            .doOnSubscribe(s -> setSyncState(new SyncState(SyncState.SYNCING_BURIED_POINT)));
    }

    private Completable syncSystemEvents(SpartanSyncResult.Builder builder) {
        return systemEventsResource.sync(builder)
            .compose(checkBusyState())
            .doOnSubscribe(
                s -> setSyncState(new SyncState(SyncState.SYNCING_SYSTEM_EVENTS_DATA)));
    }

    @Override
    protected Completable backgroundSyncCompletable(SpartanSyncResult.Builder builder) {
        Completable gpsSync = gpsSyncCompletable(builder);
        return syncLogbook(builder)
            .toSingle(() -> builder)
            .flatMapCompletable(this::syncSystemEventsIfNewWorkoutWasSynced)
            .andThen(gpsSync)
            .toSingle(() -> builder)
            .flatMapCompletable(this::syncSystemEventsIfTimePassed)
            .andThen(Completable.fromAction(() -> {
                SpartanSyncResult results = builder.build();
                builder.settingsResult(SyncStepResult.skipped());
                builder.trendDataResult(SyncStepResult.skipped());
                builder.recoveryDataResult(SyncStepResult.skipped());
                builder.sleepResult(SyncStepResult.skipped());
                if (results.getSystemEventsResult().getSyncResult().isUnknown()) {
                    builder.systemEventsResult(SyncStepResult.skipped());
                }
                builder.buriedPointResult(SyncStepResult.skipped());
                builder.routeResult(RouteSyncResult.skipped());
                builder.poiResult(POISyncResult.skipped());
                builder.weatherResult(SyncStepResult.skipped());
                builder.trainingZoneResult(TrainingZoneSyncResult.skipped());
            }));
    }

    /**
     * Creates a system events sync completable, in case at least one new workout was just
     * synced. Otherwise returns immediately completing completable.
     *
     * @param builder Sync result builder.
     * @return System event sync completable
     */
    Completable syncSystemEventsIfNewWorkoutWasSynced(SpartanSyncResult.Builder builder) {
        SpartanSyncResult results = builder.build();
        LogbookSyncResult logbookResults = results.getLogbookResult();
        if (logbookResults.getLogbookResult().isSuccess()) {
            for (LogbookEntrySyncResult entrySyncResult :
                logbookResults.getLogbookEntriesResult()) {
                if (entrySyncResult.isJustSyncedSuccessfully()) {
                    return syncSystemEvents(builder);
                }
            }
        }
        return Completable.complete();
    }

    /**
     * Creates a system events sync completable, in case
     * SYSTEM_EVENT_BACKGROUND_SYNC_MAX_DELAY_HOURS
     * hours has passed from previous system events sync. Otherwise returns immediately completing
     * completable.
     *
     * @param builder Sync result builder.
     * @return System event sync completable
     */
    Completable syncSystemEventsIfTimePassed(SpartanSyncResult.Builder builder) {
        long currentTime = System.currentTimeMillis();
        long lastSynced =
            synchronizerStorage.getLastSystemEventsSyncedTimestamp(spartanBt.getMacAddress());
        if (TimeUnit.MILLISECONDS.toHours(currentTime - lastSynced)
            >= SYSTEM_EVENT_BACKGROUND_SYNC_MAX_DELAY_HOURS) {
            return syncSystemEvents(builder);
        } else {
            return Completable.complete();
        }
    }

    @Override
    protected Observable<List<Logbook.Entry>> markEntriesSynced(
        List<Logbook.Entry> entriesToSync,
        List<Long> entriesToMark,
        String macAddress) {

        // First try to mark synced entries as synced to watch
        // then call base class to emit entries to sync for next step.
        return markEntriesAsSyncedToDevice(entriesToMark)
            .andThen(super.markEntriesSynced(entriesToSync, entriesToMark, macAddress));
    }

    /**
     * Try to mark synced entries as synced to device. Ignore errors.
     *
     * @param entriesToBeMarked List of entries to be marked.
     * @return Completable for marking entries.
     */
    private Completable markEntriesAsSyncedToDevice(List<Long> entriesToBeMarked) {
        List<Completable> markAsSyncedCompletables = new ArrayList<>();
        for (Long entryId : entriesToBeMarked) {
            Completable markAsSynced =
                markEntryAsSyncedToDevice(spartanBt, synchronizerStorage, entryId)
                    .retry(LOGBOOK_SYNC_RETRY_COUNT);
            markAsSyncedCompletables.add(markAsSynced);
        }
        return Completable.concat(markAsSyncedCompletables);
    }

    /**
     * Try to mark entry as synced to device.
     *
     * @param spartanBt Device from where entry is received.
     * @param synchronizerStorage Synchronizer storage.
     * @param entryId Entry to be marked as synced
     */
    public static Completable markEntryAsSyncedToDevice(SpartanBt spartanBt,
        SynchronizerStorage synchronizerStorage,
        long entryId) {
        final String macAddress = spartanBt.getSuuntoBtDevice().getMacAddress();
        return spartanBt.markEntryAsSynced(entryId)
            .doOnSuccess(s -> {
                synchronizerStorage.removeEntriesFromToBeMarkedAsSynced(macAddress,
                    Collections.singletonList(entryId));
            })
            .toCompletable();
    }

    private Completable syncSettings(final SpartanSyncResult.Builder builder) {
        return settingsResource.sync(spartanBt, builder)
            .doOnSubscribe(subscription -> {
                // Update state
                setSyncState(new SyncState(SyncState.SYNCING_SETTINGS));
            });
    }

    @Override
    protected int logbookSyncRetryCount() {
        return LOGBOOK_SYNC_RETRY_COUNT;
    }
}
