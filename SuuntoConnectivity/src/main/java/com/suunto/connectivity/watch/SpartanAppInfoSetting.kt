package com.suunto.connectivity.watch

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_SCHEME_PREFIX
import com.suunto.connectivity.watch.appinfo.SpartanAppInfo
import com.suunto.connectivity.watch.appinfo.SpartanAppInfoSettingValue

class SpartanAppInfoSetting(
    moshi: Moshi,
    serialNumber: String,
    mdsRx: MdsRx
) : SpartanMoshiSetting<SpartanAppInfo>(
    moshi,
    serialNumber,
    mdsRx,
    SpartanAppInfo::class.java
) {

    override val uri = "$MDS_SCHEME_PREFIX$serialNumber/Settings/Device/AppInfo"

    private val requestAdapter: JsonAdapter<SpartanAppInfoSettingValue> =
        moshi.adapter(SpartanAppInfoSettingValue::class.java)

    override fun buildRequestJson(value: SpartanAppInfo): String {
        return requestAdapter.toJson(SpartanAppInfoSettingValue(value))
    }
}
