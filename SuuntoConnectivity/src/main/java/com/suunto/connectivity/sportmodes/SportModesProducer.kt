package com.suunto.connectivity.sportmodes

import android.os.Bundle
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.SuuntoRepositoryService.ArgumentKeys.ARG_DATA
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_DELETE_SPORT_MODES
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_SPORT_MODES
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_SET_SPORT_MODES
import com.suunto.connectivity.repository.commands.DeleteSportModeQuery
import com.suunto.connectivity.repository.commands.DeleteSportModeResponse
import com.suunto.connectivity.repository.commands.GetSportModeObjectQuery
import com.suunto.connectivity.repository.commands.GetSportModeObjectResponse
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.repository.commands.SetSportModeObjectQuery
import com.suunto.connectivity.repository.commands.SetSportModeObjectResponse
import com.suunto.connectivity.repository.commands.SportModePart
import rx.Observable
import rx.Single
import timber.log.Timber

class SportModesProducer
constructor(
    private val suuntoRepositoryService: SuuntoRepositoryService
) : SuuntoResponseProducer<Response> {
    override fun isRelated(messageType: Int): Boolean {
        return when (messageType) {
            MSG_GET_SPORT_MODES -> true
            MSG_SET_SPORT_MODES -> true
            MSG_DELETE_SPORT_MODES -> true
            else -> false
        }
    }

    override fun provideResponseObservable(messageType: Int, bundle: Bundle): Observable<out Response> {
        return when (messageType) {
            MSG_GET_SPORT_MODES -> handleGetSportModeValue(bundle).toObservable()
            MSG_SET_SPORT_MODES -> handleSetSportModeValue(bundle).toObservable()
            MSG_DELETE_SPORT_MODES -> handleDeleteSportMode(bundle).toObservable()
            else -> Observable.empty<Response>()
        }
    }

    private fun handleGetSportModeValue(bundle: Bundle): Single<GetSportModeObjectResponse> {
        val (macAddress, id, sportModePart) = bundle.getParcelable<GetSportModeObjectQuery>(ARG_DATA)
            ?: return Single.just(GetSportModeObjectResponse("", SportModePart.UNKNOWN))
        Timber.d("Getting sport mode value")
        val watchBt = suuntoRepositoryService.activeDevices.getWatchBt(macAddress)
        if (watchBt == null) {
            return Single.just(GetSportModeObjectResponse("", sportModePart))
        } else {
            val sportModesDataHolder = watchBt.sportModesDataHolder
                ?: return Single.just(GetSportModeObjectResponse("", sportModePart))
            return when (sportModePart) {
                SportModePart.GROUP -> if (id == 0) {
                    sportModesDataHolder.sportModes.rawJsonString
                        .map({ response -> GetSportModeObjectResponse(response, sportModePart) })
                } else {
                    sportModesDataHolder.sportModes.rawValueForId(id)
                        .map({ response -> GetSportModeObjectResponse(response, sportModePart) })
                }
                SportModePart.DISPLAY -> sportModesDataHolder.watchSportModeDisplays.rawValueForId(id)
                    .map({ response -> GetSportModeObjectResponse(response, sportModePart) })
                SportModePart.SETTING -> sportModesDataHolder.watchSportModeSettings.rawValueForId(id)
                    .map({ response -> GetSportModeObjectResponse(response, sportModePart) })
                else -> // We don't care
                    Single.just(GetSportModeObjectResponse("", sportModePart))
            }
        }
    }

    private fun handleSetSportModeValue(bundle: Bundle): Single<SetSportModeObjectResponse> {
        val (macAddress, id, json, sportModePart) = bundle.getParcelable<SetSportModeObjectQuery>(ARG_DATA)
            ?: return Single.just(SetSportModeObjectResponse(false, SportModePart.UNKNOWN))
        Timber.d("Setting sport mode value")
        val watchBt = suuntoRepositoryService.activeDevices.getWatchBt(macAddress)
        if (watchBt == null) {
            return Single.just(SetSportModeObjectResponse(false, sportModePart))
        } else {
            val sportModesDataHolder = watchBt.sportModesDataHolder
                ?: return Single.just(SetSportModeObjectResponse(false, sportModePart))
            return when (sportModePart) {
                SportModePart.GROUP -> sportModesDataHolder.sportModes.postRawValueForId(id, json)
                    .map({ SetSportModeObjectResponse(true, sportModePart) })
                SportModePart.DISPLAY -> sportModesDataHolder.watchSportModeDisplays.putRawValueForId(id, json)
                    .map({ SetSportModeObjectResponse(true, sportModePart) })
                SportModePart.SETTING -> sportModesDataHolder.watchSportModeSettings.putRawValueForId(id, json)
                    .map({ SetSportModeObjectResponse(true, sportModePart) })
                else -> // We don't care
                    Single.just(SetSportModeObjectResponse(false, sportModePart))
            }
        }
    }

    private fun handleDeleteSportMode(bundle: Bundle): Single<DeleteSportModeResponse> {
        val (macAddress, id) = bundle.getParcelable<DeleteSportModeQuery>(ARG_DATA)
            ?: return Single.just(DeleteSportModeResponse(false))
        Timber.d("Deleting sport mode value")
        val watchBt = suuntoRepositoryService.activeDevices.getWatchBt(macAddress)
        if (watchBt == null) {
            return Single.just(DeleteSportModeResponse(false))
        } else {
            val sportModesDataHolder = watchBt.sportModesDataHolder
                ?: return Single.just(DeleteSportModeResponse(false))
            return sportModesDataHolder.sportModes.deleteValueForId(id)
                .map { DeleteSportModeResponse(true) }
        }
    }
}
