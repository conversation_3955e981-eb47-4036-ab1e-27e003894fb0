package com.suunto.connectivity.sync;

import androidx.annotation.IntDef;
import com.suunto.connectivity.repository.SingleLogbookEntrySyncResultEvent;
import com.suunto.connectivity.watch.SpartanSyncResult;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import rx.Completable;
import rx.Single;

/**
 * WatchSynchronizer is a stateful component handling synchronizing of a watch
 */
public interface WatchSynchronizer {

    class ImportedEntry {
        public final long importedEntryId;
        public final long timeStamp;
        public final int size;

        public ImportedEntry(long importedEntryId, long timeStamp, int size) {
            this.importedEntryId = importedEntryId;
            this.timeStamp = timeStamp;
            this.size = size;
        }
    }

    /**
     * Basic state types for all watch synchronizers
     */
    @Retention(RetentionPolicy.SOURCE)
    @IntDef({
        SyncState.NOT_SYNCING,
        SyncState.STARTING_SYNC,
        SyncState.CHECKING_FOR_NEW_WORKOUTS,
        SyncState.SYNCING_WORKOUTS,
        SyncState.SYNCING_SETTINGS,
        SyncState.SYNCING_ROUTES,
        SyncState.SYNCING_POIS,
        SyncState.CHECKING_FOR_NEW_GPS,
        SyncState.SYNCING_GPS,
        SyncState.SYNCING_TREND_DATA,
        SyncState.SYNCING_SLEEP,
        SyncState.SYNCING_SYSTEM_EVENTS_DATA,
        SyncState.SYNCING_RECOVERY_DATA,
        SyncState.SYNCING_SUUNTO_PLUS_GUIDES,
        SyncState.SYNCING_WEATHER,
        SyncState.SYNCING_TRAINING_ZONE,
        SyncState.SYNCING_ZONE_SENSE,
        SyncState.SYNCING_BURIED_POINT,
    })
    @interface State {
    }

    interface WatchSynchronizerListener {
        void onSingleLogbookEntrySynced(SingleLogbookEntrySyncResultEvent event);
    }

    void setWatchSynchronizerListener(WatchSynchronizerListener listener);
    void removeWatchSynchronizerListener();

    /**
     * @return True if any sync is ongoing currently
     */
    boolean isSynchronizing();

    /**
     * Generic sync error used in a sync error situation when there does not exists other
     * exception.
     */
    public class GenericSyncError extends Exception {
        public GenericSyncError(String message) {
            super(message);
        }
    }

    /**
     * Request synchronization of the watch
     *
     * @param isActivityDataOnly whether to sync only trend and sleep data
     * @param foreground True, if app is on foreground.
     * @return {@link Single} which emits result from the synchronization
     */
    Single<SpartanSyncResult> synchronize(boolean isActivityDataOnly, boolean foreground);

    Single<SpartanSyncResult> synchronizeTrainingZone();

    /**
     * Explicitly starts Route sync independently of full watch sync.
     */
    Completable explicitSyncRoutes(String navigateRouteId);

    /**
     * Explicitly starts POI sync independently of full watch sync.
     */
    Completable explicitSyncPOIs();

    /**
     * Explicitly start 247 data sync independently of full watch sync.
     * @return Sync result
     */
    Single<SpartanSyncResult> explicitSync247();

    /**
     * Explicitly starts SuuntoPlus guide sync independently of full watch sync.
     */
    Completable explicitSyncSuuntoPlusGuides();

    /**
     * Runs right after watch is connected for the first time after pairing.
     */
    Completable onInitialConnectCompletable();

    /**
     * Set imported entry id to synchronizer. This will make synchronizer to behave like
     * imported were coming from the watch, althougt it's been created by WorkoutImporter.kt.
     *
     * @param importedEntry
     */
    void setImportedEntry(ImportedEntry importedEntry);

    Boolean supportsClimbGuidance();

    int getMaxRoutePointsAllowed();
}
