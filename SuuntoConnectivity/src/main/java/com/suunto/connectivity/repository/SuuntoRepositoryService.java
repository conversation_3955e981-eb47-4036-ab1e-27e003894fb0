package com.suunto.connectivity.repository;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Notification;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.content.pm.ServiceInfo;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.Messenger;
import android.os.Parcelable;
import android.os.RemoteException;
import android.text.TextUtils;
import android.widget.Toast;
import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.LifecycleService;
import com.google.gson.Gson;
import com.movesense.mds.Logger;
import com.squareup.moshi.Moshi;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.di.initializer.POISyncInitializer;
import com.stt.android.eventtracking.EventTracker;
import com.stt.android.logging.TimberInMemoryTree;
import com.stt.android.timeline.TimelineResourceLocalDataSource;
import com.stt.android.timeline.WeChatTimelineResourceLocalDataSource;
import com.stt.android.utils.BluetoothUtils;
import com.stt.android.utils.LocationPermissionsKt;
import com.stt.android.utils.NearbyDevicesUtilsKt;
import com.suunto.connectivity.BuildConfig;
import com.suunto.connectivity.ConnectivityDeviceApiBaseUrl;
import com.suunto.connectivity.R;
import com.suunto.connectivity.ScLib;
import com.suunto.connectivity.StOkHttpClient;
import com.suunto.connectivity.SuuntoResponseProducer;
import com.suunto.connectivity.SuuntoServicePreferences;
import com.suunto.connectivity.battery.BatteryProvider;
import com.suunto.connectivity.battery.ChargingStateProvider;
import com.suunto.connectivity.battery.UsbCableStateProvider;
import com.suunto.connectivity.debug.DebugProducer;
import com.suunto.connectivity.files.DrtLogsFilesProvider;
import com.suunto.connectivity.ota.UpdateOtaManualDownloadFlagProducer;
import com.suunto.connectivity.repository.commands.InstallSelectedFirmwareResponse;
import com.suunto.connectivity.routes.RouteResource;
import com.suunto.connectivity.watch.navigate.NavigationMdsApi;
import com.suunto.connectivity.watch.navigate.NavigationResponseProducer;
import com.suunto.connectivity.routes.RouteMdsApi;
import com.suunto.connectivity.runsportmodes.RunSportModesMdsApi;
import com.suunto.connectivity.runsportmodes.RunSportModesProducer;
import com.suunto.connectivity.files.FilesProvider;
import com.suunto.connectivity.findphone.FindPhoneModel;
import com.suunto.connectivity.firmware.FirmwareInformationInterface;
import com.suunto.connectivity.gps.GpsDataResource;
import com.suunto.connectivity.gps.GpsFileManager;
import com.suunto.connectivity.hooks.OnDeviceConnectedHook;
import com.suunto.connectivity.hrintensityzones.HrIntensityZonesMdsApi;
import com.suunto.connectivity.hrintensityzones.HrIntensityZonesModel;
import com.suunto.connectivity.hrintensityzones.HrIntensityZonesProducer;
import com.suunto.connectivity.legacy.LegacyBleCentral;
import com.suunto.connectivity.location.FusionLocationResource;
import com.suunto.connectivity.location.GpsLocationProvider;
import com.suunto.connectivity.log.LogHelper;
import com.suunto.connectivity.mediacontrols.MediaControlsResource;
import com.suunto.connectivity.mediacontrols.callback.MediaControlListener;
import com.suunto.connectivity.mediacontrols.domain.MediaNotificationActionRequest;
import com.suunto.connectivity.ngBleManager.IncomingDataPacketizer;
import com.suunto.connectivity.ngBleManager.NgBLECentral;
import com.suunto.connectivity.ngBleManager.NgBleManager;
import com.suunto.connectivity.notifications.AncsNotificationDevice;
import com.suunto.connectivity.notifications.AncsService;
import com.suunto.connectivity.notifications.MdsNotificationDevice;
import com.suunto.connectivity.notifications.NotificationProducer;
import com.suunto.connectivity.notifications.NotificationStateModel;
import com.suunto.connectivity.notifications.NotificationStateRepository;
import com.suunto.connectivity.notifications.PostNotificationFilter;
import com.suunto.connectivity.notifications.SetPredefinedRepliesResponseProducer;
import com.suunto.connectivity.offlinemaps.OfflineMapsProvider;
import com.suunto.connectivity.offlinemusic.OfflineMusicProducer;
import com.suunto.connectivity.ota.WatchOtaCheckModel;
import com.suunto.connectivity.poi.POIsResponseProducer;
import static com.suunto.connectivity.repository.RepositoryConfiguration.LOGBOOK_ENTRIES_FOLDER;
import com.suunto.connectivity.repository.commands.AddNotificationsPackageQuery;
import com.suunto.connectivity.repository.commands.AddNotificationsPackageResponse;
import com.suunto.connectivity.repository.commands.Broadcast;
import com.suunto.connectivity.repository.commands.CheckForOtaUpdatesResponse;
import com.suunto.connectivity.repository.commands.ConnectResponse;
import com.suunto.connectivity.repository.commands.DisableNotificationsPackageQuery;
import com.suunto.connectivity.repository.commands.DisableNotificationsPackageResponse;
import com.suunto.connectivity.repository.commands.DisconnectQuery;
import com.suunto.connectivity.repository.commands.DisconnectResponse;
import com.suunto.connectivity.repository.commands.EmptyResponse;
import com.suunto.connectivity.repository.commands.EnableNotificationsPackageQuery;
import com.suunto.connectivity.repository.commands.EnableNotificationsPackageResponse;
import com.suunto.connectivity.repository.commands.ErrorInOtaUpdateParametersResponse;
import com.suunto.connectivity.repository.commands.FirmwareTransferStartResponse;
import com.suunto.connectivity.repository.commands.Get247ActivityValueQuery;
import com.suunto.connectivity.repository.commands.Get247ActivityValueResponse;
import com.suunto.connectivity.repository.commands.Get247HrEnabledQuery;
import com.suunto.connectivity.repository.commands.Get247HrEnabledResponse;
import com.suunto.connectivity.repository.commands.Get247TargetQuery;
import com.suunto.connectivity.repository.commands.Get247TargetResponse;
import com.suunto.connectivity.repository.commands.GetActiveDevicesResponse;
import com.suunto.connectivity.repository.commands.GetCoachEnabledQuery;
import com.suunto.connectivity.repository.commands.GetCoachEnabledResponse;
import com.suunto.connectivity.repository.commands.GetHrvEnabledQuery;
import com.suunto.connectivity.repository.commands.GetHrvEnabledResponse;
import com.suunto.connectivity.repository.commands.GetKnownNotificationsResponse;
import com.suunto.connectivity.repository.commands.GetLogsQuery;
import com.suunto.connectivity.repository.commands.GetLogsResponse;
import com.suunto.connectivity.repository.commands.GetOrSetSettingsFileResponse;
import com.suunto.connectivity.repository.commands.GetSettingsFileQuery;
import com.suunto.connectivity.repository.commands.GetSleepTrackingModeQuery;
import com.suunto.connectivity.repository.commands.GetSleepTrackingModeResponse;
import com.suunto.connectivity.repository.commands.GetSpO2NightlyEnabledQuery;
import com.suunto.connectivity.repository.commands.GetSpO2NightlyEnabledResponse;
import com.suunto.connectivity.repository.commands.GetWeeklyTargetDurationQuery;
import com.suunto.connectivity.repository.commands.GetWeeklyTargetDurationResponse;
import com.suunto.connectivity.repository.commands.GoalType;
import com.suunto.connectivity.repository.commands.ImportWorkoutFromFileQuery;
import com.suunto.connectivity.repository.commands.ImportWorkoutFromFileResponse;
import com.suunto.connectivity.repository.commands.MarkAsSyncedQuery;
import com.suunto.connectivity.repository.commands.OTAUpdateActionQuery;
import com.suunto.connectivity.repository.commands.OTAUpdateActionResponse;
import com.suunto.connectivity.repository.commands.OTAUpdateActivatedResponse;
import com.suunto.connectivity.repository.commands.OTAUpdateDisabledResponse;
import com.suunto.connectivity.repository.commands.PairQuery;
import com.suunto.connectivity.repository.commands.PostNotificationQuery;
import com.suunto.connectivity.repository.commands.RegisterClientResponse;
import com.suunto.connectivity.repository.commands.RemoveNotificationQuery;
import com.suunto.connectivity.repository.commands.ReportAppProcessForegroundQuery;
import com.suunto.connectivity.repository.commands.ResetConnectionQuery;
import com.suunto.connectivity.repository.commands.ResetConnectionResponse;
import com.suunto.connectivity.repository.commands.Response;
import com.suunto.connectivity.repository.commands.ServiceStabilityResponse;
import com.suunto.connectivity.repository.commands.Set247HrEnabledQuery;
import com.suunto.connectivity.repository.commands.Set247HrEnabledResponse;
import com.suunto.connectivity.repository.commands.Set247TargetQuery;
import com.suunto.connectivity.repository.commands.Set247TargetResponse;
import com.suunto.connectivity.repository.commands.SetCoachEnabledQuery;
import com.suunto.connectivity.repository.commands.SetCoachEnabledResponse;
import com.suunto.connectivity.repository.commands.SetHrvEnabledQuery;
import com.suunto.connectivity.repository.commands.SetHrvEnabledResponse;
import com.suunto.connectivity.repository.commands.SetSettingsFileQuery;
import com.suunto.connectivity.repository.commands.SetSleepTrackingModeQuery;
import com.suunto.connectivity.repository.commands.SetSleepTrackingModeResponse;
import com.suunto.connectivity.repository.commands.SetSpO2NightlyEnabledQuery;
import com.suunto.connectivity.repository.commands.SetSpO2NightlyEnabledResponse;
import com.suunto.connectivity.repository.commands.SetWeeklyTargetDurationQuery;
import com.suunto.connectivity.repository.commands.SetWeeklyTargetDurationResponse;
import com.suunto.connectivity.repository.commands.SleepTrackingMode;
import com.suunto.connectivity.repository.commands.StartLoggingQuery;
import com.suunto.connectivity.repository.commands.StartLoggingResponse;
import com.suunto.connectivity.repository.commands.StopLoggingResponse;
import com.suunto.connectivity.repository.commands.StopOtaUpdateResponse;
import com.suunto.connectivity.repository.commands.SyncDeviceQuery;
import com.suunto.connectivity.repository.commands.SyncDeviceResponse;
import com.suunto.connectivity.repository.commands.SyncTrainingZoneQuery;
import com.suunto.connectivity.repository.commands.SyncTrainingZoneResponse;
import com.suunto.connectivity.repository.commands.UnpairQuery;
import com.suunto.connectivity.repository.commands.UnpairResponse;
import com.suunto.connectivity.repository.commands.WatchStateUpdateBroadcast;
import com.suunto.connectivity.repository.entities.AskoUserSettings;
import com.suunto.connectivity.repository.stateMachines.connectionStateMachine.SyncLogic;
import com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine.FirmwareFileUtils;
import com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine.FirmwareUpdateStateMachine;
import com.suunto.connectivity.repository.workoutFileImport.WorkoutImporter;
import com.suunto.connectivity.routes.PostRoutesQuery;
import com.suunto.connectivity.routes.PostRoutesResponse;
import com.suunto.connectivity.routes.RoutesResponseProducer;
import com.suunto.connectivity.sdsmanager.MdsRx;
import com.suunto.connectivity.sdsmanager.SdsBleAddressMap;
import com.suunto.connectivity.settings.SetAppInfoProducer;
import com.suunto.connectivity.settings.SetUserSettingsProducer;
import com.suunto.connectivity.settings.Setting;
import com.suunto.connectivity.settings.SettingsResource;
import com.suunto.connectivity.settings.SetupWearDirectionProducer;
import com.suunto.connectivity.settings.UserSettingModel;
import com.suunto.connectivity.setuppreference.SetupPreferenceProducer;
import com.suunto.connectivity.sportmodes.SportModesProducer;
import com.suunto.connectivity.sportsappsettings.LockSportsAppSettingsProvider;
import com.suunto.connectivity.sportsappsettings.ZappPluginDirectoryProvider;
import com.suunto.connectivity.suuntoconnectivity.BleServiceDeviceInterface;
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityClient;
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityClientImpl;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType;
import com.suunto.connectivity.suuntoconnectivity.utils.BtStateMonitor;
import com.suunto.connectivity.suuntoplusguides.SyncSuuntoPlusGuidesResponseProducer;
import com.suunto.connectivity.sync.AlreadySynchronizingException;
import com.suunto.connectivity.sync.SyncResultService;
import com.suunto.connectivity.sync.SynchronizerFinalizer;
import com.suunto.connectivity.sync.SynchronizerStorage;
import com.suunto.connectivity.sync.WatchBusyException;
import com.suunto.connectivity.sync.WatchNotConnectedException;
import com.suunto.connectivity.sync.listener.WatchTriggeredSyncListener;
import com.suunto.connectivity.trainingzone.TrainingZoneResource;
import com.suunto.connectivity.util.BatteryUtils;
import com.suunto.connectivity.util.FileUtils;
import com.suunto.connectivity.util.NotificationSettingsHelper;
import com.suunto.connectivity.util.NotificationSettingsStorage;
import com.suunto.connectivity.util.SupportedDevices;
import com.suunto.connectivity.util.workqueue.WorkQueue;
import com.suunto.connectivity.voicefeedback.VoiceFeedbackModule;
import com.suunto.connectivity.watch.AmbitBt;
import com.suunto.connectivity.watch.AmbitSynchronizer;
import com.suunto.connectivity.watch.BluetoothAdapterWrapper;
import com.suunto.connectivity.watch.DataLayerDeviceBt;
import com.suunto.connectivity.watch.EonBt;
import com.suunto.connectivity.watch.EonSynchronizer;
import com.suunto.connectivity.watch.RunBt;
import com.suunto.connectivity.watch.RxSchedulerProvider;
import com.suunto.connectivity.watch.SpartanBt;
import com.suunto.connectivity.watch.SpartanSettings;
import com.suunto.connectivity.watch.SpartanSyncResult;
import com.suunto.connectivity.watch.SpartanSynchronizer;
import com.suunto.connectivity.watch.SystemEventReader;
import com.suunto.connectivity.watch.WatchBt;
import com.suunto.connectivity.watch.WatchConnector;
import com.suunto.connectivity.watch.activitydata.ActivityDataHolder;
import com.suunto.connectivity.watch.buscode.AlipayBusCodeResource;
import com.suunto.connectivity.watch.navigate.NavigateResource;
import com.suunto.connectivity.watch.time.SetFirstDayOfTheWeekResponseProducer;
import com.suunto.connectivity.watchcontrol.WatchControlMdsApi;
import com.suunto.connectivity.watchcontrol.WatchControlProducer;
import com.suunto.connectivity.weather.WeatherUpdateModel;
import com.suunto.connectivity.widget.GetWidgetsResponseProducer;
import com.suunto.connectivity.widget.SetWidgetsResponseProducer;
import com.suunto.connectivity.widget.WidgetsMdsApi;
import com.suunto.connectivity.widget.WidgetsWatchApi;
import com.suunto.connectivity.wifi.WifiProvider;
import dagger.hilt.android.AndroidEntryPoint;
import hu.akarnokd.rxjava.interop.RxJavaInterop;
import io.reactivex.disposables.CompositeDisposable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.inject.Inject;
import okhttp3.OkHttpClient;
import rx.Completable;
import rx.Observable;
import rx.Scheduler;
import rx.Single;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.exceptions.Exceptions;
import rx.functions.Actions;
import rx.functions.Func1;
import rx.schedulers.Schedulers;
import rx.subjects.BehaviorSubject;
import rx.subjects.Subject;
import rx.subscriptions.CompositeSubscription;
import timber.log.Timber;

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@AndroidEntryPoint
public class SuuntoRepositoryService extends LifecycleService {
    public static final int MSG_CONFIGURATION = 0;
    public static final int MSG_RESPONSE = 1;
    public static final int MSG_PAIR = 2;
    public static final int MSG_REGISTER_CLIENT = 3;
    public static final int MSG_BROADCAST = 4;
    public static final int MSG_DISCONNECT = 5;
    public static final int MSG_SYNC_DEVICE = 6;
    public static final int MSG_GET_ACTIVE_DEVICES = 7;
    public static final int MSG_UNPAIR = 8;

    // Notification messages
    public static final int MSG_ENABLE_NOTIFICATIONS = 10;
    public static final int MSG_DISABLE_NOTIFICATIONS = 11;
    public static final int MSG_POST_NOTIFICATION = 12;
    public static final int MSG_REMOVE_NOTIFICATION = 13;
    public static final int MSG_GET_KNOWN_NOTIFICATIONS = 14;
    public static final int MSG_ADD_NOTIFICATIONS_PACKAGE = 15;
    public static final int MSG_ENABLE_NOTIFICATIONS_PACKAGE = 16;
    public static final int MSG_DISABLE_NOTIFICATIONS_PACKAGE = 17;

    // Logging messages
    public static final int MSG_START_LOGGING = 20;
    public static final int MSG_STOP_LOGGING = 21;
    public static final int MSG_GET_LOGS = 22;

    // Coach settings messages
    public static final int MSG_GET_COACH_ENABLED = 23;
    public static final int MSG_SET_COACH_ENABLED = 24;

    // Clear connection instability.
    public static final int MSG_CLEAR_CONNENTION_INSTABILITY = 25;

    // Weekly target messages
    public static final int MSG_GET_WEEKLY_TARGET_DURATION = 26;
    public static final int MSG_SET_WEEKLY_TARGET_DURATION = 27;

    // Daily activiy goal messages
    public static final int MSG_GET_247_TARGET = 28;
    public static final int MSG_SET_247_TARGET = 29;

    // Daily activity current values
    public static final int MSG_GET_247_DAILY_ACTIVITY_VALUE = 30;

    // Sleep tracking messages
    public static final int MSG_GET_SLEEP_TRACKING_MODE = 31;
    public static final int MSG_SET_SLEEP_TRACKING_MODE = 32;

    // Mark as synced
    public static final int MSG_MARK_AS_SYNCED = 33;

    // Sport modes
    public static final int MSG_GET_SPORT_MODES = 34;
    public static final int MSG_SET_SPORT_MODES = 35;
    public static final int MSG_DELETE_SPORT_MODES = 36;

    //
    @Deprecated
    public static final int MSG_GET_UNSAFE_STATUS = 37;
    @Deprecated
    public static final int MSG_SET_UNSAFE_STATUS = 38;

    // Routes
    public static final int MSG_POST_ROUTES = 39;

    // Reset connection.
    public static final int MSG_RESET_CONNECTION = 40;

    // Service stability
    public static final int MSG_GET_SERVICE_STABILITY = 41;

    // User logout.
    public static final int MSG_USER_LOGOUT = 42;

    // Report app process foreground.
    public static final int MSG_REPORT_APP_PROCESS_FOREGROUND = 43;

    // Firmware update action.
    public static final int MSG_OTA_UPDATE_ACTION = 45;

    // Routes
    public static final int MSG_POST_POIS = 46;

    // Import workout.
    public static final int MSG_IMPORT_WORKOUT = 47;

    // Get and Set settings file.
    public static final int MSG_GET_SETTINGS_FILE = 48;
    public static final int MSG_SET_SETTINGS_FILE = 49;

    public static final int MSG_SET_PREDEFINED_REPLIES = 50;
    // SuuntoPlus guides
    public static final int MSG_SYNC_SUUNTO_PLUS_GUIDES = 51;

    public static final int MSG_SET_FIRST_DAY_OF_THE_WEEK = 52;

    // Files
    public static final int MSG_GET_FILE_LIST = 53;
    public static final int MSG_GET_FILE = 54;
    public static final int MSG_PUT_FILE = 55;
    public static final int MSG_DELETE_FILE = 56;

    // Watch widgets
    public static final int MSG_GET_WIDGETS = 57;
    public static final int MSG_SET_WIDGETS = 58;

    public static final int MSG_SHOW_INCOMING_CALL_NOTIFICATION = 59;
    public static final int MSG_REMOVE_INCOMING_CALL_NOTIFICATION = 60;
    public static final int MSG_SHOW_CALL_REJECTED_OR_MISSED_NOTIFICATION = 61;

    // Wi-Fi
    public static final int MSG_GET_SAVED_WIFI_NETWORKS_COUNT = 62;
    public static final int MSG_GET_SAVED_WIFI_NETWORKS = 63;
    public static final int MSG_SAVE_WIFI_NETWORK = 64;
    public static final int MSG_SCAN_AVAILABLE_WIFI_NETWORKS = 65;
    public static final int MSG_FORGET_WIFI_NETWORK = 66;
    public static final int MSG_SET_WIFI_GENERAL_SETTINGS = 67;
    public static final int MSG_SET_OFFLINE_MAPS_URL = 68;
    public static final int MSG_SET_AUTHORIZATION_TOKEN = 69;
    public static final int MSG_NOTIFY_AREA_SELECTION_CHANGED = 70;
    public static final int MSG_NOTIFY_AREA_UNDER_DOWNLOAD_DELETED = 71;
    public static final int MSG_OBSERVE_WIFI_ENABLED = 72;
    public static final int MSG_SET_WIFI_ENABLED = 73;
    public static final int MSG_ENABLE_INBOX_WIFI = 74;
    public static final int MSG_NUMBER_OF_AREAS = 75;

    // Battery
    public static final int MSG_GET_BATTERY_LEVEL = 76;
    public static final int MSG_GET_CHARGING_STATE = 77;
    public static final int MSG_GET_USB_CABLE_STATE = 78;

    // SuuntoPlus™ sports app settings
    public static final int MSG_LOCK_UNLOCK_SPORTS_APP = 79;
    public static final int MSG_GET_ZAPP_PLUG_IN_DIRECTORY = 80;
    public static final int MSG_SYNC_TRAINING_ZONE = 81;

    // Notification
    public static final int MSG_PUT_NOTIFICATION_STATE = 82;
    public static final int MSG_PUT_NOTIFICATION_CATEGORY_STATE = 83;
    public static final int MSG_GET_NOTIFICATION_STATE = 84;
    public static final int MSG_DEBUG_SET_LOCATION_COORDINATES = 85;

    // SpO2 nightly
    public static final int MSG_GET_SPO2_NIGHTLY_ENABLED = 86;
    public static final int MSG_SET_SPO2_NIGHTLY_ENABLED = 87;

    // HRV
    public static final int MSG_GET_HRV_ENABLED = 88;
    public static final int MSG_SET_HRV_ENABLED = 89;

    // 247 HR
    public static final int MSG_GET_247_HR_ENABLED = 90;
    public static final int MSG_SET_247_HR_ENABLED = 91;

    public static final int MSG_GET_CURRENT_WATCHFACE_ID = 92;

    public static final int MSG_SUBSCRIBE_SETUP_PREFERENCE_CANCEL = 93;
    public static final int MSG_GET_SETUP_PREFERENCE_STATE = 94;
    public static final int MSG_PUT_SETUP_PREFERENCE_STATE = 95;

    public static final int MSG_GET_DEFAULT_WATCHFACE_ID = 96;
    public static final int MSG_SET_CURRENT_WATCHFACE_ID = 97;

    public static final int MSG_SET_APP_INFO = 98;
    public static final int MSG_SET_WARE_DIRECTION = 99;

    // offline music
    public static final int MSG_GET_MUSIC_PLAY_LISTS = 100;
    public static final int MSG_GET_MUSIC_ALL_SONG_DETAIL = 101;
    public static final int MSG_GET_MUSIC_PLAYLIST_DETAIL = 102;
    public static final int MSG_GET_MUSIC_INFO = 103;
    public static final int MSG_ADD_OR_UPDATE_PLAYLIST = 104;
    public static final int MSG_DELETE_PLAYLIST = 105;
    public static final int MSG_SORT_PLAYLISTS = 106;
    public static final int MSG_SUBSCRIBE_MUSIC_UPDATE = 107;

    // Watch log files
    public static final int MSG_GET_LOG_FILES = 108;

    // Hr & intensity zones
    public static final int MSG_SET_HR_INTENSITY_ZONES = 109;

    // Run sport modes
    public static final int MSG_GET_RECENT_SPORTS = 110;
    public static final int MSG_GET_ALL_SPORTS = 111;
    public static final int MSG_GET_TRAINING_MODE_TEMPLATE_LIST = 112;
    public static final int MSG_GET_TRAINING_MODE_TEMPLATE = 113;
    public static final int MSG_GET_TRAINING_MODE_LIST = 114;
    public static final int MSG_GET_TRAINING_MODE = 115;
    public static final int MSG_POST_TRAINING_MODE = 116;
    public static final int MSG_PUT_TRAINING_MODE = 117;
    public static final int MSG_DEL_TRAINING_MODE = 118;
    public static final int MSG_GET_DATA_SCREEN_TEMPLATE_LIST = 119;
    public static final int MSG_GET_DATA_SCREEN_LIST = 120;
    public static final int MSG_POST_DATA_SCREEN = 121;
    public static final int MSG_PUT_DATA_SCREEN = 122;
    public static final int MSG_DEL_DATA_SCREEN = 123;
    public static final int MSG_POST_COMPETITION_INFO_TARGET = 124;

    public static final int MSG_POST_COMPETITION_SAMPLES_TARGET = 125;
    public static final int MSG_GET_OFFLINE_MUSIC_VERSION = 127;

    // user profile
    public static final int MSG_SET_USER_GENDER = 128;
    public static final int MSG_SET_USER_WEIGHT = 129;
    public static final int MSG_SET_USER_BIRTH_YEAR = 130;
    public static final int MSG_SET_USER_HEIGHT = 131;
    public static final int MSG_SET_USER_MAX_HR = 132;
    public static final int MSG_SET_USER_REST_HR = 133;
    public static final int MSG_SET_USER_UNIT_SYSTEM = 134;

    public static final int MSG_START_QUICK_NAVIGATE = 135;

    public static final int MSG_NAVIGATE_BY_ROUTE = 136;

    public static final int MSG_DEVICE_INFO_NAVIGATE = 137;

    public static final int MSG_UPDATE_OTA_MANUAL_DOWNLOAD_FLAG = 138;

    public static final int MSG_FIND_WATCH_CONTROL = 139;
    public static final int MSG_SUBSCRIBE_FIND_WATCH_CONTROL = 140;

    public static final int MSG_GET_DRT_LOG_FILES = 141;

    public static final String ACTION_WEATHER_UPDATED = "com.stt.android.WEATHER_UPDATED";

    private static final String LOG_FILE_PREFIX = "screpo_";

    //flag to check whether service is running
    public static AtomicBoolean isServiceRunning = new AtomicBoolean(false);

    private static final boolean DEBUG = true;

    // Count number of service starts
    private static int serviceStarts = 0;

    // Default reset connection timeout in seconds.
    public static final int DEFAULT_RESET_CONNECTION_TIMEOUT = 5;

    // Timeout in milliseconds between attempts to set connectivity data collected user property.
    private static final int SET_CONNECTIVITY_DATA_COLLECTED_TIMEOUT_MS = 20000;

    @NonNull
    private static final String KEY_START_FOREGROUND_LOCATION = "KEY_START_FOREGROUND_LOCATION";

    @NonNull
    private static final String KEY_START_FOREGROUND_CONNECTED_DEVICE = "KEY_START_FOREGROUND_CONNECTED_DEVICE";

    /**
     * Target we publish for clients to send messages to IncomingHandler.
     */
    final Messenger messenger = new Messenger(new IncomingHandler());

    final Gson gson = GsonFactory.buildGson();
    private boolean foreground = false;

    @Inject
    MdsRx mdsRx;

    @Inject
    @SuuntoServicePreferences
    SharedPreferences preferences;

    @Inject
    RepositoryConfiguration configuration;

    @Inject
    SynchronizerStorage synchronizerStorage;

    @Inject
    SynchronizerFinalizer synchronizerFinalizer;

    @Inject
    SpartanSynchronizer.Injection synchronizerInjectionSpartan;

    @Inject
    AmbitSynchronizer.Injection synchronizerInjectionAmbit;

    @Inject
    EonSynchronizer.Injection synchronizerInjectionEon;

    @Inject
    SystemEventReader systemEventReader;

    @Inject
    SupportedDevices supportedDevices;

    @Inject
    ConnectSuccessRateCounter connectSuccessRateCounter;

    @Inject
    Moshi moshi;

    @Inject
    FirmwareInformationInterface firmwareInformationInterface;

    @Inject
    POISyncInitializer poiSyncInitializer;

    @Inject
    MediaControlsResource mediaControlsResource;

    @Inject
    TimelineResourceLocalDataSource timelineResourceLocalDataSource;

    @Inject
    WeChatTimelineResourceLocalDataSource weChatTimelineResourceLocalDataSource;

    @Inject
    @ConnectivityDeviceApiBaseUrl
    String deviceApiBaseUrl;

    @Inject
    OnDeviceConnectedHook onDeviceConnectedHook;

    @Inject
    TimberInMemoryTree timberInMemoryTree;

    @Inject
    WeatherUpdateModel weatherUpdateModel;

    @Inject
    WatchTriggeredSyncListener watchTriggeredSyncListener;

    @Inject
    VoiceFeedbackModule voiceFeedbackModule;

    @Inject
    GpsLocationProvider gpsLocationProvider;

    @Inject
    FindPhoneModel findPhoneModel;

    @Inject
    TrainingZoneResource trainingZoneResource;

    @Inject
    RouteResource routeResource;

    @Inject
    SettingsResource settingsResource;

    @Inject
    NotificationStateModel notificationStateModel;

    @Inject
    NotificationStateRepository notificationStateRepository;

    @Inject
    @StOkHttpClient
    OkHttpClient stOkHttpClient;

    @Inject
    AlipayBusCodeResource alipayBusCodeResource;

    @Inject
    NavigateResource navigateResource;

    @Inject
    GpsDataResource gpsDataResource;

    @Inject
    GpsFileManager gpsFileManager;

    @Inject
    WatchOtaCheckModel watchOtaCheckModel;

    @Inject
    HrIntensityZonesModel hrIntensityZonesModel;

    @Inject
    UserSettingModel userSettingModel;

    @Inject
    EventTracker eventTracker;

    // Active devices, to where connection is automatically kept.
    private ActiveDevices activeDevices;
    private ActiveDeviceStorage deviceStorage;
    /**
     * Rather simple subject that contains the last known state of this service (See
     * {@link ServiceState})
     */
    // Todo: service state needs to have more "ready" states than just one "ServiceState::Ready",
    // Todo: because different service calls can be executed in different "ready" states.
    final Subject<ServiceState, ServiceState> serviceStateSubject =
            BehaviorSubject.create(ServiceState.INITIALIZING).toSerialized();

    /**
     * Rather simple subject that contains the last known state of the connectivity service (See
     * {@link #init()})
     */
    final Subject<ServiceState, ServiceState> connectivityServiceStateSubject =
            BehaviorSubject.create(ServiceState.INITIALIZING).toSerialized();
    /**
     * Keeps track of all current registered clients in thread safe manner.
     */
    final Set<Messenger> clients = Collections.newSetFromMap(new ConcurrentHashMap<>());

    private WatchConnector watchConnector;
    private BluetoothAdapter bluetoothAdapter;
    private BtStateMonitor btStateMonitor;
    private String logDir;
    private Subscription watchStateSubscription;
    private NotificationHandler notificationHandler;
    private NotificationManager notificationManager;
    private BleServiceDeviceInterface bleServiceDeviceInterface;
    private List<SuuntoResponseProducer<? extends Response>> suuntoResponseProducers = new ArrayList<>();
    private SuuntoConnectivityClient suuntoConnectivityClient;
    private LegacyBleCentral legacyBleCentral;
    private FusionLocationResource fusionLocationResource;
    private final Handler initConnectivityDataCollectedUserPropertyHandler = new Handler();
    private CallStateReceiver callStateReceiver;
    /**
     * Static SuuntoRepositoryClient object for ANCS service
     */
    private volatile static SuuntoRepositoryClient clientForAncsService;

    /**
     * Handler thread to be used for tasks which need to be run on separate handler thread.
     */
    // Todo: Is there actually need for separate handler thread?
    private final HandlerThread handlerThread = new HandlerThread
            ("RepositoryServiceHandlerThread");
    private Subscription locationUsageSubscription;

    private final CompositeSubscription subscriptions = new CompositeSubscription();
    private final CompositeDisposable disposables = new CompositeDisposable();

    private final BroadcastReceiver timeAndLocaleChangedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            if (Intent.ACTION_TIME_CHANGED.equals(action) ||
                Intent.ACTION_TIMEZONE_CHANGED.equals(action)) {
                for (WatchBt btDevice : activeDevices.getBtDevices()) {
                    subscriptions.add(btDevice.updateTimeZoneInfo()
                        .onErrorComplete(e -> {
                            Timber.w(e, "Error updating TimeZoneInfo");
                            return true;
                        })
                        .subscribe());
                }
            }
        }
    };

    private final BroadcastReceiver notificationListenerStatusReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            if (AncsService.ACTION_NOTIFICATION_LISTENER_CONNECTED_STATUS_CHANGED.equals(action)) {
                if (intent.getBooleanExtra(AncsService.EXTRA_CONNECTED_STATUS, false)) {
                    mediaControlsResource.launchMediaControlsUpdates();
                } else {
                    mediaControlsResource.stopMediaControlUpdates();
                }
            }
        }
    };

    private final MediaControlListener mediaControlsListener = new MediaControlListener() {
        @Override
        public void onMediaCommand(int id) {
            Collection<WatchBt> btDevices = activeDevices.getBtDevices();
            for (WatchBt btDevice : btDevices) {
                AnalyticsUtils.sendMediaControlEvent(btDevice, id);
                return;
            }
        }

        @Override
        public void onMediaNotificationAction(@NonNull MediaNotificationActionRequest action) {
            notificationManager.handleNotificationAction(action);
        }
    };

    class SpartanBtBuilder {
        @NonNull
        public SpartanBt create(SuuntoBtDevice device) {
            return new SpartanBt(SuuntoRepositoryService.this, device, watchConnector, mdsRx, gson, bluetoothAdapter,
                btStateMonitor, new MdsNotificationDevice(mdsRx, moshi, device.getSerial()),
                synchronizerStorage, gpsFileManager, gpsDataResource, synchronizerInjectionSpartan, supportedDevices,
                timelineResourceLocalDataSource, weChatTimelineResourceLocalDataSource, trainingZoneResource,
                settingsResource, eventTracker);
        }
    }


    class RunBtBuilder {
        @NonNull
        public RunBt create(SuuntoBtDevice device) {
            return new RunBt(SuuntoRepositoryService.this, device, watchConnector, mdsRx, gson, bluetoothAdapter,
                btStateMonitor, new MdsNotificationDevice(mdsRx, moshi, device.getSerial()),
                synchronizerStorage, gpsFileManager, gpsDataResource, synchronizerInjectionSpartan, supportedDevices,
                timelineResourceLocalDataSource, weChatTimelineResourceLocalDataSource, trainingZoneResource,
                settingsResource, eventTracker);
        }
    }

    class AmbitBtBuilder {
        @NonNull
        public AmbitBt create(SuuntoBtDevice device,
            BleServiceDeviceInterface bleServiceDeviceInterface) {
            return new AmbitBt(SuuntoRepositoryService.this, device, watchConnector, mdsRx, gson, moshi, bluetoothAdapter,
                btStateMonitor, new AncsNotificationDevice(bleServiceDeviceInterface),
                synchronizerStorage, gpsFileManager, gpsDataResource, synchronizerInjectionAmbit, supportedDevices);
        }
    }

    class EonComputerBtBuilder {
        @NonNull
        public EonBt create(SuuntoBtDevice device) {
            return new EonBt(SuuntoRepositoryService.this, device, watchConnector, mdsRx, gson, bluetoothAdapter,
                btStateMonitor, null, synchronizerStorage, synchronizerInjectionEon,
                supportedDevices);
        }
    }

    class EonD5BtBuilder {
        @NonNull
        public EonBt create(SuuntoBtDevice device) {
            return new EonBt(SuuntoRepositoryService.this, device, watchConnector, mdsRx, gson, bluetoothAdapter,
                btStateMonitor, new MdsNotificationDevice(mdsRx, moshi, device.getSerial()),
                synchronizerStorage, synchronizerInjectionEon, supportedDevices);
        }
    }

    class DataLayerDeviceBtBuilder {
        @NonNull
        public DataLayerDeviceBt create(SuuntoBtDevice device) {
            return new DataLayerDeviceBt(SuuntoRepositoryService.this, device, watchConnector, mdsRx, gson, bluetoothAdapter,
                btStateMonitor, null,
                synchronizerStorage, gpsFileManager, gpsDataResource, synchronizerInjectionSpartan,
                deviceStorage, supportedDevices, timelineResourceLocalDataSource,
                weChatTimelineResourceLocalDataSource, trainingZoneResource, settingsResource, eventTracker);
        }
    }

    public static void startService(Context appContext, boolean startedFromForeground, String source) {
        if (!BluetoothUtils.isBluetoothSupported(appContext)) {
            Timber.v("Bluetooth not supported. Not starting SuuntoRepositoryService.");
            return;
        }

        // Starting from Android 14:
        // 1) Foreground location service can only be started, if the app has foreground location
        //    permissions granted, AND the service is started in the foreground.
        // 2) Foreground connected device service can only be started, if the app has nearby
        //    permissions granted.
        //
        // Note that even if we have background location permission, we still can't start it from
        // background until at least #11136 is merged.
        // https://developer.android.com/about/versions/14/changes/fgs-types-required#location
        // https://developer.android.com/develop/background-work/services/foreground-services#background-start-restriction-exemptions
        boolean startForegroundLocation = true;
        boolean startForegroundConnectedDevice = true;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            startForegroundLocation = startedFromForeground &&
                LocationPermissionsKt.isForegroundLocationPermissionGranted(appContext);
            startForegroundConnectedDevice = NearbyDevicesUtilsKt.isNearbyDevicesPermissionGranted(appContext);
        }
        Timber.w("Starting from %s", source);
        Timber.w("Starting foreground location (%b), foreground connected device (%b)",
            startForegroundLocation, startForegroundConnectedDevice);

        Intent intent = new Intent(appContext, SuuntoRepositoryService.class)
            .putExtra(KEY_START_FOREGROUND_LOCATION, startForegroundLocation)
            .putExtra(KEY_START_FOREGROUND_CONNECTED_DEVICE, startForegroundConnectedDevice);

        // We only start foreground service if:
        // 1) It run on Oreo (Android 8) or above
        // AND 2) We should start at least either foreground location OR foreground connected device
        boolean startForegroundService = startForegroundLocation || startForegroundConnectedDevice;
        if (startForegroundService) {
            appContext.startForegroundService(intent);
        } else {
            appContext.startService(intent);
        }
    }

    @Override
    public void onCreate() {
        // Create SuuntoConnectivityClient
        serviceStarts++;
        Timber.v("Initializing suunto connectivity client: %d", serviceStarts);
        suuntoConnectivityClient = new SuuntoConnectivityClientImpl(getApplicationContext());
        // LegacyBleCentral must be instantiated before Obi2 to be able to register BleCentral
        // replacement
        legacyBleCentral = new LegacyBleCentral(suuntoConnectivityClient);
        super.onCreate();
        // reset poi sync status asap
        poiSyncInitializer.init(getApplication());
        refreshUserAnalyticsUUID();
        ConnectSuccessRateCounter.ResetReason resetReason =
            AnalyticsUtils.checkServiceStartReason(this, preferences, gson, !BuildConfig.DEBUG);
        handlerThread.start();
        notificationHandler = new NotificationHandler(getApplicationContext());

        // Display a foreground notification that should tell Android that we want to be alive
        showForegroundNotification(false, false);
        init();
        subscriptions.add(waitConnectivityClient()
            .andThen(activeDevices.geBtDevicesSingle())
            .flatMap(watchBts -> {
                for (WatchBt watchBt : watchBts) {
                    connectSuccessRateCounter.resetCounterIfNeeded(resetReason,
                        watchBt.getMacAddress());
                    startObservingDevice(watchBt).subscribe();
                }
                return Single.just(watchBts);
            }).toCompletable()
            .andThen(activeDevices.reconnectDevicesOnServiceStart())
            .doOnCompleted(() -> serviceStateSubject.onNext(ServiceState.READY))
            .subscribeOn(Schedulers.io())
            .subscribe(() -> Timber.v(
                "All recovered devices will be reconnected. Syncing will after connection"),
                throwable -> Timber.e(throwable, "Unable to connect to devices"))
        );
        initConnectivityDataCollectedUserProperty();
    }

    private void refreshUserAnalyticsUUID() {
        disposables.add(
            io.reactivex.Completable.fromAction(() -> {
                AskoUserSettings askoUserSettings = synchronizerStorage.readUserSettingsToWatch();
                if (askoUserSettings != null) {
                    String analyticsUUID = askoUserSettings.getAnalyticsUUID();
                    if (!TextUtils.isEmpty(analyticsUUID)) {
                        Timber.d("Updating analytics UUID in connectivity: %s", analyticsUUID);
                        AnalyticsUtils.setAnalyticsUUID(analyticsUUID);
                    }
                }
            }).subscribeOn(io.reactivex.schedulers.Schedulers.io())
                .subscribe(() -> {
                }, (e) -> Timber.w(e, "Error in refreshUserAnalyticsUUID")));
    }

    /**
     * Init AnalyticsUserProperty.SUUNTO_DEBUG_ANDROID_CONNECTIVITY_DATA_COLLECTED property when
     * analytics is initialized. Handler will internally call this method again if analytics was not
     * initialized.
     */
    private void initConnectivityDataCollectedUserProperty() {
        initConnectivityDataCollectedUserPropertyHandler.postDelayed(() -> {
            if (AnalyticsUtils.getsAnalyticsHook() != null) {
                if (AnalyticsUtils.getsAnalyticsHook().analyticsInitialised()) {
                    AnalyticsUtils.trackDebugAndroidConnectivityDataCollected();
                } else {
                    // Recursively try to init again later.
                    initConnectivityDataCollectedUserProperty();
                }
            }
        }, SET_CONNECTIVITY_DATA_COLLECTED_TIMEOUT_MS);
    }

    /**
     * @return a {@link Completable} that waits that connectivity service starts, completes if it
     * started successfully or calls onError if service failed to start.
     */
    Completable waitConnectivityClient() {
        return connectivityServiceStateSubject.distinctUntilChanged()
                // Ignore any intermediate state
                .filter(serviceState ->
                        serviceState == ServiceState.READY || serviceState == ServiceState.FAILED)
                .first().toSingle()
                .flatMapCompletable(serviceState ->
                        serviceState == ServiceState.READY ? Completable.complete()
                                : Completable.error(
                                new IllegalStateException("Unable to start connectivity service")))
                .subscribeOn(Schedulers.computation());
    }

    private void init() {
        Context context = getApplicationContext();
        initRepositoryConfiguration(context);

        subscriptions.add(suuntoConnectivityClient.serviceStateObservable()
            .filter(s -> s)
            .subscribe(
                s -> connectivityServiceStateSubject.onNext(ServiceState.READY),
                t -> connectivityServiceStateSubject.onNext(ServiceState.FAILED)
            )
        );
        bleServiceDeviceInterface = suuntoConnectivityClient;

        NgBLECentral ngBLECentral = new NgBLECentral(suuntoConnectivityClient);

        // Register GPS provider and resource
        fusionLocationResource =
            new FusionLocationResource(context, mdsRx, gpsLocationProvider, preferences);

        subscriptions.add(fusionLocationResource.register().subscribe(
            () -> Timber.v("FusionLocationResource registered"),
            throwable -> Timber.e(throwable, "FusionLocationResource registration failed"))
        );

        subscriptions.add(locationUsageSubscription = fusionLocationResource.hasLocationSubscribers()
            .subscribe(this::setGpsNotification)
        );

        mediaControlsResource.initializeResource();
        mediaControlsResource.setMediaControlListener(mediaControlsListener);

        ContextCompat.registerReceiver(
            this,
            notificationListenerStatusReceiver,
            new IntentFilter(AncsService.ACTION_NOTIFICATION_LISTENER_CONNECTED_STATUS_CHANGED),
            ContextCompat.RECEIVER_EXPORTED
        );

        // Create dependencies for NgBleManager
        IncomingDataPacketizer incomingDataPacketizer = new IncomingDataPacketizer();

        // Create dependencies required by WatchConnectorImpl
        BluetoothManager bluetoothManager =
                (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);

        SdsBleAddressMap sdsBleAddressMap = new SdsBleAddressMap();
        NgBleManager ngBleManager =
            new NgBleManager(sdsBleAddressMap, ngBLECentral, legacyBleCentral,
                incomingDataPacketizer,
                new Handler(handlerThread.getLooper()));

        bluetoothAdapter = bluetoothManager.getAdapter();
        btStateMonitor = new BtStateMonitor(context);
        // Create WatchConnector for SdsManager
        watchConnector =
            new WatchConnector(sdsBleAddressMap, ngBleManager, mdsRx,
                suuntoConnectivityClient,
                new BluetoothAdapterWrapper(bluetoothAdapter),
                new WorkQueue(), btStateMonitor,
                getRxSchedulerProvider());

        deviceStorage = new ActiveDeviceFileStorage(context, gson, new
            SpartanBtBuilder(), new AmbitBtBuilder(), new EonComputerBtBuilder(),
            new EonD5BtBuilder(), new DataLayerDeviceBtBuilder(), suuntoConnectivityClient,
            configuration, new RunBtBuilder());

        ConnectionAnalytics analytics = new ConnectionAnalytics(bluetoothAdapter,
            connectSuccessRateCounter);

        activeDevices =
            new ActiveDevices(context, supportedDevices, synchronizerStorage, synchronizerFinalizer,
                firmwareInformationInterface, configuration, preferences,
                btStateMonitor, bluetoothAdapter, deviceStorage, analytics, onDeviceConnectedHook,
                stOkHttpClient);
        activeDevices.ensureBtPairings();
        activeDevices.handleReportAppProcessForeground(foreground);
        // Create AncsNotificationManager
        NotificationSettingsHelper notificationSettingsHelper = new NotificationSettingsHelper();
        PostNotificationFilter postNotificationFilter = new PostNotificationFilter(this);
        notificationManager = new NotificationManager(this, activeDevices,
            new NotificationSettingsStorage(context), notificationSettingsHelper,
            postNotificationFilter);

        callStateReceiver = new CallStateReceiver(
            context,
            new DefaultContactNameProvider(context),
            TelephonyManagerAndroidConstants.INSTANCE,
            new Handler(Looper.getMainLooper())
        );
        callStateReceiver.addListener(notificationManager);

        if (DEBUG) {
            Logger.setPipeToOSLoggingEnabled(true);
        }

        // Android OS revokes notification permissions for debug APK every time it changes,
        // so here that listener is enabled and disabled which seems to fix this behaviour
        if (context.getResources().getBoolean(R.bool.suunto_connectivity_debug)) {
            Timber.v("Toggling notification listener");
            notificationSettingsHelper.toggleNotificationListener(this);
        }
        SportModesProducer sportModeProducer = new SportModesProducer(this);
        RoutesResponseProducer routesResponseProducer = new RoutesResponseProducer(this);
        POIsResponseProducer poisResponseProducer = new POIsResponseProducer(this);
        SetPredefinedRepliesResponseProducer setPredefinedRepliesResponseProducer =
            new SetPredefinedRepliesResponseProducer(this);
        SyncSuuntoPlusGuidesResponseProducer syncSuuntoPlusGuidesResponseProducer = new SyncSuuntoPlusGuidesResponseProducer(this);
        SetFirstDayOfTheWeekResponseProducer setFirstDayOfTheWeekResponseProducer =
            new SetFirstDayOfTheWeekResponseProducer(this);

        WidgetsWatchApi widgetsWatchApi = new WidgetsMdsApi(mdsRx, moshi);
        GetWidgetsResponseProducer getWidgetsResponseProducer = new GetWidgetsResponseProducer(
            widgetsWatchApi
        );
        SetWidgetsResponseProducer setWidgetsResponseProducer = new SetWidgetsResponseProducer(
            widgetsWatchApi
        );
        suuntoResponseProducers.add(sportModeProducer);
        suuntoResponseProducers.add(routesResponseProducer);
        suuntoResponseProducers.add(poisResponseProducer);
        suuntoResponseProducers.add(setPredefinedRepliesResponseProducer);
        suuntoResponseProducers.add(syncSuuntoPlusGuidesResponseProducer);
        suuntoResponseProducers.add(setFirstDayOfTheWeekResponseProducer);
        suuntoResponseProducers.add(new FilesProvider(this));
        suuntoResponseProducers.add(getWidgetsResponseProducer);
        suuntoResponseProducers.add(setWidgetsResponseProducer);
        suuntoResponseProducers.add(
            new NavigationResponseProducer(this, new NavigationMdsApi(mdsRx, moshi),
                new RouteMdsApi(mdsRx, moshi)));
        suuntoResponseProducers.add(new WifiProvider(this));
        suuntoResponseProducers.add(new OfflineMapsProvider(this));
        suuntoResponseProducers.add(new BatteryProvider(this));
        suuntoResponseProducers.add(new ChargingStateProvider(this));
        suuntoResponseProducers.add(new UsbCableStateProvider(this));
        suuntoResponseProducers.add(new LockSportsAppSettingsProvider(this));
        suuntoResponseProducers.add(new ZappPluginDirectoryProvider(this));
        suuntoResponseProducers.add(new NotificationProducer(this, notificationStateRepository));
        suuntoResponseProducers.add(new DebugProducer(this));
        suuntoResponseProducers.add(new SetupPreferenceProducer(this));
        suuntoResponseProducers.add(new SetAppInfoProducer(this));
        suuntoResponseProducers.add(new SetupWearDirectionProducer(this));
        suuntoResponseProducers.add(new HrIntensityZonesProducer(new HrIntensityZonesMdsApi(mdsRx, moshi)));
        suuntoResponseProducers.add(new OfflineMusicProducer(this));
        suuntoResponseProducers.add(new RunSportModesProducer(this, new RunSportModesMdsApi(mdsRx, moshi)));
        suuntoResponseProducers.add(new SetUserSettingsProducer(this));
        suuntoResponseProducers.add(new UpdateOtaManualDownloadFlagProducer(this));
        suuntoResponseProducers.add(new WatchControlProducer(this, new WatchControlMdsApi(mdsRx, moshi)));
        suuntoResponseProducers.add(new DrtLogsFilesProvider(this));

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Intent.ACTION_TIMEZONE_CHANGED);
        intentFilter.addAction(Intent.ACTION_TIME_CHANGED);
        intentFilter.addAction(Intent.ACTION_LOCALE_CHANGED);
        ContextCompat.registerReceiver(
            this,
            timeAndLocaleChangedReceiver,
            intentFilter,
            ContextCompat.RECEIVER_EXPORTED
        );
    }

    @NonNull
    private static RxSchedulerProvider getRxSchedulerProvider() {
        return new RxSchedulerProvider() {
            @Override
            public Scheduler io() {
                return Schedulers.io();
            }

            @Override
            public Scheduler computation() {
                return Schedulers.computation();
            }

            @Override
            public Scheduler mainThread() {
                return AndroidSchedulers.mainThread();
            }
        };
    }

    private void initRepositoryConfiguration(Context context) {
        subscriptions.add(FusionLocationResource.installResourceDescriptor(context)
                .subscribe(
                        () -> Timber.v("Installed fusion location resource descriptor"),
                        throwable -> Timber.e(throwable,
                                "Failed to install fusion location resource descriptor"))
        );
    }

    /**
     * When binding to the service, we return an interface to our messenger
     * for sending messages to the service.
     */
    @Override
    public IBinder onBind(@NonNull Intent intent) {
        super.onBind(intent);
        return messenger.getBinder();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        super.onStartCommand(intent, flags, startId);
        synchronized (SuuntoRepositoryService.class) {
            isServiceRunning.set(true);
        }

        if (intent != null) {
            // Display a foreground notification that should tell Android that we want to be alive
            showForegroundNotification(
                intent.getBooleanExtra(KEY_START_FOREGROUND_LOCATION, false),
                intent.getBooleanExtra(KEY_START_FOREGROUND_CONNECTED_DEVICE, false)
            );
        }

        // This service is always started as foreground service with the assumption that foreground
        // location permission is granted. So only check background location permission.
        // Calling stopSelf() doesn't help, see https://issuetracker.google.com/issues/307329994#comment97
        return hasBackgroundLocationPermission() ? START_STICKY : START_NOT_STICKY;
    }

    private boolean hasBackgroundLocationPermission() {
        return Build.VERSION.SDK_INT < Build.VERSION_CODES.UPSIDE_DOWN_CAKE ||
            ContextCompat.checkSelfPermission(
                this, Manifest.permission.ACCESS_BACKGROUND_LOCATION) == PackageManager.PERMISSION_GRANTED;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Timber.d("SuuntoRepositoryService onDestroy");
        if (clientForAncsService != null) {
            clientForAncsService.onDestroy();
            clientForAncsService = null;
        }
        // Cancel the persistent notification.
        synchronized (SuuntoRepositoryService.class) {
            isServiceRunning.set(false);
        }
        subscriptions.clear();
        if (locationUsageSubscription != null && !locationUsageSubscription.isUnsubscribed()) {
            locationUsageSubscription.unsubscribe();
        }
        unregisterReceiver(timeAndLocaleChangedReceiver);
        notificationHandler.cancelNotification();
        activeDevices.onDestroy();
        handlerThread.quit();
        initConnectivityDataCollectedUserPropertyHandler.removeCallbacksAndMessages(null);
        suuntoConnectivityClient.onDestroy();
        mediaControlsResource.onDestroy();
        unregisterReceiver(notificationListenerStatusReceiver);
    }

    synchronized private void showForegroundNotification(
        boolean startForegroundLocation,
        boolean startForegroundConnectedDevice
    ) {
        if (!startForegroundLocation && !startForegroundConnectedDevice) {
            return;
        }

        Timber.d("Move service foreground and show watch notification.");

        // Display a foreground notification that should tell Android that we want to be alive
        Notification notification = notificationHandler.buildNotification(
            NotificationHandler.NotificationType.WATCH_SERVICE_RUNNING);

        // Send the notification.
        // We use an id because it is a unique number.  We use it later to cancel.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            int foregroundServiceType = 0;
            if (startForegroundLocation) {
                foregroundServiceType |= ServiceInfo.FOREGROUND_SERVICE_TYPE_LOCATION;
            }
            if (startForegroundConnectedDevice) {
                foregroundServiceType |= ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE;
            }
            startForeground(
                R.id.suunto_connectivity_notification_id,
                notification,
                foregroundServiceType
            );
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(
                R.id.suunto_connectivity_notification_id,
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_MANIFEST
            );
        } else {
            startForeground(R.id.suunto_connectivity_notification_id, notification);
        }
    }

    private boolean postBackgroundLocationPermissionMissingNotificationIfNeeded() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q &&
            !LocationPermissionsKt.isBackgroundLocationPermissionGranted(getApplicationContext())) {
            subscriptions.add(notificationManager.postMissingBackgroundLocationPermissionNotification()
                .subscribe(
                    Actions.empty(),
                    throwable -> Timber.w(throwable,
                        "failed to post GPS may not work notification.")
                )
            );
            return true;
        } else {
            return false;
        }
    }

    void postPowerSaveModeNotificationToWatchIfNeeded() {
        if (BatteryUtils.powerSaveModeDetected(getApplicationContext())) {
            subscriptions.add(notificationManager.postPowerSaveModeNotification()
                .subscribe(
                    Actions.empty(),
                    throwable -> Timber.w(throwable, "failed to post power save mode notification.")
                )
            );
        }
    }

    synchronized private void setGpsNotification(boolean showNotification) {
        if (activeDevices != null) {
            activeDevices.setConnectedGpsInUse(showNotification);
            if (showNotification && !postBackgroundLocationPermissionMissingNotificationIfNeeded()) {
                postPowerSaveModeNotificationToWatchIfNeeded();
            }
        }
    }

    void handleConfigurationMessage(Bundle requestBundle) {
        this.configuration = requestBundle.getParcelable(ArgumentKeys.ARG_CONFIGURATION);
    }

    Observable<Integer> sendResponse(int messageId, @NonNull Messenger replyTo,
                                     @NonNull Response response) {
        return sendMessageTo(replyTo, MSG_RESPONSE, response, messageId).andThen(Observable.just
                (messageId));
    }

    void sendBroadcastMessageToClients(@NonNull Broadcast broadcast) {
        // Make a defensive copy to work with
        List<Messenger> clientsCopy = new ArrayList<>(clients);
        subscriptions.add(Observable.from(clientsCopy)
                .flatMap(client -> sendMessageTo(client, MSG_BROADCAST, broadcast, 0).onErrorComplete()
                        .toObservable())
                .toCompletable()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                        () -> Timber.v("Broadcast message [" + broadcast.toString() + "] sent"),
                        throwable -> Timber.w(throwable, "Exception while sending broadcast"))
        );
    }

    /**
     * Send MSG_RESPONSE and MSG_BROADCAST messages to client messenger.
     *
     * @param client      Client messenger
     * @param messageType Message type. MSG_RESPONSE and MSG_BROADCAST supported.
     * @param data        Data to be sent.
     * @param messageId   Message id. Valid only for message type MSG_RESPONSE.
     * @return Completable for sending a message.
     */
    @NonNull
    Completable sendMessageTo(@NonNull Messenger client, @MessageTypes int messageType,
                              @NonNull Parcelable data, int messageId) {
        return Completable.fromCallable(() -> {
            try {
                final Message message;
                if (messageType == MSG_RESPONSE) {
                    message = Message.obtain(null, MSG_RESPONSE, messageId, 0);
                } else if (messageType == MSG_BROADCAST) {
                    message = Message.obtain(null, MSG_BROADCAST);
                } else {
                    Timber.e("Unknown message type");
                    return null;
                }
                Bundle bundle = new Bundle(1);
                bundle.putParcelable(ArgumentKeys.ARG_DATA, data);
                message.setData(bundle);
                Timber.v("Sending messge [" + message + "] to client [" + client + "]");
                client.send(message);
            } catch (RemoteException ex) {
                // The client is dead.
                clients.remove(client);
                Timber.w(ex, "Client [" + client + "] probably is dead");
                throw ex;
            }
            return null;
        });
    }

    Single<ConnectResponse> handlePairMessage(PairQuery pairQuery) {
        final Single<ConnectResponse> single;
        String macAddress = pairQuery.getDevice().getMacAddress();
        final WatchBt watchBt;
        if (activeDevices.getWatchBt(macAddress) != null) {

            // Already active.
            single = Single.just(new ConnectResponse(true));
        } else {
            SuuntoBtDevice device = pairQuery.getDevice();
            final boolean advertisementHasSerial =
                SuuntoDeviceType.advertisementHasSerial(device.getDeviceType());

            if (advertisementHasSerial &&
                (device.getSerial() == null || device.getSerial().isEmpty())) {
                // Serial is empty although there should be serial. Fail connect.
                Timber.e("Empty serial on %s", device.getName());
                single = Single.just(new ConnectResponse(false));
            } else {
                SuuntoDeviceType deviceType = SuuntoDeviceType.fromBleAdvName(device.getName());
                if (deviceType.isEon()) {
                    if (deviceType == SuuntoDeviceType.SuuntoD5) {
                        watchBt = new EonD5BtBuilder().create(pairQuery.getDevice());
                    } else {
                        watchBt = new EonComputerBtBuilder().create(pairQuery.getDevice());
                    }
                } else if (supportedDevices.isSupportedLegacyWatch(device)) {
                    watchBt =
                        new AmbitBtBuilder().create(pairQuery.getDevice(),
                            bleServiceDeviceInterface);
                } else if (SuuntoDeviceType.isDataLayerDevice(device.getName())) {
                    watchBt = new DataLayerDeviceBtBuilder().create(pairQuery.getDevice());
                } else if (deviceType.isRunDevice()) {
                    watchBt = new RunBtBuilder().create(pairQuery.getDevice());
                } else {
                    watchBt = new SpartanBtBuilder().create(pairQuery.getDevice());
                }

                single = startObservingDevice(watchBt)
                    .andThen(
                        activeDevices.connectAndActivate(watchBt,
                            pairQuery.getScannedPairingState())
                    .toSingle(() -> new ConnectResponse(true))
                    .onErrorReturn(throwable -> {
                        if (throwable.getMessage() == null) {
                            return new ConnectResponse(false);
                        } else {
                            return new ConnectResponse(false, throwable.getMessage());
                        }
                    }));
            }
        }
        return single;
    }

    private Completable startObservingDevice(final WatchBt watchBt) {
        if (watchStateSubscription != null && !watchStateSubscription.isUnsubscribed()) {
            watchStateSubscription.unsubscribe();
        }

        watchConnector.getMdsConnectedSubscription(s -> {
            weatherUpdateModel.startObservingDevice(watchBt);
            watchTriggeredSyncListener.startObservingDevice(watchBt, getApplicationContext());
            findPhoneModel.startObservingDevice(watchBt);
            notificationStateModel.collectNotificationState(watchBt);
            voiceFeedbackModule.startObservingDevice(watchBt);
            alipayBusCodeResource.startObservingDevice(watchBt);
            watchOtaCheckModel.startObservingDevice(watchBt);
            hrIntensityZonesModel.startObservingDevice(watchBt);
            userSettingModel.startObservingDevice(watchBt);
            navigateResource.startObservingDevice(watchBt);
            trainingZoneResource.startObservingDevice(watchBt);
            routeResource.startObservingDevice(watchBt);
        }, throwable -> {
            Timber.d("Mds ConnectedDevice error: %s", throwable.getMessage());
        });
        return Completable.fromCallable(() -> {
            subscriptions.add(watchStateSubscription = watchBt.getStateChangeObservable()
                    .subscribeOn(AndroidSchedulers.mainThread())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(
                            watchState -> {
                                sendBroadcastMessageToClients(
                                        new WatchStateUpdateBroadcast(
                                                watchBt.getSuuntoBtDevice().getMacAddress(), watchState));
                            },
                            throwable -> Timber.e(throwable,
                                    "Error while listening to device state changes"))
            );
            return null;
        });
    }

    Single<DisconnectResponse> handleDisconnectMessage(DisconnectQuery disconnectQuery) {
        SuuntoBtDevice suuntoBtDevice = disconnectQuery.getDevice();
        WatchBt watchBt = activeDevices.getWatchBt(suuntoBtDevice.getMacAddress());
        if (watchBt == null) {
            return Single.just(new DisconnectResponse(true));
        }
        return activeDevices
                .disconnectAndDeactivate(watchBt)
                .toSingle(() -> new DisconnectResponse(true))
                .onErrorReturn(throwable -> new DisconnectResponse(false));
    }

    Single<UnpairResponse> handleUnpairQuery(UnpairQuery unpairQuery) {
        SuuntoBtDevice suuntoBtDevice = unpairQuery.getDevice();
        WatchBt watchBt = activeDevices.getWatchBt(suuntoBtDevice.getMacAddress());
        if (watchBt == null) {
            return Single.just(new UnpairResponse(true));
        }
        return activeDevices
            .unpair(watchBt)
            .toSingle(() -> new UnpairResponse(true))
            .onErrorReturn(throwable -> {
                Timber.e(throwable, "onErrorReturn");
                return new UnpairResponse(true);
            });
    }

    /**
     * Handle a {@link SyncDeviceQuery}.
     *
     * @return a {@link Single} that emits a {@link SyncDeviceResponse}
     */
    @SuppressLint("CheckResult")
    Single<SyncDeviceResponse> handleSyncDeviceMessage(SyncDeviceQuery syncDeviceQuery) {
        final SuuntoBtDevice device = syncDeviceQuery.getDevice();
        final boolean isActivityDataOnly = syncDeviceQuery.isActivityDataOnly();
        return activeDevices.syncNow(device, isActivityDataOnly)
            .onErrorResumeNext(
                Single.just(new SyncDeviceResponse(SpartanSyncResult.builder().build())));
    }

    Single<SyncTrainingZoneResponse> handleSyncTrainingZoneMessage(SyncTrainingZoneQuery syncTrainingZoneQuery) {
        final SuuntoBtDevice device = syncTrainingZoneQuery.getDevice();
        return activeDevices.requestTrainingZoneSync(device)
            .onErrorResumeNext(
                Single.just(new SyncTrainingZoneResponse(false, "Failed to handleSyncTrainingZoneMessage")));
    }

    Completable waitForServiceReady() {
        return serviceStateSubject.distinctUntilChanged()
                .filter(serviceState -> serviceState.equals(ServiceState.READY))
                .first()
                .toCompletable()
                .subscribeOn(Schedulers.computation());
    }

    /**
     * Extract all currently known Spartans and transform them into a list of devices that will be
     * returned as part of {@link RegisterClientResponse}
     *
     * @return a {@link RegisterClientResponse} which contains all known devices at this given
     * moment
     */
    Single<RegisterClientResponse> handleRegisterClientMessage(Messenger client) {
        Timber.v("Adding new client [" + client + "]");
        clients.add(client);
        return Observable.from(activeDevices.getBtDevices())
            .map(spartanBt -> {
                // We need to inform the new client about the current devices plus its latest
                // state
                return new SpartanIpc(spartanBt.getSuuntoBtDevice(),
                    spartanBt.getStateChangeObservable().toBlocking().first());
            })
            .toList()
            .toSingle()
            .map(suuntoBtDevices -> new RegisterClientResponse(suuntoBtDevices, configuration));
    }

    Single<GetActiveDevicesResponse> handleGetActiveDevices() {
        Timber.v("Getting active devices");
        return Observable.from(activeDevices.getBtDevicesEnsurePairings())
            .map(spartanBt -> {
                // We need to inform the new client about the current devices plus its latest
                // state
                return new SpartanIpc(spartanBt.getSuuntoBtDevice(),
                    spartanBt.getStateChangeObservable().toBlocking().first());
            })
            .toList()
            .toSingle()
            .map(GetActiveDevicesResponse::new);
    }

    Single<GetCoachEnabledResponse> handleGetCoachEnabled(Bundle bundle) {
        final GetCoachEnabledQuery coachEnabledQuery =
                bundle.getParcelable(ArgumentKeys.ARG_DATA);
        Timber.v("Checking whether adaptive coach is enabled");
        if (coachEnabledQuery == null) {
            return Single.just(new GetCoachEnabledResponse(false));
        }
        WatchBt watchBt = activeDevices.getWatchBt(coachEnabledQuery.getMacAddress());
        if (watchBt == null) {
            return Single.just(new GetCoachEnabledResponse(false));
        } else {
            return watchBt.getSettings().getCoachEnabled().getValue()
                    .map(GetCoachEnabledResponse::new)
                    .onErrorReturn(
                            throwable -> new GetCoachEnabledResponse(false));
        }
    }

    Single<SetCoachEnabledResponse> handleSetCoachEnabled(Bundle bundle) {
        final SetCoachEnabledQuery coachEnabledQuery =
                bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (coachEnabledQuery == null) {
            return Single.just(new SetCoachEnabledResponse(false));
        }
        Timber.v("Setting adaptive coach state");
        boolean state = coachEnabledQuery.isEnabled();
        WatchBt watchBt = activeDevices.getWatchBt(coachEnabledQuery.getMacAddress());
        if (watchBt == null) {
            return Completable.complete()
                    .toSingle(() ->
                            new SetCoachEnabledResponse(false));
        } else {
            return watchBt.getSettings().getCoachEnabled()
                    .setValue(state)
                    .toSingle(() ->
                            new SetCoachEnabledResponse(true))
                    .onErrorReturn(
                            throwable -> new SetCoachEnabledResponse(false));
        }
    }

    Single<Get247TargetResponse> handleGet247Target(Bundle bundle) {
        final Get247TargetQuery getStepsQuery =
                bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (getStepsQuery == null) {
            return Single.just(new Get247TargetResponse(0, GoalType.UNKNOWN));
        }
        Timber.v("Getting 247 target");
        final GoalType goalType = getStepsQuery.getDailyActivityType();
        WatchBt watchBt = activeDevices.getWatchBt(getStepsQuery.getMacAddress());

        if (watchBt == null) {
            return Single.just(new Get247TargetResponse(0, goalType));
        } else {
            SpartanSettings settings = watchBt.getSettings();
            if (settings == null) {
                return Single.just(new Get247TargetResponse(0, goalType));
            }
            Setting<Integer> target;
            if (goalType == GoalType.STEPS) {
                target = settings.getStepsTarget();
            } else if (goalType == GoalType.ENERGY) {
                target = settings.getEnergyTarget();
            } else if (goalType == GoalType.SLEEP) {
                target = settings.getSleepTarget();
            } else if (goalType == GoalType.BEDTIME_START) {
                target = settings.getBedtimeStart();
            } else {
                target = settings.getBedtimeEnd();
            }
            return target.getValue()
                    .map(targetResponse -> new Get247TargetResponse(targetResponse, goalType));
        }
    }

    Single<Set247TargetResponse> handleSet247Target(Bundle bundle) {
        final Set247TargetQuery set247TargetQuery =
                bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (set247TargetQuery == null) {
            return Single.just(new Set247TargetResponse(false, GoalType.UNKNOWN));
        }
        final GoalType goalType = set247TargetQuery.getDailyActivityType();
        Timber.v("Setting 247 target");
        int targetValue = set247TargetQuery.getTarget();
        WatchBt watchBt = activeDevices.getWatchBt(set247TargetQuery.getMacAddress());
        if (watchBt == null) {
            return Single.just(new Set247TargetResponse(false, goalType));
        } else {
            SpartanSettings settings = watchBt.getSettings();
            if (settings == null) {
                return Single.just(new Set247TargetResponse(false, goalType));
            }
            Setting<Integer> target;
            if (goalType == GoalType.STEPS) {
                target = settings.getStepsTarget();
            } else if (goalType == GoalType.ENERGY) {
                target = settings.getEnergyTarget();
            } else if (goalType == GoalType.SLEEP) {
                target = settings.getSleepTarget();
            } else if (goalType == GoalType.BEDTIME_START) {
                target = settings.getBedtimeStart();
            } else {
                target = settings.getBedtimeEnd();
            }
            return target
                    .setValue(targetValue)
                    .toSingle(() ->
                            new Set247TargetResponse(true, goalType))
                    .onErrorReturn(
                            throwable -> new Set247TargetResponse(false, goalType));
        }
    }

    Single<GetSleepTrackingModeResponse> handleGetSleepTrackingMode(Bundle bundle) {
        final GetSleepTrackingModeQuery sleepTrackingModeQuery =
                bundle.getParcelable(ArgumentKeys.ARG_DATA);
        Timber.v("Checking whether sleep tracking is enabled");
        if (sleepTrackingModeQuery == null) {
            return Single.just(new GetSleepTrackingModeResponse(SleepTrackingMode.OFF));
        }
        WatchBt watchBt = activeDevices.getWatchBt(sleepTrackingModeQuery.getMacAddress());
        if (watchBt == null) {
            return Single.just(new GetSleepTrackingModeResponse(SleepTrackingMode.OFF));
        } else {
            SpartanSettings settings = watchBt.getSettings();
            if (settings == null) {
                return Single.just(new GetSleepTrackingModeResponse(SleepTrackingMode.OFF));
            }
            return settings.getSleepTrackingMode().getValue()
                    .map(GetSleepTrackingModeResponse::new);
        }
    }

    Single<SetSleepTrackingModeResponse> handleSetSleepTrackingMode(Bundle bundle) {
        final SetSleepTrackingModeQuery setSleepTrackingModeQuery =
            bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (setSleepTrackingModeQuery == null) {
            return Single.just(new SetSleepTrackingModeResponse(false));
        }
        Timber.v("Setting sleep tracking mode");
        WatchBt watchBt  = activeDevices.getWatchBt(setSleepTrackingModeQuery.getMacAddress());
        if (watchBt == null) {
            return Single.just(new SetSleepTrackingModeResponse(false));
        } else {
            SpartanSettings settings = watchBt.getSettings();
            if (settings == null) {
                return Single.just(new SetSleepTrackingModeResponse(false));
            }

            SleepTrackingMode sleepTrackingMode = setSleepTrackingModeQuery.getSleepTrackingMode();
            return settings.getSleepTrackingMode()
                .setValue(sleepTrackingMode)
                .toSingle(() -> new SetSleepTrackingModeResponse(true))
                .onErrorReturn(throwable -> new SetSleepTrackingModeResponse(false));
        }
    }

    @NonNull
    Single<GetSpO2NightlyEnabledResponse> handleGetSpO2NightlyEnabled(@NonNull Bundle bundle) {
        final GetSpO2NightlyEnabledQuery spO2NightlyEnabledQuery =
            bundle.getParcelable(ArgumentKeys.ARG_DATA);
        Timber.v("Checking whether SpO2 nightly is enabled");
        if (spO2NightlyEnabledQuery == null) {
            return Single.just(new GetSpO2NightlyEnabledResponse(false));
        }
        WatchBt watchBt = activeDevices.getWatchBt(spO2NightlyEnabledQuery.getMacAddress());
        if (watchBt == null) {
            return Single.just(new GetSpO2NightlyEnabledResponse(false));
        } else {
            SpartanSettings settings = watchBt.getSettings();
            if (settings == null) {
                return Single.just(new GetSpO2NightlyEnabledResponse(false));
            }
            return settings.getSpO2NightlyEnabled().getValue()
                .map(GetSpO2NightlyEnabledResponse::new);
        }
    }

    @NonNull
    Single<SetSpO2NightlyEnabledResponse> handleSetSpO2NightlyEnabled(Bundle bundle) {
        final SetSpO2NightlyEnabledQuery setSpO2NightlyEnabledQuery =
            bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (setSpO2NightlyEnabledQuery == null) {
            return Single.just(new SetSpO2NightlyEnabledResponse(false));
        }
        Timber.v("Setting SpO2 nightly enabled");
        WatchBt watchBt  = activeDevices.getWatchBt(setSpO2NightlyEnabledQuery.getMacAddress());
        if (watchBt == null) {
            return Single.just(new SetSpO2NightlyEnabledResponse(false));
        } else {
            SpartanSettings settings = watchBt.getSettings();
            if (settings == null) {
                return Single.just(new SetSpO2NightlyEnabledResponse(false));
            }

            return settings.getSpO2NightlyEnabled()
                .setValue(setSpO2NightlyEnabledQuery.getEnabled())
                .toSingle(() -> new SetSpO2NightlyEnabledResponse(true))
                .onErrorReturn(throwable -> new SetSpO2NightlyEnabledResponse(false));
        }
    }

    @NonNull
    Single<GetHrvEnabledResponse> handleGetHrvEnabled(@NonNull Bundle bundle) {
        final GetHrvEnabledQuery hrvEnabledQuery =
            bundle.getParcelable(ArgumentKeys.ARG_DATA);
        Timber.v("Checking whether HRV is enabled");
        if (hrvEnabledQuery == null) {
            return Single.just(new GetHrvEnabledResponse(false));
        }
        WatchBt watchBt = activeDevices.getWatchBt(hrvEnabledQuery.getMacAddress());
        if (watchBt == null) {
            return Single.just(new GetHrvEnabledResponse(false));
        } else {
            SpartanSettings settings = watchBt.getSettings();
            if (settings == null) {
                return Single.just(new GetHrvEnabledResponse(false));
            }
            return settings.getHrvEnabled().getValue()
                .map(GetHrvEnabledResponse::new);
        }
    }

    @NonNull
    Single<SetHrvEnabledResponse> handleSetHrvEnabled(Bundle bundle) {
        final SetHrvEnabledQuery setHrvEnabledQuery =
            bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (setHrvEnabledQuery == null) {
            return Single.just(new SetHrvEnabledResponse(false));
        }
        Timber.v("Setting HRV enabled");
        WatchBt watchBt  = activeDevices.getWatchBt(setHrvEnabledQuery.getMacAddress());
        if (watchBt == null) {
            return Single.just(new SetHrvEnabledResponse(false));
        } else {
            SpartanSettings settings = watchBt.getSettings();
            if (settings == null) {
                return Single.just(new SetHrvEnabledResponse(false));
            }

            return settings.getHrvEnabled()
                .setValue(setHrvEnabledQuery.getEnabled())
                .toSingle(() -> new SetHrvEnabledResponse(true))
                .onErrorReturn(throwable -> new SetHrvEnabledResponse(false));
        }
    }

    @NonNull
    Single<Get247HrEnabledResponse> handleGet247HrEnabled(@NonNull Bundle bundle) {
        final Get247HrEnabledQuery get247HrEnabledQuery =
            bundle.getParcelable(ArgumentKeys.ARG_DATA);
        Timber.v("Checking whether 247 HR is enabled");
        if (get247HrEnabledQuery == null) {
            return Single.just(new Get247HrEnabledResponse(false));
        }
        WatchBt watchBt = activeDevices.getWatchBt(get247HrEnabledQuery.getMacAddress());
        if (watchBt == null) {
            return Single.just(new Get247HrEnabledResponse(false));
        } else {
            SpartanSettings settings = watchBt.getSettings();
            if (settings == null) {
                return Single.just(new Get247HrEnabledResponse(false));
            }
            return settings.get247HrEnabled().getValue()
                .map(Get247HrEnabledResponse::new);
        }
    }

    @NonNull
    Single<Set247HrEnabledResponse> handleSet247HrEnabled(Bundle bundle) {
        final Set247HrEnabledQuery set247HrEnabledQuery =
            bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (set247HrEnabledQuery == null) {
            return Single.just(new Set247HrEnabledResponse(false));
        }
        Timber.v("Setting 247 HR enabled");
        WatchBt watchBt  = activeDevices.getWatchBt(set247HrEnabledQuery.getMacAddress());
        if (watchBt == null) {
            return Single.just(new Set247HrEnabledResponse(false));
        } else {
            SpartanSettings settings = watchBt.getSettings();
            if (settings == null) {
                return Single.just(new Set247HrEnabledResponse(false));
            }

            return settings.get247HrEnabled()
                .setValue(set247HrEnabledQuery.getEnabled())
                .toSingle(() -> new Set247HrEnabledResponse(true))
                .onErrorReturn(throwable -> new Set247HrEnabledResponse(false));
        }
    }

    Single<Get247ActivityValueResponse> handleGetActivityValue(Bundle bundle) {
        final Get247ActivityValueQuery getStepsQuery =
                bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (getStepsQuery == null) {
            return Single.just(new Get247ActivityValueResponse(0, GoalType.UNKNOWN));
        }
        Timber.v("Getting 247 target");
        final GoalType goalType = getStepsQuery.getDailyActivityType();
        WatchBt watchBt = activeDevices.getWatchBt(getStepsQuery.getMacAddress());
        if (watchBt == null) {
            return Single.just(new Get247ActivityValueResponse(0, goalType));
        } else {
            ActivityDataHolder activityDataHolder = watchBt.getActivityDataHolder();
            if (activityDataHolder == null) {
                return Single.just(new Get247ActivityValueResponse(0, goalType));
            }
            if (goalType == GoalType.STEPS) {
                return activityDataHolder.getSteps().getValue()
                        .map(targetResponse -> new Get247ActivityValueResponse(targetResponse, goalType));
            } else if (goalType == GoalType.ENERGY){
                return activityDataHolder.getEnergy().getValue()
                        .map(targetResponse -> new Get247ActivityValueResponse(targetResponse.intValue(), goalType));
            } else if (goalType == GoalType.METABOLIC_ENERGY) {
                return activityDataHolder.getMetabolicEnergy().getValue()
                        .map(metabolicEnergy -> new Get247ActivityValueResponse(metabolicEnergy.intValue(), goalType));
            } else if (goalType == GoalType.SLEEP) {
                return activityDataHolder.getSleep().getValue()
                        .map(sleep -> new Get247ActivityValueResponse(sleep.intValue(), goalType));
            } else if (goalType == GoalType.BALANCE) {
                return activityDataHolder.getBalance().getValue()
                    .map(balance -> new Get247ActivityValueResponse(balance.intValue(), goalType));
            } else if (goalType == GoalType.STRESS_STATE) {
                return activityDataHolder.getStressState().getValue()
                    .map(stressState -> new Get247ActivityValueResponse(stressState.intValue(), goalType));
            } else {
                // We don't care
                return Single.just(new Get247ActivityValueResponse(0, goalType));
            }
        }
    }

    Single<EmptyResponse> handleClearConnectionInstability() {
        return Single.fromCallable(new Callable<EmptyResponse>() {
            @Override
            public EmptyResponse call() throws Exception {
                activeDevices.clearConnectionInstability();
                return new EmptyResponse();
            }
        });
    }

    Single<GetWeeklyTargetDurationResponse> handleGetWeeklyTargetDuration(Bundle bundle) {
        final GetWeeklyTargetDurationQuery weeklyTargetDurationQuery =
                bundle.getParcelable(ArgumentKeys.ARG_DATA);
        Timber.v("Getting weekly target duration");
        if (weeklyTargetDurationQuery == null) {
            return Single.just(new GetWeeklyTargetDurationResponse(0F));
        }
        WatchBt watchBt = activeDevices.getWatchBt(weeklyTargetDurationQuery.getMacAddress());
        if (watchBt == null || watchBt.getSuuntoBtDevice().getDeviceType().isAmbit()) {
            return Single.just(new GetWeeklyTargetDurationResponse(0F));
        } else {
            SpartanSettings settings = watchBt.getSettings();
            if (settings == null) {
                return Single.just(new GetWeeklyTargetDurationResponse(0F));
            }
            return settings.getWeeklyTargetDuration().getValue()
                    .map(GetWeeklyTargetDurationResponse::new)
                    .onErrorReturn(
                            throwable -> new GetWeeklyTargetDurationResponse(0F));
        }
    }

    Single<SetWeeklyTargetDurationResponse> handleSetWeeklyTargetDuration(Bundle bundle) {
        final SetWeeklyTargetDurationQuery weeklyTargetDurationQuery =
                bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (weeklyTargetDurationQuery == null) {
            return Single.just(new SetWeeklyTargetDurationResponse(false));
        }
        Timber.v("Setting weekly target duration");
        float weeklyTargetDuration = weeklyTargetDurationQuery.getWeeklyTargetDuration();
        WatchBt watchBt  = activeDevices.getWatchBt(weeklyTargetDurationQuery.getMacAddress());
        if (watchBt == null) {
            return Completable.complete()
                    .toSingle(() ->
                            new SetWeeklyTargetDurationResponse(false));
        } else {
            SpartanSettings settings = watchBt.getSettings();
            if (settings == null) {
                return Completable.complete()
                    .toSingle(() ->
                        new SetWeeklyTargetDurationResponse(false));
            }
            return settings.getWeeklyTargetDuration()
                    .setValue(weeklyTargetDuration)
                    .toSingle(() ->
                            new SetWeeklyTargetDurationResponse(true))
                    .onErrorReturn(throwable -> new SetWeeklyTargetDurationResponse(false));
        }
    }

    Observable<AddNotificationsPackageResponse> handleAddNotificationsPackage(Bundle bundle) {
        AddNotificationsPackageQuery query = bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (query != null) {
            notificationManager.addPackage(query.getPackageName());
            return Observable.just(new AddNotificationsPackageResponse(true));
        } else {
            return Observable.just(new AddNotificationsPackageResponse(false));
        }
    }

    Observable<EnableNotificationsPackageResponse> handleEnableNotificationsPackage(Bundle bundle) {
        EnableNotificationsPackageQuery query = bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (query != null) {
            notificationManager.enablePackage(query.getPackageName());
            return Observable.just(new EnableNotificationsPackageResponse(true));
        } else {
            return Observable.just(new EnableNotificationsPackageResponse(false));
        }
    }

    Observable<DisableNotificationsPackageResponse> handleDisableNotificationsPackage(
            Bundle bundle) {
        DisableNotificationsPackageQuery query = bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (query != null) {
            notificationManager.disablePackage(query.getPackageName());
            return Observable.just(new DisableNotificationsPackageResponse(true));
        } else {
            return Observable.just(new DisableNotificationsPackageResponse(false));
        }
    }

    Observable<EmptyResponse> handlePostNotification(Bundle bundle) {
        PostNotificationQuery query = bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (query != null) {
            subscriptions.add(
                notificationManager.postNotification(query.getAncsMessage())
                    .subscribe(
                        Actions.empty(),
                        e -> Timber.v(e, "Could not post notification")
                    )
            );
        }
        return Observable.just(new EmptyResponse());
    }

    Observable<EmptyResponse> handleRemoveNotification(Bundle bundle) {
        RemoveNotificationQuery query = bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (query != null) {
            subscriptions.add(notificationManager.removeNotification(query.getMessageId())
                .subscribe(Actions.empty(),
                    throwable -> Timber.v("Could not remove notification: %s",
                            throwable.toString()))
            );
        }
        return Observable.just(new EmptyResponse());
    }

    Single<StartLoggingResponse> handleStartLogging(final StartLoggingQuery query) {
        // Save the log directory to local variable for later use
        logDir = query.getDir();
        String error = LogHelper.startFileLogging(LOG_FILE_PREFIX, query.getDir());
        return Single.just(new StartLoggingResponse(error));
    }

    Single<StopLoggingResponse> handleStopLogging() {
        // Copy Mds log file now to prevent it from been flooded by system event requests later
        if (logDir == null) {
            return Single.just(new StopLoggingResponse(new ArrayList<>()));
        }
        final File mdsLogFile = safeCreateTempFile("mds_", logDir);
        if (mdsLogFile != null) {
            mdsLogFile.deleteOnExit();
            File mdsLogSource = new File(getFilesDir(), "suuntoapp.log");
            try {
                new FileUtils().copyFile(mdsLogSource, mdsLogFile);
            } catch (IOException e) {
                Timber.e(e, "Could not copy mds log to temp file");
            }
        }

        // Dump workout data for the device.
        for (WatchBt spartan : activeDevices.getBtDevices()) {
            try {
                File logbookEntriesDirectory = new File(getFilesDir(),
                    configuration.getLogbookEntriesPath(spartan.getMacAddress()));
                final File jsonsZipFile =
                    new File(logDir, "jsons_" + spartan.getSerial() + ".zip");
                jsonsZipFile.delete();
                if (jsonsZipFile.createNewFile()) {
                    FileUtils.zipFolder(
                        logbookEntriesDirectory,
                        jsonsZipFile,
                        "/" + LOGBOOK_ENTRIES_FOLDER);
                }
            } catch (IOException exception) {
                Timber.w(exception, "Unable dump workout data for device %s", spartan.getSerial());
            }
        }

        // Stop logging here first to make sure logging was stopped here in case of error and
        // to avoid having all the logging from getting system events to flood the log file
        // for repository process
        final String filepath = LogHelper.stopFileLogging();

        // Create Single for getting system events from watch
        Single<File> systemEventSingle = Single.just(null);

        // Search for connected spartan device
        for (WatchBt spartan : activeDevices.getBtDevices()) {
            if (spartan.getCurrentState().isConnected()) {
                systemEventSingle = systemEventReader.writeSystemEventsToFile(spartan, logDir)
                        .onErrorReturn(throwable -> {
                            Timber.e(throwable, "Could not write system events to file");
                            return null;
                        });
                break;
            }
        }

        return systemEventSingle.map(systemEvents -> {
            List<String> paths = new ArrayList<>();
            paths.add(filepath);

            // Add system events only if they were successfully extracted
            if (systemEvents != null) {
                paths.add(systemEvents.getPath());
            }

            // Add mds log if it exists
            if (mdsLogFile != null) {
                paths.add(mdsLogFile.getPath());
            }

            return new StopLoggingResponse(paths);
        });
    }

    Observable<GetLogsResponse> handleGetLogs(Bundle bundle) {
        GetLogsQuery query = bundle.getParcelable(ArgumentKeys.ARG_DATA);
        Single<File> fileSingle;
        if (query != null) {
            switch (query.getLogType()) {
                case ScLib.LOG_MDS:
                    fileSingle = getMdsLogs();
                    break;
                case ScLib.LOG_MDS_DESCRIPTORS:
                    fileSingle = getMdsDescriptors();
                    break;
                case ScLib.LOG_WATCH_SYSTEM_EVENTS:
                    // If there is no active device or device is not connected, system events
                    // can not be received. Send null file as response.
                    if (activeDevices.getBtDevices().isEmpty()) {
                        Timber.w("Cannot log system events: no active devices");
                        fileSingle = Single.just(null);
                    } else {
                        WatchBt watchBt = activeDevices.getBtDevices().iterator().next();
                        if (!watchBt.getCurrentState().isConnected()) {
                            Timber.w("Cannot log system events: current watch is not connected");
                            fileSingle = Single.just(null);
                        } else {
                            // Active device exists and it is connected.
                            fileSingle = getWatchSystemEvents();
                        }
                    }
                    break;
                case ScLib.LOG_AMBIT3_MOVE_BINS:
                    fileSingle = getAmbit3MoveBinaries();
                    break;
                case ScLib.LOG_SUUNTO_CONNECTIVITY:
                case ScLib.LOG_REPOSITORY_SERVICE:
                    addLogDumpMetadataToSuuntoLog();
                    fileSingle = Single.fromCallable(() -> timberInMemoryTree.writeLogsToFile(getFilesDir().getAbsolutePath(), "screpocached"));
                    break;
                default:
                    fileSingle = Single.error(new UnsupportedOperationException(
                        "Getting logs of type " + query.getLogType() + " not supported"));
            }
        } else {
            fileSingle = Single.error(new InvalidParameterException("Invalid query"));
        }

        return fileSingle
            .map(file -> GetLogsResponse.createWithPath(file.getPath()))
            .onErrorReturn(throwable -> GetLogsResponse.createWithError(throwable.toString()))
            .toObservable();
    }

    Single<File> getMdsLogs() {
        return Single.fromCallable(() -> {
            File mdsLogFile = File.createTempFile("suuntoapp_", ".log");
            mdsLogFile.deleteOnExit();
            File mdsLogSource = new File(getFilesDir(), "suuntoapp.log");
            new FileUtils().copyFile(mdsLogSource, mdsLogFile);
            return mdsLogFile;
        });
    }

    /**
     * Dumps useful metadata to the caching timber tree before the cached logs are read.
     */
    private void addLogDumpMetadataToSuuntoLog() {
        Timber.d("Metadata started");
        Timber.d("SuuntoRepositoryService start count: %d", serviceStarts);
        Timber.d("Analytics id: %s", AnalyticsUtils.getAnalyticsUUID());
        Timber.d("Metadata ended");
    }

    Single<File> getMdsDescriptors() {
        return Single.fromCallable(() -> {
            File descriptorsFile = File.createTempFile("descriptors_", ".zip");
            descriptorsFile.deleteOnExit();
            File descriptorsDirectory = new File(getFilesDir().getAbsolutePath(), "desc-store");
            FileUtils.zipFolder(descriptorsDirectory, descriptorsFile, "/desc-store");
            return descriptorsFile;
        });
    }

    Single<File> getWatchSystemEvents() {
        return Observable.from(activeDevices.getBtDevices())
            .filter(spartan -> spartan.getCurrentState().isConnected())
            .first()
            .toSingle()
            .flatMap(spartan -> systemEventReader.writeSystemEventsToFile(spartan, logDir));
    }

    Single<File> getAmbit3MoveBinaries() {
        return Single.fromCallable(() -> {
            File ambitBinFolder = new File(getFilesDir(), "ambit3_moves");
            if (ambitBinFolder.exists() && ambitBinFolder.isDirectory()) {
                File zipFile = File.createTempFile("ambit3moves_", ".zip");
                File[] files = ambitBinFolder.listFiles();
                try (ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFile))) {
                    for (File file : files) {
                        try (FileInputStream in = new FileInputStream(file)) {
                            out.putNextEntry(new ZipEntry(file.getName()));

                            byte[] buffer = new byte[1024];
                            int len;
                            while ((len = in.read(buffer)) != -1) {
                                out.write(buffer, 0, len);
                            }
                            out.closeEntry();
                        }
                    }
                }
                return zipFile;
            } else {
                return null;
            }
        });
    }

    /**
     * Try to set move as synced in watch or move to the list of synced items.
     * Errors are simply ignored.
     *
     * @param bundle Bundle containing MarkAsSyncedQuery.
     * @return Emptyresponse single.
     */
    Single<EmptyResponse> handleMarkAsSynced(Bundle bundle) {
        final MarkAsSyncedQuery markAsSyncedQuery =
            bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (markAsSyncedQuery == null) {
            return Single.just(new EmptyResponse());
        }
        final WatchBt watchBt = activeDevices.getWatchBt(markAsSyncedQuery.getMacAddress());
        final List<Long> entryIds = markAsSyncedQuery.getEntryIds();

        // Update synchronizer storage with entries synced to backend.
        Completable addEntriesToSyncedToBackend;
        if (watchBt != null) {
            addEntriesToSyncedToBackend = Completable.fromAction(() ->
                synchronizerStorage.addEntriesToSyncedToBackend(watchBt.getMacAddress(),
                markAsSyncedQuery.getEntryIds())
                );
        } else {
            addEntriesToSyncedToBackend = Completable.complete();
        }

        if (!(watchBt instanceof SpartanBt) || entryIds.size() == 0) {
            return addEntriesToSyncedToBackend.andThen(
                Single.just(new EmptyResponse())
            );
        } else {
            final SpartanBt spartanBt = (SpartanBt) watchBt;
            final String macAddress = spartanBt.getSuuntoBtDevice().getMacAddress();
            if (entryIds.size() > 1) {
                // Multiple entries marked as synced. Marking will be done during next
                // successful sync.
                return addEntriesToSyncedToBackend.andThen(
                    Single.fromCallable(() -> {
                        try {
                            synchronizerStorage.addEntriesToBeMarkedAsSynced(macAddress,
                                markAsSyncedQuery.getEntryIds());
                        } catch (Exception e) {
                            Timber.e(e, "Adding synced entries failed");
                        }
                        // Try to sync now in order to mark entries as synced. Ignore errors.
                        subscriptions.add(
                            activeDevices.syncNow(spartanBt.getSuuntoBtDevice(), false)
                                .subscribe(syncDeviceResponse -> {
                                    // Ignore.
                                }, throwable -> {
                                    if (throwable instanceof SyncLogic.DeviceNotConnectedOrBusy) {
                                        Timber.d("Device not ready for sync");
                                    } else {
                                        Timber.w(throwable,
                                            "Sync failed when trying to mark entries");
                                    }
                                })
                        );
                        return new EmptyResponse();
                    })
                );
            } else {
                // Just one entry marked as synced. Marking will be done immediately.
                final Long entryId = entryIds.get(0);
                return addEntriesToSyncedToBackend.andThen(
                    Single.just(spartanBt)
                        .map(bt -> {
                            // Add entry to the list of entries to be marked as synced.
                            try {
                                synchronizerStorage.addEntriesToBeMarkedAsSynced(macAddress,
                                    Collections.singletonList(entryId));
                            } catch (Exception e) {
                                Timber.e(e, "Adding synced entries failed");
                            }
                            return bt;
                        })
                        .flatMapCompletable(bt -> SpartanSynchronizer.markEntryAsSyncedToDevice(
                            bt,
                            synchronizerStorage, entryId))
                        .onErrorComplete()
                        .andThen(Single.just(new EmptyResponse()))
                );
            }
        }
    }

    Single<ResetConnectionResponse> handleResetConnection(Bundle bundle) {
        ResetConnectionQuery query = bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (query != null) {
            final SuuntoBtDevice device = query.getSuuntoBtDevice();
            Integer givenTimeout = query.getReconnectAfterSeconds();
            final int timeout =
                givenTimeout != null ? givenTimeout : DEFAULT_RESET_CONNECTION_TIMEOUT;
            WatchBt watchBt = activeDevices.getWatchBt(device.getMacAddress());
            if (watchBt == null) {
                return Single.just(new ResetConnectionResponse(false, 0));
            }
            return activeDevices.resetConnection(watchBt, timeout);
        }
        return Single.just(new ResetConnectionResponse(false, 0));
    }

    Single<ServiceStabilityResponse> handleGetServiceStability() {
        return Single.fromCallable(() -> new ServiceStabilityResponse(
            AnalyticsUtils.getPreviousStartInformation(preferences, gson),
            fusionLocationResource.getLatestGpsTrackingWasNotCompleted()));
    }

    /**
     * Clear device specific data.
     *
     * @return Completable for handling user logout.
     */
    Single<EmptyResponse> handleUserLogout() {
        return Single.fromCallable(() -> {
            Collection<WatchBt> watchBts = activeDevices.getBtDevices();
            for (WatchBt watchBt : watchBts) {
                final String macAddress = watchBt.getMacAddress();
                synchronizerStorage.removeEntriesFromToBeMarkedAsSynced(macAddress,
                    synchronizerStorage.getEntriesToBeMarkedAsSynced(macAddress));
                synchronizerStorage.removeEntriesFromSyncedToBackend(macAddress,
                    synchronizerStorage.getEntriesFromSyncedToBackend(macAddress));
                synchronizerStorage.clearSyncTimestamps(macAddress);
            }
            return new EmptyResponse();
        });
    }

    Single<EmptyResponse> handleReportAppProcessForeground(Bundle bundle) {
        final ReportAppProcessForegroundQuery query = bundle.getParcelable(ArgumentKeys.ARG_DATA);
        return Single.fromCallable(new Callable<EmptyResponse>() {
            @Override
            public EmptyResponse call() throws Exception {
                foreground = query.getForeground();
                Timber.v("handleReportAppProcessForeground %s", foreground);
                activeDevices.getBtDevices().forEach(watchBt ->
                    findPhoneModel.alarmAndVibrator(watchBt, false)
                );
                activeDevices.handleReportAppProcessForeground(foreground);
                return new EmptyResponse();
            }
        });
    }

    @SuppressLint("ApplySharedPref")
    Single<OTAUpdateActionResponse> handleFirmwareUpdateAction(Bundle bundle) {
        final OTAUpdateActionQuery query = bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (query != null) {
            switch (query.getAction()) {
                case StartFirmwareTransfer:
                    if (query.getFirmwareFileUri() != null && query.getSuuntoBtDevice() != null) {
                        return activeDevices.startFirmwareTransfer(
                            query.getSuuntoBtDevice().getMacAddress(),
                            query.getFirmwareFileUri(),
                            query.getFirmwareVersion())
                            .onErrorReturn(throwable -> {
                                Timber.w(throwable, "StartFirmwareTransfer failed");
                                return new FirmwareTransferStartResponse(false,
                                    throwable.toString());
                            })
                            // Map is needed for casting.
                            .map(firmwareTransferStartResponse -> firmwareTransferStartResponse);
                    } else {
                        return Single.just(new ErrorInOtaUpdateParametersResponse(
                            "Wrong arguments in StartFirmwareTransfer query"));
                    }
                case SelectFirmware:
                    if (query.getSuuntoBtDevice() != null &&
                        query.getSelectFirmwarePackageId() != null
                    ) {
                        return activeDevices.selectFirmware(
                            query.getSuuntoBtDevice().getMacAddress(),
                            query.getSelectFirmwarePackageId(),
                            query.getSelectFirmwareForceUpdate()
                        )
                            // Map is needed for casting.
                            .map(checkForOtaUpdatesResponse -> checkForOtaUpdatesResponse);
                    } else {
                        return Single.just(
                            new ErrorInOtaUpdateParametersResponse("Wrong arguments in SelectFirmware query")
                        );
                    }
                case GetSelectedFirmware:
                    if (query.getSuuntoBtDevice() != null) {
                        return activeDevices.getSelectedFirmware(query.getSuuntoBtDevice().getMacAddress())
                            // Map is needed for casting.
                            .map(checkForOtaUpdatesResponse -> checkForOtaUpdatesResponse);
                    } else {
                        return Single.just(
                            new ErrorInOtaUpdateParametersResponse("Wrong arguments in GetSelectedFirmware query")
                        );
                    }
                case OTAUpdateActivated:
                    Timber.d("OTA update activated");
                    return Completable.fromAction(() ->
                        preferences.edit()
                            .putBoolean(FirmwareUpdateStateMachine.OTA_UPDATE_ACTIVE, true)
                            .commit())
                        .andThen(Single.just(
                            new OTAUpdateActivatedResponse(true, "OTA Activated")));
                case OTAUpdateDisabled:
                    Timber.d("OTA update disabled");
                    return Completable.fromAction(() ->
                        preferences.edit()
                            .putBoolean(FirmwareUpdateStateMachine.OTA_UPDATE_ACTIVE, false)
                            .commit())
                        .andThen(Single.just(
                            new OTAUpdateDisabledResponse(true, "OTA disabled")));
                case CheckForUpdates:
                    Timber.d("Check for ota updates");
                    if (query.getSuuntoBtDevice() != null) {
                        return activeDevices.checkForUpdates(
                            query.getSuuntoBtDevice().getMacAddress(), query.getFirmwareFileUri())
                            .onErrorReturn(throwable -> {
                                Timber.w(throwable, "CheckForUpdates failed");
                                return CheckForOtaUpdatesResponse.error(throwable.toString());
                            })
                            // Map is needed for casting.
                            .map(checkForOtaUpdatesResponse -> checkForOtaUpdatesResponse);
                    } else {
                        return Single.just(new ErrorInOtaUpdateParametersResponse(
                            "Wrong arguments in CheckForUpdates query"));
                    }
                case StopOtaUpdate:
                    Timber.d("Stop ota update");
                    if (query.getSuuntoBtDevice() != null) {
                        return Completable.fromAction(() ->
                            activeDevices.stopOtaUpdate(
                                query.getSuuntoBtDevice().getMacAddress())
                        )
                            .andThen(Single.just(
                                new StopOtaUpdateResponse(true, "OTA disabled")));
                    } else {
                        return Single.just(new ErrorInOtaUpdateParametersResponse(
                            "Wrong arguments in CheckForUpdates query"));
                    }
                case InstallSelectedFirmware:
                    Timber.d("Install selected firmware");
                    if (query.getSuuntoBtDevice() != null
                        && query.getSelectFirmwarePackageId() != null) {
                        WatchBt watchBt = activeDevices.getWatchBt(query.getSuuntoBtDevice().getMacAddress());
                        if (watchBt == null) {
                            return Single.just(new InstallSelectedFirmwareResponse(false, "Watch not found"));
                        } else {
                            return watchBt.installSelectedFirmware(
                                    query.getSelectFirmwarePackageId(),
                                    query.getSelectFirmwareForceUpdate()
                                ).map(installSelectedFirmwareResponse -> installSelectedFirmwareResponse);
                        }
                    } else {
                        return Single.just(new ErrorInOtaUpdateParametersResponse(
                            "Wrong arguments in InstallSelectedFirmware query"));
                    }

                default:
                    return Single.just(
                        new ErrorInOtaUpdateParametersResponse(
                            "Unknown OTA update action: " + query.getAction()));
            }
        } else {
            return Single.just(
                new ErrorInOtaUpdateParametersResponse(
                    "Missing OTA update query"));
        }
    }

    Single<ImportWorkoutFromFileResponse> handleImportWorkout(Bundle bundle) {
        final ImportWorkoutFromFileQuery query = bundle.getParcelable(ArgumentKeys.ARG_DATA);
        Collection<WatchBt> devices = activeDevices.getBtDevices();
        if (devices.isEmpty()) {
            // There must a device in order to import workout.
            Timber.d("No watch. Can not import workouts");
            return Single.just(new ImportWorkoutFromFileResponse(false, 0, false));
        } else {
            WatchBt device = devices.iterator().next();
            WorkoutImporter workoutImporter = new WorkoutImporter(gson, synchronizerStorage,
                device.getWatchSynchronizer());
            return RxJavaInterop.toV1Single(
                workoutImporter.importFile(getApplicationContext(), query, device.getMacAddress()))
                .subscribeOn(Schedulers.io())
                .doOnSuccess(importWorkoutFromFileResponse -> {
                    final SyncResult smlZipResult;
                    if (importWorkoutFromFileResponse.getSmlZipCreated()) {
                        smlZipResult = SyncResult.success();
                    } else {
                        smlZipResult = SyncResult.skipped();
                    }
                    LogbookEntrySyncResult entrySyncResult =
                        LogbookEntrySyncResult.builder()
                            .samplesResult(SyncResult.success())
                            .summaryResult(SyncResult.success())
                            .smlZipResult(smlZipResult)
                            .entryId(importWorkoutFromFileResponse.getEntryId())
                            .build();
                    SingleLogbookEntrySyncResultEvent
                        syncResultEvent = new SingleLogbookEntrySyncResultEvent(entrySyncResult,
                        device.getMacAddress());
                    SyncResultService.sendSpartanSyncResult(
                        syncResultEvent.getSpartanSyncResult(),
                        AnalyticsEvent.SUUNTO_SYNC_MANUAL_METHOD,
                        null,
                        getApplicationContext());
                })
                .onErrorReturn(throwable -> {
                    Timber.w(throwable, "Importing workout failed");
                    return new ImportWorkoutFromFileResponse(false, 0, false);
                });
        }
    }

    Single<GetOrSetSettingsFileResponse> handleGetSettingsFile(Bundle bundle) {
        GetSettingsFileQuery query = bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (query != null) {
            return activeDevices.getSettingsFile(
                query.getSuuntoBtDevice().getMacAddress(),
                query.getFileUri());
        } else {
            return Single.just(new GetOrSetSettingsFileResponse(false, "Invalid query"));
        }
    }

    Single<GetOrSetSettingsFileResponse> handleSetSettingsFile(Bundle bundle) {
        SetSettingsFileQuery query = bundle.getParcelable(ArgumentKeys.ARG_DATA);
        if (query != null) {
            String fileName = FirmwareFileUtils.resolveFileNameFromUri(query.getFileUri(), this);
            if (fileName != null) {
                File target = new File(this.getCacheDir(), fileName);
                if (target.exists()) {
                    target.delete();
                }
                String path = FirmwareFileUtils.copyFileFromUri(this, target, query.getFileUri());
                if (path != null) {
                    return activeDevices.setSettingsFile(
                        query.getSuuntoBtDevice().getMacAddress(),
                        path);
                } else {
                    return Single.just(
                        new GetOrSetSettingsFileResponse(false, "Can not copy file from URI"));
                }
            } else {
                return Single.just(
                    new GetOrSetSettingsFileResponse(false, "Unable to resolve file name"));
            }
        } else {
            return Single.just(new GetOrSetSettingsFileResponse(false, "Invalid query"));
        }
    }

    /**
     * Helper method to safely create temp file.
     *
     * @return Created temporary file, or null if creation failed
     */
    @Nullable
    private File safeCreateTempFile(@NonNull String prefix, @NonNull String dir) {
        File directory = new File(dir);

        try {
            return File.createTempFile(prefix, ".log", directory);
        } catch (IOException e) {
            Timber.e(e, "Could not create temp file %s.log", prefix);
        }

        return null;
    }

    public ActiveDevices getActiveDevices() {
        return activeDevices;
    }

    /**
     * Get Suunto repository service client connection for Ancs service.
     *
     * @param context Application context.
     * @return Client for Ancs service.
     */
    public static synchronized SuuntoRepositoryClient getClientForAncsService(Context context) {
        if (clientForAncsService == null) {
            clientForAncsService =
                new SuuntoRepositoryClient(context);
        }
        return clientForAncsService;
    }

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({
        MSG_CONFIGURATION, MSG_RESPONSE, MSG_PAIR, MSG_REGISTER_CLIENT, MSG_BROADCAST,
        MSG_ENABLE_NOTIFICATIONS, MSG_DISABLE_NOTIFICATIONS, MSG_POST_NOTIFICATION,
        MSG_REMOVE_NOTIFICATION, MSG_GET_KNOWN_NOTIFICATIONS, MSG_ADD_NOTIFICATIONS_PACKAGE,
        MSG_ENABLE_NOTIFICATIONS_PACKAGE, MSG_DISABLE_NOTIFICATIONS_PACKAGE, MSG_DISCONNECT,
        MSG_SYNC_DEVICE, MSG_START_LOGGING, MSG_STOP_LOGGING, MSG_GET_LOGS, MSG_GET_ACTIVE_DEVICES,
        MSG_UNPAIR, MSG_GET_COACH_ENABLED, MSG_SET_COACH_ENABLED, MSG_CLEAR_CONNENTION_INSTABILITY,
        MSG_GET_WEEKLY_TARGET_DURATION, MSG_SET_WEEKLY_TARGET_DURATION, MSG_GET_247_TARGET, MSG_SET_247_TARGET,
        MSG_GET_247_DAILY_ACTIVITY_VALUE, MSG_GET_SLEEP_TRACKING_MODE, MSG_SET_SLEEP_TRACKING_MODE, MSG_MARK_AS_SYNCED,
        MSG_GET_SPORT_MODES, MSG_SET_SPORT_MODES, MSG_DELETE_SPORT_MODES,
        MSG_POST_ROUTES, MSG_RESET_CONNECTION, MSG_GET_SERVICE_STABILITY, MSG_USER_LOGOUT,
        MSG_REPORT_APP_PROCESS_FOREGROUND,
        MSG_OTA_UPDATE_ACTION, MSG_POST_POIS, MSG_IMPORT_WORKOUT, MSG_GET_SETTINGS_FILE,
        MSG_SET_SETTINGS_FILE, MSG_SET_PREDEFINED_REPLIES, MSG_SYNC_SUUNTO_PLUS_GUIDES,
        MSG_SET_FIRST_DAY_OF_THE_WEEK, MSG_GET_FILE_LIST, MSG_GET_FILE, MSG_PUT_FILE, MSG_DELETE_FILE,
        MSG_GET_WIDGETS, MSG_SET_WIDGETS,
        MSG_SHOW_INCOMING_CALL_NOTIFICATION, MSG_REMOVE_INCOMING_CALL_NOTIFICATION,
        MSG_SHOW_CALL_REJECTED_OR_MISSED_NOTIFICATION, MSG_GET_SAVED_WIFI_NETWORKS_COUNT,
        MSG_GET_SAVED_WIFI_NETWORKS, MSG_SAVE_WIFI_NETWORK, MSG_SCAN_AVAILABLE_WIFI_NETWORKS,
        MSG_FORGET_WIFI_NETWORK, MSG_SET_WIFI_GENERAL_SETTINGS, MSG_SET_OFFLINE_MAPS_URL,
        MSG_SET_AUTHORIZATION_TOKEN, MSG_NOTIFY_AREA_SELECTION_CHANGED, MSG_NOTIFY_AREA_UNDER_DOWNLOAD_DELETED,
        MSG_OBSERVE_WIFI_ENABLED, MSG_SET_WIFI_ENABLED, MSG_ENABLE_INBOX_WIFI, MSG_NUMBER_OF_AREAS,
        MSG_GET_BATTERY_LEVEL, MSG_GET_CHARGING_STATE, MSG_GET_USB_CABLE_STATE,
        MSG_LOCK_UNLOCK_SPORTS_APP, MSG_GET_ZAPP_PLUG_IN_DIRECTORY, MSG_SYNC_TRAINING_ZONE,
        MSG_PUT_NOTIFICATION_STATE, MSG_PUT_NOTIFICATION_CATEGORY_STATE, MSG_GET_NOTIFICATION_STATE,
        MSG_DEBUG_SET_LOCATION_COORDINATES, MSG_GET_SPO2_NIGHTLY_ENABLED, MSG_SET_SPO2_NIGHTLY_ENABLED,
        MSG_GET_HRV_ENABLED, MSG_SET_HRV_ENABLED, MSG_GET_247_HR_ENABLED, MSG_SET_247_HR_ENABLED,
        MSG_GET_CURRENT_WATCHFACE_ID, MSG_SUBSCRIBE_SETUP_PREFERENCE_CANCEL, MSG_GET_SETUP_PREFERENCE_STATE,
        MSG_PUT_SETUP_PREFERENCE_STATE, MSG_GET_DEFAULT_WATCHFACE_ID, MSG_SET_CURRENT_WATCHFACE_ID,
        MSG_SET_APP_INFO, MSG_SET_WARE_DIRECTION, MSG_GET_MUSIC_PLAY_LISTS, MSG_GET_MUSIC_ALL_SONG_DETAIL,
        MSG_GET_MUSIC_PLAYLIST_DETAIL, MSG_GET_MUSIC_INFO, MSG_ADD_OR_UPDATE_PLAYLIST, MSG_DELETE_PLAYLIST,
        MSG_SORT_PLAYLISTS, MSG_SUBSCRIBE_MUSIC_UPDATE, MSG_GET_LOG_FILES, MSG_SET_HR_INTENSITY_ZONES,
        MSG_GET_RECENT_SPORTS, MSG_GET_ALL_SPORTS, MSG_GET_TRAINING_MODE_TEMPLATE_LIST,
        MSG_GET_TRAINING_MODE_TEMPLATE, MSG_GET_TRAINING_MODE_LIST, MSG_GET_TRAINING_MODE, MSG_POST_TRAINING_MODE,
        MSG_PUT_TRAINING_MODE, MSG_DEL_TRAINING_MODE, MSG_GET_DATA_SCREEN_TEMPLATE_LIST, MSG_GET_DATA_SCREEN_LIST,
        MSG_POST_DATA_SCREEN, MSG_PUT_DATA_SCREEN, MSG_DEL_DATA_SCREEN, MSG_GET_OFFLINE_MUSIC_VERSION,
        MSG_SET_USER_GENDER, MSG_SET_USER_WEIGHT, MSG_SET_USER_BIRTH_YEAR, MSG_SET_USER_HEIGHT,
        MSG_SET_USER_MAX_HR, MSG_SET_USER_REST_HR, MSG_SET_USER_UNIT_SYSTEM, MSG_POST_COMPETITION_INFO_TARGET,
        MSG_POST_COMPETITION_SAMPLES_TARGET, MSG_START_QUICK_NAVIGATE, MSG_NAVIGATE_BY_ROUTE, MSG_DEVICE_INFO_NAVIGATE,
        MSG_UPDATE_OTA_MANUAL_DOWNLOAD_FLAG, MSG_FIND_WATCH_CONTROL, MSG_SUBSCRIBE_FIND_WATCH_CONTROL, MSG_GET_DRT_LOG_FILES,
    })
    public @interface MessageTypes {
    }

    public interface ArgumentKeys {
        String ARG_CONFIGURATION = "configuration";
        String ARG_DATA = "data";
    }

    /**
     * Handler of incoming messages from clients.
     */
    @SuppressLint("HandlerLeak")
    private class IncomingHandler extends Handler {

        @Override
        public void handleMessage(Message msg) {
            Timber.v("Received message from client: [" + msg + "]");
            Bundle requestBundle = msg.getData();
            requestBundle.setClassLoader(getClassLoader());
            final Messenger replyTo = msg.replyTo;
            final int messageId = msg.arg1;
            subscriptions.add(handleMessage(msg.what, replyTo, requestBundle)
                    // TODO: send back exceptions in responseObservable
                    .flatMap((Func1<Response, Observable<Integer>>) response -> {
                        if (replyTo == null) {
                            return Observable.error(
                                    new SuuntoRepositoryException("Missing replyTo messenger"));
                        }
                        return sendResponse(messageId, replyTo, response);
                    })
                    .subscribe(
                            messageId1 -> Timber.v("Response sent back with message id [%d]", messageId1),
                            throwable -> Timber.e(throwable, "Failed to send response"))
            );
        }

        private Observable<? extends Response> handleMessage(@MessageTypes int messageType,
                                                             Messenger client, Bundle requestBundle) {
            // Make sure that this Observable emits a finite amount of events, because we'll be
            // sending each event back to the client
            Observable<? extends Response> responseObservable = null;
            Timber.v("Message type: %d", messageType);
            //noinspection SwitchIntDef
            switch (messageType) {
                case MSG_CONFIGURATION:
                    handleConfigurationMessage(requestBundle);
                    responseObservable = /* TODO */ Observable.empty();
                    break;
                case MSG_PAIR:
                    PairQuery pairQuery = requestBundle.getParcelable(ArgumentKeys.ARG_DATA);
                    responseObservable = handlePairMessage(pairQuery).toObservable();
                    break;
                case MSG_DISCONNECT:
                    final DisconnectQuery disconnectQuery =
                            requestBundle.getParcelable(ArgumentKeys.ARG_DATA);
                    return waitConnectivityClient()
                            .andThen(handleDisconnectMessage(disconnectQuery).toObservable());
                case SuuntoRepositoryService.MSG_REGISTER_CLIENT:
                    // Returns immediately and not wait service to be ready.
                    return handleRegisterClientMessage(client).toObservable();
                case SuuntoRepositoryService.MSG_RESPONSE:
                case SuuntoRepositoryService.MSG_BROADCAST:
                    throw new IllegalArgumentException(
                            "Server can't handle MSG_RESPONSE or MSG_BROADCAST");
                case MSG_SYNC_DEVICE:
                    final SyncDeviceQuery syncDeviceQuery =
                            requestBundle.getParcelable(ArgumentKeys.ARG_DATA);
                    responseObservable = handleSyncDeviceMessage(syncDeviceQuery).toObservable();
                    break;
                case MSG_POST_NOTIFICATION:
                    responseObservable = handlePostNotification(requestBundle);
                    break;
                case MSG_REMOVE_NOTIFICATION:
                    responseObservable = handleRemoveNotification(requestBundle);
                    break;
                case MSG_GET_KNOWN_NOTIFICATIONS:
                    responseObservable = Observable.just(new GetKnownNotificationsResponse(
                        notificationManager.getKnownNotifications()));
                    break;
                case MSG_ADD_NOTIFICATIONS_PACKAGE:
                    responseObservable = handleAddNotificationsPackage(requestBundle);
                    break;
                case MSG_ENABLE_NOTIFICATIONS_PACKAGE:
                    responseObservable = handleEnableNotificationsPackage(requestBundle);
                    break;
                case MSG_DISABLE_NOTIFICATIONS_PACKAGE:
                    responseObservable = handleDisableNotificationsPackage(requestBundle);
                    break;
                case MSG_START_LOGGING:
                    final StartLoggingQuery startLoggingQuery =
                            requestBundle.getParcelable(ArgumentKeys.ARG_DATA);
                    responseObservable = handleStartLogging(startLoggingQuery).toObservable();
                    break;
                case MSG_STOP_LOGGING:
                    responseObservable = handleStopLogging().toObservable();
                    break;
                case MSG_GET_LOGS:
                    responseObservable = handleGetLogs(requestBundle);
                    break;
                case MSG_GET_ACTIVE_DEVICES:
                    // Returns immediately and not wait service to be ready,
                    // because UI needs this information fast.
                    // Todo: Find out the right state when this can be called safely.
                    return handleGetActiveDevices().toObservable();
                case MSG_UNPAIR:
                    final UnpairQuery unpairQuery =
                            requestBundle.getParcelable(ArgumentKeys.ARG_DATA);
                    return waitConnectivityClient()
                            .andThen(handleUnpairQuery(unpairQuery).toObservable());
                case MSG_GET_COACH_ENABLED:
                    responseObservable = handleGetCoachEnabled(requestBundle)
                            .toObservable();
                    break;
                case MSG_SET_COACH_ENABLED:
                    responseObservable = handleSetCoachEnabled(requestBundle)
                            .toObservable();
                    break;
                case MSG_CLEAR_CONNENTION_INSTABILITY:
                    responseObservable = handleClearConnectionInstability()
                            .toObservable();
                    break;
                case MSG_GET_WEEKLY_TARGET_DURATION:
                    responseObservable = handleGetWeeklyTargetDuration(requestBundle)
                            .toObservable();
                    break;
                case MSG_SET_WEEKLY_TARGET_DURATION:
                    responseObservable = handleSetWeeklyTargetDuration(requestBundle)
                            .toObservable();
                    break;
                case MSG_GET_247_TARGET:
                    responseObservable = handleGet247Target(requestBundle)
                            .toObservable();
                    break;
                case MSG_SET_247_TARGET:
                    responseObservable = handleSet247Target(requestBundle)
                            .toObservable();
                    break;
                case MSG_GET_SLEEP_TRACKING_MODE:
                    responseObservable = handleGetSleepTrackingMode(requestBundle)
                            .toObservable();
                    break;
                case MSG_SET_SLEEP_TRACKING_MODE:
                    responseObservable = handleSetSleepTrackingMode(requestBundle)
                        .toObservable();
                    break;
                case MSG_GET_247_DAILY_ACTIVITY_VALUE:
                    responseObservable = handleGetActivityValue(requestBundle)
                            .toObservable();
                    break;
                case MSG_MARK_AS_SYNCED:
                    responseObservable = handleMarkAsSynced(requestBundle)
                        .toObservable();
                    break;
                case MSG_RESET_CONNECTION:
                    return waitConnectivityClient()
                        .andThen(handleResetConnection(requestBundle).toObservable());
                case MSG_GET_SERVICE_STABILITY:
                    return waitConnectivityClient().
                        andThen(handleGetServiceStability().toObservable());
                case MSG_USER_LOGOUT:
                    return waitConnectivityClient().
                        andThen(handleUserLogout().toObservable());
                case MSG_REPORT_APP_PROCESS_FOREGROUND:
                    return waitConnectivityClient()
                        .andThen(handleReportAppProcessForeground(requestBundle)
                            .toObservable());
                case MSG_OTA_UPDATE_ACTION:
                    return waitConnectivityClient().
                        andThen(handleFirmwareUpdateAction(requestBundle).toObservable());
                case MSG_IMPORT_WORKOUT:
                    return waitConnectivityClient().
                        andThen(handleImportWorkout(requestBundle).toObservable());
                case MSG_GET_SETTINGS_FILE:
                    return waitConnectivityClient().
                        andThen(handleGetSettingsFile(requestBundle).toObservable());
                case MSG_SET_SETTINGS_FILE:
                    return waitConnectivityClient().
                        andThen(handleSetSettingsFile(requestBundle).toObservable());
                case MSG_SYNC_TRAINING_ZONE:
                    final SyncTrainingZoneQuery syncTrainingZoneQuery =
                        requestBundle.getParcelable(ArgumentKeys.ARG_DATA);
                    responseObservable = handleSyncTrainingZoneMessage(syncTrainingZoneQuery).toObservable();
                    break;
                case MSG_GET_SPO2_NIGHTLY_ENABLED:
                    responseObservable = handleGetSpO2NightlyEnabled(requestBundle)
                        .toObservable();
                    break;
                case MSG_SET_SPO2_NIGHTLY_ENABLED:
                    responseObservable = handleSetSpO2NightlyEnabled(requestBundle)
                        .toObservable();
                    break;
                case MSG_GET_HRV_ENABLED:
                    responseObservable = handleGetHrvEnabled(requestBundle)
                        .toObservable();
                    break;
                case MSG_SET_HRV_ENABLED:
                    responseObservable = handleSetHrvEnabled(requestBundle)
                        .toObservable();
                    break;
                case MSG_GET_247_HR_ENABLED:
                    responseObservable = handleGet247HrEnabled(requestBundle)
                        .toObservable();
                    break;
                case MSG_SET_247_HR_ENABLED:
                    responseObservable = handleSet247HrEnabled(requestBundle)
                        .toObservable();
                    break;
                default:
                    for (SuuntoResponseProducer<? extends Response> producer : suuntoResponseProducers) {
                        if (producer.isRelated(messageType)) {
                            responseObservable = producer.provideResponseObservable(messageType, requestBundle);
                            if (producer instanceof RoutesResponseProducer) {
                                //noinspection unchecked
                                responseObservable = addRouteSyncLogic(requestBundle,
                                    (Observable<PostRoutesResponse>) responseObservable);
                            }
                        }
                    }
                    if (responseObservable == null) {
                        responseObservable = Observable.empty();
                    }
                    break;
            }
            // TODO: Handle responseObservable onError to be communicated to the service client.
            return waitForServiceReady().andThen(responseObservable);
        }
    }

    private Observable<PostRoutesResponse> addRouteSyncLogic(Bundle requestBundle,
        Observable<PostRoutesResponse> responseObservable) {
        PostRoutesQuery query = requestBundle.getParcelable(SuuntoRepositoryService.ArgumentKeys.ARG_DATA);
        if (query == null) return Observable.empty();
        return responseObservable
            .observeOn(AndroidSchedulers.mainThread())
            .onErrorReturn(e -> {
                if (e instanceof AlreadySynchronizingException) {
                    Toast.makeText(getApplicationContext(), R.string.error_routes_sync_watch_is_syncing,
                        Toast.LENGTH_LONG).show();
                    return new PostRoutesResponse();
                } else if (e instanceof WatchNotConnectedException) {
                    Toast.makeText(getApplicationContext(), R.string.error_routes_sync_watch_not_connected,
                        Toast.LENGTH_LONG).show();
                    return new PostRoutesResponse();
                } else if (e instanceof WatchBusyException) {
                    Toast.makeText(getApplicationContext(), R.string.error_routes_sync_watch_is_busy,
                        Toast.LENGTH_LONG).show();
                    // in this case we request a pending sync so the next time the WatchState changes
                    // to not busy, sync happens
                    activeDevices.requestAutoSync(query.getMacAddress());
                    return new PostRoutesResponse();
                }
                throw Exceptions.propagate(e);
            });
    }
}
