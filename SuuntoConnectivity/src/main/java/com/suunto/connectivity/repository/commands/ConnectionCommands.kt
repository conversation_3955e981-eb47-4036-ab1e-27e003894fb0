package com.suunto.connectivity.repository.commands

import android.annotation.SuppressLint
import androidx.annotation.RestrictTo
import com.suunto.connectivity.repository.PairingState
import com.suunto.connectivity.repository.RepositoryConfiguration
import com.suunto.connectivity.repository.SpartanIpc
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDeviceImpl
import com.suunto.connectivity.watch.SpartanSyncResult
import com.suunto.connectivity.watch.WatchState
import kotlinx.parcelize.Parcelize

/**
 * Use this command to tell [SuuntoRepositoryService] that you want to disconnect a device.
 */
@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class DisconnectQuery internal constructor(
    val device: SuuntoBtDevice
) : Query {

    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_DISCONNECT

    companion object {
        @JvmStatic
        fun create(device: SuuntoBtDevice): DisconnectQuery {
            // We need to make sure that we transfer a SuuntoBtDeviceImpl
            return DisconnectQuery(SuuntoBtDeviceImpl.copy(device))
        }
    }
}

@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class DisconnectResponse(
    val isDisconnected: Boolean
) : Response

@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
class GetActiveDevicesQuery : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_ACTIVE_DEVICES
}

@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class GetActiveDevicesResponse(
    val activeDevices: List<SpartanIpc>
) : Response

/**
 * Use this command to tell [SuuntoRepositoryService] that you want to pair a new device.
 *  This will trigger a connect to the device and also store its information so
 *  [SuuntoRepositoryService] can re-connect whenever is needed (e.g. to sync data in
 *  background)
 */
@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class PairQuery(
    val device: SuuntoBtDevice,
    val scannedPairingState: PairingState
) : Query {

    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_PAIR
}

@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
class RegisterClientQuery : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_REGISTER_CLIENT
}

/**
 * @param availableDevices non-null {@link List} of already available devices (might be empty)
 */
@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class RegisterClientResponse(
    val availableDevices: List<SpartanIpc>,
    val repositoryConfiguration: RepositoryConfiguration
) : Response

/**
 * Query for getting service stability information.
 */
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
class ReportAppProcessForegroundQuery(val foreground: Boolean) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_REPORT_APP_PROCESS_FOREGROUND
}

/**
 * Use this command to tell [SuuntoRepositoryService] that you want to remove pairing to a
 *  device.
 */
@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class UnpairQuery internal constructor(
    val device: SuuntoBtDevice
) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_UNPAIR

    companion object {
        @JvmStatic
        fun create(device: SuuntoBtDevice): UnpairQuery {
            // We need to make sure that we transfer a SuuntoBtDeviceImpl
            return UnpairQuery(SuuntoBtDeviceImpl.copy(device))
        }
    }
}

@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class UnpairResponse(
    val isUnpaired: Boolean
) : Response

/**
 * Use this command to tell [SuuntoRepositoryService] that you want to force a
 *  synchronization to a device.
 */
@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class SyncDeviceQuery(
    val device: SuuntoBtDevice,
    val isActivityDataOnly: Boolean
) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_SYNC_DEVICE
}

@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class SyncDeviceResponse(
    val syncResult: SpartanSyncResult
) : Response

@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class SyncTrainingZoneQuery(
    val device: SuuntoBtDevice
) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_SYNC_TRAINING_ZONE
}

@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class SyncTrainingZoneResponse @JvmOverloads constructor(
    val success: Boolean,
    val errorMessage: String = ""
) : Response

// Query and response for getting a current value for daily activity
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class ResetConnectionQuery(
    val SuuntoBtDevice: SuuntoBtDevice,
    val reconnectAfterSeconds: Int?
) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_RESET_CONNECTION
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class ResetConnectionResponse(
    val success: Boolean,
    val reconnectingInSeconds: Int
) : Response

@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class WatchStateUpdateBroadcast(
    val watchMacAddress: String,
    val watchState: WatchState
) : Broadcast

@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class ConnectResponse @JvmOverloads constructor(
    val isConnected: Boolean,
    val reason: String = ""
) : Response

/**
 * Use this command to tell [SuuntoRepositoryService] that connection instability
 *  is cleared and service can try to reconnect immediatelly.
 */
@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
class ClearConnectionInstabilityQuery : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_CLEAR_CONNENTION_INSTABILITY
}

@RestrictTo(value = [RestrictTo.Scope.LIBRARY_GROUP])
@Parcelize
data class MarkAsSyncedQuery(
    val macAddress: String,
    /**
     * Entries to be marked as synced.
     *  @return List of entryId.
     */
    val entryIds: List<Long>
) : Query {

    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_MARK_AS_SYNCED
}

/**
 * Query for getting service stability information.
 */
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
class UserLogoutQuery : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_USER_LOGOUT
}
