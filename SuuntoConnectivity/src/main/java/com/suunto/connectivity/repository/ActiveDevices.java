package com.suunto.connectivity.repository;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.analytics.AnalyticsEventProperty;
import com.stt.android.analytics.AnalyticsProperties;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.utils.NearbyDevicesUtilsKt;
import com.suunto.connectivity.firmware.FirmwareInformationInterface;
import com.suunto.connectivity.hooks.OnDeviceConnectedHook;
import com.suunto.connectivity.repository.commands.CheckForOtaUpdatesResponse;
import com.suunto.connectivity.repository.commands.FirmwareTransferStartResponse;
import com.suunto.connectivity.repository.commands.GetOrSetSettingsFileResponse;
import com.suunto.connectivity.repository.commands.GetSelectedFirmwareResponse;
import com.suunto.connectivity.repository.commands.ResetConnectionResponse;
import com.suunto.connectivity.repository.commands.SelectFirmwareResponse;
import com.suunto.connectivity.repository.commands.SyncDeviceResponse;
import com.suunto.connectivity.repository.commands.SyncTrainingZoneResponse;
import com.suunto.connectivity.repository.stateMachines.connectionStateMachine.ConnectionStateMachine;
import com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine.CheckNeedForFirmwareUpdateState;
import com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine.FirmwareFileUtils;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice;
import com.suunto.connectivity.suuntoconnectivity.utils.BtStateMonitor;
import com.suunto.connectivity.suuntoconnectivity.utils.PairingUtilsKt;
import com.suunto.connectivity.sync.SynchronizerFinalizer;
import com.suunto.connectivity.sync.SynchronizerStorage;
import com.suunto.connectivity.util.SupportedDevices;
import com.suunto.connectivity.util.workqueue.WorkQueue;
import com.suunto.connectivity.watch.WatchBt;
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareInfoData;
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareInfoMetadata;
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareInfoState;
import hu.akarnokd.rxjava.interop.RxJavaInterop;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import okhttp3.OkHttpClient;
import rx.Completable;
import rx.Single;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import timber.log.Timber;

/**
 * Active devices class is keeping and storing information about active device(s), which will be
 * connected automatically by repository service. Currently supports only ONE active device!
 */
public class ActiveDevices {

    final private Handler handler = new Handler(Looper.getMainLooper());
    final private Context context;
    final private SupportedDevices supportedDevices;
    final private SynchronizerStorage synchronizerStorage;
    final private SynchronizerFinalizer synchronizerFinalizer;
    final private FirmwareInformationInterface firmwareInformationInterface;
    final private RepositoryConfiguration repositoryConfiguration;
    final private SharedPreferences suuntoServicePreferences;
    final private OnDeviceConnectedHook onDeviceConnectedHook;

    private class DeviceHolder {
        final WatchBt watchBt;
        final ConnectionStateMachine connectionStateMachine;
        final Subscription unpairSubscription;
        private boolean aboutToUnpairDevice;

        private DeviceHolder(WatchBt watchBt) {
            this.watchBt = watchBt;
            connectionStateMachine = new ConnectionStateMachine(watchBt, btStateMonitor, context,
                analytics, new WorkQueue(), bluetoothAdapter, supportedDevices,
                synchronizerStorage, firmwareInformationInterface, repositoryConfiguration,
                suuntoServicePreferences, onDeviceConnectedHook, stOkHttpClient);
            unpairSubscription = connectionStateMachine.completeWhenUnpaired()
                .observeOn(AndroidSchedulers.from(handler.getLooper()))
                .subscribe(() -> removeDevice(watchBt),
                    throwable -> {
                        // Do nothing.
                    });
        }

        /**
         * Setting the aboutToUnpairDevice -flag to true when un-pairing of the device is
         * expected to happen. This flag is used for sending the ForgetWatch/SpontaneousUnpair
         * analytics event in the situation where un-pairing was not expected.
         */
        void setAboutToUnpairDevice() {
            aboutToUnpairDevice = true;
        }

        private void setPaired(boolean paired) {
            watchBt.setPaired(paired);
        }

        private void onDestroy() {
            connectionStateMachine.onDestroy();
            unpairSubscription.unsubscribe();
        }

        Completable serviceStartConnect() {
            watchBt.setRegistered(true);
            return connectionStateMachine.serviceStartConnect();
        }

        Completable initialConnect(@NonNull PairingState scannedPairingState) {
            return connectionStateMachine.initialConnect(scannedPairingState);
        }

        Completable unpair() {
            return connectionStateMachine.unpair()
                .doOnSubscribe(subscription -> setAboutToUnpairDevice());
        }

        Single<SyncDeviceResponse> syncNow(boolean isActivityDataOnly) {
            return connectionStateMachine.syncNow(isActivityDataOnly);
        }

        SyncTrainingZoneResponse requestTrainingZoneSync() {
            return connectionStateMachine.requestTrainingZoneSync();
        }

        public void clearConnectionInstability() {
            connectionStateMachine.clearConnectionInstability();
        }

        Single<ResetConnectionResponse> resetConnection(int reconnectTimeOut) {
            return connectionStateMachine.resetConnection(reconnectTimeOut);
        }

        void handleReportAppProcessForeground(boolean foreground) {
            connectionStateMachine.handleReportAppProcessForeground(foreground);
        }

        Single<FirmwareTransferStartResponse> startFirmwareTransfer(
            Uri fileUri,
            @Nullable String firmwareVersion) {
            return connectionStateMachine.startFirmwareTransfer(fileUri, firmwareVersion);
        }

        Single<SelectFirmwareResponse> selectFirmware(long packageId, boolean forceUpdate) {
            return connectionStateMachine.selectFirmware(packageId, forceUpdate);
        }

        Single<GetSelectedFirmwareResponse> getSelectedFirmware() {
            return watchBt.getUploadedFirmwares()
                .map((firmwareInfoResult) -> {
                    for (FirmwareInfoData firmware : firmwareInfoResult.getContent().getData())  {
                        if (firmware.getState() == FirmwareInfoState.SELECTED.getValue()) {
                            FirmwareInfoMetadata metadata = firmware.getMetadata();
                            return new GetSelectedFirmwareResponse(
                                true,
                                "",
                                metadata.getDescriptor(),
                                metadata.getPackageId()
                            );
                        }
                    }

                    return new GetSelectedFirmwareResponse(
                        true,
                        "",
                        null,
                        null
                    );
                });
        }

        public void stopOtaUpdate() {
            connectionStateMachine.stopOtaUpdate();
        }

        public boolean isAboutToUnpairDevice() {
            return aboutToUnpairDevice;
        }
    }

    /**
     * Active device, which will be connected automatically by repository service. Only one device
     * supported at the moment.
     */
    private final Map<String, DeviceHolder> activeDevices = new HashMap<>();
    private final ActiveDeviceStorage deviceStorage;
    private final BtStateMonitor btStateMonitor;

    private final BluetoothAdapter bluetoothAdapter;
    private final ConnectionAnalytics analytics;

    private final OkHttpClient stOkHttpClient;

    ActiveDevices(Context context, SupportedDevices supportedDevices,
        SynchronizerStorage synchronizerStorage,
        SynchronizerFinalizer synchronizerFinalizer,
        FirmwareInformationInterface firmwareInformationInterface,
        RepositoryConfiguration repositoryConfiguration,
        SharedPreferences suuntoServicePreferences,
        BtStateMonitor btStateMonitor,
        BluetoothAdapter bluetoothAdapter,
        ActiveDeviceStorage deviceStorage, ConnectionAnalytics analytics,
        OnDeviceConnectedHook onDeviceConnectedHook,
        OkHttpClient stOkHttpClient) {
        this.context = context;
        this.supportedDevices = supportedDevices;
        this.synchronizerStorage = synchronizerStorage;
        this.synchronizerFinalizer = synchronizerFinalizer;
        this.firmwareInformationInterface = firmwareInformationInterface;
        this.repositoryConfiguration = repositoryConfiguration;
        this.suuntoServicePreferences = suuntoServicePreferences;
        this.btStateMonitor = btStateMonitor;
        this.bluetoothAdapter = bluetoothAdapter;
        this.deviceStorage = deviceStorage;
        this.analytics = analytics;
        this.onDeviceConnectedHook = onDeviceConnectedHook;
        this.stOkHttpClient = stOkHttpClient;
        putDevice(deviceStorage.getActiveDevice());
    }

    @Nullable
    private synchronized DeviceHolder putDevice(@Nullable WatchBt watchBt) {
        if (watchBt != null && activeDevices.isEmpty()) {
            final String macAddress = watchBt.getMacAddress();
            DeviceHolder device = new DeviceHolder(watchBt);
            activeDevices.put(macAddress, device);
            return device;
        }
        return null;
    }

    private synchronized void removeDevice(@NonNull WatchBt watchBt) {
        final String macAddress = watchBt.getMacAddress();
        DeviceHolder device = activeDevices.remove(macAddress);
        if (device != null) {
            Timber.d("Removing %s from active devices", watchBt.getMacAddress());
            if (device.isAboutToUnpairDevice()) {
                Timber.d("User forgot the device, pairing error or disconnect on purpose");
            } else {
                Timber.d("Device lost spontaneously");
                if (NearbyDevicesUtilsKt.isNearbyDevicesPermissionGranted(context) &&
                    PairingUtilsKt.checkPairingState(watchBt.getSuuntoBtDevice()) == PairingState.Unpaired) {
                    // Send analytics.
                    AnalyticsProperties properties = new AnalyticsProperties();
                    properties.put(AnalyticsEventProperty.CONTEXT,
                        AnalyticsPropertyValue.UnpairContext.SPONTANEOUS_UNPAIR);
                    AnalyticsUtils.sendForgetWatchAmplitudeEvent(properties);
                }
            }
            device.onDestroy();
            deviceStorage.deleteActiveDevices();
            synchronizerFinalizer.cleanup(watchBt);
            watchBt.onDestroy();
        }
    }

    @Nullable
    private synchronized DeviceHolder getDevice(@Nullable String macAddress) {
        return activeDevices.get(macAddress);
    }

    public void onDestroy() {
    }

    /**
     * Ensure that active devices are still paired. If not remove active device(s).
     * Synchronized just in case because this may be called concurrently.
     */
    @SuppressLint("MissingPermission")
    public synchronized void ensureBtPairings() {
        boolean nearbyDevicesGranted = NearbyDevicesUtilsKt.isNearbyDevicesPermissionGranted(context);
        if (nearbyDevicesGranted && bluetoothAdapter != null && bluetoothAdapter.isEnabled()) {
            List<String> pairedDevices = new ArrayList<>();
            if (NearbyDevicesUtilsKt.isNearbyDevicesPermissionGranted(context)) {
                Set<BluetoothDevice> bondedDevices = bluetoothAdapter.getBondedDevices();
                if (bondedDevices != null && !bondedDevices.isEmpty()) {
                    for (BluetoothDevice bluetoothDevice : bondedDevices) {
                        pairedDevices.add(bluetoothDevice.getAddress());
                    }
                }
            } else {
                Timber.e("Missing nearby devices permission");
            }
            List<WatchBt> devicesToRemove = new ArrayList<>();
            for (Map.Entry<String, DeviceHolder> deviceEntry : activeDevices.entrySet()) {
                if (pairedDevices.contains(deviceEntry.getKey())) {
                    // Device is paired.
                    deviceEntry.getValue().setPaired(true);
                } else if (deviceEntry.getValue().watchBt.getSuuntoBtDevice().getDeviceType().isBleDevice()){
                    // Device is not paired.
                    Timber.d("Device %s not paired anymore.", deviceEntry.getKey());
                    deviceEntry.getValue().setPaired(false);
                    devicesToRemove.add(deviceEntry.getValue().watchBt);
                }
            }
            // Remove devices.
            for (WatchBt removed : devicesToRemove) {
                removeDevice(removed);
            }
        }
    }

    /**
     * Reconnect devices when service starting.
     * Todo: This is not ok interface for multiple devices.
     *
     * @return Completable for devices reconnection.
     */
    Completable reconnectDevicesOnServiceStart() {
        if (!activeDevices.isEmpty()) {
            DeviceHolder device = activeDevices.values().iterator().next();
            return device.serviceStartConnect();
        } else {
            return Completable.complete();
        }
    }

    /**
     * Connect device and save it as active device.
     *
     * @param watchBt watchBt device to be connected.
     * @param scannedPairingState cannedPairingState is watch advertising itself as paired when it
     * was scanned.
     * @return Connection result as single.
     */
    Completable connectAndActivate(final WatchBt watchBt, PairingState scannedPairingState) {
        DeviceHolder device = putDevice(watchBt);
        if (device != null) {
            device.handleReportAppProcessForeground(true);
            return device.initialConnect(scannedPairingState)
                .andThen(saveActiveDeviceToDisk(watchBt))
                .doOnError(throwable -> {
                    Timber.w(throwable, "Device connect and save failed");
                    watchBt.setRegistered(false);
                    device.setAboutToUnpairDevice();
                    removeDevice(watchBt);
                })
                .doOnSuccess(savedWatchBt -> savedWatchBt.setRegistered(true))
                .toCompletable();
        }
        return Completable.error(new Exception("Can not add new device and connect"));
    }

    /**
     * Disconnect device and remove it from activate devices.
     *
     * @param watchBt watchBt Device to be disconnected.
     * @return Disconnect result as single.
     */
    Completable disconnectAndDeactivate(final WatchBt watchBt) {
        DeviceHolder deviceHolder = getDevice(watchBt.getMacAddress());
        if (deviceHolder == null) {
            return Completable.error(new Exception("Not connected to given watch"));
        } else {
            return Completable.fromAction(() -> {
                deviceHolder.setAboutToUnpairDevice();
                removeDevice(watchBt);
            });
        }
    }

    /**
     * Remove device pairing.
     *
     * @param watchBt Device to be unpaired.
     * @return Complete after unpaired.
     */
    Completable unpair(final WatchBt watchBt) {
        DeviceHolder deviceHolder = getDevice(watchBt.getMacAddress());
        if (deviceHolder == null) {
            return Completable.error(new Exception("Not connected to given watch"));
        } else {
            return deviceHolder.unpair()
                .onErrorComplete();
        }
    }

    /**
     * Reset connection to Watch. This will first disconnect from the device
     * and then reconnect after timeout defined in seconds.
     *
     * @param watchBt Watch
     * @param reconnectTimeOut Reconnect in seconds.
     * @return Single emitting ResetConnectionResponse.
     */
    Single<ResetConnectionResponse> resetConnection(final WatchBt watchBt, int reconnectTimeOut) {
        final String macAddress = watchBt.getMacAddress();
        DeviceHolder deviceHolder = getDevice(macAddress);
        if (deviceHolder == null) {
            return Single.just(new ResetConnectionResponse(false, 0));
        } else {
            return deviceHolder.resetConnection(reconnectTimeOut);
        }
    }

    /**
     * Save active device to disk and set active device.
     *
     * @param device Device.
     * @return Single emitting same WatchBt back received as parameter.
     */
    private Single<WatchBt> saveActiveDeviceToDisk(final WatchBt device) {
        Timber.d("saveActiveDeviceToDisk");
        return Single.fromCallable(() -> {
            if (!deviceStorage.storeActiveDevice(device)) {
                Timber.e("Failed to store active device!");
                throw new RuntimeException("Failed to store active device!");
            }
            return device;
        });
    }

    @NonNull
    private synchronized Collection<WatchBt> doGetSuuntoBtDevices() {
        List<WatchBt> suuntoBtDevices = new ArrayList<>();
        for (DeviceHolder device : activeDevices.values()) {
            suuntoBtDevices.add(device.watchBt);
        }
        return suuntoBtDevices;
    }

    @NonNull
    Single<Collection<WatchBt>> geBtDevicesSingle() {
        return Single.just(doGetSuuntoBtDevices());
    }

    @NonNull
    Collection<WatchBt> getBtDevices() {
        return doGetSuuntoBtDevices();
    }

    @NonNull
    Collection<WatchBt> getBtDevicesEnsurePairings() {
        ensureBtPairings();
        return doGetSuuntoBtDevices();
    }

    @Nullable
    public WatchBt getWatchBt(@Nullable String macAddress) {
        DeviceHolder device = getDevice(macAddress);
        return device != null ? device.watchBt : null;
    }

    Single<SyncDeviceResponse> syncNow(SuuntoBtDevice device, boolean isActivityDataOnly) {
        final String macAddress = device.getMacAddress();
        DeviceHolder deviceHolder = getDevice(macAddress);
        if (deviceHolder == null) {
            return Single.error(new Exception("Unknown device [" + macAddress + "]"));
        } else {
            return deviceHolder.syncNow(isActivityDataOnly);
        }
    }

    Single<SyncTrainingZoneResponse> requestTrainingZoneSync(SuuntoBtDevice device) {
        final String macAddress = device.getMacAddress();
        DeviceHolder deviceHolder = getDevice(macAddress);
        if (deviceHolder == null) {
            return Single.error(new Exception("Unknown device [" + macAddress + "]"));
        } else {
            return Single.fromCallable(deviceHolder::requestTrainingZoneSync);
        }
    }

    /**
     * Triggers autoSync at the next WatchState non-busy update
     */
    public void requestAutoSync(@Nullable String macAddress) {
        if (macAddress == null) return;
        DeviceHolder deviceHolder = getDevice(macAddress);
        if (deviceHolder != null) {
            deviceHolder.connectionStateMachine.requestAutoSync();
        }
    }

    public Completable syncRoutes(@Nullable String macAddress,@Nullable String navigateRouteId) {
        WatchBt watchBtDevice = getWatchBt(macAddress);
        if (watchBtDevice == null) {
            return Completable.complete();
        }
        return watchBtDevice.getWatchSynchronizer().explicitSyncRoutes(navigateRouteId);
    }

    public Boolean supportsClimbGuidance(@Nullable String macAddress){
        WatchBt watchBtDevice = getWatchBt(macAddress);
        if (watchBtDevice == null) {
            return false;
        }
        return watchBtDevice.getWatchSynchronizer().supportsClimbGuidance();
    }

    public int getMaxRoutePointsAllowed(@Nullable String macAddress) {
        WatchBt watchBtDevice = getWatchBt(macAddress);
        if (watchBtDevice == null) {
            return -1;
        }
        return watchBtDevice.getWatchSynchronizer().getMaxRoutePointsAllowed();
    }

    public Completable syncPOIs(@Nullable String macAddress) {
        WatchBt watchBtDevice = getWatchBt(macAddress);
        if (watchBtDevice == null) {
            return Completable.complete();
        }
        return watchBtDevice.getWatchSynchronizer().explicitSyncPOIs();
    }

    public Completable syncSuuntoPlusGuides(@Nullable String macAddress) {
        WatchBt watchBtDevice = getWatchBt(macAddress);
        if (watchBtDevice == null) {
            return Completable.complete();
        }
        return watchBtDevice.getWatchSynchronizer().explicitSyncSuuntoPlusGuides();
    }

    /**
     * Clear connection instability in case there is connection instability.
     */
    void clearConnectionInstability() {
        for (DeviceHolder holder : activeDevices.values()) {
            holder.clearConnectionInstability();
        }
    }

    void handleReportAppProcessForeground(boolean foreground) {
        for (DeviceHolder holder : activeDevices.values()) {
            holder.handleReportAppProcessForeground(foreground);
        }
    }

    Single<FirmwareTransferStartResponse> startFirmwareTransfer(
        @NonNull String macAddress,
        Uri fileUri,
        @Nullable String firmwareVersion) {
        DeviceHolder deviceHolder = getDevice(macAddress);
        if (deviceHolder != null) {
            return deviceHolder.startFirmwareTransfer(fileUri, firmwareVersion);
        } else {
            return Single.just(new FirmwareTransferStartResponse(false,
                "Device to be updated not registered"));
        }
    }

    Single<SelectFirmwareResponse> selectFirmware(
        @NonNull String macAddress,
        long packageId,
        boolean forceUpdate
    ) {
        DeviceHolder deviceHolder = getDevice(macAddress);
        if (deviceHolder != null) {
            return deviceHolder.selectFirmware(packageId, forceUpdate);
        } else {
            return Single.just(
                new SelectFirmwareResponse(false, "Device not registered")
            );
        }
    }

    public Single<GetSelectedFirmwareResponse> getSelectedFirmware(
        @NonNull String macAddress
    ) {
        DeviceHolder deviceHolder = getDevice(macAddress);
        if (deviceHolder != null) {
            return deviceHolder.getSelectedFirmware();
        } else {
            return Single.just(
                new GetSelectedFirmwareResponse(
                    false,
                    "Device not registered",
                    null,
                    null
                )
            );
        }
    }

    Single<CheckForOtaUpdatesResponse> checkForUpdates(@NonNull String macAddress, @Nullable Uri deepLinkFirmware) {
        DeviceHolder deviceHolder = getDevice(macAddress);
        if (deviceHolder != null) {
            // Somewhat hackish to check this here.
            String pathToFirmwareFile = new FirmwareFileUtils(deviceHolder.watchBt, context,
                repositoryConfiguration).pathToCurrentFirmwareFile();
            if (pathToFirmwareFile != null
                && !pathToFirmwareFile.isEmpty()
                && deepLinkFirmware == null) {
                // Firmware file transfer is in progress, when firmware file exists.
                return Single.just(CheckForOtaUpdatesResponse.firmwareTransferInProgress());
            } else {
                return
                    Completable.fromAction(() -> {
                        if (deepLinkFirmware != null) {
                            // Todo: Changes this? Any on going update is stopped when deep link
                            // update attempted.
                            stopOtaUpdate(deviceHolder.watchBt.getMacAddress());
                        }
                    })
                        .andThen(
                            RxJavaInterop.toV1Single(
                                CheckNeedForFirmwareUpdateState.checkForUpdates(
                                    deviceHolder.watchBt,
                                    firmwareInformationInterface,
                                    deepLinkFirmware))
                        );
            }
        } else {
            return Single.just(
                CheckForOtaUpdatesResponse.error("Device to be updated not registered"));
        }
    }

    public void stopOtaUpdate(String macAddress) {
        DeviceHolder deviceHolder = getDevice(macAddress);
        if (deviceHolder != null) {
            deviceHolder.stopOtaUpdate();
        }
    }

    public void setConnectedGpsInUse(boolean inUse) {
        for (DeviceHolder holder : activeDevices.values()) {
            holder.watchBt.setConnectedGpsInUse(inUse);
        }
    }

    Single<GetOrSetSettingsFileResponse> getSettingsFile(@NonNull String macAddress, @NonNull Uri fileUri) {
        DeviceHolder deviceHolder = getDevice(macAddress);
        if (deviceHolder != null) {
            return deviceHolder.watchBt.getSettingsFile(fileUri.getPath())
                .andThen(Single.just(new GetOrSetSettingsFileResponse(true, null)))
                .onErrorReturn(throwable -> new GetOrSetSettingsFileResponse(false, throwable.toString()));
        } else {
            return Single.just(new GetOrSetSettingsFileResponse(false, "No device"));
        }
    }

    Single<GetOrSetSettingsFileResponse> setSettingsFile(@NonNull String macAddress,
        @NonNull String filePath) {
        DeviceHolder deviceHolder = getDevice(macAddress);
        if (deviceHolder != null) {
            return deviceHolder.watchBt.setSettingsFile(filePath)
                .andThen(Single.just(new GetOrSetSettingsFileResponse(true, null)))
                .onErrorReturn(
                    throwable -> new GetOrSetSettingsFileResponse(false, throwable.toString()));
        } else {
            return Single.just(new GetOrSetSettingsFileResponse(false, "No device"));
        }
    }
}
