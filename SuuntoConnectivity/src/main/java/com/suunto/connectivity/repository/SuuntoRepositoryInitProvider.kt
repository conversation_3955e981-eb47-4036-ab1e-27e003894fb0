package com.suunto.connectivity.repository

import android.companion.CompanionDeviceManager
import android.content.ContentProvider
import android.content.ContentValues
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Build
import timber.log.Timber

class SuuntoRepositoryInitProvider : ContentProvider() {

    override fun onCreate(): Boolean {
        val appContext = context!!.applicationContext
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (appContext.packageManager.hasSystemFeature(PackageManager.FEATURE_COMPANION_DEVICE_SETUP)) {
                val deviceManager = appContext.getSystemService(CompanionDeviceManager::class.java)
                if (deviceManager.associations.isEmpty()) {
                    // user has not enabled companion, we cannot start the service from the background
                    return false
                }
            } else {
                // companion is not available on this device, we cannot start the service from the background
                return false
            }
        }
        Timber.d("Start foreground service...")
        SuuntoRepositoryService.startService(context, false, "SuuntoRepositoryInitProvider")
        Timber.d("Start foreground service called")
        return false
    }

    override fun query(
        uri: Uri,
        projection: Array<String>?,
        selection: String?,
        selectionArgs: Array<String>?,
        sortOrder: String?
    ): Cursor? {
        return null
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun delete(
        uri: Uri,
        selection: String?,
        selectionArgs: Array<String>?
    ): Int {
        return 0
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<String>?
    ): Int {
        return 0
    }
}
