package com.suunto.connectivity.notifications

import android.content.Context
import com.stt.android.utils.toV2
import com.suunto.connectivity.repository.SuuntoRepositoryClient
import com.suunto.connectivity.repository.SuuntoRepositoryService
import kotlinx.coroutines.rx2.await

/**
 * Internal class for posting status bar notifications via ANCS.
 */
internal class SuuntoNotificationClient private constructor(
    private val suuntoRepositoryClient: SuuntoRepositoryClient,
) {
    suspend fun postNotification(ancsMessage: AncsMessage) {
        suuntoRepositoryClient.postNotification(ancsMessage)
            .toV2()
            .await()
    }

    suspend fun removeNotification(messageId: Int) {
        suuntoRepositoryClient.removeNotification(messageId)
            .toV2()
            .await()
    }

    companion object {
        fun create(appContext: Context): SuuntoNotificationClient = SuuntoNotificationClient(
            suuntoRepositoryClient = SuuntoRepositoryService.getClientForAncsService(appContext),
        )
    }
}
