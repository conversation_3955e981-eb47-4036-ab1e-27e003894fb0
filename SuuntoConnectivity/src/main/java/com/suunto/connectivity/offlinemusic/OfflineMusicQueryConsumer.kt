package com.suunto.connectivity.offlinemusic

import com.suunto.connectivity.SuuntoQueryConsumer
import com.suunto.connectivity.repository.ResponseMessage
import com.suunto.connectivity.repository.SuuntoRepositoryClient
import com.suunto.connectivity.repository.SuuntoRepositoryException
import com.suunto.connectivity.repository.commands.AddOrUpdatePlayListQuery
import com.suunto.connectivity.repository.commands.AddOrUpdatePlayListResponse
import com.suunto.connectivity.repository.commands.DeletePlayListQuery
import com.suunto.connectivity.repository.commands.DeletePlayListResponse
import com.suunto.connectivity.repository.commands.GetAllSongQuery
import com.suunto.connectivity.repository.commands.GetAllSongResponse
import com.suunto.connectivity.repository.commands.GetOfflineMusicInfoQuery
import com.suunto.connectivity.repository.commands.GetOfflineMusicInfoResponse
import com.suunto.connectivity.repository.commands.GetOfflineMusicVersionQuery
import com.suunto.connectivity.repository.commands.GetOfflineMusicVersionResponse
import com.suunto.connectivity.repository.commands.GetPlayListDetailQuery
import com.suunto.connectivity.repository.commands.GetPlayListDetailResponse
import com.suunto.connectivity.repository.commands.GetPlayListsQuery
import com.suunto.connectivity.repository.commands.GetPlayListsResponse
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.repository.commands.SortPlayListsQuery
import com.suunto.connectivity.repository.commands.SortPlayListsResponse
import com.suunto.connectivity.repository.commands.SubscribeMusicUpdateQuery
import com.suunto.connectivity.repository.commands.SubscribeMusicUpdateResponse
import rx.Observable
import rx.Single

class OfflineMusicQueryConsumer(
    private val suuntoRepositoryClient: SuuntoRepositoryClient,
) : SuuntoQueryConsumer {
    private val relatedClasses = listOf(
        GetAllSongResponse::class.java,
        GetPlayListsResponse::class.java,
        GetPlayListDetailResponse::class,
        GetOfflineMusicInfoResponse::class.java,
        AddOrUpdatePlayListResponse::class.java,
        DeletePlayListResponse::class.java,
        SortPlayListsResponse::class.java,
        SubscribeMusicUpdateResponse::class.java,
        GetOfflineMusicVersionResponse::class.java
    )

    override fun isResponseRelated(response: Response): Boolean {
        return response::class.java in relatedClasses
    }

    override fun getResponseMessage(messageId: Int, response: Response): ResponseMessage<*>? {
        return ResponseMessage(messageId, response)
    }

    fun getOfflineAllPlayList(macAddress: String): Single<List<PlayListHeader>> {
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(GetPlayListsQuery(macAddress))
                .first()
                .toSingle()
                .map { response ->
                    if (response is GetPlayListsResponse) {
                        return@map response.playLists
                    }
                    throw SuuntoRepositoryException("Invalid response [$response]")
                }
        )
    }

    fun getAllSongPlayList(macAddress: String): Single<PlayList> {
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(GetAllSongQuery(macAddress))
                .first()
                .toSingle()
                .map { response ->
                    if (response is GetAllSongResponse) {
                        return@map response.playList
                    }
                    throw SuuntoRepositoryException("Invalid response [$response]")
                }
        )
    }

    fun getPlayListDetail(macAddress: String, playListId: Long): Single<PlayList> {
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(GetPlayListDetailQuery(macAddress, playListId))
                .first()
                .toSingle()
                .map { response ->
                    if (response is GetPlayListDetailResponse) {
                        return@map response.playList
                    }
                    throw SuuntoRepositoryException("Invalid response [$response]")
                }
        )
    }

    fun deletePlayList(macAddress: String, playListId: Long): Single<Boolean> {
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(DeletePlayListQuery(macAddress, playListId))
                .first()
                .toSingle()
                .map { response ->
                    if (response is DeletePlayListResponse) {
                        return@map response.isSuccessful
                    }
                    throw SuuntoRepositoryException("Invalid response [$response]")
                }
        )
    }

    fun getMusicInfo(macAddress: String, musicKey: Long): Single<MusicInfo> {
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(GetOfflineMusicInfoQuery(macAddress, musicKey))
                .first()
                .toSingle()
                .map { response ->
                    if (response is GetOfflineMusicInfoResponse) {
                        return@map response.musicInfo
                    }
                    throw SuuntoRepositoryException("Invalid response [$response]")
                }
        )
    }

    fun createPlayList(macAddress: String, playList: PlayList): Single<Boolean> {
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(AddOrUpdatePlayListQuery(macAddress, false, playList))
                .first()
                .toSingle()
                .map { response ->
                    if (response is AddOrUpdatePlayListResponse) {
                        return@map response.isSuccessful
                    }
                    throw SuuntoRepositoryException("Invalid response [$response]")
                }
        )
    }

    fun updatePlayList(macAddress: String, playList: PlayList): Single<Boolean> {
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(AddOrUpdatePlayListQuery(macAddress, true, playList))
                .first()
                .toSingle()
                .map { response ->
                    if (response is AddOrUpdatePlayListResponse) {
                        return@map response.isSuccessful
                    }
                    throw SuuntoRepositoryException("Invalid response [$response]")
                }
        )
    }

    fun sortPlayLists(macAddress: String, sorts: List<PlayListSort>): Single<Boolean> {
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(SortPlayListsQuery(macAddress, sorts))
                .first()
                .toSingle()
                .map { response ->
                    if (response is SortPlayListsResponse) {
                        return@map response.isSuccessful
                    }
                    throw SuuntoRepositoryException("Invalid response [$response]")
                }
        )
    }

    fun observeMusicUpdate(macAddress: String): Observable<Boolean> {
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(SubscribeMusicUpdateQuery(macAddress))
                .map { response ->
                    if (response is SubscribeMusicUpdateResponse) {
                        return@map response.updated
                    }
                    throw SuuntoRepositoryException("Invalid response [$response]")
                }
        )
    }

    fun getOfflineMusicVersion(macAddress: String): Single<Int> {
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(GetOfflineMusicVersionQuery(macAddress))
                .first()
                .toSingle()
                .map { response ->
                    if (response is GetOfflineMusicVersionResponse) {
                        return@map response.version
                    }
                    throw SuuntoRepositoryException("Invalid response [$response]")
                }
        )
    }
}
