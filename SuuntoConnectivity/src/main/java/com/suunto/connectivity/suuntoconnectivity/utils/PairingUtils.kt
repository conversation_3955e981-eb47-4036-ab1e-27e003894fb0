package com.suunto.connectivity.suuntoconnectivity.utils

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import com.suunto.connectivity.repository.PairingState
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice
import timber.log.Timber

/**
 * Requires nearby permissions
 */
@SuppressLint("MissingPermission")
fun checkPairingState(suuntoBtDevice: SuuntoBtDevice): PairingState {
    val bluetoothAdapter: BluetoothAdapter? = BluetoothAdapter.getDefaultAdapter()
    val deviceName: String = suuntoBtDevice.name
    val macAddress: String = suuntoBtDevice.macAddress
    return if (bluetoothAdapter != null && bluetoothAdapter.isEnabled) {
        val bondedDevices = bluetoothAdapter.bondedDevices
        if (suuntoBtDevice.deviceType.isDataLayerDevice) {
            if (bondedDevices?.any { it.name == deviceName } == null) {
                PairingState.Paired
            } else {
                PairingState.Unpaired
            }
        } else if (bondedDevices?.any { it.address == macAddress } == null) {
            PairingState.Paired
        } else {
            Timber.d("Device (%s,%s) not paired.", macAddress, deviceName)
            PairingState.Unpaired
        }
    } else {
        Timber.d("Device (%s,%s) pairing state unknown.", macAddress, deviceName)
        PairingState.Unknown
    }
}
