package com.suunto.connectivity.suuntoconnectivity.notification;

import android.provider.BaseColumns;

/**
 * Contract to lay out what can be requested from NotificationSettingsProvider and
 * by which URLs
 * <p/>
 * Created by stenhamt on 02/09/16.
 */
public class NotificationSettingsContract {

    /**
     * Constants for the Packages table of the notification settigns provider.
     */
    public static final class Packages implements BaseColumns {

        /**
         * The name of the package.
         */
        public static final String NAME = "name";

        /**
         * Enabled status of the package package. (0 = false, 1 = true)
         */
        public static final String ENABLED = "enabled";

        /**
         * Int value for boolean false in database
         */
        public static final int FALSE = 0;

        /**
         * Int value for boolean true in database
         */
        public static final int TRUE = 1;

        /**
         * A projection of all columns in the packages table.
         */
        public static final String[] PROJECTION_ALL = {_ID, NAME, ENABLED};

    }
}
