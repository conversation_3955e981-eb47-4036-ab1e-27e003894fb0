package com.suunto.connectivity.suuntoconnectivity.ble.operation;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import static android.bluetooth.BluetoothDevice.TRANSPORT_LE;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattConnectionStateChangedEvent;
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattEvent;
import com.suunto.connectivity.suuntoconnectivity.ble.exception.GattConnectException;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import timber.log.Timber;

/**
 * Operation for connecting to GATT server hosted by the given device.
 *
 * The operation completes when a connection is successfully established.
 */
public class BleGattOperationConnect extends BleGattOperation<BluetoothGatt> {

    private static final int ERROR_DELAY_ON_DISCONNECTED_EVENT_MS = 2000;

    private final Context context;
    private final BluetoothGattCallback gattCallback;
    private final boolean autoConnect;
    private final Object gattLock = new Object();
    protected final Handler handler;

    private Runnable disconnectedErrorRunnable;

    /**
     * Constructor.
     *
     * @param context Context
     * @param bluetoothDevice Bluetooth device hosting the GATT server where to connect
     * @param gattCallback Receiver for GATT calbacks. Usually this should be an instance of
     *      {@link com.suunto.connectivity.suuntoconnectivity.ble.BleGattCallbackEventPublisher}
     * @param autoConnect Whether to directly connect to the remote device (false)
     *      or to automatically connect as soon as the remote device becomes available (true).
     */
    public BleGattOperationConnect(@NonNull Context context, @NonNull BluetoothDevice bluetoothDevice,
        @NonNull BluetoothGattCallback gattCallback, boolean autoConnect) {
        super(bluetoothDevice);

        this.context = context;
        this.gattCallback = gattCallback;
        this.autoConnect = autoConnect;
        this.handler = handlerProvider.createHandler();

        setTimeout(0); // No timeout by default
    }

    @Override
    protected void protectedRun() throws Throwable {
        super.protectedRun();

        // Lock access to the bluetoothGatt until we have set its value.
        // Otherwise connection state change event received on another thread
        // may cause reading the gatt value too soon.
        synchronized (gattLock) {
            BluetoothGatt gatt = null;
            if (autoConnect && Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            /*
            * Some old implementations of Android bluetooth stack have a race
            * condition where autoConnect flag is not properly set before
            * calling connectGatt. We try to set the flag manually using
            * reflection.
            */
                gatt = connectUsingReflection();
            }
            if (gatt == null) {
                gatt = connectWithoutReflection();
            }

            if (gatt == null) {
                onError(new GattConnectException(OPERATION_START_EXCEPTION_MESSAGE));
            } else {
                bluetoothGatt.set(gatt);

                if (autoConnect) {
                    // The operation continues running, but the queue is already released here.
                    releaseQueue();
                }
            }
        }
    }

    @SuppressLint("MissingPermission")
    @Override
    protected void onError(@Nullable Throwable throwable) {
        BluetoothGatt gatt;
        synchronized (gattLock) {
            gatt = bluetoothGatt.getAndSet(null);
        }
        if (gatt != null) {
            try {
                gatt.close();
            } catch (Throwable t) {
                Timber.e(t, "BluetoothGatt.close() failed");
            }
        }
        super.onError(throwable);
    }

    @Override
    protected void onStop() {
        super.onStop();

        handler.removeCallbacks(disconnectedErrorRunnable);
    }

    @Nullable
    @Override
    protected Exception customTimeoutException() {
        return new GattConnectException(OPERATION_TIMEOUT);
    }

    @Override
    protected void handleBleGattEvent(BleGattEvent event) {
        super.handleBleGattEvent(event);

        if (event instanceof BleGattConnectionStateChangedEvent) {
            BleGattConnectionStateChangedEvent connectionStateEvent =
                    (BleGattConnectionStateChangedEvent) event;
            if (connectionStateEvent.getNewState() == BluetoothProfile.STATE_CONNECTED) {
                if (connectionStateEvent.getStatus() == BluetoothGatt.GATT_SUCCESS) {
                    BluetoothGatt gatt;
                    synchronized (gattLock) {
                        gatt = bluetoothGatt.get();
                    }
                    onCompleted(gatt);
                }
                else {
                    // In some cases a timeout may cause the connection state to be reported incorrectly as CONNECTED.
                    // See http://stackoverflow.com/questions/22214254/
                    onError(new GattConnectException("Gatt client connection was not successful"));
                }
            }
            else if (connectionStateEvent.getNewState() == BluetoothProfile.STATE_DISCONNECTED) {
                // Sometimes connection state may change to disconnected and then right after
                // that to connected. Call onError with delay, and cancel the call if the
                // connection state changes to connected during the wait time.

                disconnectedErrorRunnable =
                    () -> onError(new GattConnectException("Gatt client connection disconnected"));

                handler.postDelayed(
                    disconnectedErrorRunnable,
                    ERROR_DELAY_ON_DISCONNECTED_EVENT_MS
                );
            }
        }
    }

    @SuppressLint("MissingPermission")
    private BluetoothGatt connectWithoutReflection() {
        Timber.v("Connecting without reflection");
        return bluetoothDevice.connectGatt(context, autoConnect, gattCallback, TRANSPORT_LE);
    }

    @SuppressLint("MissingPermission")
    private BluetoothGatt connectUsingReflection() {
        Timber.v("Trying to connect using reflection");
        try {
            Object iBluetoothGatt = getIBluetoothGatt(getIBluetoothManager());
            if (iBluetoothGatt == null) {
                Timber.w("Failed to get ibluetoothGatt object");
                return null;
            }

            BluetoothGatt bluetoothGatt = createBluetoothGatt(iBluetoothGatt, bluetoothDevice);

            setAutoConnectValue(bluetoothGatt, autoConnect);
            @SuppressWarnings({"JavaReflectionMemberAccess", "DiscouragedPrivateApi"})
            Method connectMethod = bluetoothGatt.getClass().getDeclaredMethod(
                    "connect", Boolean.class, BluetoothGattCallback.class);
            connectMethod.setAccessible(true);
            //noinspection ConstantConditions
            if (!((boolean) connectMethod.invoke(bluetoothGatt, true, gattCallback))) {
                Timber.w("Connect using reflection failed, closing GATT");
                bluetoothGatt.close();
                return null;
            }
            else {
                return bluetoothGatt;
            }
        } catch (Throwable exception) {
            Timber.w(exception, "Error during reflection in connectUsingReflection()");
            return null;
        }
    }

    @NonNull
    private BluetoothGatt createBluetoothGatt(Object iBluetoothGatt, BluetoothDevice remoteDevice)
            throws IllegalAccessException, InvocationTargetException, InstantiationException {
        Constructor bluetoothGattConstructor = BluetoothGatt.class.getDeclaredConstructors()[0];
        bluetoothGattConstructor.setAccessible(true);

        if (bluetoothGattConstructor.getParameterTypes().length == 4) {
            return (BluetoothGatt) (bluetoothGattConstructor.newInstance(
                    context, iBluetoothGatt, remoteDevice, TRANSPORT_LE));
        } else {
            return (BluetoothGatt) (bluetoothGattConstructor.newInstance(
                    context, iBluetoothGatt, remoteDevice));
        }
    }

    private Object getIBluetoothGatt(Object iBluetoothManager)
            throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        if (iBluetoothManager == null) {
            return null;
        }

        Method getBluetoothGattMethod = getMethodFromClass(
                iBluetoothManager.getClass(), "getBluetoothGatt");

        return getBluetoothGattMethod.invoke(iBluetoothManager);
    }

    private Object getIBluetoothManager()
            throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (bluetoothAdapter == null) {
            return null;
        }

        Method getBluetoothManagerMethod = getMethodFromClass(
                bluetoothAdapter.getClass(), "getBluetoothManager");

        return getBluetoothManagerMethod.invoke(bluetoothAdapter);
    }

    private Method getMethodFromClass(Class<?> cls, String methodName)
            throws NoSuchMethodException {
        Method method = cls.getDeclaredMethod(methodName);
        method.setAccessible(true);

        return method;
    }

    private void setAutoConnectValue(BluetoothGatt bluetoothGatt, boolean autoConnect)
            throws NoSuchFieldException, IllegalAccessException {
        @SuppressWarnings({"JavaReflectionMemberAccess", "DiscouragedPrivateApi"})
        Field autoConnectField = bluetoothGatt.getClass().getDeclaredField("mAutoConnect");
        autoConnectField.setAccessible(true);
        autoConnectField.setBoolean(bluetoothGatt, autoConnect);
    }
}
