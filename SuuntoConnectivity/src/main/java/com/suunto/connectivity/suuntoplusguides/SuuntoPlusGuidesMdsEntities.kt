package com.suunto.connectivity.suuntoplusguides

import androidx.annotation.RestrictTo
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.commands.Query
import com.suunto.connectivity.repository.commands.Response
import kotlinx.parcelize.Parcelize

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class SyncSuuntoPlusGuidesQuery(val macAddress: String) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_SYNC_SUUNTO_PLUS_GUIDES
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
class SyncSuuntoPlusGuidesResponse : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class GetCurrentWatchfaceIdQuery(
    val macAddress: String
) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_CURRENT_WATCHFACE_ID
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class GetCurrentWatchfaceIdResponse(
    val id: String?
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class GetDefaultWatchfaceIdQuery(
    val macAddress: String
) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_DEFAULT_WATCHFACE_ID
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class GetDefaultWatchfaceIdResponse(
    val id: String?
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class SetCurrentWatchfaceQuery(
    val macAddress: String,
    val id: String
) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_SET_CURRENT_WATCHFACE_ID
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
class SetCurrentWatchfaceResponse(
    val isSuccessful: Boolean
) : Response
