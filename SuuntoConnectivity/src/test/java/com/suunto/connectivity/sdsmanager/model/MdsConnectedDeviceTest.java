package com.suunto.connectivity.sdsmanager.model;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

/**
 * Tests to check that MdsConnectedDevice is deserialized to proper default value
 */
public class MdsConnectedDeviceTest {

    private Gson gson;

    @Before
    public void setUp() {
        gson = new GsonBuilder().create();
    }

    @Test
    public void testDefaultDeserialization() {
        String json = "{\"Serial\":\"123123\"}";
        MdsConnectedDevice device = gson.fromJson(json, MdsConnectedDevice.class);

        assertNotNull(device);
        assertEquals("123123", device.getSerial());
        assertNull(device.getConnection());
        assertNull(device.getDeviceInfo());
        assertFalse(device.isConnected());
    }

}
