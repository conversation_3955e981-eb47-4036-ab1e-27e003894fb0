package com.suunto.connectivity.deviceid

import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

/**
 * Created by <PERSON><PERSON> on 9/6/2016.
 */
class SuuntoDeviceCapabilityInfoProviderTest {

    @Before
    @Throws(Exception::class)
    fun setUp() {
    }

    @Test
    fun testGetSuuntoDeviceCapabilityInfo() {
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SPARTAN_ULTRA_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.SpartanUltra
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SPARTAN_SPORT_WHR_BARO_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.SpartanSportWristHRBaro
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SUUNTO_9_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.Suunto9
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SUUNTO_9_LIMA_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.Suunto9Lima
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SUUNTO_5_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.Suunto5
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SUUNTO_5_PEAK_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.Suunto5Peak
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SUUNTO_5_PEAK_VARIANT_NAME_CHINA
            ).suuntoDeviceType == SuuntoDeviceType.Suunto5Peak
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SPARTAN_TRAINER_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.SpartanTrainer
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SPARTAN_SPORT_WHR_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.SpartanSportWristHR
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SPARTAN_SPORT_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.SpartanSport
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SUUNTO_3_FITNESS_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.Suunto3Fitness
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SUUNTO_3_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.Suunto3
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.AMBIT3_PEAK_VARIANT_NAME
            ).suuntoDeviceType == SuuntoDeviceType.Ambit3Peak
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.AMBIT3_SPORT_VARIANT_NAME
            ).suuntoDeviceType == SuuntoDeviceType.Ambit3Sport
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.AMBIT3_VERTICAL_VARIANT_NAME
            ).suuntoDeviceType == SuuntoDeviceType.Ambit3Vertical
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.TRAVERSE_VARIANT_NAME
            ).suuntoDeviceType == SuuntoDeviceType.Traverse
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.TRAVERSE_ALPHA_VARIANT_NAME
            ).suuntoDeviceType == SuuntoDeviceType.TraverseAlpha
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.EON_STEEL_BLACK_VARIANT_NAME
            ).suuntoDeviceType == SuuntoDeviceType.EonSteelBlack
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.EON_STEEL_VARIANT_NAME
            ).suuntoDeviceType == SuuntoDeviceType.EonSteel
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.EON_CORE_VARIANT_NAME
            ).suuntoDeviceType == SuuntoDeviceType.EonCore
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SUUNTO_D5_VARIANT_NAME
            ).suuntoDeviceType == SuuntoDeviceType.SuuntoD5
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SUUNTO_RACE_S_VARIANT_NAME
            ).suuntoDeviceType == SuuntoDeviceType.SuuntoRaceS
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SUUNTO_VERTICAL_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.SuuntoVertical
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SUUNTO_RACE_VARIANT_NAME
            ).suuntoDeviceType == SuuntoDeviceType.SuuntoRace
        )
        assertTrue(
            SuuntoDeviceCapabilityInfoProvider.get(
                SuuntoConnectivityConstants.SUUNTO_OCEAN_VARIANT_NAME_GLOBAL
            ).suuntoDeviceType == SuuntoDeviceType.SuuntoOcean
        )
    }
}
