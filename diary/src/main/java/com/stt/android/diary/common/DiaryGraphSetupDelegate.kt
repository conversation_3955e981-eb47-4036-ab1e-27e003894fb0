package com.stt.android.diary.common

import com.stt.android.diary.graph.data.SelectedPrimaryGraphLiveData
import com.stt.android.diary.graph.data.SelectedSecondaryGraphLiveData
import com.stt.android.domain.diary.models.DiaryGraphSetup
import com.stt.android.domain.diary.models.DiaryPage
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.diary.models.primaryGraphDataTypes
import com.stt.android.domain.diary.models.secondaryGraphDataTypes
import com.stt.android.home.diary.SelectedGraphTimeRangeLiveData
import javax.inject.Inject

class DiaryGraphSetupDelegate @Inject constructor(
    private val selectedGraphTimeRange: SelectedGraphTimeRangeLiveData,
    private val selectedPrimaryGraphLiveData: SelectedPrimaryGraphLiveData,
    private val selectedSecondaryGraphLiveData: SelectedSecondaryGraphLiveData,
) : DiaryGraphSetup {

    override lateinit var currentSetup: DiaryGraphSetup.CurrentSetup
        private set

    override fun initializeGraphSetup(diaryPage: DiaryPage) {
        selectedPrimaryGraphLiveData.initialize(diaryPage)
        selectedSecondaryGraphLiveData.initialize(diaryPage)
        currentSetup = DiaryGraphSetup.CurrentSetup(
            graphTimeRange = selectedGraphTimeRange.value ?: GraphTimeRange.entries.first(),
            primaryGraphType = selectedPrimaryGraphLiveData.value ?: diaryPage.primaryGraphDataTypes.first(),
            secondaryGraphType = selectedSecondaryGraphLiveData.value ?: diaryPage.secondaryGraphDataTypes.firstOrNull()
        )
    }

    override fun updateTimeRange(range: GraphTimeRange) {
        currentSetup = currentSetup.copy(graphTimeRange = range)
    }

    override fun updatePrimaryGraphType(type: GraphDataType) {
        currentSetup = currentSetup.copy(primaryGraphType = type)
    }

    override fun updateSecondaryGraphType(type: GraphDataType) {
        currentSetup = currentSetup.copy(secondaryGraphType = type)
    }
}
