package com.stt.android.diary.tss

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import androidx.core.util.TypedValueCompat
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.CallbackProp
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import com.stt.android.analytics.TrendsAnalytics
import com.stt.android.diary.common.DragToHighlightGraphHandler
import com.stt.android.diary.tss.chartrendering.FlexibleYAxisLabelRenderer
import com.stt.android.diary.tss.chartrendering.SimpleLinePathRenderer
import com.stt.android.diary.tss.chartrendering.YAxisWithFixedLongestLabel
import com.stt.android.diary.tss.chartrendering.localDateXAxisRenderer
import com.stt.android.domain.diary.models.DiaryPage
import com.stt.android.domain.diary.models.FormPhase
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.extensions.buildDataSet
import com.stt.android.extensions.currentlyHighlightedIndex
import com.stt.android.extensions.disableGestures
import com.stt.android.extensions.drawHighlightForRegion
import com.stt.android.extensions.drawPhaseIndicatorBar
import com.stt.android.extensions.drawPhaseIndicatorBarSeparator
import com.stt.android.extensions.fillPaint
import com.stt.android.extensions.highlightIndex
import com.stt.android.extensions.setupXAxisAndYAxisOnRight
import com.stt.android.home.diary.R
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.time.temporal.WeekFields
import java.util.Locale
import kotlin.math.roundToInt
import com.stt.android.R as BR
import com.stt.android.core.R as CR

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_WRAP_HEIGHT)
class TSSAnalysisFormChart @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LineChart(context, attrs, defStyleAttr) {

    @set:[ModelProp]
    var formValues: List<Float?> = emptyList()

    @set:[ModelProp]
    var yAxisMax: Float = TSSConstants.FORM_GRAPH_DEFAULT_MAX_VALUE

    @set:[ModelProp]
    var yAxisMin: Float = TSSConstants.FORM_GRAPH_DEFAULT_MIN_VALUE

    @set:[ModelProp]
    var chartStartDate: LocalDate = LocalDate.MIN

    @set:[ModelProp]
    var endDate: LocalDate = LocalDate.MIN

    @set:[ModelProp]
    var weekFields: WeekFields = WeekFields.of(Locale.getDefault())

    @set:[ModelProp]
    var currentPhase = FormPhase.TOO_EASY

    @set:[ModelProp]
    var indexToHighlight: Int? = null

    // TODO: remove when old progress is removed
    @set:[ModelProp]
    var newProgressStyle: Boolean = false

    @set:[ModelProp]
    lateinit var timeRange: GraphTimeRange

    @set:[ModelProp(ModelProp.Option.DoNotHash)]
    lateinit var trendsAnalytics: TrendsAnalytics

    @set:[CallbackProp]
    var onValueHighlighted: ((index: Int?) -> Unit)? = null

    private val gridColor: Int
        get() = if (newProgressStyle) {
            ContextCompat.getColor(context, BR.color.cloudy_grey)
        } else {
            ContextCompat.getColor(context, R.color.tss_graph_grid)
        }

    private val lineColor: Int
        get() = if (newProgressStyle) {
            ContextCompat.getColor(context, BR.color.suunto_dark_gray)
        } else {
            ContextCompat.getColor(context, R.color.tss_graph_color_form)
        }

    private val goingTooHardBarPaint = fillPaint(R.color.form_going_too_hard)
    private val productiveBarPaint = fillPaint(R.color.form_productive_training)
    private val maintainingBarPaint = fillPaint(R.color.form_maintaining_fitness)
    private val goingTooEasyBarPaint = fillPaint(R.color.form_going_too_easy)

    private val separatorPaint = fillPaint(CR.color.white)
    private val activeAreaBackgroundPaint = fillPaint(R.color.form_active_area_fill)

    private val zeroLineDashPeriod = resources.getDimension(R.dimen.form_graph_zero_line_dash)
    private val phaseBarSeparatorHeight = resources.getDimension(R.dimen.form_graph_bar_separator)
    private val phaseBarWidth = resources.getDimension(R.dimen.form_graph_bar_width)

    private var boundFormValues: List<Float?>? = null
    private var boundChartStartDate: LocalDate? = null
    private var dataXOffset: Int = 0

    private var hasSetupChart = false

    init {
        minimumHeight = resources.getDimensionPixelSize(R.dimen.tss_graph_height)
    }

    @AfterPropsSet
    fun setup() {
        if (!hasSetupChart) {
            setupChart()
            hasSetupChart = true
        }

        axisRight.axisMinimum = yAxisMin
        axisRight.axisMaximum = yAxisMax
        xAxis.axisMinimum = 0f
        xAxis.axisMaximum = ChronoUnit.DAYS.between(chartStartDate, endDate).toFloat()

        // Keep track of previously bound values in order not to update unless needed.
        if (boundFormValues != formValues ||
            boundChartStartDate != chartStartDate
        ) {
            mXAxisRenderer =
                localDateXAxisRenderer(
                    context = context,
                    timeRange = timeRange,
                    startDate = chartStartDate,
                    endDate = endDate,
                    dayOfWeekField = weekFields.dayOfWeek(),
                    newProgressStyle = newProgressStyle,
                )

            val yLabels = getYLabels()

            mAxisRendererRight =
                FlexibleYAxisLabelRenderer(
                    yLabels,
                    viewPortHandler,
                    axisRight,
                    mRightAxisTransformer
                )

            data = LineData(buildDataSet(formValues, lineColor))

            invalidate()

            boundFormValues = formValues
            boundChartStartDate = chartStartDate
        }

        val indexToHighlightWithOffset = indexToHighlight?.plus(dataXOffset)
        if (currentlyHighlightedIndex != indexToHighlightWithOffset) {
            highlightIndex(indexToHighlightWithOffset)
        }
    }

    private fun setupChart() {
        disableGestures()
        mAxisRight =
            YAxisWithFixedLongestLabel(resources.getDimension(R.dimen.tss_graph_horizontal_space_for_labels))
        // Y axis renderer still keeps a reference to the stale YAxis object, but the renderer
        // will be replaced in setup().
        setupXAxisAndYAxisOnRight()

        if (newProgressStyle) {
            axisRight.xOffset = 4f
            xAxis.axisLineWidth = 1f
            xAxis.axisLineColor = ContextCompat.getColor(context, BR.color.medium_grey)
        }

        // Don't use grid, only limit lines. This allows custom positioning and styling.
        axisRight.setDrawGridLines(false)
        axisRight.setDrawLimitLinesBehindData(true)
        axisRight.removeAllLimitLines()
        for (limit in LIMIT_LINES) {
            axisRight.addLimitLine(
                LimitLine(limit).apply {
                    lineColor = gridColor
                    lineWidth = if (newProgressStyle) {
                        1f
                    } else {
                        TSSConstants.GRAPH_GRID_LINE_WIDTH_DP
                    }
                    if (limit == 0f) {
                        if (newProgressStyle) {
                            val dashLength = TypedValueCompat.dpToPx(4f, resources.displayMetrics)
                            enableDashedLine(dashLength, dashLength / 2f, 0f)
                        } else {
                            enableDashedLine(zeroLineDashPeriod, zeroLineDashPeriod, 0f)
                        }
                    } else {
                        disableDashedLine()
                    }
                }
            )
        }

        setOnChartValueSelectedListener(object : OnChartValueSelectedListener {
            override fun onValueSelected(e: Entry, h: Highlight) {
                val entryIndex = e.x.roundToInt() - dataXOffset
                if (entryIndex != indexToHighlight) {
                    onValueHighlighted?.invoke(entryIndex)
                }
            }

            override fun onNothingSelected() {
                if (indexToHighlight != null) {
                    onValueHighlighted?.invoke(null)
                }
            }
        })

        renderer = SimpleLinePathRenderer(
            context,
            this,
            animator,
            viewPortHandler,
            drawTriangle = false,
            topOffsetDp = if (newProgressStyle) -40f else 0f,
            bottomOffsetDp = 0f,
        )

        setOnTouchListener(
            object : DragToHighlightGraphHandler(this@TSSAnalysisFormChart) {
                override fun startHighLighting() {
                    super.startHighLighting()
                    trendsAnalytics.trackTrendsGraphValueTapped(
                        DiaryPage.PROGRESS,
                        null,
                        null,
                        timeRange
                    )
                }
            }
        )
    }

    override fun onDraw(canvas: Canvas) {
        // Highlighted background for the 'active area'. Drawn below the rest of the graph.
        drawHighlightForRegion(
            canvas,
            0f,
            currentPhase.upperLimit,
            xAxis.axisMaximum,
            currentPhase.lowerLimit,
            activeAreaBackgroundPaint
        )

        // MPAndroidChart rendering
        super.onDraw(canvas)

        // Color bar for phases at the left hand side of the chart
        drawPhaseIndicatorBar(
            canvas = canvas,
            widthInPixels = phaseBarWidth,
            minYValue = FormPhase.TOO_HARD.lowerLimit,
            maxYValue = FormPhase.TOO_HARD.upperLimit,
            paint = goingTooHardBarPaint
        )

        drawPhaseIndicatorBar(
            canvas = canvas,
            widthInPixels = phaseBarWidth,
            minYValue = FormPhase.PRODUCTIVE_TRAINING.lowerLimit,
            maxYValue = FormPhase.PRODUCTIVE_TRAINING.upperLimit,
            paint = productiveBarPaint
        )

        drawPhaseIndicatorBar(
            canvas = canvas,
            widthInPixels = phaseBarWidth,
            minYValue = FormPhase.MAINTAINING_FITNESS.lowerLimit,
            maxYValue = FormPhase.MAINTAINING_FITNESS.upperLimit,
            paint = maintainingBarPaint
        )

        drawPhaseIndicatorBar(
            canvas = canvas,
            widthInPixels = phaseBarWidth,
            minYValue = FormPhase.TOO_EASY.lowerLimit,
            maxYValue = FormPhase.TOO_EASY.upperLimit,
            paint = goingTooEasyBarPaint
        )

        for (limit in LIMIT_SEPARATORS) {
            drawPhaseIndicatorBarSeparator(
                canvas = canvas,
                widthInPixels = phaseBarWidth,
                separatorHeight = phaseBarSeparatorHeight,
                yValue = limit,
                paint = separatorPaint
            )
        }

        if (valuesToHighlight()) {
            (renderer as SimpleLinePathRenderer).drawHighlightedFreely(canvas, mIndicesToHighlight)
        }
    }

    private fun getYLabels(): Map<Float, String> {
        val values = when {
            yAxisMin <= MIN_Y_THRESHOLD_FOR_REDUCED_LABELS ->
                // Vertical space too tight to fit labels for all threshold. Also show yAxisMin.
                REDUCED_LIMIT_LINE_LABELS + yAxisMin

            yAxisMin <= TSSConstants.FORM_GRAPH_SHOW_MIN_VALUE_THRESHOLD ->
                // Show all labels and yAxisMin
                LIMIT_LINES + yAxisMin

            else ->
                // Form value within typical range, no need to show yAxisMin
                LIMIT_LINES
        }

        return values.associateWith { String.format(Locale.getDefault(), "%.0f", it) }
    }

    private val FormPhase.upperLimit: Float
        get() = when (this) {
            FormPhase.TOO_EASY -> yAxisMax
            FormPhase.MAINTAINING_FITNESS -> FormPhase.FORM_GOING_TOO_EASY_THRESHOLD.toFloat()
            FormPhase.PRODUCTIVE_TRAINING -> FormPhase.FORM_PRODUCTIVE_TRAINING_THRESHOLD.toFloat()
            FormPhase.TOO_HARD -> FormPhase.FORM_GOING_TOO_HARD_THRESHOLD.toFloat()
        }

    private val FormPhase.lowerLimit: Float
        get() = when (this) {
            FormPhase.TOO_EASY -> FormPhase.FORM_GOING_TOO_EASY_THRESHOLD.toFloat()
            FormPhase.MAINTAINING_FITNESS -> FormPhase.FORM_PRODUCTIVE_TRAINING_THRESHOLD.toFloat()
            FormPhase.PRODUCTIVE_TRAINING -> FormPhase.FORM_GOING_TOO_HARD_THRESHOLD.toFloat()
            FormPhase.TOO_HARD -> yAxisMin
        }

    companion object {
        // Show horizontal limit lines for each of these Y values. If there is enough vertical
        // space, also show a label.
        private val LIMIT_LINES = listOf(
            FormPhase.FORM_GOING_TOO_EASY_THRESHOLD.toFloat(),
            FormPhase.FORM_MAINTAINING_FITNESS_THRESHOLD.toFloat(),
            FormPhase.FORM_PRODUCTIVE_TRAINING_THRESHOLD.toFloat(),
            FormPhase.FORM_GOING_TOO_HARD_THRESHOLD.toFloat(),
            TSSConstants.FORM_GRAPH_DEFAULT_MAX_VALUE,
            0f,
        )

        // Add separator lines between the colored bars at left side of chart for each of these Y values
        private val LIMIT_SEPARATORS = listOf(
            FormPhase.FORM_MAINTAINING_FITNESS_THRESHOLD.toFloat(),
            FormPhase.FORM_PRODUCTIVE_TRAINING_THRESHOLD.toFloat(),
            FormPhase.FORM_GOING_TOO_HARD_THRESHOLD.toFloat(),
        )

        // Only show these labels on the Y axis if vertical space is tight
        private val REDUCED_LIMIT_LINE_LABELS = listOf(
            FormPhase.FORM_GOING_TOO_HARD_THRESHOLD.toFloat(),
            TSSConstants.FORM_GRAPH_DEFAULT_MAX_VALUE,
            0f,
        )

        private const val MIN_Y_THRESHOLD_FOR_REDUCED_LABELS = -90f
    }
}
