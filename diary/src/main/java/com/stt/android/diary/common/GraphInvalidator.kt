package com.stt.android.diary.common

import com.stt.android.diary.common.GraphInvalidator.Companion.FIRST_PAGE
import com.stt.android.diary.graph.paging.GraphId
import com.stt.android.diary.graph.paging.GraphPagingSource
import com.stt.android.domain.diary.models.GraphDataType
import java.util.concurrent.ConcurrentHashMap

interface GraphInvalidator {
    val pageToLoad: Int
    fun addGraphPagingSource(graphPagingSource: GraphPagingSource)
    fun reloadGraphs(pageToLoad: Int = FIRST_PAGE, skip: List<GraphId> = emptyList())

    companion object {
        const val FIRST_PAGE = 1
    }

    fun onClearedGraphPagingSources()
}

class GraphInvalidatorDelegate : GraphInvalidator {
    data class Key(
        val primary: GraphDataType,
        val secondary: GraphDataType?
    )

    private val pagingSources = ConcurrentHashMap<Key, GraphPagingSource>()
    override var pageToLoad: Int = FIRST_PAGE
        private set

    override fun addGraphPagingSource(graphPagingSource: GraphPagingSource) {
        pagingSources[graphPagingSource.key] = graphPagingSource
    }

    override fun reloadGraphs(pageToLoad: Int, skip: List<GraphId>) {
        this.pageToLoad = pageToLoad
        for (pagingSource in pagingSources.values) {
            if (skip.contains(pagingSource.graphId)) continue
            pagingSource.invalidate()
        }
    }

    override fun onClearedGraphPagingSources() {
        for (pagingSource in pagingSources.values) {
            pagingSource.onCleared()
        }
    }
}

private val GraphPagingSource.key
    get() = GraphInvalidatorDelegate.Key(
        primaryGraphType,
        secondaryGraphType
    )
