package com.stt.android.diary.workout.dive

import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.data.source.local.DaoFactory
import com.stt.android.diary.graph.data.SelectedPrimaryGraphLiveData
import com.stt.android.diary.graph.data.SelectedSecondaryGraphLiveData
import com.stt.android.diary.graph.paging.GraphId
import com.stt.android.diary.graph.paging.GraphPagingSourceFactory
import com.stt.android.diary.workout.DiaryWorkoutViewModel
import com.stt.android.diary.workout.paging.WorkoutPagingSource
import com.stt.android.domain.diary.models.DiaryPage
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.GetPagedWorkoutHeadersUseCase
import com.stt.android.home.diary.SelectedGraphTimeRangeLiveData
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class FreeDivingViewModel @Inject constructor(
    daoFactory: DaoFactory,
    coroutinesDispatchers: CoroutinesDispatchers,
    userController: CurrentUserController,
    graphPagingSourceFactory: GraphPagingSourceFactory,
    getPagedWorkoutHeadersUseCase: GetPagedWorkoutHeadersUseCase,
    selectedGraphTimeRange: SelectedGraphTimeRangeLiveData,
    selectedPrimaryGraphLiveData: SelectedPrimaryGraphLiveData,
    selectedSecondaryGraphLiveData: SelectedSecondaryGraphLiveData,
) : DiaryWorkoutViewModel(
    daoFactory,
    coroutinesDispatchers,
    userController,
    graphPagingSourceFactory,
    getPagedWorkoutHeadersUseCase,
    DiaryPage.FREE_DIVING,
    selectedGraphTimeRange,
    selectedPrimaryGraphLiveData,
    selectedSecondaryGraphLiveData,
) {
    init {
        selectedPrimaryGraphLiveData.initialize(DiaryPage.FREE_DIVING)
        selectedSecondaryGraphLiveData.initialize(DiaryPage.FREE_DIVING)
    }

    override val activityTypeFilter: WorkoutPagingSource.ActivityTypeFilter =
        WorkoutPagingSource.ActivityTypeFilter.OnlyActivityType(ActivityType.FREEDIVING)

    override fun createGraphPagingSource() = graphPagingSourceFactory.create(
        viewModelScope,
        GraphId.FREE_DIVE,
        selectedGraphTimeRange.value ?: GraphTimeRange.entries.first(),
        GraphDataType.FREE_DIVE_COUNT,
        null,
        pageToLoad
    )
}
