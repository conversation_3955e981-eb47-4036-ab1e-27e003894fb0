package com.stt.android.diary.insights.recovery

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.darkestGrey
import com.stt.android.compose.theme.elevation
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.common.trainingHubDimens
import com.stt.android.diary.insights.common.AverageComparisonChip
import com.stt.android.diary.insights.common.BoldValueWithSmallSubtitle
import com.stt.android.diary.insights.common.CoachText
import com.stt.android.diary.insights.common.DummyTrainingHubUiStates
import com.stt.android.diary.insights.common.ExpandableSection
import com.stt.android.diary.insights.common.MultiSeriesProgress
import com.stt.android.diary.insights.common.NoData
import com.stt.android.diary.insights.common.ProgressValue
import com.stt.android.diary.insights.common.SeriesProgressMarker
import com.stt.android.diary.insights.common.SubSection
import com.stt.android.diary.insights.common.TrainingHubDateRange
import com.stt.android.diary.insights.common.TwoValuesWithSmallSubTitle
import com.stt.android.diary.insights.common.rememberDecimalFormat
import com.stt.android.diary.insights.intensity.ComparisonIndicator
import com.stt.android.domain.sleep.HrvGrade
import com.stt.android.domain.sleep.SleepHrv
import com.stt.android.domain.sleep.color
import com.stt.android.domain.sleep.hrvGrade
import com.stt.android.home.diary.R
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import java.time.LocalDate
import kotlin.math.roundToInt
import com.stt.android.R as BaseR

@Composable
fun TrainingHubRecovery(
    recoveryUiState: RecoveryUiState,
    onViewMoreOrLessClick: () -> Unit,
    onFormActionClick: () -> Unit,
    onSleepActionClick: () -> Unit,
    onHrvShowInfoClicked: () -> Unit,
    onFormShowInfoClicked: () -> Unit,
    onSleepShowInfoClicked: () -> Unit,
    onFeelingShowInfoClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val showHrv = recoveryUiState.hrvUiState?.showHrv == true
    ExpandableSection(
        titleRes = R.string.training_hub_recovery,
        modifier = modifier,
        isExpanded = recoveryUiState.isExpanded,
        onViewMoreOrLessClick = onViewMoreOrLessClick,
        showViewMoreOrLess = true
    ) {
        if (showHrv) {
            TrainingHubHrv(
                hrvUiState = recoveryUiState.hrvUiState,
                onActionClick = onHrvShowInfoClicked
            )
        }
        TrainingHubForm(
            formUiState = recoveryUiState.formUiState,
            onInfoClicked = onFormShowInfoClicked,
            onTrendClicked = onFormActionClick
        )
        if (recoveryUiState.isExpanded) {
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
            TrainingHubSleep(
                sleepUiState = recoveryUiState.sleepUiState,
                onInfoClicked = onSleepShowInfoClicked,
                onTrendClicked = onSleepActionClick
            )
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
            TrainingHubFeeling(
                feelingUiState = recoveryUiState.feelingUiState,
                onInfoClicked = onFeelingShowInfoClicked
            )
        }
    }
}

@Composable
private fun TrainingHubHrv(
    hrvUiState: HrvUiState?,
    onActionClick: (() -> Unit)?,
    modifier: Modifier = Modifier
) {
    val sleepHrv = hrvUiState?.sleepHrv
    SubSection(
        titleRes = BaseR.string.sleep_hrv_title,
        subTitleRes = R.string.sleep_hrv_sub_title,
        onActionClick = onActionClick,
        spaceBetweenHeaderAndContent = 0.dp,
        modifier = modifier
    ) {
        if (hrvUiState?.sleepHrv?.hasDataWithin60Days == true) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (sleepHrv?.avg7DayHrv != null) {
                    BoldValueWithSmallSubtitle(
                        value = sleepHrv.avg7DayHrv?.roundToInt(),
                        titleRes = BaseR.string.sleep_hrv_7_day_hrv_label,
                        Modifier
                            .widthIn(min = MaterialTheme.spacing.xxlarge)
                            .padding(bottom = MaterialTheme.spacing.medium),
                        formatter = rememberDecimalFormat(positivePrefix = "")
                    )
                    Spacer(modifier = Modifier.width(MaterialTheme.spacing.xlarge))
                    if (hrvUiState.coachTextId != null) {
                        CoachText(textId = hrvUiState.coachTextId, modifier = Modifier.weight(1f))
                    } else if (sleepHrv.normalRange == null) {
                        Text(
                            text = stringResource(id = R.string.sleep_hrv_coach_requires_normal_range),
                            style = MaterialTheme.typography.body,
                            color = MaterialTheme.colors.nearBlack,
                        )
                    }
                } else {
                    Text(
                        text = stringResource(R.string.sleep_hrv_7_day_avg_missing),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                vertical = MaterialTheme.spacing.xlarge
                            ),
                        style = MaterialTheme.typography.body,
                        color = MaterialTheme.colors.nearBlack,
                    )
                }
            }
            NormalRangeBar(sleepHrv = sleepHrv)
            if (sleepHrv?.normalRange != null) {
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                HrvGrades(sleepHrv.hrvGrade)
            }
            if (hrvUiState.isSleepHrvFromToday) {
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.xlarge))
                TwoValuesWithSmallSubTitle(
                    leftValue = sleepHrv?.previousAvgHrv?.roundToInt(),
                    leftTitleRes = R.string.sleep_hrv_yesterday_label,
                    rightValue = sleepHrv?.avgHrv?.roundToInt(),
                    rightTitleRes = R.string.sleep_hrv_today_label,
                )
            }
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.xxlarge))
        } else {
            Text(
                text = stringResource(BaseR.string.sleep_hrv_no_data),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        vertical = MaterialTheme.spacing.xlarge
                    ),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colors.darkGrey,
            )
        }
    }
}

@Composable
fun HrvGrades(
    hrvGrade: HrvGrade,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier.fillMaxWidth()
    ) {
        HrvGrading(
            textRes = BaseR.string.sleep_hrv_avg_low,
            color = Color(HrvGrade.LOW.color),
            modifier = Modifier.align(Alignment.CenterStart),
            isSelected = hrvGrade == HrvGrade.LOW
        )
        HrvGrading(
            textRes = BaseR.string.sleep_hrv_avg_in_normal_range,
            color = Color(HrvGrade.IN_NORMAL_RANGE.color),
            modifier = Modifier.align(Alignment.Center),
            isSelected = hrvGrade == HrvGrade.IN_NORMAL_RANGE
        )
        HrvGrading(
            textRes = BaseR.string.sleep_hrv_avg_high,
            color = Color(HrvGrade.HIGH.color),
            modifier = Modifier.align(Alignment.CenterEnd),
            isSelected = hrvGrade == HrvGrade.HIGH
        )
    }
}

@Composable
private fun HrvGrading(
    @StringRes textRes: Int,
    color: Color,
    modifier: Modifier = Modifier,
    isSelected: Boolean = false
) {
    Row(
        modifier = modifier
            .wrapContentHeight()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .padding(end = MaterialTheme.spacing.small)
                .size(
                    if (isSelected) {
                        10.dp
                    } else {
                        0.dp
                    }
                )
                .background(
                    color = if (isSelected) {
                        color
                    } else {
                        Color.Transparent
                    },
                    shape = CircleShape
                )
        )
        Text(
            text = stringResource(textRes),
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colors.nearBlack,
        )
    }
}

@Composable
fun NormalRangeBar(
    sleepHrv: SleepHrv?,
    modifier: Modifier = Modifier,
) {
    val normalRange = sleepHrv?.normalRange

    if (normalRange == null) {
        Column {
            Text(
                text = stringResource(BaseR.string.sleep_hrv_normal_range_missing),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = MaterialTheme.spacing.xlarge,
                        vertical = MaterialTheme.spacing.medium,
                    )
                    .requiredWidth(212.dp),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colors.darkGrey,
                textAlign = TextAlign.Center,
            )
            Image(
                painter = painterResource(id = R.drawable.normal_range_placholder),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = MaterialTheme.spacing.medium),
            )
        }
    } else {
        val start = normalRange.start.roundToInt().toFloat()
        val endInclusive = normalRange.endInclusive.roundToInt().toFloat()
        val span = endInclusive - start
        MultiSeriesProgress(
            modifier = modifier,
            items = persistentListOf(
                SeriesProgressItem(
                    min = start - span,
                    max = start,
                    color = Color(HrvGrade.LOW.color)
                ),
                SeriesProgressItem(
                    min = start,
                    max = endInclusive,
                    color = Color(HrvGrade.IN_NORMAL_RANGE.color),
                ),
                SeriesProgressItem(
                    min = endInclusive,
                    max = endInclusive + span,
                    color = Color(HrvGrade.HIGH.color)
                ),
            ),
            markers = persistentListOf(
                SeriesProgressMarker(
                    value = start,
                    showLabel = true,
                    showLine = true
                ),
                SeriesProgressMarker(
                    value = endInclusive,
                    showLabel = true,
                    showLine = true
                ),
            ),
            selectedMarker = sleepHrv.avg7DayHrv?.roundToInt()?.toFloat(),
            formatter = rememberDecimalFormat(positivePrefix = "")
        )
    }
}

@Composable
private fun TrainingHubForm(
    formUiState: FormUiState?,
    onInfoClicked: () -> Unit,
    onTrendClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    SubSection(
        titleRes = R.string.training_hub_form,
        subTitleRes = R.string.training_hub_form_sub_title,
        onActionClick = onInfoClicked,
        spaceBetweenHeaderAndContent = 0.dp,
        modifier = modifier
    ) {
        if (formUiState == null) {
            NoData(textRes = R.string.training_hub_no_form_data)
        } else {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                BoldValueWithSmallSubtitle(
                    value = formUiState.tsb,
                    titleRes = R.string.training_hub_tsb
                )
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.xlarge))
                formUiState.coachTextId?.let {
                    CoachText(textId = it, modifier = Modifier.weight(1f))
                }
            }
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        FormSeriesProgress(tsb = formUiState?.tsb)

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        TextButton(
            onClick = onTrendClicked,
            modifier = Modifier.align(Alignment.CenterHorizontally)
        ) {
            Text(
                text = stringResource(R.string.training_hub_tsb_trend),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colors.primary
            )
        }
    }
}

@Composable
private fun TrainingHubSleep(
    sleepUiState: SleepUiState,
    onInfoClicked: () -> Unit,
    onTrendClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    SubSection(
        titleRes = R.string.training_hub_sleep,
        onActionClick = onInfoClicked,
        modifier = modifier
    ) {
        if (sleepUiState.isEmpty) {
            NoData(textRes = R.string.training_hub_no_sleep_data)
        } else {
            Row(verticalAlignment = Alignment.CenterVertically) {
                SleepCard(
                    iconRes = R.drawable.sleeping_outline,
                    titleRes = R.string.training_hub_sleep_start,
                    formattedCurrentValue = sleepUiState.formattedCurrentSleepStart,
                    formattedComparisonValue = sleepUiState.formattedComparisonSleepStart,
                    currentComparisonIndicator = sleepUiState.sleepStartComparisonIndicator,
                    modifier = Modifier.weight(1f)
                )
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
                SleepCard(
                    iconRes = BaseR.drawable.duration_outline,
                    titleRes = BaseR.string.duration,
                    formattedCurrentValue = sleepUiState.formattedCurrentDuration,
                    formattedComparisonValue = sleepUiState.formattedComparisonDuration,
                    currentComparisonIndicator = sleepUiState.durationComparisonIndicator,
                    currentUnit = sleepUiState.currentDurationUnitRes,
                    comparisonUnit = sleepUiState.comparisonDurationUnitRes,
                    modifier = Modifier.weight(1f)
                )
            }
        }
        
        if (sleepUiState.supportSleepData) {
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
            TextButton(
                onClick = onTrendClicked,
                modifier = Modifier.align(Alignment.CenterHorizontally)
            ) {
                Text(
                    text = stringResource(R.string.training_hub_sleep_trend),
                    style = MaterialTheme.typography.bodyBold,
                    color = MaterialTheme.colors.primary
                )
            }
        }
    }
}

@Composable
private fun TrainingHubFeeling(
    feelingUiState: FeelingUiState?,
    onInfoClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    SubSection(
        titleRes = R.string.training_hub_feeling,
        onActionClick = onInfoClicked,
        modifier = modifier
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(
                painter = painterResource(feelingUiState?.iconRes ?: R.drawable.no_feeling),
                contentDescription = null,
                tint = Color.Unspecified
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))

            if (feelingUiState?.coachTextId == null) {
                NoData(textRes = R.string.training_hub_no_feeling_data)
            } else {
                CoachText(
                    textId = feelingUiState.coachTextId,
                    modifier = Modifier.weight(1f)
                )
            }
        }

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        FeelingsWeekComparison(
            dateRange = feelingUiState?.dateRange ?: TrainingHubDateRange.CurrentWeek,
            currentFeelingDistributionItems = feelingUiState?.currentFeelingDistributionItems
                ?: persistentListOf(),
            currentFeelingDistributionMarker = feelingUiState?.currentFeelingDistributionMarker,
            comparisonFeelingDistributionItems = feelingUiState?.comparisonFeelingDistributionItems
                ?: persistentListOf(),
            comparisonFeelingDistributionMarker = feelingUiState?.comparisonFeelingDistributionMarker,
        )
    }
}

@Composable
private fun FeelingsWeekComparison(
    dateRange: TrainingHubDateRange,
    modifier: Modifier = Modifier,
    currentFeelingDistributionItems: ImmutableList<SeriesProgressItem> = persistentListOf(),
    currentFeelingDistributionMarker: SeriesProgressMarker? = null,
    comparisonFeelingDistributionItems: ImmutableList<SeriesProgressItem> = persistentListOf(),
    comparisonFeelingDistributionMarker: SeriesProgressMarker? = null,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(verticalArrangement = Arrangement.Center) {
            FeelingBarDistributionTitle(
                when (dateRange) {
                    is TrainingHubDateRange.CurrentWeek -> stringResource(dateRange.value)
                    is TrainingHubDateRange.CustomRange -> dateRange.value
                }
            )
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmaller))
            FeelingBarDistributionTitle(stringResource(R.string.training_hub_previous_six_weeks))
        }

        Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))

        Column(verticalArrangement = Arrangement.Center) {
            MultiSeriesProgress(
                items = currentFeelingDistributionItems,
                markers = currentFeelingDistributionMarker?.let { persistentListOf(it) }
            )
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmaller))
            MultiSeriesProgress(
                items = comparisonFeelingDistributionItems,
                markers = comparisonFeelingDistributionMarker?.let { persistentListOf(it) }
            )
        }
    }
}

@Composable
private fun SleepCard(
    @DrawableRes iconRes: Int,
    @StringRes titleRes: Int,
    formattedCurrentValue: String,
    formattedComparisonValue: String,
    currentComparisonIndicator: ComparisonIndicator?,
    modifier: Modifier = Modifier,
    currentUnit: Int? = null,
    comparisonUnit: Int? = null,
) {
    Card(
        modifier = modifier.height(MaterialTheme.trainingHubDimens.sleepCardHeight),
        elevation = MaterialTheme.elevation.elevatedCard
    ) {
        Column(
            verticalArrangement = Arrangement.SpaceBetween,
            horizontalAlignment = Alignment.Start,
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        ) {
            SleepIconWithTitle(
                iconRes = iconRes,
                titleRes = titleRes,
            )
            ProgressValue(
                text = formattedCurrentValue,
                unit = currentUnit,
                comparisonIndicator = currentComparisonIndicator
            )
            AverageComparisonChip(
                text = formattedComparisonValue,
                unit = comparisonUnit,
                contentAlignment = Alignment.CenterEnd
            )
        }
    }
}

@Composable
private fun SleepIconWithTitle(
    @DrawableRes iconRes: Int,
    @StringRes titleRes: Int,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = painterResource(iconRes),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.medium)
        )
        Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))
        Text(
            text = stringResource(titleRes),
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colors.darkestGrey
        )
    }
}

@Composable
private fun FormSeriesProgress(
    tsb: Int?,
    modifier: Modifier = Modifier
) {
    MultiSeriesProgress(
        modifier = modifier,
        items = persistentListOf(
            SeriesProgressItem(
                min = -35f,
                max = -30f,
                color = Color(0xFF831D36)
            ),
            SeriesProgressItem(
                min = -30f,
                max = -10f,
                color = Color(0xFFDB315A)
            ),
            SeriesProgressItem(
                min = -10f,
                max = 15f,
                color = Color(0xFFE9839C)
            ),
            SeriesProgressItem(
                min = 15f,
                max = 25f,
                color = Color(0xFFF1ADBD)
            )
        ),
        markers = persistentListOf(
            SeriesProgressMarker(value = -30f, showLabel = true, showLine = true),
            SeriesProgressMarker(value = -10f, showLabel = true, showLine = true),
            SeriesProgressMarker(value = 15f, showLabel = true, showLine = true)
        ),
        selectedMarker = tsb?.toFloat()
    )
}

@Composable
private fun FeelingBarDistributionTitle(
    text: String,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier.height(MaterialTheme.trainingHubDimens.feelingBarContainerHeight)) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colors.darkestGrey,
            textAlign = TextAlign.Center,
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun TrainingHubPreview() {
    AppTheme {
        TrainingHubRecovery(
            recoveryUiState = DummyTrainingHubUiStates.recoveryUiState,
            onViewMoreOrLessClick = {},
            onFormActionClick = {},
            onSleepActionClick = {},
            onHrvShowInfoClicked = {},
            onFormShowInfoClicked = {},
            onSleepShowInfoClicked = {},
            onFeelingShowInfoClicked = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun SleepCardPreview() {
    AppTheme {
        SleepCard(
            iconRes = R.drawable.sleeping_outline,
            titleRes = R.string.training_hub_sleep_start,
            formattedCurrentValue = DummyTrainingHubUiStates.recoveryUiState.sleepUiState.formattedCurrentSleepStart,
            formattedComparisonValue = DummyTrainingHubUiStates.recoveryUiState.sleepUiState.formattedComparisonSleepStart,
            currentComparisonIndicator = DummyTrainingHubUiStates.recoveryUiState.sleepUiState.sleepStartComparisonIndicator,
            currentUnit = null,
            comparisonUnit = null,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun TrainingHubFeelingPreview() {
    AppTheme {
        TrainingHubFeeling(
            feelingUiState = DummyTrainingHubUiStates.recoveryUiState.feelingUiState,
            onInfoClicked = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun TrainingHubHrvPreview(
    @PreviewParameter(HrvParamsProvider::class) hrvUiState: HrvUiState
) {
    AppTheme {
        TrainingHubHrv(
            hrvUiState = hrvUiState,
            onActionClick = {},
            modifier = Modifier.padding(16.dp)
        )
    }
}

private class HrvParamsProvider :
    PreviewParameterProvider<HrvUiState> {
    override val values = sequenceOf(
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now(),
                avgHrv = 65f,
                previousAvgHrv = 75f,
                avg7DayHrv = 45f,
                normalRange = 55f..85f,
                hasDataWithin60Days = true,
            ),
            coachTextId = null,
            isSleepHrvFromToday = true
        ),
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now(),
                avgHrv = null,
                previousAvgHrv = null,
                avg7DayHrv = 45f,
                normalRange = 55f..85f,
                hasDataWithin60Days = true,
            ),
            coachTextId = null,
            isSleepHrvFromToday = true
        ),
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now(),
                avgHrv = 65f,
                previousAvgHrv = 75f,
                avg7DayHrv = null,
                normalRange = 55f..85f,
                hasDataWithin60Days = true,
            ),
            coachTextId = null,
            isSleepHrvFromToday = true
        ),
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now(),
                avgHrv = 65f,
                previousAvgHrv = 75f,
                avg7DayHrv = 45f,
                normalRange = null,
                hasDataWithin60Days = true,
            ),
            coachTextId = null,
            isSleepHrvFromToday = true
        ),
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now(),
                avgHrv = 65f,
                previousAvgHrv = 75f,
                avg7DayHrv = null,
                normalRange = null,
                hasDataWithin60Days = true,
            ),
            coachTextId = null,
            isSleepHrvFromToday = true
        ),
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now(),
                avgHrv = null,
                previousAvgHrv = null,
                avg7DayHrv = null,
                normalRange = null,
                hasDataWithin60Days = true,
            ),
            coachTextId = null,
            isSleepHrvFromToday = true
        ),
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now().minusDays(2),
                avgHrv = 65f,
                previousAvgHrv = 75f,
                avg7DayHrv = 45f,
                normalRange = 55f..85f,
                hasDataWithin60Days = true,
            ),
            coachTextId = null,
            isSleepHrvFromToday = false
        ),
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now().minusDays(2),
                avgHrv = null,
                previousAvgHrv = null,
                avg7DayHrv = 45f,
                normalRange = 55f..85f,
                hasDataWithin60Days = true,
            ),
            coachTextId = null,
            isSleepHrvFromToday = false
        ),
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now().minusDays(2),
                avgHrv = 65f,
                previousAvgHrv = 75f,
                avg7DayHrv = null,
                normalRange = 55f..85f,
                hasDataWithin60Days = true,
            ),
            coachTextId = null,
            isSleepHrvFromToday = false
        ),
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now().minusDays(2),
                avgHrv = 65f,
                previousAvgHrv = 75f,
                avg7DayHrv = 29.5f,
                normalRange = 30.4f..33f,
                hasDataWithin60Days = true,
            ),
            coachTextId = null,
            isSleepHrvFromToday = false
        ),
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now().minusDays(2),
                avgHrv = 65f,
                previousAvgHrv = 75f,
                avg7DayHrv = 45f,
                normalRange = null,
                hasDataWithin60Days = true,
            ),
            coachTextId = null,
            isSleepHrvFromToday = false
        ),
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now().minusDays(2),
                avgHrv = 65f,
                previousAvgHrv = 75f,
                avg7DayHrv = null,
                normalRange = null,
                hasDataWithin60Days = true,
            ),
            coachTextId = null,
            isSleepHrvFromToday = false
        ),
        HrvUiState(
            showHrv = true,
            sleepHrv = SleepHrv(
                date = LocalDate.now().minusDays(2),
                avgHrv = null,
                previousAvgHrv = null,
                avg7DayHrv = null,
                normalRange = null,
                hasDataWithin60Days = false,
            ),
            coachTextId = null,
            isSleepHrvFromToday = false
        )
    )
}
