package com.stt.android.domain.diary

import com.stt.android.domain.CoroutineUseCase
import javax.inject.Inject

class GetVo2MaxStateRangesUseCase @Inject constructor() :
    CoroutineUseCase<Pair<Vo2MaxState?, List<Vo2MaxRange>>, GetVo2MaxStateRangesUseCase.Params> {

    data class Params(
        val vo2Max: Float?,
        val isMale: <PERSON><PERSON><PERSON>,
        val userAge: Int,
    )

    override suspend fun run(params: Params): Pair<Vo2MaxState?, List<Vo2MaxRange>> {
        val isMale = params.isMale
        val vo2MaxRanges = when (params.userAge) {
            in Int.MIN_VALUE..29 -> LESS_THAN_30_VO2MAX_RANGES.filter { it.isMale == isMale }
            in 30..39 -> BETWEEN_30_AND_39_VO2MAX_RANGES.filter { it.isMale == isMale }
            in 40..49 -> BETWEEN_40_AND_49_VO2MAX_RANGES.filter { it.isMale == isMale }
            in 50..59 -> BETWEEN_50_AND_59_VO2MAX_RANGES.filter { it.isMale == isMale }
            in 60..69 -> BETWEEN_60_AND_69_VO2MAX_RANGES.filter { it.isMale == isMale }
            else -> GREATER_THAN_70_VO2MAX_RANGES.filter { it.isMale == isMale }
        }
        val vo2MaxState = params.vo2Max?.let { vo2Max ->
            vo2MaxRanges.find { it.contains(vo2Max) }?.state
        }
        return vo2MaxState to vo2MaxRanges
    }
}
