package com.stt.android.domain.diary.insights

import com.stt.android.domain.diary.models.TrainingProgressData
import com.stt.android.domain.sleep.FetchSleepHrvUseCase
import com.stt.android.domain.tss.CalculateRefSwimSpeedUseCase
import com.stt.android.domain.user.CurrentUserDataSource
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.domain.workouts.feeling.FetchDailyFeelingUseCase
import com.stt.android.infomodel.getMcIdForStId
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.iterator
import com.stt.android.utils.toEpochMilli
import com.suunto.connectivity.trainingzone.TrainingZoneExerciseDataItem
import com.suunto.connectivity.trainingzone.TrainingZoneSyncData
import com.suunto.connectivity.trainingzone.TrainingZoneSyncDataProvider
import com.suunto.connectivity.trainingzone.TrainingZoneSyncExercises
import com.suunto.connectivity.trainingzone.TrainingZoneSyncProgress
import com.suunto.connectivity.trainingzone.TrainingZoneSyncRecovery
import com.suunto.connectivity.trainingzone.TrainingZoneSyncThresholds
import com.suunto.connectivity.trainingzone.TrainingZoneSyncTraining
import kotlinx.coroutines.flow.firstOrNull
import timber.log.Timber
import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject
import kotlin.math.roundToInt

class TrainingZoneSyncDataProviderImpl @Inject constructor(
    private val trainingHubPeriodAnalysisUseCase: TrainingHubPeriodAnalysisUseCase,
    private val currentUserDataSource: CurrentUserDataSource,
    private val trainingHubPeriodsCalculationUseCase: TrainingHubPeriodsCalculationUseCase,
    private val calculateCtlRampRateUseCase: CalculateCtlRampRateUseCase,
    private val fetchSleepHrvUseCase: FetchSleepHrvUseCase,
    private val calculateRefSwimSpeedUseCase: CalculateRefSwimSpeedUseCase,
    private val workoutDataSource: WorkoutDataSource,
    private val fetchRecoveryStateScoreUseCase: FetchRecoveryStateScoreUseCase,
    private val fetchDailyFeelingUseCase: FetchDailyFeelingUseCase,
) : TrainingZoneSyncDataProvider {
    override suspend fun getAnalysisForPeriod(
        firstDayOfWeek: DayOfWeek,
        currentDate: LocalDate
    ): TrainingZoneSyncData {
        val currentUser = currentUserDataSource.getCurrentUserOrNull() ?: run {
            throw IllegalStateException("No current user is found")
        }
        val currentTimeInSeconds = System.currentTimeMillis() / 1000
        val username = currentUser.username
        val periods =
            trainingHubPeriodsCalculationUseCase(
                weeksToAdd = 0, // We only sync data of the current week
                firstDayOfWeek = firstDayOfWeek
            )

        val current = trainingHubPeriodAnalysisUseCase.getAnalysisForPeriod(
            username = username,
            startDate = periods.firstPeriodStartDate,
            endDateInclusive = periods.firstPeriodEndDateInclusive
        )

        val comparison = trainingHubPeriodAnalysisUseCase.getAnalysisForPeriod(
            username = username,
            startDate = periods.secondPeriodStartDate,
            endDateInclusive = periods.secondPeriodEndDateInclusive
        )

        val recentAnalysisForRecovery = trainingHubPeriodAnalysisUseCase.getAnalysisForPeriod(
            username = username,
            startDate = periods.recent7DaysRecoveryStart,
            endDateInclusive = periods.recent7DaysRecoveryEndInclusive
        )

        val previousAnalysisForRecovery = trainingHubPeriodAnalysisUseCase.getAnalysisForPeriod(
            username = username,
            startDate = periods.previous42DaysRecoveryStart,
            endDateInclusive = periods.previous42DaysRecoveryEndInclusive
        )

        val combinedTrainingProgressData =
            current.recoveryAnalysis.trainingProgressData + comparison.recoveryAnalysis.trainingProgressData

        val recoveryStateScore = fetchRecoveryStateScoreUseCase(
            fromDate = currentDate,
            toDate = currentDate,
            includeContributors = false,
        ).firstOrNull()?.getOrNull(0)?.recoveryStateData?.recoveryScore

        val feelingByDate = fetchDailyFeelingUseCase.fetchDailyFeelingForDateRange(
            fromDate = periods.recent42DaysFeelingsStart,
            toDate = periods.recent42DaysFeelingsEndInclusive,
        ).firstOrNull()?.groupBy { it.localDate }?.mapValues { it.value.first() } ?: emptyMap()
        val recent42DaysFeelingSums = mutableListOf<Int>()
        val recent42DaysFeelingCounts = mutableListOf<Int>()
        (periods.recent42DaysFeelingsStart..periods.recent42DaysFeelingsEndInclusive).iterator().forEach { date ->
            val feelings = feelingByDate[date]?.values ?: emptyList()
            recent42DaysFeelingSums.add(feelings.sum())
            recent42DaysFeelingCounts.add(feelings.size)
        }

        Timber.d("currentDate: $currentDate, recoveryStateScore: $recoveryStateScore")

        return TrainingZoneSyncData(
            timestampInSeconds = currentTimeInSeconds,
            training = TrainingZoneSyncTraining(
                tssWeek = current.totalTSS,
                tssWeekComparison = comparison.totalTSS,
                timestampInSeconds = currentTimeInSeconds,
                tssDaily = getTssDaily(
                    firstDayOfWeek = firstDayOfWeek,
                    currentDate = currentDate,
                    combinedTrainingProgressData = combinedTrainingProgressData
                ),
                durationDaily = getDurationDaily(
                    firstDayOfWeek = firstDayOfWeek,
                    currentDate = currentDate,
                    durationByDay = current.durationByDay
                ),
                durationWeek = current.totalDuration,
                durationWeekComparison = comparison.totalDuration,
            ),
            progress = getProgressData(
                currentTimeInSeconds = currentTimeInSeconds,
                currentTrainingProgressData = current.recoveryAnalysis.trainingProgressData,
                comparisonTrainingProgressData = comparison.recoveryAnalysis.trainingProgressData,
                currentDate = currentDate
            ),
            recovery = getRecoveryData(
                currentTimeInSeconds = currentTimeInSeconds,
                currentAnalysis = recentAnalysisForRecovery,
                comparisonAnalysis = previousAnalysisForRecovery,
                currentPeriodEndDateInclusive = periods.recent7DaysRecoveryEndInclusive,
                recent42DaysFeelingSums = recent42DaysFeelingSums.reversed(), // decreasing date
                recent42DaysFeelingCounts = recent42DaysFeelingCounts.reversed(),
                recoveryStateScore = recoveryStateScore,
            ),
            thresholds = getThresholdsData(
                currentTimeInSeconds = currentTimeInSeconds,
            ),
            exercises = getExerciseData(
                username = username,
                firstDayOfWeek = firstDayOfWeek,
                currentDate = currentDate
            )
        )
    }

    private fun getTssDaily(
        firstDayOfWeek: DayOfWeek,
        currentDate: LocalDate,
        combinedTrainingProgressData: List<TrainingProgressData>
    ): List<Int> {
        val tssDaily = mutableListOf<Int>()
        for (index in 0L until 7) {
            val current = currentDate.minusDays(index)
            val trainingProgressData =
                combinedTrainingProgressData.find { it.day == current } ?: return tssDaily

            tssDaily.add(trainingProgressData.roundedTss)
            if (current.dayOfWeek == firstDayOfWeek) break
        }
        return tssDaily
    }

    private fun getDurationDaily(
        firstDayOfWeek: DayOfWeek,
        currentDate: LocalDate,
        durationByDay: Map<DayOfWeek, Double>
    ): List<Double> {
        val durationDaily = mutableListOf<Double>()
        for (index in 0L until 7) {
            val current = currentDate.minusDays(index)
            val duration = durationByDay[current.dayOfWeek] ?: return emptyList()

            durationDaily.add(duration)

            if (current.dayOfWeek == firstDayOfWeek) break
        }
        return durationDaily
    }

    private suspend fun getThresholdsData(
        currentTimeInSeconds: Long,
    ): TrainingZoneSyncThresholds {
        val poolSwimThreshold = calculateRefSwimSpeedUseCase(ActivityType.SWIMMING.id)
        val openwaterSwimThreshold =
            calculateRefSwimSpeedUseCase(ActivityType.OPENWATER_SWIMMING.id)

        return TrainingZoneSyncThresholds(
            timestampInSeconds = currentTimeInSeconds,
            poolSwimThreshold = poolSwimThreshold,
            openwaterSwimThreshold = openwaterSwimThreshold
        )
    }

    private suspend fun getRecoveryData(
        currentTimeInSeconds: Long,
        currentAnalysis: TrainingHubPeriodAnalysis,
        comparisonAnalysis: TrainingHubPeriodAnalysis,
        currentPeriodEndDateInclusive: LocalDate,
        recent42DaysFeelingSums: List<Int>,
        recent42DaysFeelingCounts: List<Int>,
        recoveryStateScore: Int?,
    ): TrainingZoneSyncRecovery {
        val sleepHrv = runCatching {
            fetchSleepHrvUseCase.fetchAvgHrv(currentPeriodEndDateInclusive)
        }.onFailure {
            Timber.d(it, "Failed to fetch hrv values")
        }.getOrNull()

        return TrainingZoneSyncRecovery(
            timestampInSeconds = currentTimeInSeconds,
            hrvWeeklyAvg = sleepHrv?.avg7DayHrv,
            hrvMin60d = sleepHrv?.normalRange?.start,
            hrvMax60d = sleepHrv?.normalRange?.endInclusive,
            feelingsCurrentDist = (1..5).map {
                currentAnalysis.recoveryAnalysis.feelingsCount[it] ?: 0
            },
            feelingsComparisonDist = (1..5).map {
                comparisonAnalysis.recoveryAnalysis.feelingsCount[it] ?: 0
            },
            sleepDurationCurrentAvg = currentAnalysis.recoveryAnalysis.avgSleepDuration,
            sleepDurationComparisonAvg = comparisonAnalysis.recoveryAnalysis.avgSleepDuration,
            sleepStartCurrentAvg = currentAnalysis.recoveryAnalysis.avgSleepStartTime?.toSecondOfDay(),
            sleepStartComparisonAvg = comparisonAnalysis.recoveryAnalysis.avgSleepStartTime?.toSecondOfDay(),
            feelings42EveryDaySum = recent42DaysFeelingSums,
            feelings42EveryDayCount = recent42DaysFeelingCounts,
            recoveryPercent = recoveryStateScore,
            updateSource = true, // APP always pass true
        )
    }

    private fun getProgressData(
        currentTimeInSeconds: Long,
        currentTrainingProgressData: List<TrainingProgressData>,
        comparisonTrainingProgressData: List<TrainingProgressData>,
        currentDate: LocalDate
    ): TrainingZoneSyncProgress {
        fun empty(): TrainingZoneSyncProgress = TrainingZoneSyncProgress(
            timestampInSeconds = currentTimeInSeconds,
            ctlDaily = emptyList(),
            ctlRampRate = null,
            atlDaily = emptyList(),
            tsbDaily = emptyList(),
        )

        // In order to calculate the ctl ramp rate, we need the ctl of today (in current) and 42 days ago (available in comparison data)
        val trainingProgressDataCombined =
            currentTrainingProgressData + comparisonTrainingProgressData

        // if we do not have progress data for today then we don't have any data
        val todayProgressData =
            trainingProgressDataCombined.findLast { it.day == currentDate } ?: return empty()

        // yesterday progress data can be null for 1st time users
        val yesterdayProgressData =
            trainingProgressDataCombined.findLast { it.day == currentDate.minusDays(1) }

        return TrainingZoneSyncProgress(
            timestampInSeconds = currentTimeInSeconds,
            ctlDaily = listOfNotNull(
                todayProgressData.fitness.roundToInt(),
                yesterdayProgressData?.fitness?.roundToInt()
            ),
            ctlRampRate = calculateCtlRampRateUseCase(
                trainingProgressData = trainingProgressDataCombined,
                targetDate = currentDate
            ),
            atlDaily = listOfNotNull(
                todayProgressData.fatigue.roundToInt(),
                yesterdayProgressData?.fatigue?.roundToInt()
            ),
            tsbDaily = listOfNotNull(
                todayProgressData.form.roundToInt(),
                yesterdayProgressData?.form?.roundToInt()
            ),
        )
    }

    private suspend fun getExerciseData(
        username: String,
        firstDayOfWeek: DayOfWeek,
        currentDate: LocalDate
    ): TrainingZoneSyncExercises {
        val exerciseDataItems = mutableListOf<TrainingZoneExerciseDataItem>()

        val threeWeeksAgo = currentDate.minusWeeks(3)
        val periodStart = threeWeeksAgo.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
        val periodEnd = currentDate

        val workoutList = workoutDataSource.fetchUserWorkoutsBasic(
            username = username,
            minStartTime = periodStart.atStartOfDay().toEpochMilli(),
            maxStartTime = periodEnd.atEndOfDay().toEpochMilli()
        )

        val workoutsByWeek = workoutList.groupBy { workout ->
            val workoutDate = Instant.ofEpochMilli(workout.startTime)
                .atZone(ZoneId.systemDefault()).toLocalDate()
            workoutDate.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
        }

        // Generate 4 weeks of data with zero placeholders for weeks without data, sorted in descending chronological order
        val fourWeeksDurationByTotal = (0 until 4).map { weekOffset ->
            val weekStart = periodStart.plusWeeks(weekOffset.toLong())
            val weekWorkouts = workoutsByWeek[weekStart].orEmpty()
            weekWorkouts.sumOf { it.totalTime }.toFloat()
        }.reversed()

        val fourWeekExerciseDataItemByDuration = TrainingZoneExerciseDataItem(
            activityId = 0,
            distance = workoutsByWeek.values.flatten().sumOf { it.totalDistance }.toInt(),
            fourWeeksDuration = fourWeeksDurationByTotal
        )
        exerciseDataItems.add(fourWeekExerciseDataItemByDuration)

        // Get activity type with max total duration by aggregating all workouts by activity type
        val activityTypeIdWithMaxDuration = workoutList
            .groupBy { it.activityTypeId }
            .mapValues { (_, workouts) -> workouts.sumOf { it.totalTime } }
            .maxByOrNull { it.value }
            ?.key

        if (activityTypeIdWithMaxDuration == null) {
            val emptyExerciseDataItem = TrainingZoneExerciseDataItem(
                activityId = 0,
                distance = 0,
                fourWeeksDuration = listOf(0f, 0f, 0f, 0f)
            )
            exerciseDataItems.add(emptyExerciseDataItem)
        } else {
            val filteredDurationsByWeek = (0 until 4).map { weekOffset ->
                val weekStart = periodStart.plusWeeks(weekOffset.toLong())
                val weekWorkouts = workoutsByWeek[weekStart].orEmpty()
                val matchedWorkouts = weekWorkouts.filter { workout ->
                    workout.activityTypeId == activityTypeIdWithMaxDuration
                }
                matchedWorkouts.sumOf { it.totalTime }.toFloat()
            }.reversed()

            val currentWeekStart = periodStart.plusWeeks(3)
            val currentWeekWorkouts = workoutsByWeek[currentWeekStart].orEmpty()
                .filter { it.activityTypeId == activityTypeIdWithMaxDuration }
            val filteredDistance = currentWeekWorkouts.sumOf { it.totalDistance }.toInt()

            val fourWeekExerciseDataItemByActivityType = TrainingZoneExerciseDataItem(
                activityId = getMcIdForStId(activityTypeIdWithMaxDuration),
                distance = filteredDistance,
                fourWeeksDuration = filteredDurationsByWeek
            )
            exerciseDataItems.add(fourWeekExerciseDataItemByActivityType)
        }
        return TrainingZoneSyncExercises(exerciseDataItems)
    }
}
