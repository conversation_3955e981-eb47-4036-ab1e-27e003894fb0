package com.stt.android.data.source.local.workout

import android.content.ContentValues
import com.stt.android.data.source.local.routes.LocalPoint
import com.stt.android.data.source.local.tags.LocalSuuntoTag
import com.stt.android.data.source.local.workout.tss.LocalTSS
import com.stt.android.data.source.local.workout.tss.LocalTSSCalculationMethod
import com.stt.android.data.toEpochMilli
import java.time.ZoneOffset
import java.time.ZonedDateTime

object WorkoutHeaderTestUtils {
    fun createLocalWorkoutHeader(): LocalWorkoutHeader = fakeWorkoutHeader
}

val fakeWorkoutHeader = LocalWorkoutHeader(
    id = 123,
    key = "123",
    totalDistance = 123.4,
    maxSpeed = 12.3,
    activityId = 2,
    avgSpeed = 10.3,
    description = "fake workout header",
    startPosition = LocalPoint(10.0, 10.0, 5.0),
    stopPosition = LocalPoint(30.0, 30.0, 5.0),
    centerPosition = LocalPoint(20.0, 20.0, 5.0),
    startTime = ZonedDateTime.of(2022, 3, 10, 10, 0, 0, 0, ZoneOffset.UTC).toEpochMilli(),
    stopTime = ZonedDateTime.of(2022, 3, 10, 11, 0, 0, 0, ZoneOffset.UTC).toEpochMilli(),
    totalTime = 3600.0,
    energyConsumption = 123.0,
    username = "fake_user",
    heartRateAvg = 90.5,
    heartRateAvgPercentage = 90.0,
    heartRateMax = 160.0,
    heartRateMaxPercentage = 88.9,
    heartRateUserSetMax = 180.0,
    pictureCount = 1,
    viewCount = 20,
    commentCount = 2,
    sharingFlags = (LocalSharingOption.EVERYONE.backendId or LocalSharingOption.SHARED_MASK),
    locallyChanged = false,
    deleted = false,
    manuallyCreated = false,
    averageCadence = 25,
    maxCadence = 30,
    polyline = "this is a fake polyline",
    stepCount = 4500,
    reactionCount = 5,
    totalAscent = 25.0,
    totalDescent = 26.0,
    recoveryTime = 12345,
    maxAltitude = 30.0,
    minAltitude = 5.0,
    seen = true,
    extensionsFetched = true,
    tss = LocalTSS(55f, LocalTSSCalculationMethod.MANUAL),
    tssList = listOf(LocalTSS(55f, LocalTSSCalculationMethod.MANUAL)),
    suuntoTags = listOf(LocalSuuntoTag.COMMUTE),
    zoneSense = null,
)

val fakeWorkoutHeaderAsContentValues = ContentValues().apply {
    put(LocalWorkoutHeader.ID, fakeWorkoutHeader.id)
    put(LocalWorkoutHeader.KEY, fakeWorkoutHeader.key)
    put(LocalWorkoutHeader.TOTAL_DISTANCE, fakeWorkoutHeader.totalDistance)
    put(LocalWorkoutHeader.MAX_SPEED, fakeWorkoutHeader.maxSpeed)
    put(LocalWorkoutHeader.ACTIVITY_ID, fakeWorkoutHeader.activityId)
    put(LocalWorkoutHeader.AVERAGE_SPEED, fakeWorkoutHeader.avgSpeed)
    put(LocalWorkoutHeader.DESCRIPTION, fakeWorkoutHeader.description)
    put(
        LocalWorkoutHeader.START_TIME,
        fakeWorkoutHeader.startTime
    )
    put(
        LocalWorkoutHeader.STOP_TIME,
        fakeWorkoutHeader.stopTime
    )
    put(LocalWorkoutHeader.TOTAL_TIME, fakeWorkoutHeader.totalTime)
    put(LocalWorkoutHeader.ENERGY_CONSUMPTION, fakeWorkoutHeader.energyConsumption)
    put(LocalWorkoutHeader.USERNAME, fakeWorkoutHeader.username)
    put(LocalWorkoutHeader.HEART_RATE_AVG, fakeWorkoutHeader.heartRateAvg)
    put(LocalWorkoutHeader.HEART_RATE_AVG_PERCENTAGE, fakeWorkoutHeader.heartRateAvgPercentage)
    put(LocalWorkoutHeader.HEART_RATE_MAX, fakeWorkoutHeader.heartRateMax)
    put(LocalWorkoutHeader.HEART_RATE_MAX_PERCENTAGE, fakeWorkoutHeader.heartRateMaxPercentage)
    put(LocalWorkoutHeader.HEART_RATE_USER_SET_MAX, fakeWorkoutHeader.heartRateUserSetMax)
    put(LocalWorkoutHeader.PICTURE_COUNT, fakeWorkoutHeader.pictureCount)
    put(LocalWorkoutHeader.VIEW_COUNT, fakeWorkoutHeader.viewCount)
    put(LocalWorkoutHeader.COMMENT_COUNT, fakeWorkoutHeader.commentCount)
    put(LocalWorkoutHeader.SHARING_FLAGS, fakeWorkoutHeader.sharingFlags)
    put(LocalWorkoutHeader.LOCALLY_CHANGED, fakeWorkoutHeader.locallyChanged)
    put(LocalWorkoutHeader.DELETED, fakeWorkoutHeader.deleted)
    put(LocalWorkoutHeader.MANUALLY_CREATED, fakeWorkoutHeader.manuallyCreated)
    put(LocalWorkoutHeader.AVERAGE_CADENCE, fakeWorkoutHeader.averageCadence)
    put(LocalWorkoutHeader.MAX_CADENCE, fakeWorkoutHeader.maxCadence)
    put(LocalWorkoutHeader.POLYLINE, fakeWorkoutHeader.polyline)
    put(LocalWorkoutHeader.STEP_COUNT, fakeWorkoutHeader.stepCount)
    put(LocalWorkoutHeader.REACTION_COUNT, fakeWorkoutHeader.reactionCount)
    put(LocalWorkoutHeader.TOTAL_ASCENT, fakeWorkoutHeader.totalAscent)
    put(LocalWorkoutHeader.TOTAL_DESCENT, fakeWorkoutHeader.totalDescent)
    put(LocalWorkoutHeader.RECOVERY_TIME, fakeWorkoutHeader.recoveryTime)
    put(LocalWorkoutHeader.MAX_ALTITUDE, fakeWorkoutHeader.maxAltitude)
    put(LocalWorkoutHeader.MIN_ALTITUDE, fakeWorkoutHeader.minAltitude)
    put(LocalWorkoutHeader.SEEN, fakeWorkoutHeader.seen)
    put(LocalWorkoutHeader.EXTENSIONS_FETCHED, fakeWorkoutHeader.extensionsFetched)
}
