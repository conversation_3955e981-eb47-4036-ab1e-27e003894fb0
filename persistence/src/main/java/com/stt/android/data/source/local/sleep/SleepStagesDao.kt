package com.stt.android.data.source.local.sleep

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.stt.android.data.source.local.TABLE_SLEEP_STAGE_INTERVALS
import com.stt.android.data.source.local.sleep.LocalSleepStageInterval.Companion.COLUMN_SYNCED_STATUS
import com.stt.android.data.source.local.sleep.LocalSleepStageInterval.Companion.COLUMN_TIMESTAMP_SECONDS
import kotlinx.coroutines.flow.Flow

@Dao
interface SleepStagesDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSleepStages(sleepStages: List<LocalSleepStageInterval>)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertSleepStagesSafe(sleepStages: List<LocalSleepStageInterval>): List<Long>

    @Query(
        """
        SELECT *
        FROM $TABLE_SLEEP_STAGE_INTERVALS
        WHERE $COLUMN_TIMESTAMP_SECONDS BETWEEN :fromTimestampSeconds AND :toTimestampSeconds
        ORDER BY $COLUMN_TIMESTAMP_SECONDS ASC
        """
    )
    fun flowSleepStagesBetween(
        fromTimestampSeconds: Long,
        toTimestampSeconds: Long
    ): Flow<List<LocalSleepStageInterval>>

    /**
     * Returns list of [LocalSleepStageInterval] sorted by timestamp ASCENDING
     * (because this is used by backend sync job)
     */
    @Query(
        """
        SELECT *
        FROM $TABLE_SLEEP_STAGE_INTERVALS
        WHERE $COLUMN_SYNCED_STATUS = :syncedStatus
        ORDER BY $COLUMN_TIMESTAMP_SECONDS ASC
        """
    )
    suspend fun fetchSleepStagesWithSyncedStatus(syncedStatus: Int): List<LocalSleepStageInterval>

    @Query("DELETE FROM $TABLE_SLEEP_STAGE_INTERVALS")
    fun deleteAll()

    @Query(
        """
        SELECT *
        FROM $TABLE_SLEEP_STAGE_INTERVALS
        WHERE $COLUMN_SYNCED_STATUS = 1
        ORDER BY $COLUMN_TIMESTAMP_SECONDS DESC
        LIMIT 1
    """
    )
    suspend fun fetchLatestSynced(): LocalSleepStageInterval?
}
