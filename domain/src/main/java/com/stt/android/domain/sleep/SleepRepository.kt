package com.stt.android.domain.sleep

import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

interface SleepRepository {
    /**
     * Get [Flow] that emits a list of [Sleep] entities for the given date range.
     *
     * In practice, it returns sleeps from noon on the day before [from], until noon on the day of [to].
     * If the returned [Sleep] objects can be incomplete if the sleep starts before the starting time,
     * or ends after the ending time.
     *
     * @param from date of first instant to query from
     * @param to date of last instant to query to
     * @return Flow that emits a list of [Sleep] entities whenever data changes
     */
    fun fetchSleeps(
        from: LocalDate,
        to: LocalDate,
    ): Flow<List<Sleep>>

    /**
     * Save a list of [SleepSegment] entities in the local data source
     * @param sleepList list of [SleepSegment] entities to save in the data source
     * @param replaceConflicts true if data needs to be replaced in case of conflict
     * @param requireBackendSync true if data needs to be synced to backend in the future
     * @return If any SleepSegments were stored in the local database. Can be false if the
     * list is empty or [replaceConflicts] is false and all of Segments had been inserted already.
     */
    suspend fun saveSleep(
        sleepList: List<SleepSegment>,
        requireBackendSync: Boolean
    ): Boolean

    /**
     * Save a list of [SleepStageInterval] entities in the local data source
     * @param sleepStageList list of [SleepStageInterval] entities to save in the data source
     * @param replaceConflicts true if data needs to be replaced in case of conflict
     * @param requireBackendSync true if data needs to be synced to backend in the future
     * @return If any SleepStageInterval were stored in the local database. Can be false if the
     * list is empty or [replaceConflicts] is false and all of SleepStageInterval had been inserted already.
     */
    suspend fun saveSleepStages(
        sleepStageList: List<SleepStageInterval>,
        replaceConflicts: Boolean,
        requireBackendSync: Boolean
    ): Boolean

    suspend fun fetchSleepDataExists(maxDaysLoaded: Int): Boolean

    suspend fun fetchOldestSleepTimestamp(): Long?

    /**
     * @param checkLimit - Stop the count at threshold value. If user has data for longer periods
     *   of time and we just want to see if some threshold is met, using this optimizes the
     *   fetching to stop at the threshold value and not go through all of the data.
     */
    suspend fun fetchNumDaysWithSleepData(checkLimit: Int = Int.MAX_VALUE): Int

    /**
     * Get [Flow] that emits a list of [SleepStageInterval] entities for the given date range.
     * The returned [SleepStageInterval] objects can be incomplete if the query endpoints are in middle
     * of the sleep period.
     *
     * @param fromTimestamp UTC timestamp in seconds of first instant to query from
     * @param toTimestamp UTC timestamp in seconds of last instant to query to
     * @return Flow that emits a list of [SleepStageInterval] entities whenever data changes
     */
    fun fetchSleepStagesForDateRange(
        fromTimestamp: Long,
        toTimestamp: Long
    ): Flow<List<SleepStageInterval>>
}
