package com.stt.android.domain.firmware

import android.content.SharedPreferences
import androidx.annotation.VisibleForTesting
import androidx.core.content.edit
import com.stt.android.domain.BaseUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.suunto.connectivity.firmware.FirmwareInformationInterface
import com.suunto.connectivity.firmware.WatchFirmwareInfo
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import io.reactivex.Flowable
import io.reactivex.Scheduler
import io.reactivex.Single
import kotlinx.coroutines.rx2.await
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case for comparing versions of the watch firmware against latest available on the backend
 */
class CheckForNewerFirmwareUseCase
@Inject constructor(
    private val firmwareDataSource: FirmwareDataSource,
    private val sharedPreferences: SharedPreferences,
    @IoThread scheduler: Scheduler,
    @MainThread mainThread: Scheduler
) : BaseUseCase(scheduler, mainThread), FirmwareInformationInterface, BaseFirmwareInformationInterface {

    fun isUpdateAvailable(
        variant: String,
        hwCompatibilityIdentifier: String,
        fwVersion: String,
        productVersion: String?,
        isSuunto7: Boolean,
        isSupportOtaUpdateCheck: Boolean = false
    ): Flowable<Boolean> {
        return if (isSuunto7) {
            Flowable.just(false)
        } else {
            val key =
                "com.stt.android.domain.firmware.CheckForNewerFirmwareUseCase.NEW_FIRMWARE_$variant$hwCompatibilityIdentifier"
            val cachedLatestFirmwareVersion = sharedPreferences.getString(key, "")
            val isCachedFirmwareVersionNewer =
                if (cachedLatestFirmwareVersion.isNullOrBlank()) {
                    false
                } else {
                    isBackendVersionNewer(
                        fwVersion,
                        cachedLatestFirmwareVersion
                    )
                }
            return Flowable.just(isCachedFirmwareVersionNewer)
                .concatWith(
                    // Todo: Resolve correct isOtaUpdateFirmware argument here.
                    getLatestFirmwareInfo(
                        variant = variant,
                        hwCompatibilityIdentifier = hwCompatibilityIdentifier,
                        currentVersion = fwVersion,
                        productVersion = productVersion,
                        isOtaUpdateFirmware = false,
                        isSupportOtaUpdateCheck = isSupportOtaUpdateCheck
                    )
                        .doOnSuccess {
                            sharedPreferences.edit {
                                putString(key, it.latestFirmwareVersion)
                            }
                        }
                        .map { deviceInfo: FirmwareInfo ->
                            // New firmware is available when backend version is newer than local
                            isBackendVersionNewer(fwVersion, deviceInfo.latestFirmwareVersion)
                        }
                        .onErrorReturn {
                            Timber.e(it, "New firmware check failed")
                            isCachedFirmwareVersionNewer
                        }
                )
                .subscribeOn(scheduler)
                .observeOn(scheduler)
        }
    }

    override fun getLatestFirmwareInfo(
        mdsDeviceInfo: MdsDeviceInfo,
        isSupportOtaUpdateCheck: Boolean,
    ): Single<WatchFirmwareInfo> {
        return getLatestFirmwareInfo(
            variant = mdsDeviceInfo.variant,
            hwCompatibilityIdentifier = mdsDeviceInfo.hwCompatibilityId,
            currentVersion = mdsDeviceInfo.swVersion,
            productVersion = mdsDeviceInfo.productVersion,
            isOtaUpdateFirmware = true,
            isSupportOtaUpdateCheck = isSupportOtaUpdateCheck
        ).map { it.toWatchFirmware() }
    }

    override suspend fun getLatestFirmwareInfo(
        variant: String,
        hwCompatibilityIdentifier: String,
        currentVersion: String,
        productVersion: String?,
        isSupportOtaUpdateCheck: Boolean,
    ): BaseWatchFirmwareInfo {
        return getLatestFirmwareInfo(
            variant = variant,
            hwCompatibilityIdentifier = hwCompatibilityIdentifier,
            currentVersion = currentVersion,
            productVersion = productVersion,
            isOtaUpdateFirmware = true,
            isSupportOtaUpdateCheck = isSupportOtaUpdateCheck
        )
            .await()
            .toBaseWatchFirmware()
    }

    private fun getLatestFirmwareInfo(
        variant: String,
        hwCompatibilityIdentifier: String,
        currentVersion: String,
        productVersion: String?,
        isOtaUpdateFirmware: Boolean,
        isSupportOtaUpdateCheck: Boolean,
    ): Single<FirmwareInfo> {
        return if (isSupportOtaUpdateCheck) {
            firmwareDataSource.fetchFirmwareWithUpdateCheck(
                variant = variant,
                hwCompatibilityIdentifier = hwCompatibilityIdentifier,
                currentVersion = currentVersion,
                productVersion = productVersion,
                isOtaUpdateFirmware = isOtaUpdateFirmware,
            )
                .subscribeOn(scheduler)
                .observeOn(scheduler)
        } else {
            firmwareDataSource.fetchFirmware(
                variant = variant,
                hwCompatibilityIdentifier = hwCompatibilityIdentifier,
                isOtaUpdateFirmware = isOtaUpdateFirmware,
            )
                .subscribeOn(scheduler)
                .observeOn(scheduler)
        }
    }

    /**
     * Method to figure out whether the backend firmware version is newer than the local one
     * Version usually looks like 1.2.3beta4, where beta and following number are optional
     * This function converts inputs to semantic versions (x.y.z) and compares them.
     * @param firmwareVersion is the version that we currently have on the watch attached to the app
     * @param backendVersion is the version of firmware from the backend
     * @return True if backend version is newer than local
     */
    @VisibleForTesting
    fun isBackendVersionNewer(firmwareVersion: String, backendVersion: String): Boolean {
        try {
            val local =
                Version(convertToSemanticVersion(firmwareVersion))
            val backend =
                Version(convertToSemanticVersion(backendVersion))

            return backend > local
        } catch (e: RuntimeException) {
            Timber.w(e, "Failed to parse firmware version")
        }

        return false
    }

    /**
     * Convert input string to semantic version (x.y.z)
     */
    private fun convertToSemanticVersion(input: String) = input
        .replace("beta", ".")
        .split(".")
        .filter { value -> value != "" }
        .take(3)
        .joinToString(separator = ".")
}
