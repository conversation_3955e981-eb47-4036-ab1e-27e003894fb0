package com.stt.android.domain.sleep

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import java.util.concurrent.TimeUnit
import javax.inject.Inject

/**
 * Use case for fetching sleep data
 */
class FetchSleepUseCase @Inject constructor(
    private val sleepRepository: SleepRepository,
) {
    /**
     * Get [Flow] that emits a list of [Sleep] entities for the given date range.
     * The returned [Sleep] objects can be incomplete if the query endpoints are in middle
     * of the sleep period.
     *
     * @param from date of first instant to query from
     * @param to date of last instant to query to
     * @return Flow that emits a list of [Sleep] entities whenever data changes
     */
    fun fetchSleeps(
        from: LocalDate,
        to: LocalDate,
    ): Flow<List<Sleep>> = sleepRepository.fetchSleeps(
        from = from,
        to = to,
    )

    suspend fun fetchSleepDataExists(): Boolean =
        sleepRepository.fetchSleepDataExists(SLEEP_DAYS_LOADED)

    suspend fun fetchOldestSleepTimestamp(): Long? =
        sleepRepository.fetchOldestSleepTimestamp()

    /**
     * Get [Flow] that emits a list of [SleepStageInterval] entities for the given date range.
     * The returned [SleepStageInterval] objects can be incomplete if the query endpoints are in middle
     * of the sleep period.
     *
     * @param from date of first instant to query from
     * @param to date of last instant to query to
     * @return Flow that emits a list of [SleepStageInterval] entities whenever data changes
     */
    fun fetchSleepStages(
        from: LocalDate,
        to: LocalDate,
    ): Flow<Map<Long, List<SleepStageInterval>>> = fetchSleeps(from, to)
        .map { sleeps ->
            sleeps.mapNotNull { sleep ->
                if (!sleep.hasLongSleep) return@mapNotNull null

                // longSleep is not null because of the hasLongSleep check above
                val longSleep = requireNotNull(sleep.longSleep)
                val sleepStages = sleepRepository.fetchSleepStagesForDateRange(
                    fromTimestamp = TimeUnit.MILLISECONDS.toSeconds(longSleep.fellAsleep),
                    toTimestamp = TimeUnit.MILLISECONDS.toSeconds(longSleep.wokeUp),
                ).first()
                sleep.timestamp to sleepStages
            }.toMap()
        }

    companion object {
        const val SLEEP_DAYS_LOADED = 365
    }
}
