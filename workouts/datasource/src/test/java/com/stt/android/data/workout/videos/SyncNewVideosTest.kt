package com.stt.android.data.workout.videos

import com.stt.android.data.session.CurrentUser
import com.stt.android.domain.sync.AggregatedSyncException
import com.stt.android.remote.workout.video.VideoRemoteApi
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Spy
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(MockitoJUnitRunner::class)
class SyncNewVideosTest {
    @Mock
    private lateinit var currentUser: CurrentUser

    @Spy
    private lateinit var videoDataSource: FakeVideoDataSource

    @Spy
    private lateinit var videoFileRepository: FakeVideoFileRepository

    @Spy
    private lateinit var videoRestApi: FakeVideoRestApi

    private lateinit var syncNewVideos: SyncNewVideos

    @Before
    fun setup() {
        runTest {
            whenever(currentUser.getUsername()).thenReturn("username")

            syncNewVideos = SyncNewVideos(
                videoDataSource,
                videoFileRepository,
                VideoRemoteApi(videoRestApi),
                currentUser
            )
        }
    }

    @Test
    fun `no videos to upload should return 0 uploaded`() = runTest {
        whenever(videoDataSource.findUnsyncedVideos(any())).thenReturn(emptyList())
        assertThat(syncNewVideos()).isEqualTo(0)
    }

    @Test
    fun `skip video upload if error occurs when finding unsynced videos`() = runTest {
        whenever(videoDataSource.findUnsyncedVideos(any())).thenThrow(RuntimeException::class.java)

        assertThat(syncNewVideos()).isEqualTo(0)
    }

    @Test
    fun `upload 1 video should return 1 uploaded`() = runTest {
        assertThat(syncNewVideos()).isEqualTo(1)
    }

    @Test(expected = AggregatedSyncException::class)
    fun `first upload fails due to null filename should continue and finally throw AggregatedSyncException`() = runTest {
        whenever(videoDataSource.findUnsyncedVideos(any()))
            .thenReturn(
                listOf(
                    createVideo().copy(filename = null),
                    createVideo()
                )
            )

        syncNewVideos()
    }

    @Test(expected = AggregatedSyncException::class)
    fun `first upload fails due to null workout key should continue and finally throw AggregatedSyncException`() = runTest {
        whenever(videoDataSource.findUnsyncedVideos(any()))
            .thenReturn(
                listOf(
                    createVideo().copy(workoutKey = null),
                    createVideo()
                )
            )

        syncNewVideos()
    }

    @Test(expected = AggregatedSyncException::class)
    fun `first upload fails due to null thumbnail filename should continue and finally throw AggregatedSyncException`() = runTest {
        whenever(videoDataSource.findUnsyncedVideos(any()))
            .thenReturn(
                listOf(
                    createVideo().copy(thumbnailFilename = null),
                    createVideo()
                )
            )

        syncNewVideos()
    }
}
