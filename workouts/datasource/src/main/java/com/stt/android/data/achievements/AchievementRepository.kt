package com.stt.android.data.achievements

import com.stt.android.data.source.local.achievements.AchievementDao
import com.stt.android.domain.achievements.Achievement
import com.stt.android.domain.achievements.AchievementDataSource
import com.stt.android.domain.achievements.AchievementProvider
import com.stt.android.domain.achievements.RecordItem
import com.stt.android.domain.workouts.WorkoutHeader
import javax.inject.Inject

class AchievementRepository @Inject constructor(
    private val achievementProvider: AchievementProvider,
    private val achievementDao: AchievementDao,
    private val achievementMapper: AchievementMapper
) : AchievementDataSource {
    override suspend fun getAchievementFromLocalStore(workoutKey: String): Achievement? =
        achievementDao.findByKey(workoutKey)
            ?.let { achievementMapper.toDomainEntity()(it) }

    override suspend fun getAchievementsFromLocalStore(
        workoutKeys: List<String>
    ): Map<String, Achievement> = achievementDao.findByWorkoutKeys(workoutKeys)
        .map { achievementMapper.toDomainEntity()(it) }
        .associateBy(Achievement::workoutKey)

    override suspend fun storeAchievementToLocalStore(achievement: Achievement) {
        val localAchievement = achievementMapper.toDataEntity()(achievement)
        achievementDao.insert(localAchievement)
    }

    override suspend fun calculateRecordsForTrailRunning(): List<RecordItem> =
        achievementProvider.calculateRecordsForTrailRunning()

    override suspend fun calculateRecordsForCycling(): List<RecordItem> =
        achievementProvider.calculateRecordsForCycling()

    override suspend fun calculateAchievements(workout: WorkoutHeader) {
        achievementProvider.calculateAchievements(workout)
            ?.let {
                storeAchievementToLocalStore(it)
            }
    }

    override suspend fun calculateRecordsForRunning(): List<RecordItem> =
        achievementProvider.calculateRecordsForRunning()
}
