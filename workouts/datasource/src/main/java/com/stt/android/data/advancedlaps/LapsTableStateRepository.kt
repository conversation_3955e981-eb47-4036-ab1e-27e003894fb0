package com.stt.android.data.advancedlaps

import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.domain.advancedlaps.LapsTableStateDataSource
import javax.inject.Inject

class LapsTableStateRepository
@Inject constructor(
    private val lapsTableStateLocalDataSource: LapsTableStateLocalDataSource
) : LapsTableStateDataSource {
    override fun fetchColumnsState(key: String): List<LapsTableDataType>? =
        lapsTableStateLocalDataSource.fetchColumnsState(key)

    override fun saveColumnsState(key: String, columns: List<LapsTableDataType>) {
        lapsTableStateLocalDataSource.saveColumnsState(key, columns)
    }

    override fun saveIsLapsTableColouringEnabled(enabled: Boolean) {
        lapsTableStateLocalDataSource.saveIsLapsTableColouringEnabled(enabled)
    }

    override fun fetchIsLapsTableColouringEnabled(): Boolean {
        return lapsTableStateLocalDataSource.fetchIsLapsTableColouringEnabled()
    }
}
