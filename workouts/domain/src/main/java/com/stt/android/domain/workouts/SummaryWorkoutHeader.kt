package com.stt.android.domain.workouts

import androidx.compose.runtime.Immutable
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.tss.TSS
import kotlinx.parcelize.IgnoredOnParcel

/**
 * Basic version of [WorkoutHeader] loading only necessary data for heavier computations of training zone comparison
 */
@Immutable
data class SummaryWorkoutHeader(
    val id: Int,
    val key: String?,
    val username: String,
    val totalDistance: Double,
    val activityTypeId: Int,
    val avgSpeed: Double,
    val startTime: Long, // ms UTC
    val totalTime: Double, // seconds
    val heartRateAverage: Double,
    val totalAscent: Double,
    val energyConsumption: Double, // kCal
    val tss: TSS? = null,
    val vo2Max: Float?,
    // Cycling
    val avgPower: Float?,
    val normalizedPower: Float?,
    // ----
    val supportsDistance: Boolean,
    val supportsAvgSpeed: Boolean,
    val supportsAvgPace: Boolean,
    val supportsAvgHeartRate: Boolean,
    val supportsAscent: Boolean,
    val supportsEnergyConsumption: Boolean,
    val supportsTss: Boolean,
    val supportsVo2Max: Boolean,
    val supportsAvgPower: Boolean,
    val supportsNormalizedPower: Boolean,
    val supportsAvgSwimPace: Boolean,
) {
    @IgnoredOnParcel
    val activityType = ActivityType.valueOf(activityTypeId)
}
