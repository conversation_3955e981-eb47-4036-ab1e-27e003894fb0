package com.stt.android.domain.sml.reader

import com.stt.android.core.domain.SuuntoPlusChannel
import com.stt.android.data.toEpochMilli
import com.stt.android.domain.advancedlaps.WindowType
import com.stt.android.domain.sml.ActivityEvent
import com.stt.android.domain.sml.AlarmEvent
import com.stt.android.domain.sml.AlarmMarkType
import com.stt.android.domain.sml.BookmarkEvent
import com.stt.android.domain.sml.CatchEvent
import com.stt.android.domain.sml.CatchMarkType
import com.stt.android.domain.sml.DiveTimerEvent
import com.stt.android.domain.sml.ErrorEvent
import com.stt.android.domain.sml.ErrorMarkType
import com.stt.android.domain.sml.GasEditEvent
import com.stt.android.domain.sml.GasSwitchEvent
import com.stt.android.domain.sml.IntervalEvent
import com.stt.android.domain.sml.IntervalEventType
import com.stt.android.domain.sml.Location
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.NotifyEvent
import com.stt.android.domain.sml.NotifyMarkType
import com.stt.android.domain.sml.OoamEvent
import com.stt.android.domain.sml.OoamMarkType
import com.stt.android.domain.sml.RecordingStatusEvent
import com.stt.android.domain.sml.RecordingStatusEventType
import com.stt.android.domain.sml.SetPointEvent
import com.stt.android.domain.sml.ShotEvent
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlEvent
import com.stt.android.domain.sml.SmlEventData
import com.stt.android.domain.sml.SmlExtensionStreamPoint
import com.stt.android.domain.sml.SmlStreamData
import com.stt.android.domain.sml.SmlStreamSamplePoint
import com.stt.android.domain.sml.SmlSummary
import com.stt.android.domain.sml.StateEvent
import com.stt.android.domain.sml.StateMarkType
import com.stt.android.domain.sml.SwimmingEvent
import com.stt.android.domain.sml.UnknownEvent
import com.stt.android.domain.sml.VerticalLapEvent
import com.stt.android.domain.sml.VerticalLapEventType
import com.stt.android.domain.sml.WarningEvent
import com.stt.android.domain.sml.WarningMarkType
import com.stt.android.domain.sml.avgMinMaxOrNull
import com.stt.android.domain.sml.dataPointsWithoutPauses
import com.stt.android.domain.sml.dive.DiveStreamAlgorithm
import com.stt.android.domain.sml.fillConstantWithSampling
import com.stt.android.domain.workouts.AvgMinMax
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.infomodel.getStIdForMcId
import com.stt.android.logbook.LogbookGasData
import com.stt.android.logbook.NgDiveFooter
import com.stt.android.logbook.NgDiveHeader
import com.stt.android.logbook.NgDiveRouteOrigin
import com.stt.android.logbook.SmlSampleStatistics
import com.stt.android.logbook.SuuntoLogbookSample
import com.stt.android.logbook.SuuntoLogbookSummary
import com.stt.android.logbook.SuuntoLogbookSummaryContent
import com.stt.android.logbook.SuuntoLogbookWindow
import com.stt.android.logbook.SuuntoLogbookZapp
import com.stt.android.logbook.activityWindows
import com.stt.android.logbook.convertToLogbookGasData
import com.stt.android.sim.Coordinates
import com.stt.android.sim.DiveRoute
import com.stt.android.sim.Marks
import com.stt.android.utils.CoordinateUtils

object SmlFactory {
    @JvmStatic
    fun create(
        summaryContent: SuuntoLogbookSummaryContent,
        samples: List<SuuntoLogbookSample>,
        statistics: SmlSampleStatistics
    ) = createSml(summaryContent, samples, statistics)

    @JvmField
    val EMPTY: Sml = SmlInternal(
        SmlSummaryInternal(),
        SmlStreamDataInternal()
    )

    private fun createSml(
        summaryContent: SuuntoLogbookSummaryContent,
        samples: List<SuuntoLogbookSample>,
        statistics: SmlSampleStatistics
    ): Sml {
        val summary = processSummaries(summaryContent)
        val data = SmlStreamDataInternal()
        val events = mutableListOf<SmlEvent>()
        var lastSampleTime = 0L
        for (sample in samples) {
            when (sample) {
                is SuuntoLogbookSample.Events -> {
                    events += processEventSample(
                        sample = sample,
                        summary = summary,
                        data = data
                    )
                }

                is SuuntoLogbookSample.HR.RecoveryInThreeMins -> {
                    processRecoveryHeartRateInThreeMinsSample(sample, data)
                    processSamplePoint(sample, data)?.run { data.samplePoint.add(this) }
                    lastSampleTime = maxOf(lastSampleTime, sample.timestamp)
                }

                is SuuntoLogbookSample.HR.RawIbi,
                is SuuntoLogbookSample.HR.Optical,
                is SuuntoLogbookSample.HR.Ibi,
                is SuuntoLogbookSample.Location -> {
                    processSamplePoint(sample, data)?.run { data.samplePoint.add(this) }
                    lastSampleTime = maxOf(lastSampleTime, sample.timestamp)
                }

                is SuuntoLogbookSample.VerticalOscillation -> {
                    processVerticalOscillationSample(sample, data)
                    processSamplePoint(sample, data)?.run { data.samplePoint.add(this) }
                    lastSampleTime = maxOf(lastSampleTime, sample.timestamp)
                }

                is SuuntoLogbookSample.GroundContactTime -> {
                    processGroundContactTimeSample(sample, data)
                    processSamplePoint(sample, data)?.run { data.samplePoint.add(this) }
                    lastSampleTime = maxOf(lastSampleTime, sample.timestamp)
                }

                is SuuntoLogbookSample.Zapp -> {
                    processZappSample(sample, data, summary)
                    processSamplePoint(sample, data)?.run { data.samplePoint.add(this) }
                    lastSampleTime = maxOf(lastSampleTime, sample.timestamp)
                }

                is SuuntoLogbookSample.Periodic -> {
                    processPeriodicSample(sample, data)
                    processSamplePoint(sample, data)?.run { data.samplePoint.add(this) }
                    lastSampleTime = maxOf(lastSampleTime, sample.timestamp)
                }

                is SuuntoLogbookSample.Dive -> {
                    processDiveSample(sample, summary, statistics, data)
                    processSamplePoint(sample, data)?.run { data.samplePoint.add(this) }
                    lastSampleTime = maxOf(lastSampleTime, sample.timestamp)
                }

                is SuuntoLogbookSample.DiveEvent -> {
                    val event =
                        processDiveEventSample(
                            sample = sample,
                            summary = summary,
                            data = data
                        )
                    if (event != null) {
                        events += event
                    }
                    lastSampleTime = maxOf(lastSampleTime, sample.timestamp)
                }
            }
        }

        data.events += cleanRecordingStateEvents(events, lastSampleTime)
        data.strokeRate += processStrokeRateData(data)
        data.swolf += processSwolfData(summaryContent)

        data.suuntoPlusSamplePoints.values.forEach { points ->
            // fill missing sample before the last Pause event
            val lastPauseTimestamp = data.events.last {
                it is RecordingStatusEvent && it.type == RecordingStatusEventType.Pause
            }.data.timestamp
            points.fillConstantWithSampling(1000L, lastPauseTimestamp)
        }
        calculateSuuntoPlusSamplesStatistics(
            summary.suuntoPlusSamplesStatistics,
            data
        )
        return SmlInternal(summary, data)
    }

    private fun calculateSuuntoPlusSamplesStatistics(
        suuntoPlusSamplesStatistics: MutableMap<SuuntoPlusChannel, AvgMinMax>,
        streamData: SmlStreamDataInternal
    ) {
        val suuntoPlusSamplePoints = streamData.suuntoPlusSamplePoints
        for ((graph, points) in suuntoPlusSamplePoints.entries) {
            val stats = streamData.dataPointsWithoutPauses(points)
                .map { it.streamPoint }
                .avgMinMaxOrNull()
            if (stats != null) suuntoPlusSamplesStatistics[graph] = stats
        }
    }

    /**
     * Get's back a list with the swolf streamData as single point guides based on window start/end times
     * TODO this should be refactored ie streamData should be part of streamSegment or so
     */
    private fun processSwolfData(
        summaryContent: SuuntoLogbookSummaryContent,
    ): List<SmlExtensionStreamPoint> {
        var totalDistance = 0f
        var previousEndTimeStampInMilli = 0L
        return summaryContent.windows
            .filter { it.type == WindowType.POOLLENGTH.type }
            .fold(mutableListOf()) { acc, window ->
                // Setters
                val swolf = window.swolf?.firstOrNull()?.avg ?: 0.0f
                val duration = window.duration?.let { it * 1000.0f }?.toLong() ?: return acc
                // Due to an ESW bug:
                // If the previous window ended after this window's start time set the start time of this window the previous window's end time
                val endTimeStampInMilli = window.timestamp?.toEpochMilli() ?: return acc
                val startTimeStampInMilli =
                    (endTimeStampInMilli - duration).coerceAtLeast(previousEndTimeStampInMilli)
                // Set the prev value for future reference
                previousEndTimeStampInMilli = endTimeStampInMilli
                // Buffers and totals
                val startDistance = totalDistance
                totalDistance += window.distance ?: 0f
                val endDistance = totalDistance
                // Add 2 SWOLF samples representing a SWOLF period for that window
                // Sample 1 the start
                acc.add(SmlExtensionStreamPoint(startTimeStampInMilli, startDistance, swolf))
                // Sample 2 the end
                acc.add(SmlExtensionStreamPoint(endTimeStampInMilli, endDistance, swolf))
                acc
            }
    }

    private fun processStrokeRateData(
        data: SmlStreamDataInternal,
    ): List<SmlExtensionStreamPoint> {
        val swimmingStrokeEvents = data.getSwimmingEvents(type = "Stroke")
        if (swimmingStrokeEvents.isEmpty()) return emptyList()

        var previousTimeStamp = data.getActivityStartTimestamp()
        return swimmingStrokeEvents.fold(mutableListOf()) { acc, swimmingStrokeEvent ->
            // We divide 1 with the time (in seconds) it took for each stroke in order to find the value in Hz
            val valueInHz =
                1.0.div(swimmingStrokeEvent.timestamp.rem(previousTimeStamp).div(1000.0))
            previousTimeStamp = swimmingStrokeEvent.timestamp
            // Activities synced from a watch may under some circumstances (incorrectly) contain multiple stroke events with the exact same timestamp.
            if (valueInHz.isFinite()) {
                acc.add(
                    SmlExtensionStreamPoint(
                        swimmingStrokeEvent.timestamp,
                        null,
                        valueInHz.toFloat().times(60)
                    )
                )
            }
            acc
        }
    }

    private fun processSummaries(summaryContent: SuuntoLogbookSummaryContent): SmlSummaryInternal {
        // initially there is no active gas
        val diveFooter = summaryContent.diveFooter
        val gases =
            diveFooter?.gases?.convertToLogbookGasData() ?: DiveStreamAlgorithm.extractGases(
                summaryContent.summary
            )
        return SmlSummaryInternal(
            windows = summaryContent.windows,
            gases = gases,
            header = summaryContent.summary,
            diveHeader = summaryContent.diveHeader,
            diveFooter = summaryContent.diveFooter,
            zappChannels = summaryContent.zapps
                .filter { it.id != null && it.channels != null }
                .flatMap { zapp -> zapp.channels!!.map { zappChannel -> zapp.id!! to zappChannel } }
                .associateBy { (_, zappChannel) -> zappChannel.channelId }
        )
    }

    private fun processEventSample(
        sample: SuuntoLogbookSample.Events,
        summary: SmlSummaryInternal,
        data: SmlStreamDataInternal
    ): List<SmlEvent> {
        val events = sample.events.map { mark ->
            processSingleEvent(mark, sample.timestamp, summary, data)
        }
        val wetOutside = events.any {
            it is StateEvent && it.type == StateMarkType.WET_OUTSIDE && it.active == true
        }
        return events.mapNotNull { event ->
            if (event is StateEvent && event.type == StateMarkType.DIVE_ACTIVE) {
                event.copy(optionalData = wetOutside)
            } else {
                event
            }
        }
    }

    private fun processDiveEventSample(
        sample: SuuntoLogbookSample.DiveEvent,
        summary: SmlSummaryInternal,
        data: SmlStreamDataInternal
    ): SmlEvent? {
        return processSingleEvent(
            mark = sample.event,
            timestamp = sample.timestamp,
            summary = summary,
            data = data
        )
    }

    private fun processSingleEvent(
        mark: Marks,
        timestamp: Long,
        summary: SmlSummaryInternal,
        data: SmlStreamDataInternal
    ): SmlEvent? {
        return when {
            mark.activity != null -> mark.activity?.processActivityMark(timestamp)
            mark.lap != null -> mark.lap?.processLapMark(timestamp)
            mark.interval != null -> mark.interval?.processIntervalMark(timestamp)
            mark.verticalLap != null -> mark.verticalLap?.processVerticalLapMark(timestamp)
            mark.pause != null -> mark.pause?.processPauseMark(timestamp)
            mark.gasSwitchMark != null -> mark.gasSwitchMark?.processGasSwitchMark(
                timestamp,
                summary
            )

            mark.setPointMark != null -> mark.setPointMark?.processSetPointMark(
                timestamp,
                summary
            )

            mark.diveTimerMark != null -> mark.diveTimerMark?.processDiveTimerMark(timestamp)
            mark.gasEditMark != null -> mark.gasEditMark?.processGasEditMark(timestamp)
            mark.notifyMark != null -> mark.notifyMark?.processNotifyMark(
                timestamp,
                summary,
                data
            )

            mark.stateMark != null -> mark.stateMark?.processStateMark(timestamp)
            mark.warningMark != null -> mark.warningMark?.processWarningMark(
                timestamp,
                summary,
                data
            )

            mark.alarmMark != null -> mark.alarmMark?.processAlarmMark(timestamp)
            mark.errorMark != null -> mark.errorMark?.processErrorMark(timestamp)
            mark.bookmarkMark != null -> mark.bookmarkMark?.processBookmark(timestamp)
            mark.shotMark != null -> mark.shotMark?.processShotMark(timestamp)
            mark.catchMark != null -> mark.catchMark?.processCatchMark(timestamp)
            mark.swimmingMark != null -> mark.swimmingMark?.processSwimmingMark(timestamp)
            mark.diveStatusMark != null -> mark.diveStatusMark?.processDiveStateMark(timestamp)
            mark.ooamMark != null -> mark.ooamMark?.processOoamMark(timestamp)
            else -> UnknownEvent(SmlEventDataInternal(timestamp))
        }
    }

    private fun Marks.NotifyMark.processNotifyMark(
        timestamp: Long,
        summary: SmlSummaryInternal,
        data: SmlStreamDataInternal
    ): SmlEvent {
        val type = NotifyMarkType.from(type)
        return if (type == NotifyMarkType.TANK_PRESSURE || type == NotifyMarkType.CCR_O2_TANK_PRESSURE) {
            val tankPressure = data.tankPressures[summary.activeGasNumber]?.lastOrNull()?.value
            NotifyEvent(
                SmlEventDataInternal(timestamp),
                type,
                active,
                optionalData = tankPressure
            )
        } else {
            NotifyEvent(SmlEventDataInternal(timestamp), type, active)
        }
    }

    private fun Marks.StateMark.processStateMark(timestamp: Long): SmlEvent =
        StateEvent(SmlEventDataInternal(timestamp), StateMarkType.from(type), active)

    private fun Marks.WarningMark.processWarningMark(
        timestamp: Long,
        summary: SmlSummaryInternal,
        data: SmlStreamDataInternal
    ): SmlEvent {
        val type = WarningMarkType.from(type)
        return if (type == WarningMarkType.USER_TANK_PRESSURE) {
            val tankPressure = data.tankPressures[summary.activeGasNumber]?.lastOrNull()?.value
            WarningEvent(
                SmlEventDataInternal(timestamp),
                type,
                active,
                optionalData = tankPressure
            )
        } else {
            WarningEvent(SmlEventDataInternal(timestamp), type, active)
        }
    }

    private fun Marks.AlarmMark.processAlarmMark(timestamp: Long): SmlEvent =
        AlarmEvent(SmlEventDataInternal(timestamp), AlarmMarkType.from(type), active)

    private fun Marks.ErrorMark.processErrorMark(timestamp: Long): SmlEvent =
        ErrorEvent(SmlEventDataInternal(timestamp), ErrorMarkType.from(type))

    private fun Marks.BookmarkMark.processBookmark(timestamp: Long): SmlEvent =
        BookmarkEvent(SmlEventDataInternal(timestamp), name, screenshot)

    private fun Marks.ShotMark.processShotMark(timestamp: Long): SmlEvent =
        ShotEvent(SmlEventDataInternal(timestamp), location?.processCoordinates())

    private fun Marks.CatchMark.processCatchMark(timestamp: Long): SmlEvent = CatchEvent(
        SmlEventDataInternal(timestamp),
        count,
        CatchMarkType.from(description?.ordinal),
        type,
        location?.processCoordinates()
    )

    private fun Marks.SwimmingMark.processSwimmingMark(timestamp: Long): SmlEvent = SwimmingEvent(
        SmlEventDataInternal(timestamp),
        type,
    )

    private fun Boolean.processDiveStateMark(timestamp: Long): SmlEvent {
        // Map Ng "DiveStatus" event to old StateMarkEvent,
        val diveState = if (this) "Dive Active" else "Unknown Type"
        return StateEvent(
            data = SmlEventDataInternal(timestamp),
            type = StateMarkType.from(diveState),
            active = this,
            optionalData = this
        )
    }

    private fun Marks.OoamMark.processOoamMark(timestamp: Long): SmlEvent {
        return OoamEvent(
            data = SmlEventDataInternal(timestamp),
            type = OoamMarkType.from(this.type)
        )
    }

    private fun Coordinates.processCoordinates(): Location =
        Location(CoordinateUtils.toDeg(latitude), CoordinateUtils.toDeg(longitude))

    private fun Marks.ActivityMark.processActivityMark(
        timestamp: Long
    ): SmlEvent {
        return ActivityEvent(
            SmlEventDataInternal(timestamp),
            this.activityType,
            this.name,
            this.customModeId
        )
    }

    private fun Marks.LapMark.processLapMark(
        timestamp: Long
    ): SmlEvent {
        return when (this.type) {
            Marks.LapMarkType.START -> {
                RecordingStatusEvent(
                    SmlEventDataInternal(timestamp),
                    RecordingStatusEventType.Start
                )
            }

            Marks.LapMarkType.STOP -> {
                RecordingStatusEvent(
                    SmlEventDataInternal(timestamp),
                    RecordingStatusEventType.Stop
                )
            }

            else -> UnknownEvent(SmlEventDataInternal(timestamp))
        }
    }

    private fun Marks.IntervalMark.processIntervalMark(
        timestamp: Long
    ): SmlEvent {
        return when (this.type) {
            Marks.IntervalMarkType.INTERVAL -> {
                IntervalEvent(
                    SmlEventDataInternal(timestamp),
                    IntervalEventType.Interval
                )
            }

            Marks.IntervalMarkType.RECOVERY -> {
                IntervalEvent(
                    SmlEventDataInternal(timestamp),
                    IntervalEventType.Recovery
                )
            }

            else -> UnknownEvent(SmlEventDataInternal(timestamp))
        }
    }

    private fun Marks.VerticalLapMark.processVerticalLapMark(
        timestamp: Long
    ): SmlEvent = VerticalLapEvent(
        SmlEventDataInternal(timestamp),
        when (this.type) {
            Marks.VerticalLapMarkType.DOWNHILL -> VerticalLapEventType.Downhill
            else -> VerticalLapEventType.Unknown
        }
    )

    private fun Marks.PauseMark.processPauseMark(
        timestamp: Long
    ): SmlEvent {
        return RecordingStatusEvent(
            SmlEventDataInternal(timestamp),
            if (this.state) {
                RecordingStatusEventType.Pause
            } else {
                RecordingStatusEventType.Resume
            }
        )
    }

    private fun Marks.GasSwitchMark.processGasSwitchMark(
        timestamp: Long,
        summary: SmlSummaryInternal
    ): SmlEvent {
        val getGasName = { gasNumber: Int? ->
            gasNumber?.run {
                summary.gases.firstOrNull { it.gasIndex == this }?.gasName
            }
        }
        val previousGasNumber = summary.activeGasNumber
        val previousGasName = getGasName(previousGasNumber)
        summary.activeGasNumber = gasNumber
        val gasName = getGasName(summary.activeGasNumber) ?: ""
        return GasSwitchEvent(
            SmlEventDataInternal(timestamp),
            gasNumber,
            previousGasNumber = previousGasNumber,
            previousGasName = previousGasName,
            text = gasName
        )
    }

    private fun Marks.SetPointMark.processSetPointMark(
        timestamp: Long,
        summary: SmlSummaryInternal
    ): SmlEvent {
        val depth = when {
            type == Marks.SetPointMarkType.HIGH && automatic -> summary.header?.diving?.switchHighSetPoint?.depth
            type == Marks.SetPointMarkType.LOW && automatic -> summary.header?.diving?.switchLowSetPoint?.depth
            else -> null
        }

        return SetPointEvent(
            data = SmlEventDataInternal(timestamp),
            type = type,
            automatic = automatic,
            po2 = po2,
            depth = depth
        )
    }

    private fun Marks.DiveTimerMark.processDiveTimerMark(
        timestamp: Long
    ): SmlEvent = DiveTimerEvent(SmlEventDataInternal(timestamp), active, time)

    private fun Marks.GasEditMark.processGasEditMark(
        timestamp: Long
    ): SmlEvent =
        GasEditEvent(SmlEventDataInternal(timestamp), insertGasNumber, removeGasNumber)

    /**
     * Conforms recording status changes to always have [Start|Resume] - [Pause/Stop] pairs e.g.
     */
    private fun cleanRecordingStateEvents(
        events: List<SmlEvent>,
        lastSampleTime: Long
    ): MutableList<SmlEvent> {
        val curated = mutableListOf<SmlEvent>()
        var previous: RecordingStatusEvent? = null
        events
            .forEach { current ->
                if (current !is RecordingStatusEvent) {
                    curated.add(current) // Don't care about other events, but don't lose them either
                } else {
                    val previousChange = previous?.type
                    if (previousChange == null) {
                        // No previous status event, this should be start or resume or we ignore it
                        if (listOf(
                                RecordingStatusEventType.Start,
                                RecordingStatusEventType.Resume
                            ).contains(current.type)
                        ) {
                            previous = current
                            curated.add(current)
                        }
                    } else if (current.type.isAllowedAfter(previousChange)) { // ignore if the condition is not met
                        previous = current
                        curated.add(current)
                    }
                }
            }

        if (curated.isNotEmpty()) {
            if (curated.none { it is RecordingStatusEvent }) {
                // If there are no status events (e.g. dives) we assume that the first event started the workout
                curated.add(
                    0,
                    RecordingStatusEvent(
                        SmlEventDataInternal(curated.first().data.timestamp),
                        RecordingStatusEventType.Start
                    )
                )
            }

            if (curated.none { it is RecordingStatusEvent && it.type == RecordingStatusEventType.Stop }) {
                // We had that case when STOP was missing from the SML
                val stopEventTime = maxOf(
                    lastSampleTime,
                    curated.maxOf { (it as? RecordingStatusEvent)?.timestamp ?: 0L }
                )
                curated.add(
                    RecordingStatusEvent(
                        SmlEventDataInternal(stopEventTime),
                        RecordingStatusEventType.Stop
                    )
                )
            }
        }

        calculateEventDurations(curated)
        return curated
    }

    private fun calculateEventDurations(events: List<SmlEvent>) {
        var recordingStart: Long? = null // timestamp of the first start/resume event
        var currentStart: Long? = null // timestamp of the most recent start/resume event
        var recordingDuration: Long = 0 // duration from the first start/resume event without pauses

        events.forEach { event ->
            val eventData = event.data as SmlEventDataInternal

            if (event is RecordingStatusEvent) {
                currentStart?.let {
                    // currentStart is null if we are on pause or if it is the first start/resume
                    // duration increased with time elapsed between last 2 status events.
                    recordingDuration += event.timestamp - it
                }

                if (listOf(
                        RecordingStatusEventType.Start,
                        RecordingStatusEventType.Resume
                    ).contains(event.type)
                ) {
                    recordingStart = recordingStart ?: event.timestamp
                    currentStart = event.timestamp
                } else {
                    // set to null so that duration is not changed during a pause
                    currentStart = null
                    eventData.duration = recordingDuration
                }
            }
            recordingStart?.let {
                eventData.elapsed = eventData.timestamp - it
            }

            currentStart?.let {
                eventData.duration = recordingDuration + eventData.timestamp - it
            }
        }
    }

    private fun processDiveSample(
        sample: SuuntoLogbookSample.Dive,
        summary: SmlSummaryInternal,
        statistics: SmlSampleStatistics,
        data: SmlStreamDataInternal
    ) {
        val timestamp = sample.timestamp

        sample.ventilation?.let {
            data.ventilation.add(
                SmlExtensionStreamPoint(
                    timestamp = timestamp,
                    cumulativeDistance = null,
                    value = it
                )
            )
        }

        sample.depth?.let {
            // We want to wipe out depths smaller than the smallest scale we can show (1 decimal) but still keep the sample of 0
            data.depth.add(
                SmlExtensionStreamPoint(
                    timestamp = timestamp,
                    cumulativeDistance = null,
                    value = if (it >= 0.1f) it else 0.0f
                )
            )
        }

        sample.temperature?.let {
            data.temperature.add(SmlExtensionStreamPoint(timestamp, null, it))
        }

        sample.diveRoute?.let(data.diveTrack::add)

        sample.diveRouteOrigin?.let(data.diveTrackOrigin::add)

        sample.diveRouteQuality?.let(data.diveTrackQuality::add)

        if (statistics.cylinderInfoCount > 0 || summary.gases.isNotEmpty()) {
            // Skip tank pressure processing if we have no cylinder information (e.g. non-dive
            // workouts)
            processCylinderData(timestamp, sample, summary, data)
        }
    }

    private fun processZappSample(
        sample: SuuntoLogbookSample.Zapp,
        data: SmlStreamDataInternal,
        summary: SmlSummaryInternal
    ) {
        val timestamp = sample.timestamp
        val channelId = sample.zappSample.channelId
        val (zappId, channel) = summary.zappChannels[channelId] ?: return
        val suuntoPlusChannel = SuuntoPlusChannel(
            zappId = zappId,
            channelId = channel.channelId,
            format = channel.format,
            inverted = channel.inverted,
            name = channel.name,
            variableId = channel.variableId,
        )
        data.suuntoPlusChannelsById.putIfAbsent(channel.channelId, suuntoPlusChannel)
        sample.zappSample.value?.let { zappSampleValue ->
            data.suuntoPlusSamplePoints.getOrPut(suuntoPlusChannel) { mutableListOf() }.add(
                SmlExtensionStreamPoint(timestamp, null, zappSampleValue)
            )
        }
    }

    private fun processPeriodicSample(
        sample: SuuntoLogbookSample.Periodic,
        data: SmlStreamDataInternal
    ) {
        val timestamp = sample.timestamp

        sample.verticalSpeed?.let {
            data.verticalSpeed.add(SmlExtensionStreamPoint(timestamp, sample.distance, it))
        }

        sample.power?.let {
            data.power.add(SmlExtensionStreamPoint(timestamp, sample.distance, it))
        }

        sample.speed?.let {
            data.speed.add(SmlExtensionStreamPoint(timestamp, sample.distance, it))
        }

        sample.cadence?.let {
            data.cadence.add(SmlExtensionStreamPoint(timestamp, sample.distance, it))
        }

        sample.temperature?.let {
            data.temperature.add(SmlExtensionStreamPoint(timestamp, sample.distance, it))
        }

        sample.altitude?.let {
            data.altitude.add(SmlExtensionStreamPoint(timestamp, sample.distance, it))
        }

        sample.duration?.let {
            data.duration.add(SmlExtensionStreamPoint(timestamp, sample.distance, it))
        }
        sample.freestyleHeadAngle?.let {
            data.freestyleHeadAngle.add(
                SmlExtensionStreamPoint(
                    timestamp,
                    sample.distance,
                    it.toFloat()
                )
            )
        }
        sample.breaststrokeHeadAngle?.let {
            data.breaststrokeHeadAngle.add(
                SmlExtensionStreamPoint(
                    timestamp,
                    sample.distance,
                    it.toFloat()
                )
            )
        }
        sample.freestyleAvgBreathAngle?.let {
            data.freestyleAvgBreathAngle.add(
                SmlExtensionStreamPoint(
                    timestamp,
                    sample.distance,
                    it.toFloat()
                )
            )
        }
        sample.breaststrokeAvgBreathAngle?.let {
            data.breaststrokeAvgBreathAngle.add(
                SmlExtensionStreamPoint(
                    timestamp,
                    sample.distance,
                    it.toFloat()
                )
            )
        }
        sample.breaststrokeGlideTime?.let {
            data.breaststrokeGlideTime.add(
                SmlExtensionStreamPoint(
                    timestamp,
                    sample.distance,
                    it.toFloat()
                )
            )
        }
        sample.breathingRate?.let {
            data.breathingRate.add(
                SmlExtensionStreamPoint(
                    timestamp,
                    sample.distance,
                    it.toFloat()
                )
            )
        }
        sample.skipsPerRound?.let {
            data.skipsPerRound.add(
                SmlExtensionStreamPoint(
                    timestamp,
                    sample.distance,
                    it.toFloat()
                )
            )
        }
    }

    private fun processGroundContactTimeSample(
        sample: SuuntoLogbookSample.GroundContactTime,
        data: SmlStreamDataInternal
    ) {
        data.groundContactTimes.add(
            SmlExtensionStreamPoint(
                sample.timestamp,
                null,
                sample.groundContactTime
            )
        )
    }

    private fun processVerticalOscillationSample(
        sample: SuuntoLogbookSample.VerticalOscillation,
        data: SmlStreamDataInternal
    ) {
        data.verticalOscillations.add(
            SmlExtensionStreamPoint(
                sample.timestamp,
                null,
                sample.verticalOscillation
            )
        )
    }

    private fun processRecoveryHeartRateInThreeMinsSample(
        sample: SuuntoLogbookSample.HR.RecoveryInThreeMins,
        data: SmlStreamDataInternal
    ) {
        data.recoveryHeartInThreeMins.add(
            SmlExtensionStreamPoint(
                sample.timestamp,
                null,
                sample.hr
            )
        )
    }

    private fun processSamplePoint(
        sample: SuuntoLogbookSample,
        data: SmlStreamDataInternal
    ): SmlStreamSamplePoint? {
        // todo next refactoring should get rid of copying all data to these sample points
        val timestamp = sample.timestamp
        val smlSample = SmlStreamSamplePoint(
            timestamp = timestamp,
            cumulativeDistance = (sample as? SuuntoLogbookSample.Periodic)?.distance,
            heartrate = (sample as? SuuntoLogbookSample.HR)?.hr,
            speed = (sample as? SuuntoLogbookSample.Periodic)?.speed,
            altitude = (sample as? SuuntoLogbookSample.Periodic)?.altitude,
            temperature = (sample as? SuuntoLogbookSample.Periodic)?.temperature
                ?: (sample as? SuuntoLogbookSample.Dive)?.temperature,
            verticalSpeed = (sample as? SuuntoLogbookSample.Periodic)?.verticalSpeed,
            power = (sample as? SuuntoLogbookSample.Periodic)?.power,
            cadence = (sample as? SuuntoLogbookSample.Periodic)?.cadence,
            latitude = (sample as? SuuntoLogbookSample.Location)?.latitude?.run(CoordinateUtils::toDeg),
            longitude = (sample as? SuuntoLogbookSample.Location)?.longitude?.run(CoordinateUtils::toDeg),
            suuntoPlusSample = (sample as? SuuntoLogbookSample.Zapp)?.zappSample?.let {
                // we use the map cache to avoid recreating a new channel object for each SuuntoPlus sample
                val channel = data.suuntoPlusChannelsById[it.channelId]
                if (channel != null && it.value != null) channel to it.value!! else null
            },
            rawIbiSuuntoLogbookSample = (sample as? SuuntoLogbookSample.HR.RawIbi),
            recoveryHeartrateInThreeMins = (sample as? SuuntoLogbookSample.HR.RecoveryInThreeMins)?.hr,
            verticalOscillation = (sample as? SuuntoLogbookSample.VerticalOscillation)?.verticalOscillation,
            groundContactTime = (sample as? SuuntoLogbookSample.GroundContactTime)?.groundContactTime,
            breaststrokeGlideTime = (sample as? SuuntoLogbookSample.Periodic)?.breaststrokeGlideTime,
            freestyleAvgBreathAngle = (sample as? SuuntoLogbookSample.Periodic)?.freestyleAvgBreathAngle,
            breaststrokeAvgBreathAngle = (sample as? SuuntoLogbookSample.Periodic)?.breaststrokeAvgBreathAngle,
            breathingRate = (sample as? SuuntoLogbookSample.Periodic)?.breathingRate,
            breaststrokeHeadAngle = (sample as? SuuntoLogbookSample.Periodic)?.breaststrokeHeadAngle,
            freestyleHeadAngle = (sample as? SuuntoLogbookSample.Periodic)?.freestyleHeadAngle,
        )
        return if (smlSample.isSampleEmpty()) {
            null
        } else {
            smlSample
        }
    }

    private fun processCylinderData(
        timestamp: Long,
        sample: SuuntoLogbookSample.Dive,
        summary: SmlSummaryInternal,
        data: SmlStreamDataInternal
    ) {
        val activeGasIndex: Int? = summary.activeGasNumber
        val gasType: String? = activeGasIndex?.let { index ->
            summary.gases.firstOrNull { it.gasIndex == index }?.gasName
        }

        // Determine the gas index to use, falling back to -1 if no active gas or gas type is found
        val gasIndexToUse = if (activeGasIndex != null && gasType != null) activeGasIndex else -1
        val tankPressuresList = data.tankPressures.getOrPut(gasIndexToUse) { mutableListOf() }
        val tankPressures2List = data.tankPressures2.getOrPut(gasIndexToUse) { mutableListOf() }
        val gasConsumptionsList = data.gasConsumptions.getOrPut(gasIndexToUse) { mutableListOf() }

        if (activeGasIndex != null && gasType != null) {
            // we have an active gas, we look for the corresponding cylinder pressure
            sample.cylinders?.firstOrNull {
                it.gasNumber != null && it.gasNumber == activeGasIndex
            }?.let { cylinder ->
                // adding correct cylinder pressure if found
                cylinder.pressureKPa?.let { pressureKPa ->
                    tankPressuresList.add(SmlExtensionStreamPoint(timestamp, null, pressureKPa))
                }
                cylinder.pressure2KPa?.let { pressure2KPa ->
                    tankPressures2List.add(SmlExtensionStreamPoint(timestamp, null, pressure2KPa))
                }
                // add cylinder ventilation, Ng dive devices
                cylinder.ventilation?.let { ventilation ->
                    gasConsumptionsList.add(SmlExtensionStreamPoint(timestamp, null, ventilation))
                }
            }
        }
    }

    private data class SmlInternal internal constructor(
        override val summary: SmlSummary,
        override val streamData: SmlStreamData
    ) : Sml

    private data class SmlSummaryInternal internal constructor(
        override var windows: List<SuuntoLogbookWindow> = listOf(),
        var activeGasNumber: Int? = null,
        var previousGasNumber: Int? = null,
        val gases: List<LogbookGasData> = listOf(),
        override val header: SuuntoLogbookSummary? = null,
        // zapp channels grouped by channelId, carrying over zapp.id string
        val zappChannels: Map<Int, Pair<String, SuuntoLogbookZapp.ZappChannel>> = mapOf(),
        override val suuntoPlusSamplesStatistics: MutableMap<SuuntoPlusChannel, AvgMinMax> = mutableMapOf(),
        override val diveHeader: NgDiveHeader? = null,
        override val diveFooter: NgDiveFooter? = null
    ) : SmlSummary {
        override val activityWindows: List<SuuntoLogbookWindow>
            get() = windows.activityWindows
                // Transition windows appear inbetween multisport activities
                .filter { it.activityId != ActivityMapping.TRANSITION.mcId }
    }

    private data class SmlStreamDataInternal internal constructor(
        override val events: MutableList<SmlEvent> = mutableListOf(),
        override val verticalSpeed: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val power: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val cadence: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val temperature: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val speed: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val depth: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val diveTrack: MutableList<DiveRoute> = mutableListOf(),
        override val diveTrackOrigin: MutableList<NgDiveRouteOrigin> = mutableListOf(),
        override val diveTrackQuality: MutableList<Double> = mutableListOf(),
        override val ventilation: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val tankPressures: MutableMap<Int, MutableList<SmlExtensionStreamPoint>> = mutableMapOf(),
        override val tankPressures2: MutableMap<Int, MutableList<SmlExtensionStreamPoint>> = mutableMapOf(),
        override val strokeRate: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val swolf: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val altitude: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val samplePoint: MutableList<SmlStreamSamplePoint> = mutableListOf(),
        override val suuntoPlusChannelsById: MutableMap<Int, SuuntoPlusChannel> = mutableMapOf(),
        override val suuntoPlusSamplePoints: MutableMap<SuuntoPlusChannel, MutableList<SmlExtensionStreamPoint>> = mutableMapOf(),
        override val gasConsumptions: MutableMap<Int, MutableList<SmlExtensionStreamPoint>> = mutableMapOf(),
        override val verticalOscillations: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val groundContactTimes: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val recoveryHeartInThreeMins: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val duration: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val breaststrokeGlideTime: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val freestyleAvgBreathAngle: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val breaststrokeAvgBreathAngle: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val breathingRate: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val breaststrokeHeadAngle: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val freestyleHeadAngle: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
        override val skipsPerRound: MutableList<SmlExtensionStreamPoint> = mutableListOf(),
    ) : SmlStreamData {
        override val multisportPartActivities: List<MultisportPartActivity> by lazy {
            mutableListOf<MultisportPartActivity>().apply {
                val filtered = events.filter {
                    it is ActivityEvent || (it is RecordingStatusEvent && it.type == RecordingStatusEventType.Stop)
                }
                var shouldUpdate = false
                for (event in filtered) {
                    if (shouldUpdate) {
                        if (isNotEmpty()) {
                            // Current activity has ended. Update the stop timestamp
                            this[lastIndex] = last().copy(stopTime = event.data.timestamp)
                        }
                        shouldUpdate = false
                    }
                    if (event is ActivityEvent && event.activityType != ActivityMapping.TRANSITION.mcId) {
                        add(
                            MultisportPartActivity(
                                activityType = getStIdForMcId(event.activityType),
                                startTime = event.timestamp,
                                // This is later updated with correct value. At this point we do not
                                // yet know when the activity ends.
                                stopTime = event.timestamp,
                                elapsed = event.elapsed
                            )
                        )
                        shouldUpdate = true
                    }
                }
            }
        }

        fun getActivityStartTimestamp(): Long {
            return events.find {
                it is RecordingStatusEvent && it.type == RecordingStatusEventType.Start
            }?.data?.timestamp ?: throw Exception("No activity start time found")
        }
    }

    internal data class SmlEventDataInternal(
        override val timestamp: Long,
        override var elapsed: Long? = null,
        override var duration: Long? = null
    ) : SmlEventData
}
