package com.stt.android.domain.sml

import android.os.Parcelable
import com.stt.android.core.domain.SuuntoPlusChannel
import com.stt.android.data.toEpochMilli
import com.stt.android.domain.workouts.AvgMinMax
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.infomodel.getMcIdForStId
import com.stt.android.infomodel.getStIdForMcId
import com.stt.android.logbook.NgDiveFooter
import com.stt.android.logbook.NgDiveHeader
import com.stt.android.logbook.NgDiveRouteOrigin
import com.stt.android.logbook.SuuntoLogbookSample
import com.stt.android.logbook.SuuntoLogbookSummary
import com.stt.android.logbook.SuuntoLogbookWindow
import com.stt.android.logbook.moveWindows
import com.stt.android.sim.DiveRoute
import com.stt.android.utils.removeFirstOccurrence
import kotlinx.parcelize.Parcelize
import timber.log.Timber

interface Sml {
    val summary: SmlSummary
    val streamData: SmlStreamData

    fun getActivityWindow(multisportPartActivity: MultisportPartActivity?): SuuntoLogbookWindow? {
        // Would be nice to have a better way to map activity type change events to windows
        return if (multisportPartActivity == null) {
            val activityType = summary.header?.activityType
            summary.activityWindows.firstOrNull { it.activityId == activityType }
                // Fallback to "Move" window if "Activity" window not found for the activity type
                ?: summary.windows.moveWindows
                    .firstOrNull { it.activityId == activityType }
        } else {
            val index = streamData.multisportPartActivities.indexOf(multisportPartActivity)
            val window = summary.activityWindows.getOrNull(index)
            val activityType = window?.activityId?.run { getStIdForMcId(this) } ?: -1
            if (activityType == multisportPartActivity.activityType) {
                window
            } else {
                null
            }
        }
    }

    fun getMultisportWindows(multisportPartActivity: MultisportPartActivity): List<SuuntoLogbookWindow> {
        val partActivities = streamData.multisportPartActivities
        val mcId = getMcIdForStId(multisportPartActivity.activityType)
        val multisportWindows = summary.multisportWindows
        val index = partActivities.indexOf(multisportPartActivity)
        return if (index != -1 && multisportWindows.size == partActivities.size &&
            multisportWindows[index].first == mcId
        ) {
            multisportWindows[index].second
        } else {
            getWindowsFallback(summary.windows, multisportPartActivity)
        }
    }

    /**
     * Fallback method to determine multisport part activity windows based on start and end timestamps
     * @param windows List of all windows from the SML
     * @param multisportPartActivity Part activity to get windows for
     */
    private fun getWindowsFallback(
        windows: List<SuuntoLogbookWindow>,
        multisportPartActivity: MultisportPartActivity?
    ): List<SuuntoLogbookWindow> {
        return if (multisportPartActivity == null) {
            windows
        } else {
            // Need to figure out which lap windows apply to the given MultisportPartActivity
            windows
                .filter {
                    // Filter in windows during the multisport part activities
                    val epochMilli = it.timestamp?.toEpochMilli() ?: return@filter false
                    epochMilli in multisportPartActivity.startTime..multisportPartActivity.stopTime
                }
                .filter {
                    // Sanity check, activity types must match
                    val mcId = it.activityId
                    mcId != null && getStIdForMcId(mcId) == multisportPartActivity.activityType
                }
        }
    }
}

interface SmlSummary {
    val activityWindows: List<SuuntoLogbookWindow>
    val windows: List<SuuntoLogbookWindow>
    val header: SuuntoLogbookSummary?
    val diveHeader: NgDiveHeader?
    val diveFooter: NgDiveFooter?
    val moveWindow: SuuntoLogbookWindow?
        get() = windows.firstOrNull { it.type == "Move" }

    val suuntoPlusSamplesStatistics: Map<SuuntoPlusChannel, AvgMinMax>

    val isDiveAfterTissueReset: Boolean
        get() = header?.diving?.timeFromReset != null && header?.diving?.timeFromReset != 0

    val isMultisport: Boolean
        get() = moveWindow?.let {
            listOf(
                ActivityMapping.TRIATHLON.mcId,
                ActivityMapping.DUATHLON.mcId,
                ActivityMapping.SWIMRUN.mcId,
                ActivityMapping.AQUATHLON.mcId,
                ActivityMapping.MULTISPORT.mcId
            ).contains(it.activityId)
        } ?: false

    /**
     * Combines consecutive windows with the same activity id into a list and pairs it to the
     * activity id.
     */
    val multisportWindows: List<Pair<Int, List<SuuntoLogbookWindow>>>
        get() {
            val result: MutableList<Pair<Int, List<SuuntoLogbookWindow>>> = mutableListOf()
            val filtered = windows.filter { it.activityId != ActivityMapping.TRANSITION.mcId }
            val list: MutableList<SuuntoLogbookWindow> = filtered.take(1).toMutableList()
            filtered.windowed(2, 1, false) { windowed ->
                if (!windowed.all { window -> window.activityId == list.first().activityId }) {
                    list.first().activityId?.run { result.add(this to list.toMutableList()) }
                    list.clear()
                }
                list.add(windowed.last())
            }
            return result
        }
}

// TODO: Optimize: this data structure is memory heavy with long workouts. The highest memory
// usage occurs when building this structure based on the parsed SML since both need to be fully
// in memory at that time. Also, many of the lists will be full of null values since we don't have
// all data for all workouts and some are dive specific.
//
// Using Lists here has resulted in much of the further processing and filtering to be based on
// Lists as well. This causes even more memory load due to the Lists being duplicated. Would be
// better to expose Iterables/Sequences or a custom API instead.
interface SmlStreamData {
    val events: List<SmlEvent>
    val power: List<SmlExtensionStreamPoint>
    val cadence: List<SmlExtensionStreamPoint>
    val temperature: List<SmlExtensionStreamPoint>
    val speed: List<SmlExtensionStreamPoint>
    val depth: List<SmlExtensionStreamPoint>
    val diveTrack: List<DiveRoute> // 3D track in XYZ
    val diveTrackOrigin: List<NgDiveRouteOrigin>
    val diveTrackQuality: List<Double>
    val ventilation: List<SmlExtensionStreamPoint>
    val verticalSpeed: List<SmlExtensionStreamPoint>

    // tank pressure by gas index. Pressure values are in kPa
    val tankPressures: Map<Int, List<SmlExtensionStreamPoint>>

    // secondary tank pressure by gas index (sidemount). Pressure values are in kPa
    val tankPressures2: Map<Int, List<SmlExtensionStreamPoint>>
    val gasConsumptions: Map<Int, List<SmlExtensionStreamPoint>>
    val strokeRate: List<SmlExtensionStreamPoint>
    val swolf: List<SmlExtensionStreamPoint>
    val altitude: List<SmlExtensionStreamPoint>
    val verticalOscillations: List<SmlExtensionStreamPoint>
    val groundContactTimes: List<SmlExtensionStreamPoint>
    val recoveryHeartInThreeMins: List<SmlExtensionStreamPoint>

    // TODO: If we have R-R values (IBI data) from the workout, samplePoints will have a large
    // number of HR-only samples (one for each heartbeat). Consider using a separate data structure
    // for this.
    val samplePoint: List<SmlStreamSamplePoint>

    val multisportPartActivities: List<MultisportPartActivity>

    val suuntoPlusChannelsById: Map<Int, SuuntoPlusChannel>
    val suuntoPlusSamplePoints: Map<SuuntoPlusChannel, List<SmlExtensionStreamPoint>>
    val duration: List<SmlExtensionStreamPoint>
    val breaststrokeGlideTime: List<SmlExtensionStreamPoint>
    val freestyleAvgBreathAngle: List<SmlExtensionStreamPoint>
    val breaststrokeAvgBreathAngle: List<SmlExtensionStreamPoint>
    val breathingRate: List<SmlExtensionStreamPoint>
    val breaststrokeHeadAngle: List<SmlExtensionStreamPoint>
    val freestyleHeadAngle: List<SmlExtensionStreamPoint>
    val skipsPerRound: List<SmlExtensionStreamPoint>

    fun getDiveEvents(isDiveAfterTissueReset: Boolean): List<DiveEvent> {
        val filterGasSwitches = tankPressures.isEmpty() || tankPressures.all { it.key == -1 }

        val diveEvents = events
            .asSequence()
            .filterIsInstance<DiveEvent>()
            .filter { it.isSupported }
            // WARNING_MINI_LOCK event is shown in UI only if the timestamp == 0L. See TP #102258
            .filterNot { it is WarningEvent && it.type == WarningMarkType.MINI_LOCK && it.elapsed != 0L }
            .filterNot { it is NotifyEvent && it.type == NotifyMarkType.SET_POINT_SWITCH }
            .filterNot { it is GasSwitchEvent && (filterGasSwitches || it.previousGasNumber == null) }
            // Remove the init CC event (activation of CC)
            .removeFirstOccurrence { it is StateEvent && it.type == StateMarkType.CLOSED_CIRCUIT_MODE && it.active == true }
            .toList()

        return if (isDiveAfterTissueReset) {
            listOf(createTissueResetEvent()) + diveEvents
        } else {
            diveEvents
        }
    }

    fun getSwimmingEvents(type: String): List<SwimmingEvent> {
        return events
            .filterIsInstance<SwimmingEvent>()
            .filter { it.type == type }
    }

    private fun createTissueResetEvent(): DiveEvent {
        return object : DiveEvent {
            override val stringRes = com.stt.android.core.R.string.tissue_reset
            override val descriptionStringRes =
                com.stt.android.core.R.string.tissue_reset_description
            override val iconRes = com.stt.android.core.R.drawable.dive_event_red_triangle
            override val iconSmallRes =
                com.stt.android.core.R.drawable.dive_event_red_triangle_small
            override val text = ""
            override val data = object : SmlEventData {
                override val timestamp = 0L
                override val elapsed = 0L
                override val duration: Long? = null
            }
        }
    }
}

interface TimestampedDataPoint<T> {
    val timestamp: Long
    val cumulativeDistance: Float? // Excluding pauses
    fun toTimedDataPoint(time: Long, cumulativeDistance: Float): T
}

interface TimedDataPoint {
    val time: Long
    val cumulativeDistance: Float // Excluding pauses
}

interface ValueDataPoint {
    val value: Float
}

interface TimedValueDataPoint : ValueDataPoint, TimedDataPoint

@Parcelize
data class MultisportPartActivity constructor(
    val activityType: Int,
    val startTime: Long,
    val stopTime: Long,
    val elapsed: Long? = null
) : Parcelable

data class SmlTimedExtensionValuePoint(
    override val time: Long, // Relative time from start timestamp excluding pauses
    override val cumulativeDistance: Float, // Excluding pauses
    override val value: Float
) : TimedValueDataPoint

data class SmlTimedExtensionStreamPoint(
    override val time: Long, // Relative time from start timestamp excluding pauses
    override val cumulativeDistance: Float, // Excluding pauses
    val streamPoint: SmlExtensionStreamPoint
) : TimedValueDataPoint {
    override val value: Float
        get() = streamPoint.value
}

data class SmlExtensionStreamPoint(
    override val timestamp: Long,
    override val cumulativeDistance: Float?,
    override val value: Float
) : ValueDataPoint,
    TimestampedDataPoint<SmlTimedExtensionStreamPoint> {
    override fun toTimedDataPoint(
        time: Long,
        cumulativeDistance: Float
    ): SmlTimedExtensionStreamPoint =
        SmlTimedExtensionStreamPoint(
            time,
            cumulativeDistance,
            this.copy(cumulativeDistance = cumulativeDistance)
        )
}

/**
 * When updating properties of this dataclass please take look at the isEmpty implementation and update it
 */
data class SmlStreamSamplePoint(
    override val timestamp: Long,
    override val cumulativeDistance: Float?,
    val heartrate: Float?,
    val speed: Float?,
    val altitude: Float?,
    val temperature: Float?,
    val verticalSpeed: Float?,
    val power: Float?,
    val cadence: Float?,
    val latitude: Double?,
    val longitude: Double?,
    val suuntoPlusSample: Pair<SuuntoPlusChannel, Float>?,
    val rawIbiSuuntoLogbookSample: SuuntoLogbookSample?,
    val recoveryHeartrateInThreeMins: Float?,
    val verticalOscillation: Float?,
    val groundContactTime: Float?,
    val breaststrokeGlideTime: Int? = null,
    val freestyleAvgBreathAngle: Int? = null,
    val breaststrokeAvgBreathAngle: Int? = null,
    val breathingRate: Int? = null,
    val breaststrokeHeadAngle: Int? = null,
    val freestyleHeadAngle: Int? = null,
) : TimestampedDataPoint<SmlTimedStreamSamplePoint> {
    override fun toTimedDataPoint(
        time: Long,
        cumulativeDistance: Float
    ): SmlTimedStreamSamplePoint =
        SmlTimedStreamSamplePoint(
            time,
            cumulativeDistance,
            this.copy(cumulativeDistance = cumulativeDistance)
        )

    /**
     * Checks if the class only has a timestamp and nothing else
     */
    fun isSampleEmpty(): Boolean {
        return cumulativeDistance == null &&
            heartrate == null &&
            speed == null &&
            altitude == null &&
            temperature == null &&
            verticalSpeed == null &&
            power == null &&
            cadence == null &&
            latitude == null &&
            longitude == null &&
            suuntoPlusSample == null &&
            rawIbiSuuntoLogbookSample == null &&
            recoveryHeartrateInThreeMins == null &&
            verticalOscillation == null &&
            groundContactTime == null
    }

    /**
     * Checks if the class only has HR data but nothing else
     */
    fun isHeartRateOnly(): Boolean {
        return cumulativeDistance == null &&
            heartrate != null &&
            speed == null &&
            altitude == null &&
            temperature == null &&
            verticalSpeed == null &&
            power == null &&
            cadence == null &&
            latitude == null &&
            longitude == null &&
            suuntoPlusSample == null &&
            rawIbiSuuntoLogbookSample == null
    }
}

data class SmlTimedStreamSamplePoint(
    override val time: Long, // Relative time from start timestamp excluding pauses
    override val cumulativeDistance: Float, // Excluding pauses
    val samplePoint: SmlStreamSamplePoint
) : TimedDataPoint

/**
 * FOR DIVE WORKOUTS ONLY. Use [dataPointsWithoutPauses] for other workouts.
 *
 * Calculates the relative duration to [startTimestamp] excluding pauses.
 */
fun List<SmlExtensionStreamPoint>.toTimedStreamPoints(
    startTimestamp: Long? = firstOrNull()?.timestamp
): List<SmlTimedExtensionStreamPoint> {
    if (isEmpty() || startTimestamp == null) return emptyList()

    return filter { it.timestamp >= startTimestamp }
        // Cumulative distance not supported for dives. Set to 0f.
        .map { SmlTimedExtensionStreamPoint(it.timestamp - startTimestamp, 0f, it) }
}

/**
 * Filters out data points that are outside the recording state and calculates their relative
 * duration to start event excluding pauses.
 *
 * NOT supported with dive workouts. Dive workouts do not have recording events.
 * Use [toTimedStreamPoints] to convert dive data points to timed data points.
 *
 * TODO: This is called many times (even with the same timestampedDataPoints values). Could we
 * cache the result and/or somehow avoid duplicating the lists multiple times?
 */
fun <T, R> SmlStreamData.dataPointsWithoutPauses(
    timestampedDataPoints: List<T>,
    startTimestamp: Long? = null,
    startDistance: Float? = null
): List<R> where T : TimestampedDataPoint<R>, R : TimedDataPoint {
    val statusEvents = events.mapNotNull { it as? RecordingStatusEvent }
    val filtered = mutableListOf<R>()

    val isSorted = isSortedAsc(timestampedDataPoints)
    val dataPointIterator: ListIterator<T> = if (isSorted) {
        timestampedDataPoints
    } else {
        timestampedDataPoints.sortedBy { it.timestamp }
    }.listIterator()

    val eventIterator = statusEvents.iterator()

    var previousStatusEvent: RecordingStatusEvent? = null
    var cumulativeDistance = 0f

    val startEventTypes = listOf(
        RecordingStatusEventType.Start,
        RecordingStatusEventType.Resume
    )

    val stopEventTypes = listOf(
        RecordingStatusEventType.Stop,
        RecordingStatusEventType.Pause
    )

    // We check to have in order to apply our logic 2 things
    // 1. A start/resume event
    // 2. A pause/stop event
    if (statusEvents.none { it.type in startEventTypes } || statusEvents.none { it.type in stopEventTypes }) {
        Timber.w("Cannot filter points without pauses. Missing a start/resume or stop/pause event")
        val timestamp = startTimestamp ?: timestampedDataPoints.firstOrNull()?.timestamp ?: 0L
        return timestampedDataPoints.map {
            cumulativeDistance = it.cumulativeDistance?.run {
                this - (startDistance ?: 0f)
            } ?: cumulativeDistance
            it.toTimedDataPoint(it.timestamp - timestamp, cumulativeDistance)
        }
    }

    // Adjusting the times properly requires finding the event that starts / resumes the workout
    // for the dataPoints' time range. It can either be the last event before the time range if its
    // a resume event, and if not then the first resume event after time range starts
    val lastEventBeforePart = statusEvents.lastOrNull { it.timestamp <= (startTimestamp ?: 0) }
    val partStartingEvent =
        if (lastEventBeforePart != null && startEventTypes.contains(lastEventBeforePart.type)) {
            lastEventBeforePart
        } else {
            statusEvents.firstOrNull {
                it.timestamp >= (startTimestamp ?: 0) && startEventTypes.contains(it.type)
            }
        }
    val partStartEventDuration = partStartingEvent?.duration ?: 0L

    for (statusEvent in eventIterator) {
        val timeExcludingPauses = previousStatusEvent?.let {
            it.duration?.minus(partStartEventDuration)
        } ?: 0

        if (timeExcludingPauses < 0) {
            previousStatusEvent = statusEvent
            continue
        }

        while (dataPointIterator.hasNext()) {
            val dataPoint = dataPointIterator.next()

            if (dataPoint.timestamp <= statusEvent.data.timestamp) {
                @Suppress("ControlFlowWithEmptyBody")
                if (previousStatusEvent == null) {
                    // drop data before first start/resume
                    continue
                } else if (startEventTypes.contains(previousStatusEvent.type)) {
                    cumulativeDistance = dataPoint.cumulativeDistance?.run {
                        this - (startDistance ?: 0f)
                    } ?: cumulativeDistance
                    // Workout is being recorded. Calculate delta from the previous
                    // start/resume event and add current timestamp without pauses to it.
                    val delta =
                        if (startTimestamp == null || previousStatusEvent.timestamp > startTimestamp) {
                            dataPoint.timestamp - previousStatusEvent.timestamp
                        } else {
                            dataPoint.timestamp - startTimestamp
                        }

                    filtered.add(
                        dataPoint.toTimedDataPoint(
                            timeExcludingPauses + delta,
                            cumulativeDistance
                        )
                    )
                }
            } else {
                // Rewind so that we won't lose the data and carry on from the next status event
                dataPointIterator.previous()
                break
            }
        }

        previousStatusEvent = statusEvent
    }

    return filtered
}

fun SmlStreamData.addPauseTimeToMillisInWorkout(millisInWorkout: Long): Long {
    val lastEventBeforeTargetTime = events
        .filterIsInstance<RecordingStatusEvent>()
        .lastOrNull { it.data.duration != null && it.data.elapsed != null && it.data.duration!! <= millisInWorkout }

    return if (lastEventBeforeTargetTime != null) {
        millisInWorkout + (lastEventBeforeTargetTime.data.elapsed!! - lastEventBeforeTargetTime.data.duration!!)
    } else {
        millisInWorkout
    }
}

fun List<SmlStreamSamplePoint>.filterSamplePoint(
    multisportPartActivity: MultisportPartActivity?
): List<SmlStreamSamplePoint> = filterSamplePoint(
    multisportPartActivity?.startTime,
    multisportPartActivity?.stopTime
)

fun List<SmlStreamSamplePoint>.filterSamplePoint(
    startTime: Long?,
    stopTime: Long?
): List<SmlStreamSamplePoint> = if (startTime == null || stopTime == null) {
    this
} else {
    filter {
        it.timestamp in startTime..stopTime
    }
}

fun List<SmlExtensionStreamPoint>.filterStreamPoint(
    multisportPartActivity: MultisportPartActivity?
): List<SmlExtensionStreamPoint> = filterStreamPoint(
    multisportPartActivity?.startTime,
    multisportPartActivity?.stopTime
)

fun List<SmlExtensionStreamPoint>.filterStreamPoint(
    startTime: Long?,
    stopTime: Long?
): List<SmlExtensionStreamPoint> = if (startTime == null || stopTime == null) {
    this
} else {
    filter {
        it.timestamp >= startTime && it.timestamp <= stopTime
    }
}

fun List<SmlTimedExtensionValuePoint>.filterTimedValues(
    multisportPartActivity: MultisportPartActivity?
): List<SmlTimedExtensionValuePoint> =
    if (multisportPartActivity?.elapsed == null) {
        this
    } else {
        val elapsed = multisportPartActivity.elapsed
        val elapsedStop =
            (multisportPartActivity.stopTime - multisportPartActivity.startTime) + elapsed
        asSequence()
            .filter { it.time in elapsed..elapsedStop }
            .map { it.copy(time = it.time - elapsed) }
            .toList()
    }

// TODO: This function is very expensive to call on long workouts as the duplicates the stream point
// list when  converting to TimestampedDataPoint. There should be no need to iterate through all the
// samples just in order to check if any of them has cumulativeDistance value.
//
// This is called multiple times when opening workout details.
fun SmlStreamData.doStreamDataHaveDistance(
    streamPoints: List<SmlExtensionStreamPoint>,
    multisportPartActivity: MultisportPartActivity? = null
): Boolean = dataPointsWithoutPauses(
    streamPoints.filterStreamPoint(multisportPartActivity),
    startTimestamp = multisportPartActivity?.startTime
)
    .any { it.cumulativeDistance != 0f }

private fun <T> isSortedAsc(timestampedDataPoints: List<T>): Boolean where T : TimestampedDataPoint<*> {
    return timestampedDataPoints.asSequence().zipWithNext { a, b -> a.timestamp <= b.timestamp }
        .all { it }
}

fun List<SmlExtensionStreamPoint>.averageOrNull() =
    if (isEmpty()) null else map { it.value }.average()

private data class OngoingAvgCalculation(
    var accWeighedSum: Float = 0f,
    var accWeighs: Float = 0f,
    var min: Float = Float.MAX_VALUE,
    var max: Float = Float.MIN_VALUE
) {
    fun toAvgMinMax() = AvgMinMax(
        avg = if (accWeighs != 0f) (accWeighedSum / accWeighs).toDouble() else null,
        min = min.toDouble(),
        max = max.toDouble()
    )
}

/**
 * @return null if the list is empty, otherwise returns the integral mean value
 * https://en.wikipedia.org/wiki/Mean_value_theorem#Mean_value_theorems_for_definite_integrals
 */
fun List<SmlExtensionStreamPoint>.avgMinMaxOrNull(): AvgMinMax? {
    if (isEmpty()) return null
    if (size == 1) {
        val value = this[0].value.toDouble()
        return AvgMinMax(value, value, value)
    }
    return windowed(2).fold(OngoingAvgCalculation()) { calc, (p1, p2) ->
        calc.min = minOf(calc.min, p1.value, p2.value)
        calc.max = maxOf(calc.min, p1.value, p2.value)
        val dt = p2.timestamp - p1.timestamp
        calc.accWeighs += dt
        calc.accWeighedSum += dt * (p2.value + p1.value) / 2
        calc
    }.toAvgMinMax()
}

/**
 * This method mutates the list to add missing samples if the stream has consecutive samples
 * with difference in time greater or equal than 2 * samplingMillis.
 * This is useful for SuuntoPlus samples values, as the SML contains samples data only when the
 * value actually changes.
 */
fun MutableList<SmlExtensionStreamPoint>.fillConstantWithSampling(
    samplingMillis: Long,
    untilTimestampMillis: Long
): MutableList<SmlExtensionStreamPoint> {
    require(samplingMillis > 0) { "samplingMillis should be greater than 0" }
    sortBy { it.timestamp }
    listIterator().run {
        var lastPoint: SmlExtensionStreamPoint? = null
        // fill intermediate missing samples
        while (hasNext()) {
            val point = next()
            if (lastPoint != null && point.timestamp - lastPoint.timestamp >= 2 * samplingMillis) {
                // rewind and insert same value at samplingMillis after lastPoint
                previous()
                lastPoint = lastPoint.copy(
                    timestamp = lastPoint.timestamp + samplingMillis,
                    cumulativeDistance = null
                )
                add(lastPoint)
            } else {
                lastPoint = point
            }
        }
        // fill missing sample before the last Pause event
        while (lastPoint != null && untilTimestampMillis - lastPoint.timestamp >= 2 * samplingMillis) {
            lastPoint = lastPoint.copy(
                timestamp = lastPoint.timestamp + samplingMillis,
                cumulativeDistance = null
            )
            add(lastPoint)
        }
    }
    return this
}
