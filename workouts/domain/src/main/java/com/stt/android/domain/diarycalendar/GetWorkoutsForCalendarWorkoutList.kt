package com.stt.android.domain.diarycalendar

import com.stt.android.domain.user.workoutextension.SlopeSkiSummary
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.domain.workouts.ActivityGroupMapper
import com.stt.android.domain.workouts.GetWorkoutHeadersForDateUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.DiveExtensionDataSource
import com.stt.android.domain.workouts.extensions.SlopeSkiExtensionDataSource
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.extensions.SummaryExtensionDataSource
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import javax.inject.Inject

class GetWorkoutsForCalendarWorkoutList
@Inject
constructor(
    private val getWorkoutHeadersForDateUseCase: GetWorkoutHeadersForDateUseCase,
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val diveExtensionDataSource: DiveExtensionDataSource,
    private val slopeSkiExtensionDataSource: SlopeSkiExtensionDataSource,
    private val summaryExtensionDataSource: SummaryExtensionDataSource,
    private val mapper: ActivityGroupMapper
) {
    suspend operator fun invoke(params: Params): List<WorkoutWithExtensions> {
        val workouts: List<WorkoutHeader> = if (params.date != null) {
            getWorkoutHeadersForDateUseCase(
                GetWorkoutHeadersForDateUseCase.Params(
                    params.username,
                    params.date,
                    params.zoneId
                )
            )
        } else {
            getWorkoutsById(params.workoutHeaderIds)
        }

        return workouts.map { workoutHeader ->
            var diveExtension: DiveExtension? = null
            var slopeSkiSummary: SlopeSkiSummary? = null
            var summaryExtension: SummaryExtension? = null
            if (mapper.activityTypeIdToGroup(workoutHeader.activityTypeId) == ActivityGroup.Diving) {
                workoutHeader.id.let {
                    diveExtension = diveExtensionDataSource.findById(it)
                }
            } else if (mapper.activityTypeIdToGroup(workoutHeader.activityTypeId) == ActivityGroup.WinterSports) {
                workoutHeader.id.let {
                    slopeSkiSummary = slopeSkiExtensionDataSource.findByWorkoutId(it)
                }
            } else if (workoutHeader.activityType.isIndoor && workoutHeader.activityType.isSwimming) {
                summaryExtension = summaryExtensionDataSource.findByWorkoutId(workoutHeader.id)
            }

            WorkoutWithExtensions(
                workout = workoutHeader,
                diveExtension = diveExtension,
                slopeSkiSummary = slopeSkiSummary,
                summaryExtension = summaryExtension
            )
        }
    }

    private suspend fun getWorkoutsById(workoutHeaderIds: List<Int>): List<WorkoutHeader> =
        workoutHeaderDataSource.findByIds(workoutHeaderIds)

    class Params(
        val workoutHeaderIds: List<Int> = emptyList(),
        val username: String,
        val date: LocalDate? = null,
        val zoneId: ZoneId = ZoneOffset.systemDefault()
    )
}
