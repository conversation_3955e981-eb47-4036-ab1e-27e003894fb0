package com.stt.android.remote.workout

import com.squareup.moshi.JsonDataException
import com.squareup.moshi.JsonEncodingException
import com.squareup.moshi.Moshi
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.exceptions.remote.HttpException
import com.stt.android.moshi.toJson
import com.stt.android.remote.MediaTypes
import com.stt.android.remote.otp.GenerateOTPUseCase
import com.stt.android.remote.response.AskoEmptyPayloadException
import com.stt.android.remote.response.AskoResponse
import com.stt.android.utils.onEachSuccess
import com.stt.android.utils.runSuspendCatchingEach
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import javax.inject.Inject

const val DEFAULT_PARTIAL_FETCH_AMOUNT = 50
const val DEFAULT_TOTAL_AMOUNT = 20_000
const val DEFAULT_FOLLOWEES_WORKOUTS_LIMIT = 50
const val DEFAULT_SINCE_TIMESTAMP = 0L
internal const val METADATA_UNTIL = "until"

/**
 * Class responsible of making remote calls to Workout API Service
 */
class WorkoutRemoteApi
@Inject constructor(
    private val workoutRestApi: WorkoutRestApi,
    private val workoutRestV2Api: WorkoutRestV2Api,
    private val moshi: Moshi,
    private val generateOTPUseCase: GenerateOTPUseCase,
) {
    /**
     * Saves workout on server
     *
     * @param remoteUnsyncedWorkout [RemoteUnsyncedWorkout] to save it on server side
     * @return [RemoteSyncedWorkout]
     */
    suspend fun saveWorkout(remoteUnsyncedWorkout: RemoteUnsyncedWorkout): RemoteSyncedWorkout =
        workoutRestApi.saveWorkout(
            remoteUnsyncedWorkout.workoutFile?.let {
                MultipartBody.Part.createFormData(
                    "workoutBinary",
                    "binary",
                    it.asRequestBody(MediaTypes.OCTET_STREAM_CONTENT_TYPE)
                )
            },
            MultipartBody.Part.createFormData(
                "workoutExtensions",
                "extension",
                remoteUnsyncedWorkout.remoteWorkoutExtensions.toJson(moshi).toRequestBody(
                    MediaTypes.JSON_TYPE
                )
            ),
            remoteUnsyncedWorkout.smlZipFile?.let {
                MultipartBody.Part.createFormData(
                    "sml",
                    "sml.zip",
                    it.asRequestBody(MediaTypes.ZIP_TYPE)
                )
            },
        ).payloadOrThrow()

    suspend fun getWorkout(workoutKey: String): RemoteSyncedWorkout =
        workoutRestApi.getWorkout(workoutKey).payloadOrThrow()

    suspend fun getCombinedWorkout(
        username: String,
        workoutKey: String,
        extensions: String?,
        additionalData: String?
    ): RemoteCombinedWorkout =
        workoutRestV2Api
            .getCombinedWorkout(
                username,
                workoutKey,
                extensions,
                additionalData
            )
            .payloadOrThrow()

    /**
     * Deletes a workout
     * @param key the workout key to delete
     */
    @Throws(HttpException::class, AskoEmptyPayloadException::class)
    suspend fun deleteWorkout(key: String): Boolean =
        workoutRestApi.deleteWorkout(key).payloadOrThrow()

    /**
     * Deletes workout attributes
     * @param workoutKey Identifies the workout
     * @param fields Comma-separated list of fields identifying the attributes to be deleted.
     * @return Updated workout
     */
    suspend fun deleteWorkoutAttributes(
        workoutKey: String,
        fields: String
    ): RemoteSyncedWorkout = workoutRestApi.deleteWorkoutAttributes(workoutKey, fields)
        .payloadOrThrow()

    /**
     * Updates a workouts
     * @param workouts List of workouts to update
     * @param shareToFB should these workouts be shared on facebook
     */
    suspend fun updateWorkouts(
        workouts: List<RemoteUpdatedWorkout>,
        shareToFB: Boolean = false
    ): Map<String, Boolean> =
        // TODO migrate to workout attributes update
        workoutRestApi.updateWorkouts(shareToFB, workouts).payloadOrThrow()

    /**
     * Updates workout attributes. Eventually this will replace [updateWorkouts], but for now
     * only startPosition attribute is supported by the backend.
     * @param workoutKey Identifies the workout
     * @param attributes Attributes with updated values
     * @return Updated workout
     */
    suspend fun updateWorkoutAttributes(
        workoutKey: String,
        attributes: RemoteWorkoutAttributes
    ): RemoteSyncedWorkout = workoutRestApi.updateWorkoutAttributes(workoutKey, attributes)
        .payloadOrThrow()

    /**
     * Fetches own workouts
     *
     * This function will trigger multiple requests depending on its input.
     *
     * @param limit the amount of workouts that should be returned per one request
     *              defaults to [DEFAULT_PARTIAL_FETCH_AMOUNT]
     * @param max the maximum amount of workouts that should be fetched
     *            defaults to [DEFAULT_TOTAL_AMOUNT]
     * @param since Epoch ms UTC timestamp from which to fetch from the server. Default to zero.
     *
     * @return A A Flow of pairs where the first represents the server last modified timestamp
     *          and the second is a list of workouts. The Flow emits each backend request's
     *          results separately
     */
    fun fetchOwnWorkoutsPaged(
        limit: Int = DEFAULT_PARTIAL_FETCH_AMOUNT,
        max: Int = DEFAULT_TOTAL_AMOUNT,
        since: Long = DEFAULT_SINCE_TIMESTAMP
    ): Flow<Pair<Long, List<RemoteSyncedWorkout>>> =
        fetchWorkoutsPaged(limit, max, since, workoutRestApi::fetchOwnWorkouts)

    /**
     * Fetches public workouts in a given area
     *
     * @param lowerlat The lower latitude
     * @param lowerlng The lower longitude
     * @param upperlat The upper latitude
     * @param upperlng The upper longitude
     * @param limit the amount of workouts that should be returned per one request
     *              defaults to [DEFAULT_PARTIAL_FETCH_AMOUNT]
     *
     * @return A list of pairs of user and he's associated workout
     */
    suspend fun fetchPublicWorkouts(
        lowerlat: Double,
        lowerlng: Double,
        upperlat: Double,
        upperlng: Double,
        limit: Int = DEFAULT_PARTIAL_FETCH_AMOUNT
    ): List<RemotePublicWorkout> =
        workoutRestApi.fetchPublicWorkouts(lowerlat, lowerlng, upperlat, upperlng, limit)
            .payloadOrThrow()

    /**
     * Fetches followees workouts
     *
     * This function will trigger multiple requests depending on its input.
     *
     * @param limit the amount of workouts that should be returned per one request
     *              defaults to [DEFAULT_PARTIAL_FETCH_AMOUNT]
     * @param max the maximum amount of workouts that should be fetched
     *            defaults to [DEFAULT_FOLLOWEES_WORKOUTS_LIMIT]
     * @param since Epoch ms UTC timestamp from which to fetch from the server. Default to zero.
     *
     * @return A Flow of pairs where the first represents the server last modified timestamp
     *          and the second is a list of workouts. The Flow emits each backend request's
     *          results separately
     */
    fun fetchFolloweesWorkoutsPaged(
        limit: Int = DEFAULT_PARTIAL_FETCH_AMOUNT,
        max: Int = DEFAULT_FOLLOWEES_WORKOUTS_LIMIT,
        since: Long = DEFAULT_SINCE_TIMESTAMP
    ): Flow<Pair<Long, List<RemoteSyncedWorkout>>> =
        fetchWorkoutsPaged(limit, max, since, workoutRestApi::fetchFolloweesWorkouts)

    private fun fetchWorkoutsPaged(
        limit: Int = DEFAULT_PARTIAL_FETCH_AMOUNT,
        max: Int = DEFAULT_TOTAL_AMOUNT,
        since: Long = DEFAULT_SINCE_TIMESTAMP,
        fetchWorkoutRestApi: suspend (
            since: Long,
            limit: Int,
            offset: Int
        ) -> AskoResponse<List<RemoteSyncedWorkout>>
    ): Flow<Pair<Long, List<RemoteSyncedWorkout>>> = callbackFlow {
        Timber.d("Fetching workouts since $since")
        var untilTimestamp = since

        fun updateUntilTimestampFromResponse(response: AskoResponse<*>) {
            untilTimestamp = try {
                response.metadata?.get(METADATA_UNTIL)?.toLong() ?: untilTimestamp
            } catch (e: NumberFormatException) {
                // Something went wrong with the response, fallback to the last untilTimestamp
                untilTimestamp
            }
        }

        var numFetchedWorkouts = 0
        var offset = 0
        var payloadSize = 0
        do {
            Timber.v("Fetching workouts from %d onwards", offset)
            runSuspendCatching {
                fetchWorkoutRestApi(since, limit, offset)
            }.onSuccess { response ->
                updateUntilTimestampFromResponse(response)
                response.payload?.also {
                    numFetchedWorkouts += it.size
                    payloadSize = it.size
                    offset += it.size
                    channel.send(untilTimestamp to it)
                } ?: run {
                    // no more workouts from backend
                    payloadSize = 0
                }
            }.onFailure { e ->
                if (e is JsonDataException || e is JsonEncodingException) {
                    Timber.w(e, "Error parsing workouts from backend")
                    // parsing error happened, we try fetching 1-by-1
                    (0 until limit).runSuspendCatchingEach { i ->
                        fetchWorkoutRestApi(since, 1, offset + i)
                    }.onEachSuccess { response ->
                        updateUntilTimestampFromResponse(response)
                        response.payload?.also {
                            numFetchedWorkouts += it.size
                            channel.send(untilTimestamp to it)
                        }
                    }
                    // offsetting manually because this batch could not be parsed completely
                    payloadSize = limit
                    offset += limit
                } else {
                    // any other cause of error is not worth retrying, prob backend is overloaded
                    channel.close(e)
                }
            }
        } while (payloadSize == limit && numFetchedWorkouts < max && !channel.isClosedForSend)

        channel.close()
    }

    @Throws(HttpException::class, AskoEmptyPayloadException::class)
    suspend fun fetchWorkoutStatsForUser(username: String): RemoteWorkoutStats =
        workoutRestApi.fetchWorkoutStatsForUser(username).payloadOrThrow()

    @Throws(HttpException::class, AskoEmptyPayloadException::class)
    suspend fun fetchCompetitionWorkoutResult(
        username: String,
        workoutKey: String
    ): RemoteCompetitionWorkoutDetails =
        workoutRestApi.fetchCompetitionWorkoutResult(
            generateOTPUseCase.generateTOTP(),
            username,
            workoutKey
        ).payloadOrThrow()

    suspend fun fetchWorkoutByUsername(
        username: String,
        offset: Int,
        limit: Int,
        withMediaOnly: Boolean,
    ): List<RemoteSyncedWorkout> {
        return workoutRestApi.fetchWorkoutsByUsername(username, offset, limit, withMediaOnly).payloadOrThrow()
    }

    suspend fun searchWorkouts(
        username: String,
        activityIds: List<Int>,
        suuntoTags: List<RemoteSuuntoTag>,
        query: String,
        offset: Int,
        limit: Int,
    ): List<RemoteSyncedWorkout> {
        return workoutRestV2Api.searchWorkouts(
            username = username,
            activityIds = activityIds.takeIf { it.any() }?.joinToString(
                separator = ",",
                prefix = "",
                postfix = "",
            ),
            suuntoTags = suuntoTags.takeIf { it.any() }?.joinToString(
                separator = ",",
                prefix = "",
                postfix = "",
            ),
            searchKeywords = query,
            offset = offset,
            limit = limit,
        ).payloadOrThrow()
    }
}
