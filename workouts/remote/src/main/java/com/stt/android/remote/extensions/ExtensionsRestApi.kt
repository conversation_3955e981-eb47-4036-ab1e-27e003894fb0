package com.stt.android.remote.extensions

import com.stt.android.remote.response.AskoResponse
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Path

const val EXTENSIONS_ENDPOINT = "workout/extensions"

interface ExtensionsRestApi {
    @POST("$EXTENSIONS_ENDPOINT/{workoutKey}")
    @Headers("Content-Type: application/json")
    suspend fun fetchExtensions(
        @Path("workoutKey") workoutKey: String,
        @Body extensionTypes: List<String>
    ): AskoResponse<ExtensionListResponse>
}
