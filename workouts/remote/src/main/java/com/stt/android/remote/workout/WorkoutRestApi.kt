package com.stt.android.remote.workout

import com.stt.android.remote.BaseRemoteApi
import com.stt.android.remote.response.AskoResponse
import okhttp3.MultipartBody
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * RestAPI to access Workout Remote Service
 */
interface WorkoutRestApi {

    @Multipart
    @POST("workout")
    suspend fun saveWorkout(
        @Part filePart: MultipartBody.Part?,
        @Part workoutExtensionsPart: MultipartBody.Part,
        @Part smlZip: MultipartBody.Part?,
    ): AskoResponse<RemoteSyncedWorkout>

    @GET("workouts/{key}")
    suspend fun getWorkout(@Path("key") workoutKey: String): AskoResponse<RemoteSyncedWorkout>

    @DELETE("workouts/{key}/delete")
    suspend fun deleteWorkout(@Path("key") workoutKey: String): AskoResponse<Boolean>

    @Deprecated("This is marked as deprecated on the backend. Replaced by PUT workouts/{workoutIdOrKey}/attributes")
    @POST("workouts/header")
    suspend fun updateWorkouts(
        @Query("shareToFB") shareToFB: Boolean,
        @Body workouts: List<RemoteUpdatedWorkout>
    ): AskoResponse<Map<String, Boolean>>

    @GET("workouts")
    suspend fun fetchOwnWorkouts(
        @Query("since") since: Long,
        @Query("limit") limit: Int,
        @Query("offset") offset: Int
    ): AskoResponse<List<RemoteSyncedWorkout>>

    @GET("workouts/public/within")
    suspend fun fetchPublicWorkouts(
        @Query("lowerlat") lowerlat: Double,
        @Query("lowerlng") lowerlng: Double,
        @Query("upperlat") upperlat: Double,
        @Query("upperlng") upperlng: Double,
        @Query("limit") limit: Int
    ): AskoResponse<List<RemotePublicWorkout>>

    @GET("workouts/friends/latest")
    suspend fun fetchFolloweesWorkouts(
        @Query("since") since: Long,
        @Query("limit") limit: Int,
        @Query("offset") offset: Int
    ): AskoResponse<List<RemoteSyncedWorkout>>

    @PUT("workouts/{key}/attributes")
    suspend fun updateWorkoutAttributes(
        @Path("key") workoutKey: String,
        @Body attributes: RemoteWorkoutAttributes
    ): AskoResponse<RemoteSyncedWorkout>

    @DELETE("workouts/{key}/attributes")
    suspend fun deleteWorkoutAttributes(
        @Path("key") workoutKey: String,
        @Query("fields") attributes: String
    ): AskoResponse<RemoteSyncedWorkout>

    @GET("workouts/{username}/stats")
    suspend fun fetchWorkoutStatsForUser(@Path("username") username: String): AskoResponse<RemoteWorkoutStats>

    @GET("competition/{username}/{key}/result")
    suspend fun fetchCompetitionWorkoutResult(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
        @Path("username") username: String,
        @Path("key") workoutKey: String
    ): AskoResponse<RemoteCompetitionWorkoutDetails>

    @GET("workouts/{username}/public")
    suspend fun fetchWorkoutsByUsername(
        @Path("username") username: String,
        @Query("offset") offset: Int,
        @Query("limit") limit: Int,
        @Query("includeMedia") withMediaOnly: Boolean,
    ): AskoResponse<List<RemoteSyncedWorkout>>
}
