<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="titleText"
            type="java.lang.String" />
    </data>

    <TextView
        android:id="@+id/signup_title_text"
        style="@style/Body.Larger.Bold"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/size_spacing_large"
        android:paddingEnd="@dimen/size_spacing_large"
        android:gravity="center"
        android:text="@{titleText}"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/sign_up" />
</layout>
