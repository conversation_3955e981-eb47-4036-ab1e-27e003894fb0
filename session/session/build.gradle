plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.epoxy"
    id "stt.android.plugin.moshi"
    id "stt.android.plugin.compose"
    alias libs.plugins.androidx.navigation.safeargs
}

android {
    namespace 'com.stt.android.session'
    buildFeatures {
        dataBinding true
        buildConfig = true
    }
}

dependencies {
    implementation project(Deps.appBase)
    implementation project(Deps.domain)
    implementation project(Deps.userDomain)
    implementation project(Deps.datasource)
    implementation project(Deps.userDataSource)
    implementation project(Deps.sessionDomain)
    implementation project(Deps.sessionDataSource)
    implementation project(Deps.analytics)
    implementation project(Deps.composeUi)
    implementation project(Deps.remoteBase)
    implementation project(Deps.remoteConfigApi)
    implementation project(Deps.utils)

    suuntoImplementation project(Deps.connectivity)

    implementation libs.okhttp
    implementation libs.retrofit

    implementation libs.phone.number
    implementation libs.play.auth
    implementation libs.androidx.work
    implementation libs.facebook.sdk
    implementation libs.helpshift
    implementation libs.better.link.movement.method
    implementation libs.mapbox.search
    implementation libs.firebase.crashlytics
    implementation libs.androidx.browser
}
