package com.stt.android.sportmode.widget.fragment

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.stringResource
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.sportmode.modesetting.sportStr
import com.stt.android.ui.utils.ExpandedBottomSheetDialogFragment
import com.stt.android.utils.argument

class DurationDistanceWheelFragment : ExpandedBottomSheetDialogFragment() {

    internal var dataCallback: ((data: DurationDistanceWheelFragmentData) -> Unit)? = null

    private var argData: DurationDistanceWheelFragmentData by argument()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )
        setContentWithTheme {
            DurationDistanceWheelContent(
                title = stringResource(sportStr.reminder_remind_in),
                data = argData,
                onDoneClick = { distanceSelected, indices ->
                    if (distanceSelected) {
                        if (!argData.distanceDisableDoneStrategy.isValidIndices(indices)) return@DurationDistanceWheelContent
                        argData = argData.copy(
                            distanceChecked = true,
                            distanceColumns = argData.distanceColumns.mapIndexed { index, column ->
                                column.copy(
                                    defaultIndex = indices[index]
                                )
                            }
                        )
                    } else {
                        if (!argData.durationDisableDoneStrategy.isValidIndices(indices)) return@DurationDistanceWheelContent
                        argData = argData.copy(
                            distanceChecked = false,
                            durationColumns = argData.durationColumns.mapIndexed { index, column ->
                                column.copy(
                                    defaultIndex = indices[index]
                                )
                            }
                        )
                    }
                    dismissAllowingStateLoss()
                },
                onDragging = { dragging ->
                    setBottomSheetDraggable(!dragging)
                },
            )
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        dataCallback?.invoke(argData)
        dataCallback = null
    }

    companion object {
        fun create(
            data: DurationDistanceWheelFragmentData,
            callback: (data: DurationDistanceWheelFragmentData) -> Unit
        ) = DurationDistanceWheelFragment().apply {
            argData = data
            dataCallback = callback
        }
    }
}
