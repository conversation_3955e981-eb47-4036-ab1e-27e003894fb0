package com.stt.android.sportmode.trainingmode.reducer

import android.content.Context
import com.stt.android.sportmode.datascreen.DataScreenEditActivity
import com.stt.android.sportmode.trainingmode.TrainingMode
import com.stt.android.sportmode.trainingmode.TrainingModeReducer
import com.stt.android.utils.activityresult.ResultLauncherActivity
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class EditDataScreensReducer @Inject constructor(
    private val context: Context
) : TrainingModeReducer {
    override suspend fun invoke(trainingMode: TrainingMode): TrainingMode {
        if (context !is ResultLauncherActivity) return trainingMode
        val contract = DataScreenEditActivity.ResultContract()
        val updated = suspendCoroutine { continuation ->
            context.startActivityForResult(
                contract.createIntent(
                    context,
                    trainingMode.sportId to trainingMode.dataScreenList
                )
            ) { resultCode, data ->
                continuation.resume(contract.parseResult(resultCode, data))
            }
        }
        return trainingMode.copy(
            dataScreenList = updated
        )
    }
}
