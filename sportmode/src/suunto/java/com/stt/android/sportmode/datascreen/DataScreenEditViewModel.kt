package com.stt.android.sportmode.datascreen

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.DataOptionMapping
import com.stt.android.sportmode.datascreen.DataScreenEditActivity.Companion.EXTRA_DATA_SCREENS
import com.stt.android.sportmode.datascreen.DataScreenEditActivity.Companion.EXTRA_SPORT_ID
import com.stt.android.sportmode.datascreen.options.EditDataOptions
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class DataScreenEditViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val measurementUnit: MeasurementUnit,
) : ViewModel() {

    private var initialDataScreenList: DataScreenList = DataScreenList()

    private val _dataScreensFlow by lazy {
        MutableStateFlow(initialDataScreenList)
    }

    internal val dataScreensFlow by lazy {
        _dataScreensFlow.asStateFlow()
    }

    private val _dataScreenEditEvent = MutableSharedFlow<DataScreenEditEvent>(
        replay = 0,
        extraBufferCapacity = 1,
    )
    internal val dataScreenEditEvent = _dataScreenEditEvent.asSharedFlow()

    private var activityId = ActivityType.RUNNING.id
    private var selectedFieldsAvailable = false
    private var currentChangeIndex = -1
    private var currentChangeData: DataScreenComponent? = null

    var currentEditDataOptions: EditDataOptions? = null
        private set

    init {
        val dataScreenList = savedStateHandle.get<DataScreenList>(EXTRA_DATA_SCREENS)
            ?: run {
                Timber.w("no data screen list in intent")
                sendEvent(DataScreenEditEvent.Exit)
                DataScreenList()
            }
        activityId = savedStateHandle.get<Int>(EXTRA_SPORT_ID)
            ?: run {
                Timber.w("no sport id in intent")
                sendEvent(DataScreenEditEvent.Exit)
                -1
            }
        initialDataScreenList = dataScreenList.copy(
            dataScreens = dataScreenList.dataScreens.map {
                it.copy(editing = true)
            }
        )
    }

    fun checkLaunchedRoute() {
        if (initialDataScreenList.selectedDataScreen.items.none()) {
            sendEvent(DataScreenEditEvent.EditFields)
        } else {
            selectedFieldsAvailable = true
        }
    }

    fun onFieldsUpdated(fields: Int) {
        if (fields > 0) {
            selectedFieldsAvailable = true
        }
        reduce {
            reduceByFields(fields)
        }
        sendEvent(DataScreenEditEvent.ExitEditingFields)
    }

    fun cancelEditFields() {
        if (!selectedFieldsAvailable) {
            sendEvent(DataScreenEditEvent.Exit)
        } else {
            sendEvent(DataScreenEditEvent.ExitEditingFields)
        }
    }

    private fun DataScreenList.reduceByFields(fields: Int): DataScreenList {
        if (selectedDataScreen.items.size == fields) return this
        val updated = copy(
            dataScreens = dataScreens.map { watchData ->
                if (watchData === selectedDataScreen) {
                    val currentSize = watchData.items.size
                    watchData.copy(
                        items = if (fields < currentSize) {
                            watchData.items.subList(0, fields)
                        } else if (fields > currentSize) {
                            watchData.items + (1..fields - currentSize).map {
                                watchData.items.lastOrNull()
                                    ?: DataScreenComponent.fromMapping(DataOptionMapping.BATTERY)
                            }
                        } else {
                            watchData.items
                        }
                    )
                } else {
                    watchData
                }
            }
        )
        return if (updated.dataScreens.none { it.items.none() } && updated.dataScreens.size < MAX_DATA_SCREEN_SIZE) {
            updated.copy(
                dataScreens = updated.dataScreens + DataScreen(editing = true)
            )
        } else {
            updated
        }
    }

    fun updateSelectedIndex(index: Int) {
        viewModelScope.launch {
            reduce {
                copy(selectedIndex = index)
            }
        }
    }

    fun changeWatchDataItem(changeIndex: Int, data: DataScreenComponent) {
        currentChangeIndex = changeIndex
        currentChangeData = data
        currentEditDataOptions = EditDataOptions(
            activityId = activityId,
            dataOptionId = data.id,
        ).also {
            sendEvent(DataScreenEditEvent.EditOptions(it))
        }
    }

    fun onDataOptionsUpdated(updatedDataOptions: EditDataOptions) {
        val updatedWatchDataItem =
            updatedDataOptions.mapComponent(isImperial = measurementUnit == MeasurementUnit.IMPERIAL)
        reduce {
            copy(
                dataScreens = dataScreens.map { watchData ->
                    if (watchData === selectedDataScreen) {
                        watchData.copy(
                            items = watchData.items.mapIndexed { index, watchDataItem ->
                                if (index == currentChangeIndex) {
                                    updatedWatchDataItem
                                } else {
                                    watchDataItem
                                }
                            }
                        )
                    } else {
                        watchData
                    }
                }
            )
        }
        sendEvent(DataScreenEditEvent.ExitEditingOptions)
    }

    fun delete() {
        val size = _dataScreensFlow.value.dataScreens.size
        reduce {
            val updatedDataScreens = (dataScreens - selectedDataScreen).toMutableList()
            if (updatedDataScreens.none { it.items.none() }) {
                updatedDataScreens += DataScreen(editing = true)
            }
            copy(
                dataScreens = updatedDataScreens.toList(),
                selectedIndex = selectedIndex.coerceAtMost(size - 2)
            )
        }
    }

    fun generateDataScreenListToSave(dataScreenList: DataScreenList): DataScreenList {
        // make editing false
        var toSave = dataScreenList.copy(
            dataScreens = dataScreenList.dataScreens.map {
                it.copy(
                    editing = false
                )
            }
        )
        // new data screen created
        if (toSave.dataScreens.find { it.items.none() } == null && toSave.dataScreens.size < MAX_DATA_SCREEN_SIZE) {
            toSave = toSave.copy(
                dataScreens = toSave.dataScreens + DataScreen(editing = false)
            )
        }
        return toSave
    }

    private fun reduce(reduce: DataScreenList.() -> DataScreenList) {
        _dataScreensFlow.value = _dataScreensFlow.value.reduce()
    }
    
    private fun sendEvent(event: DataScreenEditEvent) {
        _dataScreenEditEvent.tryEmit(event)
    }
}
