package com.stt.android.sportmode.datasource.mapper

import com.stt.android.sportmode.datascreen.DataScreenList
import com.stt.android.sportmode.modesetting.Mapper
import com.suunto.connectivity.runsportmodes.GetDataScreenResponse
import com.suunto.connectivity.runsportmodes.GetDataScreenResponse.Companion.DATA_OPTION_SEPARATOR
import com.suunto.connectivity.runsportmodes.GetDataScreenResponse.Companion.GROUP_SEPARATOR
import javax.inject.Inject

class DataScreenOutMapper @Inject constructor() : Mapper<DataScreenList, GetDataScreenResponse> {
    override suspend fun invoke(dataScreenList: DataScreenList): GetDataScreenResponse {
        val dsData = dataScreenList.dataScreens.filter { it.items.any() }
            .joinToString(separator = GROUP_SEPARATOR) { dataScreen ->
                dataScreen.items.joinToString(separator = DATA_OPTION_SEPARATOR) { it.id }
            }
        return GetDataScreenResponse(
            dsData = dsData
        )
    }
}
