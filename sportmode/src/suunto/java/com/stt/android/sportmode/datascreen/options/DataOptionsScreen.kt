package com.stt.android.sportmode.datascreen.options

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.nearBlack
import com.stt.android.sportmode.composables.CheckSaveChangesDialog
import com.stt.android.sportmode.modesetting.baseColor
import com.stt.android.sportmode.modesetting.sportStr
import java.util.Locale

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun DataOptionsScreen(
    dataOptions: EditDataOptions?,
    onSaveClick: (dataOptions: EditDataOptions) -> Unit,
    modifier: Modifier = Modifier,
) {
    dataOptions ?: return

    var showDialog by remember { mutableStateOf(false) }

    val viewModel: DataOptionsViewModel = hiltViewModel<DataOptionsViewModel>().apply {
        initial(dataOptions)
    }

    fun checkedOnBackClick() =
        if (viewModel.dataOptionsFlow.value.dataOptionId != viewModel.initialDataOptions.dataOptionId) {
            showDialog = true
        } else {
            onSaveClick(viewModel.dataOptionsFlow.value)
        }

    BackHandler {
        checkedOnBackClick()
    }

    val listState by viewModel.dataOptionsDisplayDataFlow.collectAsStateWithLifecycle(
        initialValue = DataOptionsDisplayData(
            headerList = emptyList(),
            contentList = emptyList()
        )
    )
    Scaffold(
        topBar = {
            DataOptionsTopBar(
                {
                    onSaveClick(viewModel.dataOptionsFlow.value)
                },
                {
                    checkedOnBackClick()
                }
            )
        },
        modifier = modifier
    ) {
        DataOptionsContent(
            dataOptionsDisplayData = listState,
            onHeaderItemClick = {
                viewModel.focusHeaderIndex(it)
            },
            onContentItemClick = {
                viewModel.checkContentIndex(it)
            },
            modifier = Modifier
                .narrowContent()
                .fillMaxHeight()
                .background(MaterialTheme.colors.surface)
        )

        if (showDialog) {
            CheckSaveChangesDialog(
                onSaveRequest = {
                    showDialog = false
                    onSaveClick(viewModel.dataOptionsFlow.value)
                },
                onNotSaveRequest = {
                    showDialog = false
                    onSaveClick(viewModel.initialDataOptions)
                },
                onDismissRequest = {
                    showDialog = false
                },
            )
        }
    }
}

@Composable
fun DataOptionsTopBar(
    onSaveClick: () -> Unit,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = {
            Text(
                text = stringResource(sportStr.data_options).uppercase(),
                color = MaterialTheme.colors.onSecondary
            )
        },
        modifier = modifier,
        navigationIcon = {
            SuuntoIconButton(
                icon = SuuntoIcons.ActionClose,
                onClick = onBackClick,
                contentDescription = stringResource(R.string.back),
                tint = MaterialTheme.colors.onSecondary,
            )
        },
        backgroundColor = MaterialTheme.colors.nearBlack,
        actions = {
            TextButton(onClick = {
                onSaveClick()
            }) {
                Text(
                    text = stringResource(R.string.save).uppercase(Locale.getDefault()),
                    color = colorResource(baseColor.suunto_blue)
                )
            }
        },
    )
}

@Preview
@Composable
private fun DataOptionsTopBarPreview() {
    DataOptionsTopBar({}, {})
}
