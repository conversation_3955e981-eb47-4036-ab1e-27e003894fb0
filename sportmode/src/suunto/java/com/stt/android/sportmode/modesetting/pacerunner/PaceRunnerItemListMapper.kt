package com.stt.android.sportmode.modesetting.pacerunner

import android.content.Context
import com.stt.android.sportmode.modesetting.ModeSettingItemList
import com.stt.android.sportmode.modesetting.ModeSettingItemListMapper
import com.stt.android.sportmode.modesetting.ModeSettingReducer
import com.stt.android.sportmode.modesetting.list.RadioButtonFullItem
import com.stt.android.sportmode.modesetting.pacerunner.reducer.PaceRunnerSwitchModeReducer
import com.stt.android.sportmode.modesetting.resString
import com.stt.android.sportmode.modesetting.sportStr
import javax.inject.Inject

interface PaceRunnerItemListMapper : ModeSettingItemListMapper<PaceRunner>

class PaceRunnerItemListMapperImpl @Inject constructor(
    private val context: Context,
) : PaceRunnerItemListMapper {
    override var onReducer: (ModeSettingReducer<PaceRunner>) -> Unit = {}
    override suspend fun invoke(paceRunner: PaceRunner): ModeSettingItemList {
        return ModeSettingItemList(
            title = sportStr.pace_mode.resString(context),
            itemList = PaceMode.entries.mapIndexed { index, mode ->
                RadioButtonFullItem(
                    index = index,
                    title = mode.titleRes.resString(context),
                    tips = mode.tipsRes.resString(context),
                    checked = mode == paceRunner.paceMode,
                    onCheck = { _ ->
                        onReducer(PaceRunnerSwitchModeReducer(mode))
                    }
                )
            }
        )
    }
}
