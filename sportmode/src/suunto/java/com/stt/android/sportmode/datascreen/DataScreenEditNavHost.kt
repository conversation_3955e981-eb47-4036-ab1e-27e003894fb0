package com.stt.android.sportmode.datascreen

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.stt.android.compose.util.SystemBarsColorController
import com.stt.android.sportmode.datascreen.DataScreenEditNav.DATA_SCREEN_EDIT
import com.stt.android.sportmode.datascreen.DataScreenEditNav.DATA_SCREEN_EDIT_FIELDS
import com.stt.android.sportmode.datascreen.DataScreenEditNav.DATA_SCREEN_EDIT_OPTIONS
import com.stt.android.sportmode.datascreen.fields.DataOptionsFieldsScreen
import com.stt.android.sportmode.datascreen.options.DataOptionsScreen
import kotlinx.coroutines.flow.map

@Composable
fun DataScreenEditNavHost(
    onBackClick: (DataScreenList) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: DataScreenEditViewModel = hiltViewModel(),
    navController: NavHostController = rememberNavController(),
) {
    LaunchedEffect(Unit) {
        viewModel.dataScreenEditEvent.collect { event ->
            when (event) {
                DataScreenEditEvent.Exit -> onBackClick(viewModel.dataScreensFlow.value)
                DataScreenEditEvent.EditFields -> navController.navigate(DATA_SCREEN_EDIT_FIELDS)
                DataScreenEditEvent.ExitEditingFields -> navController.popBackStack()
                is DataScreenEditEvent.EditOptions -> navController.navigate(DATA_SCREEN_EDIT_OPTIONS)
                DataScreenEditEvent.ExitEditingOptions -> navController.popBackStack()
            }
        }
    }

    LaunchedEffect(Unit) {
        viewModel.checkLaunchedRoute()
    }

    SystemBarsColorController(navController) {
        navController.currentBackStackEntryFlow.map { backStackEntry ->
            when (backStackEntry.destination.route) {
                DATA_SCREEN_EDIT_FIELDS -> true
                DATA_SCREEN_EDIT_OPTIONS -> true
                else -> false
            }
        }
    }

    NavHost(
        navController = navController,
        startDestination = DATA_SCREEN_EDIT,
        modifier = modifier,
    ) {
        composable(DATA_SCREEN_EDIT) {
            DataScreenEditScreen(
                viewModel = viewModel,
                onBackClick = onBackClick,
                onChangeFieldClick = {
                    navController.navigate(DATA_SCREEN_EDIT_FIELDS)
                },
            )
        }
        composable(DATA_SCREEN_EDIT_FIELDS) {
            val checkedFields = viewModel.dataScreensFlow.value.selectedDataScreen.items.size
            DataOptionsFieldsScreen(
                checkedFields = checkedFields,
                onBackClick = viewModel::cancelEditFields,
                onSaveClick = viewModel::onFieldsUpdated,
            )
        }
        composable(DATA_SCREEN_EDIT_OPTIONS) {
            DataOptionsScreen(
                dataOptions = viewModel.currentEditDataOptions,
                onSaveClick = viewModel::onDataOptionsUpdated,
            )
        }
    }
}

object DataScreenEditNav {
    const val DATA_SCREEN_EDIT = "data_screen_edit"
    const val DATA_SCREEN_EDIT_FIELDS = "data_screen_edit_fields"
    const val DATA_SCREEN_EDIT_OPTIONS = "data_screen_edit_options"
}
