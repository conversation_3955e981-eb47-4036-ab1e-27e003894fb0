package com.stt.android.sportmode.datascreen

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.ListItem
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.sportmode.R
import com.stt.android.sportmode.composables.DeleteDisplayDialog
import com.stt.android.sportmode.composables.DisplayDataScreens
import com.stt.android.sportmode.composables.SportModeTopBar
import com.stt.android.sportmode.composables.WATCH_SHELL_HEIGHT
import java.util.Locale
import com.stt.android.R as BR
import com.stt.android.core.R as CR

@Composable
fun DataScreenEditScreen(
    viewModel: DataScreenEditViewModel,
    onBackClick: (DataScreenList) -> Unit,
    onChangeFieldClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    BackHandler {
        onBackClick(viewModel.dataScreensFlow.value)
    }

    var openDeleteDisplayDialog by rememberSaveable {
        mutableStateOf(false)
    }

    val dataScreenList by viewModel.dataScreensFlow.collectAsState()

    Scaffold(
        topBar = {
            SportModeTopBar(
                title = stringResource(id = R.string.sport_mode_display_edit),
                onBackClick = {
                    onBackClick(viewModel.dataScreensFlow.value)
                }
            )
        },
        modifier = modifier
    ) { paddingValues ->
        DataScreenEditContent(
            dataScreenList,
            onIndexSelect = { index ->
                viewModel.updateSelectedIndex(index)
            },
            onChangeFieldClick = onChangeFieldClick,
            onItemClick = { index, data ->
                viewModel.changeWatchDataItem(index, data)
            },
            onDeleteClick = {
                openDeleteDisplayDialog = true
            },
            modifier = Modifier
                .fillMaxHeight()
                .verticalScroll(rememberScrollState())
                .padding(paddingValues)
                .narrowContent()
                .background(MaterialTheme.colors.surface)
        )

        if (openDeleteDisplayDialog) {
            DeleteDisplayDialog(
                onConfirmRequest = {
                    viewModel.delete()
                    openDeleteDisplayDialog = false
                },
                onDismissRequest = { openDeleteDisplayDialog = false }
            )
        }
    }
}

@Composable
private fun DataScreenEditContent(
    dataScreenList: DataScreenList,
    onIndexSelect: (Int) -> Unit,
    onChangeFieldClick: () -> Unit,
    onItemClick: (index: Int, data: DataScreenComponent) -> Unit,
    onDeleteClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val displayListState =
        rememberLazyListState(initialFirstVisibleItemIndex = dataScreenList.selectedIndex)
    val selectedWatchDataList = dataScreenList.selectedDataScreen
    Column(
        modifier = modifier
    ) {
        DisplayDataScreens(
            dataScreenList = dataScreenList,
            displayListState = displayListState,
            onIndexSelect = { index ->
                if (index != dataScreenList.selectedIndex) {
                    onIndexSelect(index)
                }
            },
            onAddClick = {},
            canClickAdd = false,
            modifier = Modifier.height(WATCH_SHELL_HEIGHT),
        )

        val fields = selectedWatchDataList.items.size
        val fieldsText = if (fields == 0) {
            stringResource(id = R.string.sport_mode_display_fields_count_zero)
        } else {
            pluralStringResource(
                id = R.plurals.sport_mode_display_fields_count,
                fields,
                fields
            )
        }
        Row(
            modifier = Modifier
                .height(56.dp)
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.medium),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(
                    BR.string.sport_modes_display,
                    fieldsText
                ).uppercase(Locale.US),
                style = MaterialTheme.typography.bodyLargeBold
            )

            TextButton(
                onClick = {
                    onChangeFieldClick()
                }
            ) {
                Text(
                    text = if (fields == 0) {
                        stringResource(id = BR.string.add)
                    } else {
                        stringResource(id = BR.string.change)
                    }.uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.bodyLargeBold,
                    color = MaterialTheme.colors.primary
                )
            }
        }
        Divider()

        selectedWatchDataList.items.forEachIndexed { index, data ->
            FieldItem(
                title = "${index + 1}. ${stringResource(data.nameRes)}",
                onItemClick = {
                    onItemClick(index, data)
                }
            )
        }

        Spacer(modifier = Modifier.weight(1f))

        if (fields > 0 && dataScreenList.notFixedDataScreens.size > 2) {
            TextButton(
                onClick = onDeleteClick,
                modifier = Modifier
                    .padding(MaterialTheme.spacing.medium)
                    .align(Alignment.CenterHorizontally)
            ) {
                Text(
                    text = stringResource(id = R.string.sport_mode_display_delete).uppercase(
                        Locale.getDefault()
                    ),
                    style = MaterialTheme.typography.bodyBold,
                    color = colorResource(id = CR.color.bright_red)
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun FieldItem(
    title: String,
    onItemClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .clickableThrottleFirst { onItemClick() }
    ) {
        ListItem(
            text = {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = MaterialTheme.spacing.xxxlarge),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            },
            trailing = {
                Icon(
                    painter = painterResource(com.stt.android.R.drawable.chevron_right),
                    contentDescription = null,
                    modifier = Modifier.size(MaterialTheme.iconSizes.medium)
                )
            },
        )

        Divider()
    }
}

@Preview(showBackground = true)
@Composable
private fun EditWatchDisplayScreenPreview() {
    AppTheme {
        DataScreenEditContent(
            dataScreenList = DataScreenList(
                dataScreens = testDataScreens.map { it.copy(editing = true) },
                selectedIndex = 3
            ),
            onIndexSelect = {},
            onChangeFieldClick = {},
            onItemClick = { _, _ -> },
            onDeleteClick = {},
        )
    }
}
