package com.stt.android.sportmode.datascreen.fields

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.nearBlack
import com.stt.android.sportmode.composables.CheckSaveChangesDialog
import com.stt.android.sportmode.modesetting.baseColor
import com.stt.android.sportmode.modesetting.sportStr
import java.util.Locale

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun DataOptionsFieldsScreen(
    checkedFields: Int,
    onBackClick: () -> Unit,
    onSaveClick: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    var updatedFields by rememberSaveable { mutableIntStateOf(checkedFields) }
    var showDialog by remember { mutableStateOf(false) }
    var isChanged by remember { mutableStateOf(false) }
    val saveable = updatedFields != 0

    LaunchedEffect(updatedFields) {
        isChanged = updatedFields != checkedFields
    }

    fun checkedOnBackClick() = if (isChanged) {
        showDialog = true
    } else {
        onBackClick()
    }

    BackHandler {
        checkedOnBackClick()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = stringResource(sportStr.data_screen_options).uppercase(Locale.getDefault()),
                        color = MaterialTheme.colors.onSecondary
                    )
                },
                navigationIcon = {
                    SuuntoIconButton(
                        icon = SuuntoIcons.ActionClose,
                        onClick = ::checkedOnBackClick,
                        contentDescription = stringResource(R.string.back),
                        tint = MaterialTheme.colors.onSecondary,
                    )
                },
                actions = {
                    if (saveable) {
                        TextButton(onClick = {
                            onSaveClick(updatedFields)
                        }) {
                            Text(
                                text = stringResource(R.string.save).uppercase(Locale.getDefault()),
                                color = colorResource(baseColor.suunto_blue)
                            )
                        }
                    }
                },
                backgroundColor = MaterialTheme.colors.nearBlack,
            )
        },
        modifier = modifier
    ) {
        DataOptionsFieldsContent(
            checkedFields = updatedFields,
            onChecked = {
                updatedFields = it
            },
            modifier = Modifier
                .narrowContent()
                .fillMaxHeight()
                .background(MaterialTheme.colors.surface)
        )

        if (showDialog) {
            CheckSaveChangesDialog(
                onSaveRequest = {
                    showDialog = false
                    onSaveClick(updatedFields)
                },
                onNotSaveRequest = {
                    showDialog = false
                    onBackClick()
                },
                onDismissRequest = {
                    showDialog = false
                },
            )
        }
    }
}

@Preview
@Composable
private fun DataOptionsFieldsScreenPreview() {
    DataOptionsFieldsScreen(
        checkedFields = 1,
        onBackClick = {},
        onSaveClick = {},
    )
}
