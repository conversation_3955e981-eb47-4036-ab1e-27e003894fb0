package com.stt.android.sportmode.datascreen

import android.os.Parcelable
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.infomodel.DataOptionMapping
import com.stt.android.sportmode.datascreen.options.DataOptionIconMapping
import com.stt.android.sportmode.datascreen.options.DataOptionTitleMapping
import com.stt.android.sportmode.datascreen.options.EditDataOptions
import kotlinx.parcelize.Parcelize

const val MAX_DATA_SCREEN_SIZE = 11 // 1 hidden + 10 visible

@Parcelize
data class DataScreenList(
    val dataScreens: List<DataScreen> = emptyList(),
    val selectedIndex: Int = 0,
) : Parcelable {
    val selectedDataScreen: DataScreen
        get() = notFixedDataScreens[selectedIndex]

    val notFixedDataScreens: List<DataScreen>
        get() = dataScreens.filterNot { it.fixed }
}

@Parcelize
data class DataScreen(
    val fixed: Boolean = false,
    val editing: Boolean = true,
    val items: List<DataScreenComponent> = emptyList(),
) : Parcelable

@Parcelize
data class DataScreenComponent(
    val id: String,
    @StringRes
    val nameRes: Int,
    val unit: String,
    val value: String,
    val labels: List<String> = emptyList(),
    @DrawableRes
    val icon: Int = 0,
    val textForIcon: String = "",
) : Parcelable {

    companion object {
        fun fromMapping(dataOptionMapping: DataOptionMapping, isImperial: Boolean = false): DataScreenComponent {
            val labels = generateLabel(dataOptionMapping)
            val icon = DataOptionIconMapping.getIconResByDataOption(dataOptionMapping)
            return DataScreenComponent(
                id = dataOptionMapping.valueId,
                unit = if (isImperial) dataOptionMapping.imperialUnit else dataOptionMapping.metricUnit,
                labels = labels,
                nameRes = DataOptionTitleMapping.getTextResByName(dataOptionMapping.valuePhrase),
                icon = icon,
                value = if (isImperial) dataOptionMapping.imperialExampleStr else dataOptionMapping.exampleStr,
            )
        }

        private fun generateLabel(dataOptionMapping: DataOptionMapping): List<String> =
            buildList {
                if (dataOptionMapping.name.contains("LAP")) add("LAP")
                if (dataOptionMapping.name.contains("INTERVAL")) add("INT")
                if (dataOptionMapping.name.contains("SEGMENT")) add("SEG")
                if (dataOptionMapping.name.contains("AVG")) add("AVG")
                if (dataOptionMapping.name.contains("MIN")) add("MIN")
                if (dataOptionMapping.name.contains("MAX")) add("MAX")
                if (dataOptionMapping.name.contains("3S")) add("3S")
                if (dataOptionMapping.name.contains("10S")) add("10S")
                if (dataOptionMapping.name.contains("30S")) add("30S")
            }
    }
}

sealed interface DataScreenEditEvent {
    data object Exit : DataScreenEditEvent
    data object EditFields : DataScreenEditEvent
    data object ExitEditingFields : DataScreenEditEvent
    data class EditOptions(val dataOptions: EditDataOptions) : DataScreenEditEvent
    data object ExitEditingOptions : DataScreenEditEvent
}

private fun createDataScreen(vararg mappings: DataOptionMapping, fixed: Boolean = false): DataScreen {
    return DataScreen(
        fixed = fixed,
        editing = false,
        items = mappings.map { DataScreenComponent.fromMapping(it, true) }
    )
}

internal val testDataScreens = listOf(
    createDataScreen(DataOptionMapping.CARBOHYDRATE_EXPENDITURE_RATE, fixed = true),
    createDataScreen(DataOptionMapping.AVG_VERTICAL_OSCILLATION, DataOptionMapping.CURRENT_SEGMENT_AVG_POOL_SWIM_PACE),
    createDataScreen(DataOptionMapping.CARBOHYDRATE_EXPENDITURE_RATE, DataOptionMapping.MAX_CONTINUOUS_JUMPS, DataOptionMapping.JUMPS),
    createDataScreen(DataOptionMapping.CARBOHYDRATE_EXPENDITURE_RATE, DataOptionMapping.DISTANCE, DataOptionMapping.CURRENT_SEGMENT_AVG_POOL_SWIM_PACE, DataOptionMapping.FAT_EXPENDITURE),
    createDataScreen()
)
