package com.stt.android.sportmode.datascreen

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.compose.util.setContentWithTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DataScreenEditActivity : AppCompatActivity() {
    private val viewModel: DataScreenEditViewModel by viewModels()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithTheme {
            DataScreenEditNavHost(
                onBackClick = ::saveAndExit,
            )
        }
    }

    private fun saveAndExit(dataScreenList: DataScreenList) {
        val toSave = viewModel.generateDataScreenListToSave(dataScreenList)
        setResult(RESULT_OK, intent.apply {
            putExtra(
                EXTRA_DATA_SCREENS,
                toSave
            )
        })
        finish()
    }

    class ResultContract : ActivityResultContract<Pair<Int, DataScreenList>, DataScreenList>() {
        override fun createIntent(context: Context, input: Pair<Int, DataScreenList>): Intent =
            Intent().apply {
                setClass(context, DataScreenEditActivity::class.java)
                putExtra(EXTRA_SPORT_ID, input.first)
                putExtra(EXTRA_DATA_SCREENS, input.second)
            }

        override fun parseResult(resultCode: Int, intent: Intent?): DataScreenList {
            return intent?.getParcelableExtra(EXTRA_DATA_SCREENS)
                ?: throw UnsupportedOperationException("no edit display in intent")
        }
    }

    companion object {
        internal const val EXTRA_SPORT_ID = "sport_id"
        internal const val EXTRA_DATA_SCREENS = "data_screens"
    }
}
