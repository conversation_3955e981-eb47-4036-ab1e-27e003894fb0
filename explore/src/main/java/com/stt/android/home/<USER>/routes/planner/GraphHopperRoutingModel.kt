package com.stt.android.home.explore.routes.planner

import android.content.Context
import android.content.SharedPreferences
import androidx.core.util.Pair
import com.google.android.gms.maps.model.LatLng
import com.google.gson.annotations.SerializedName
import com.google.maps.android.SphericalUtil
import com.squareup.moshi.JsonClass
import com.stt.android.domain.Point
import com.stt.android.domain.routes.RouteSegment
import com.stt.android.home.explore.routes.RouteUtils
import com.stt.android.home.explore.routes.planner.RoutingApiModel.MAX_TURN_BY_TURN_WAYPOINT_COUNT
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointType
import com.stt.android.home.explore.routes.toPoint
import com.stt.android.network.HttpResponseException
import com.stt.android.network.interfaces.ANetworkProvider
import com.stt.android.remote.RoutingBaseUrl
import com.stt.android.remoteconfig.api.RemoteConfig
import com.stt.android.routes.toLatLng
import com.stt.android.utils.STTConstants
import io.reactivex.Observable
import timber.log.Timber
import java.io.IOException
import javax.inject.Inject

class GraphHopperRoutingModel @Inject constructor(
    private val context: Context,
    private val networkProvider: ANetworkProvider,
    private val remoteConfig: RemoteConfig,
    private val routePlannerUtils: RoutePlannerUtils,
    private val sharedPreferences: SharedPreferences,
    @param:RoutingBaseUrl private val routingBaseUrl: String,
) : RoutingApiModel {
    private val routingApiUrl: String get() {
        val baseUrl = (sharedPreferences.getString(STTConstants.DefaultPreferences.KEY_GRAPHHOPPER_BASE_URL, null)
            ?: (remoteConfig.getGraphhopperBaseUrl().url).takeUnless(String::isEmpty))
            ?: routingBaseUrl
        return "$baseUrl/route"
    }

    private var maximumDistanceFromRoute = Long.MAX_VALUE

    private var turnByTurnWaypointCount = 0

    private enum class Sign(val value: Int) {
        U_TURN(-98),
        LEFT_U_TURN(-8),
        KEEP_LEFT(-7),
        NOT_YET_USED(-6),
        TURN_SHARP_LEFT(-3),
        TURN_LEFT(-2),
        TURN_SLIGHT_LEFT(-1),
        CONTINUE_ON_STREET(0),
        TURN_SLIGHT_RIGHT(1),
        TURN_RIGHT(2),
        TURN_SHARP_RIGHT(3),
        FINISH_INSTRUCTION(4),
        BEFORE_VIA_POINT(5),
        BEFORE_ROUNDABOUT(6),
        KEEP_RIGHT(7),
        RIGHT_U_TURN(8),
        UNKNOWN(Int.MIN_VALUE);

        companion object {
            fun fromValue(value: Int) = values().firstOrNull {
                it.value == value
            } ?: UNKNOWN
        }
    }

    private fun Sign.toWaypoint(exitNumber: Int?): WaypointType? {
        return when (this) {
            Sign.U_TURN -> WaypointType.U_TURN
            Sign.LEFT_U_TURN -> WaypointType.U_TURN
            Sign.KEEP_LEFT -> WaypointType.LEFT_AT_FORK_TURN
            Sign.NOT_YET_USED -> null
            Sign.TURN_SHARP_LEFT -> WaypointType.SHARP_LEFT_TURN
            Sign.TURN_LEFT -> WaypointType.LEFT_TURN
            Sign.TURN_SLIGHT_LEFT -> WaypointType.SLIGHT_LEFT_TURN
            // Ignored, otherwise there would be a continue waypoint at the
            // start of each segment.
            Sign.CONTINUE_ON_STREET -> null // not used
            Sign.TURN_SLIGHT_RIGHT -> WaypointType.SLIGHT_RIGHT_TURN
            Sign.TURN_RIGHT -> WaypointType.RIGHT_TURN
            Sign.TURN_SHARP_RIGHT -> WaypointType.SHARP_RIGHT_TURN
            // Ignored, otherwise there would be a finish waypoint at the
            // end of each segment.
            Sign.FINISH_INSTRUCTION -> null // not used
            Sign.BEFORE_VIA_POINT -> null // not used
            Sign.BEFORE_ROUNDABOUT -> when (exitNumber) {
                1 -> WaypointType.ROUNDABOUT_EXIT_1_TURN
                2 -> WaypointType.ROUNDABOUT_EXIT_2_TURN
                3 -> WaypointType.ROUNDABOUT_EXIT_3_TURN
                4 -> WaypointType.ROUNDABOUT_EXIT_4_TURN
                5 -> WaypointType.ROUNDABOUT_EXIT_5_TURN
                else -> WaypointType.ROUNDABOUT_EXIT_N_TURN
            }
            Sign.KEEP_RIGHT -> WaypointType.RIGHT_AT_FORK_TURN
            Sign.RIGHT_U_TURN -> WaypointType.U_TURN
            Sign.UNKNOWN -> null
        }
    }

    override fun setMaxDistanceFromRoute(maxDistanceFromRoute: Long) {
        maximumDistanceFromRoute = maxDistanceFromRoute
    }

    override fun setTurnByTurnWaypointCount(turnByTurnWaypointCount: Int) {
        this.turnByTurnWaypointCount = turnByTurnWaypointCount
    }

    override fun createBikeSegment(
        start: LatLng,
        end: LatLng,
        previousRouteEnd: Point?,
        position: Int
    ): Observable<RouteSegment>? {
        return getRoute(start, end, previousRouteEnd, "bike", position)
    }

    override fun createStraightSegment(
        start: LatLng,
        end: LatLng,
        previousRouteEnd: Point?,
        position: Int,
        requireAltitude: Boolean
    ): Observable<RouteSegment> {
        val createFootSegmentObservable = createFootSegment(start, end, null, position)
            .map {
                // Use latitude and longitude from given start and end points, but use altitude
                // value from GraphHopper. This allows having some altitude data even when using
                // the 'Free drawing' routing mode.

                val firstPointAltitude = it.routePoints.firstOrNull()?.altitude
                val lastPointAltitude = it.routePoints.lastOrNull()?.altitude

                val startPoint = (previousRouteEnd ?: it.startPoint).copy(
                    altitude = firstPointAltitude ?: 0.0
                )

                val endPoint = it.endPoint.copy(
                    altitude = lastPointAltitude ?: 0.0
                )

                val ascent = if (firstPointAltitude != null && lastPointAltitude != null) {
                    (lastPointAltitude - firstPointAltitude).coerceAtLeast(0.0)
                } else {
                    null
                }

                it.copy(
                    startPoint = startPoint,
                    endPoint = endPoint,
                    routePoints = listOf(startPoint, endPoint),
                    ascent = ascent
                )
            }

        return if (requireAltitude) {
            createFootSegmentObservable
        } else {
            createFootSegmentObservable
                .onErrorReturn {
                    Timber.d(it, "createFootSegment failed, creating segment with no altitude")
                    RouteUtils.newStraightSegment(start, end, position)
                }
        }
    }

    override fun createFootSegment(
        start: LatLng,
        end: LatLng,
        previousRouteEnd: Point?,
        position: Int,
    ): Observable<RouteSegment> {
        return getRoute(start, end, previousRouteEnd, "hike", position)
    }

    override fun createMtbSegment(
        start: LatLng,
        end: LatLng,
        previousRouteEnd: Point?,
        position: Int,
    ): Observable<RouteSegment> {
        return getRoute(start, end, previousRouteEnd, "mtb", position)
    }

    override fun createRacingBikeSegment(
        start: LatLng,
        end: LatLng,
        previousRouteEnd: Point?,
        position: Int,
    ): Observable<RouteSegment> {
        return getRoute(start, end, previousRouteEnd, "racingbike", position)
    }

    private fun getRoute(start: LatLng, end: LatLng, previousRouteEnd: Point?, vehicle: String, position: Int): Observable<RouteSegment> {
        return Observable.fromCallable {
            queryRouteEndpoint(start, end, previousRouteEnd, vehicle, position)
        }
    }

    @Throws(IOException::class)
    private fun queryRouteEndpoint(start: LatLng, end: LatLng, previousRouteEnd: Point?, vehicle: String, position: Int): RouteSegment {
        val instructionsEnabled = turnByTurnWaypointCount <= MAX_TURN_BY_TURN_WAYPOINT_COUNT
        val params = mutableListOf<Pair<String, String>>().apply {
            add(Pair("point", "${start.latitude}, ${start.longitude}"))
            add(Pair("point", "${end.latitude}, ${end.longitude}"))
            add(Pair("vehicle", vehicle))
            add(Pair("type", "json"))
            add(Pair("locale", "en"))
            add(Pair("elevation", "true"))
            add(Pair("points_encoded", "false"))
            add(Pair("instructions", instructionsEnabled.toString()))
            add(Pair("optimize", "false"))
            add(Pair("key", remoteConfig.getGraphHopperKey()))
        }

        return try {
            val graphHopperRoute = networkProvider.getJson<GraphHopperRoute>(
                routingApiUrl,
                null,
                params.toList(),
                GraphHopperRoute::class.java
            )
            toRouteSegment(graphHopperRoute, start, end, previousRouteEnd, position)
        } catch (e: HttpResponseException) {
            Timber.w(e, "Unable to find route point, will return straight route segment")
            toStraightRouteSegment(start, end, position, emptyList())
        }
    }

    private fun toRouteSegment(
        graphHopperRoute: GraphHopperRoute,
        start: LatLng,
        end: LatLng,
        previousRouteEnd: Point?,
        position: Int
    ): RouteSegment {
        val routePoints = getRoutePoints(graphHopperRoute.paths.first().points.coordinates)
        if (routePoints.size <= 1) {
            return toStraightRouteSegment(start, end, position, routePoints)
        } else {
            val startDistance =
                SphericalUtil.computeDistanceBetween(start, routePoints.first().toLatLng())
            val endDistance =
                SphericalUtil.computeDistanceBetween(end, routePoints.last().toLatLng())
            return if (startDistance > maximumDistanceFromRoute || endDistance > maximumDistanceFromRoute) {
                toStraightRouteSegment(previousRouteEnd?.toLatLng() ?: start, end, position, routePoints)
            } else {
                val instructions = graphHopperRoute.paths.first().instructions
                val routeWithWaypoints = if (instructions != null) {
                    addNavigationWaypoints(instructions, routePoints)
                } else {
                    routePoints
                }
                val route = if (previousRouteEnd != null) {
                    listOf(previousRouteEnd) + routeWithWaypoints
                } else {
                    routeWithWaypoints
                }
                val verticalDelta = routePlannerUtils.calculateCumulativeVerticalDelta(route)
                RouteSegment(
                    start.toPoint(),
                    end.toPoint(),
                    position,
                    route,
                    verticalDelta?.ascent ?: 0.0,
                    verticalDelta?.descent ?: 0.0
                )
            }
        }
    }

    private fun addNavigationWaypoints(
        instructions: List<GraphHopperInstruction>,
        routePoints: List<Point>
    ): List<Point> = instructions.associateBy { routePoints[it.interval.first()] }
        .let { pointsToInstructions ->
            var turnByTurnCount = this.turnByTurnWaypointCount
            routePoints.map { point ->
                val instruction = pointsToInstructions[point]
                instruction?.run {
                    val waypoint =
                        Sign.fromValue(instruction.sign).toWaypoint(instruction.exitNumber)
                    if (waypoint != null && turnByTurnCount < MAX_TURN_BY_TURN_WAYPOINT_COUNT) {
                        ++turnByTurnCount
                        point.copy(
                            type = waypoint.typeId,
                            name = context.getString(waypoint.nameResId)
                        )
                    } else {
                        point
                    }
                } ?: point
            }
        }

    private fun toStraightRouteSegment(
        start: LatLng,
        end: LatLng,
        position: Int,
        routePoints: List<Point>
    ): RouteSegment {
        // Use latitude and longitude from given start and end points, but use altitude
        // value from GraphHopper. This allows having some altitude data even when using
        // the 'Free drawing' routing mode.

        val firstPointAltitude = routePoints.firstOrNull()?.altitude
        val lastPointAltitude = routePoints.lastOrNull()?.altitude

        val startPoint = start.toPoint().copy(
            altitude = firstPointAltitude ?: 0.0
        )

        val endPoint = end.toPoint().copy(
            altitude = lastPointAltitude ?: 0.0
        )

        val ascent = if (firstPointAltitude != null && lastPointAltitude != null) {
            (lastPointAltitude - firstPointAltitude).coerceAtLeast(0.0)
        } else {
            null
        }

        return RouteSegment(
            startPoint,
            endPoint,
            position,
            listOf(startPoint, endPoint),
            ascent
        )
    }

    private fun getRoutePoints(coordinates: List<List<Double>>): List<Point> {
        return coordinates.map { Point(it[0], it[1], it[2]) }
    }

    @JsonClass(generateAdapter = true)
    internal data class GraphHopperRoute(@SerializedName("paths") val paths: List<GraphHopperPath>)

    @JsonClass(generateAdapter = true)
    internal data class GraphHopperPath(
        @SerializedName("points") val points: GraphHopperCoordinates,
        @SerializedName("ascend") val ascend: Double,
        @SerializedName("instructions") val instructions: List<GraphHopperInstruction>?
    )

    @JsonClass(generateAdapter = true)
    internal data class GraphHopperInstruction(
        @SerializedName("text") val text: String,
        @SerializedName("street_name") val streetName: String,
        @SerializedName("distance") val distance: Double,
        @SerializedName("time") val time: Int,
        @SerializedName("heading") val heading: Double,
        @SerializedName("sign") val sign: Int,
        @SerializedName("interval") val interval: List<Int>,
        @SerializedName("exit_number") val exitNumber: Int? = null,
        @SerializedName("turn_angle") val turnAngle: Double? = null
    )

    @JsonClass(generateAdapter = true)
    internal class GraphHopperCoordinates(@SerializedName("coordinates") val coordinates: List<List<Double>>)
}
