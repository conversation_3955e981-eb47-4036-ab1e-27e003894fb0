<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/mapContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/workout_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="80dp"
            android:layout_marginEnd="@dimen/padding"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@id/mapFloatingActionButtonLayout"
            app:layout_constraintEnd_toEndOf="parent" />

        <include
            android:id="@+id/mapButtons"
            layout="@layout/map_buttons" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/avalanche_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="72dp"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_xsmaller"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
