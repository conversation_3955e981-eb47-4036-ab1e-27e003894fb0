package com.stt.android.home.explore.offlinemaps.selection.search

import androidx.compose.runtime.Stable
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionResult
import kotlinx.collections.immutable.ImmutableList

@Stable
internal sealed interface OfflineMapsSearchViewState {
    val searchTerm: String

    data object NoSearchTerm : OfflineMapsSearchViewState {
        override val searchTerm: String = ""
    }

    data class Searching(
        override val searchTerm: String,
    ) : OfflineMapsSearchViewState

    data class NoMatchFound(
        override val searchTerm: String,
    ) : OfflineMapsSearchViewState

    data class MatchesFound(
        override val searchTerm: String,
        val searchResult: ImmutableList<OfflineRegionResult>,
    ) : OfflineMapsSearchViewState
}
