package com.stt.android.utils

import io.reactivex.Completable
import io.reactivex.Flowable
import io.reactivex.Maybe
import io.reactivex.Observable
import io.reactivex.Single
import java.lang.reflect.Array
import java.lang.reflect.GenericArrayType
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type
import java.lang.reflect.TypeVariable
import java.lang.reflect.WildcardType

fun Class<*>.boxed(): Class<out Any> = if (isPrimitive) {
    when {
        this == Byte::class.java -> java.lang.Byte::class.java
        this == Short::class.java -> java.lang.Short::class.java
        this == Int::class.java -> java.lang.Integer::class.java
        this == Long::class.java -> java.lang.Long::class.java
        this == Float::class.java -> java.lang.Float::class.java
        this == Double::class.java -> java.lang.Double::class.java
        else -> this
    }
} else {
    this
}

/**
 * Copied from retrofit2.Utils.getParameterUpperBound(int index, ParameterizedType type)
 */
fun ParameterizedType.getParameterUpperBound(index: Int): Type {
    val types = this.actualTypeArguments
    require(!(index < 0 || index >= types.size)) { "Index " + index + " not in range [0," + types.size + ") for " + this }
    val paramType = types[index]
    return if (paramType is WildcardType) {
        paramType.upperBounds[0]
    } else {
        paramType
    }
}

/**
 * Copied from retrofit2.Utils.getRawType(Type type)
 */
fun Type.rawType(): Class<*> {
    if (this is Class<*>) {
        // Type is a normal class.
        return this
    }
    if (this is ParameterizedType) {
        // I'm not exactly sure why rawType() returns Type instead of Class. Neal isn't either but
        // suspects some pathological case related to nested classes exists.
        val rawType = this.rawType
        require(rawType is Class<*>)
        return rawType
    }
    if (this is GenericArrayType) {
        val componentType = this.genericComponentType
        return Array.newInstance(componentType.rawType(), 0).javaClass
    }
    if (this is TypeVariable<*>) {
        // We could use the variable's bounds, but that won't work if there are multiple. Having a raw
        // type that's more general than necessary is okay.
        return Any::class.java
    }
    if (this is WildcardType) {
        return this.upperBounds[0].rawType()
    }

    throw IllegalArgumentException(
        "Expected a Class, ParameterizedType, or GenericArrayType, but <" + this + "> is of type " + this.javaClass.name
    )
}

fun Class<*>.isRx2(): Boolean =
    this == Observable::class.java ||
        this == Flowable::class.java ||
        this == Single::class.java ||
        this == Maybe::class.java ||
        this == Completable::class.java
