package com.stt.android.remote.routes

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class RemotePoint(
    @<PERSON><PERSON>(name = "x") val longitude: Double,
    @<PERSON><PERSON>(name = "y") val latitude: Double,
    @<PERSON><PERSON>(name = "z") val altitude: Double? = null,
    @<PERSON><PERSON>(name = "name") val name: String? = null,
    @<PERSON><PERSON>(name = "type") val type: Int? = null,
    @<PERSON><PERSON>(name = "description") val description: String? = null,
)
