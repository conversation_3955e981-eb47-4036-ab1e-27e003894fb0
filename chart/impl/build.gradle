plugins {
    id "stt.android.plugin.library"
    id "stt.android.plugin.compose"
    id "stt.android.plugin.hilt"
}

android {
    namespace = "com.stt.android.chart.impl"
    buildFeatures.buildConfig = true
}

dependencies {
    api project(Deps.chartApi)
    implementation project(Deps.appBase)
    implementation project(Deps.domain)
    implementation project(Deps.infoModel)
    implementation project(Deps.workoutsDomain)
    implementation project(Deps.core)
    implementation project(Deps.datasource)
    implementation project(Deps.diaryDomain)
    implementation project(Deps.eventtracking)
    implementation project(Deps.analytics)
    implementation(libs.sim.formatter) {
        exclude group: 'org.javolution', module: 'javolution'
    }

    implementation libs.vico
    implementation libs.mpandroid
    implementation libs.androidx.paging.compose
}
