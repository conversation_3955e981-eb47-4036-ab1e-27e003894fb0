<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="chart_content_sleep">Sleep</string>
    <string name="chart_content_steps">Steps</string>
    <string name="chart_content_calories">Calories</string>
    <string name="chart_content_duration">Duration</string>
    <string name="chart_content_ascent">Ascent</string>
    <string name="chart_content_heart_rate">Heart Rate</string>
    <string name="chart_content_minimum_heart_rate">Min. DayTime HR</string>
    <string name="chart_content_sleep_minimum_heart_rate">Min. sleep HR</string>
    <string name="chart_content_resting_heart_rate">Resting HR</string>
    <string name="chart_content_resources">Resources</string>
    <string name="chart_content_hrv">Hrv</string>

    <!-- HRV current values -->
    <string name="hrv_track_sleep_for_range">Track sleep for 14 days to establish your HRV range</string>
    <string name="hrv_recent_normal_range">Recent normal range</string>
    <string name="hrv_last_night">Last night</string>
    <string name="hrv_seven_day_avg">7-day avg.</string>

    <!-- Resource states -->
    <string name="resources_state_active">Active</string>
    <string name="resources_state_inactive">Inactive</string>
    <string name="resources_state_stressed">Stressed</string>
    <string name="resources_state_recovering">Recovering</string>

    <string name="resources_state_active_avg">Avg. active</string>
    <string name="resources_state_inactive_avg">Avg. inactive</string>
    <string name="resources_state_stressed_avg">Avg. stressed</string>
    <string name="resources_state_recovering_avg">Avg. recovering</string>

    <!-- Resource levels -->
    <string name="resources_level_low">Low</string>
    <string name="resources_level_moderate">Moderate</string>
    <string name="resources_level_high">High</string>
    <string name="resources_level_very_high">Very high</string>

    <string name="chart_value_total">Total</string>
    <string name="chart_value_total_co2e_saved">Total CO₂e saved</string>
    <string name="chart_value_daily_avg">Daily avg.</string>
    <string name="chart_value_daily_avg_calories">Active daily avg.</string>
    <string name="chart_value_monthly_avg">Monthly avg.</string>
    <string name="chart_value_Yearly_avg">Yearly avg.</string>
    <string name="chart_value_range">Range</string>
    <string name="chart_value_min_daytime_hr">Min. daytime HR</string>
    <string name="chart_value_avg_min_daytime_hr">Avg. min. daytime HR</string>
    <string name="chart_value_min_sleep_hr">Min. sleep HR</string>
    <string name="chart_value_avg_sleep_hr">Avg. min. sleep HR</string>
    <string name="chart_value_resting_hr">Resting HR</string>
    <string name="chart_value_avg_resting_hr">Avg. Resting HR</string>
    <string name="chart_value_active">Active</string>
    <string name="chart_value_active_calories">Active calories</string>
    <string name="chart_value_avg">Avg.</string>

    <string name="chart_daily_target">Daily target</string>
    <string name="chart_daily_step_target">Daily step target</string>
    <string name="chart_daily_calorie_target">Daily calorie target</string>
    <string name="chart_daily_sleep_target">Daily sleep target</string>
    <string name="chart_weekly_training_target">Weekly training target</string>

    <string name="chart_comparison_compare">Compare</string>
    <string name="chart_comparison_hide_comparison">Hide comparison</string>

    <string name="chart_granularity_daily_abbreviation">D</string>
    <string name="chart_granularity_weekly_abbreviation">W</string>
    <string name="chart_granularity_seven_days_abbreviation">7 D</string>
    <string name="chart_granularity_monthly_abbreviation">M</string>
    <string name="chart_granularity_thirty_days_abbreviation">30 D</string>
    <string name="chart_granularity_six_weeks_abbreviation">6 W</string>
    <string name="chart_granularity_six_months_abbreviation">6 M</string>
    <string name="chart_granularity_yearly_abbreviation">Y</string>
    <string name="chart_granularity_eight_years_abbreviation">8 Y</string>
    <string name="chart_granularity_sixty_day_days_abbreviation">60 D</string>
    <string name="chart_granularity_three_hundred_sixty_five_days_abbreviation">365 D</string>
    <string name="chart_granularity_one_hundred_eight_days_abbreviation">180 D</string>

    <string name="chart_granularity_seven_days">Last 7 days</string>
    <string name="chart_granularity_thirty_days">Last 30 days</string>
    <string name="chart_granularity_six_weeks">6 weeks</string>
    <string name="chart_granularity_six_months">6 months</string>
    <string name="chart_granularity_one_year">Year</string>
    <string name="chart_granularity_eight_years">8 years</string>
    <string name="chart_granularity_day">Day</string>
    <string name="chart_granularity_year">Year</string>
    <string name="chart_granularity_week">Week</string>
    <string name="chart_granularity_month">Month</string>

    <string name="chart_granularity_daily_interval">Daily interval</string>
    <string name="chart_granularity_weekly_interval">Weekly interval</string>
    <string name="chart_granularity_monthly_interval">Monthly interval</string>
    <string name="chart_granularity_yearly_interval">Yearly interval</string>

    <string name="chart_granularity_more">More</string>
    <string name="chart_granularity_time_range_title">Time range</string>
    <string name="chart_granularity_time_range_desc">Select the time window of your analysis</string>

    <string name="calories_bmr">BMR</string>
    <string name="total_calories">Total calories</string>
    <string name="about_calories">About calories</string>
    <string name="calories_instruction">Total calories represent the energy consumed in a day, including basal metabolic rate (BMR) and activity calories. BMR is the minimum energy required to maintain essential physiological functions, such as breathing, heartbeat, and temperature regulation, while at rest. Activity calories account for energy expended during exercise and daily activities. Increasing BMR can be achieved by building muscle mass, engaging in regular exercise, staying hydrated, and maintaining good sleep habits.</string>
    <string name="workout_sessions_title">Workout Sessions</string>
    <string name="workout_filter_all">All</string>

    <string name="about_minimum_hr_title">About minimum daytime heart rate</string>
    <string name="about_minimum_hr_description">Minimum daytime heart rate is the lowest heart rate measured while awake, usually higher than both resting and sleeping heart rates. It reflects overall activity levels and heart health, helping to identify fatigue, stress, and recovery status when sleep data is unavailable.</string>
    <string name="about_sleep_minimum_hr_title">About sleep minimum heart rate</string>"
    <string name="about_sleep_minimum_hr_description">Minimum sleep heart rate is the lowest heart rate recorded during sleep. It serves as an indicator of sleep quality and recovery, with a lower value generally suggesting better recovery.</string>
    <string name="about_heart_rate_title">About Heart rate</string>"
    <string name="about_heart_rate_description">Heart rate (HR) refers to the number of heartbeats per minute and is a key indicator of heart health and fitness. Understanding heart rate variations can help optimize training and improve overall well-being. Heart rate fluctuates during sleep, exercise, and daily activities. Resting heart rate and minimum sleeping heart rate tend to be lower, aiding in recovery assessment and guiding training intensity. Monitoring daily heart rate helps track trends, detect abnormalities, and maintain awareness of overall health.</string>

    <string name="about_resting_heart_rate_title">About resting heart rate (RHR)</string>
    <string name="about_resting_heart_rate_description">Resting heart rate (RHR) is the heart rate measured while at rest, reflecting cardiovascular health and fitness. A lower RHR typically indicates better heart efficiency and conditioning. Tracking RHR can also help assess recovery and adjust training and rest periods accordingly.</string>
    <string name="about_resources_title">About resources</string>
    <string name="about_resources_description">Resources reflect your daily recovery and energy expenditure, helping you monitor your physical state and adjust activity levels. Energy is generally restored during sleep.</string>

    <!-- HRV instructions -->
    <string name="about_hrv_title">What is HRV?</string>
    <string name="about_hrv_description">Heart rate variability (HRV) measures the variation in time intervals between heartbeats. It reflects autonomic nervous system (ANS) balance and provides insights into overall health and stress levels. HRV is a valuable tool for understanding autonomic function and promoting well-being.</string>
    <string name="how_to_read_hrv_data_title">How should I read the data?</string>
    <string name="how_to_read_hrv_data_description">For optimal HRV, values should remain within your normal range, ideally closer to the "Too high" limit. While higher HRV is generally associated with better health, it should always be interpreted relative to your baseline. Factors such as relaxation, physical and mental exertion, or illness (e.g., flu) can cause fluctuations in HRV.</string>
    <string name="how_to_measure_hrv_title">How to measure my HRV?</string>
    <string name="how_to_measure_hrv_description">1.Suunto measures your HRV during your sleep. To obtain HRV data, wear your watch while sleeping and ensure sleep tracking is enabled. Heart rate variability is continuously measured throughout the sleep period to calculate the average RMSSD value for the night. RMSSD (root mean square of successive differences) is a widely used metric for assessing HRV.</string>
    <string name="hrv_values_explained_title">2.Values explained</string>
    <string name="hrv_todays_value_explained">a.Today\'s HRV value is derived from measurements taken during the previous night, while Yesterday\'s HRV value refers to the night before.</string>
    <string name="hrv_seven_day_average_explained">b.The 7-day average is calculated based on HRV measurements from the past 7 nights.</string>
    <string name="hrv_normal_range_explained">c.To determine your normal range, you need 14 HRV measurements taken over a span of 60 days.</string>

    <string name="sleep_heart_rate_compare">Heart rate</string>
    <string name="sleep_heart_rate_hide_comparison">Hide HR</string>

    <string name="sleep_stages_title">Stages</string>
    <string name="sleep_stages_avg_awake">Avg. awake</string>
    <string name="sleep_stages_avg_rem">Avg. REM</string>
    <string name="sleep_stages_avg_light">Avg. light</string>
    <string name="sleep_stages_avg_deep">Avg. deep</string>
    <string name="sleep_quality_title">Sleep quality</string>
    <string name="sleep_quality_description_default">Please wear your watch during sleep to collect more relevant data.</string>
    <string name="sleep_comparison_title">Comparison</string>
    <string name="sleep_comparison_graph_type_sleep_duration">Sleep duration</string>
    <string name="sleep_comparison_graph_type_sleep_regularity">Sleep regularity</string>
    <string name="sleep_comparison_graph_type_nap_duration">Nap duration</string>
    <string name="sleep_comparison_graph_type_total_duration">Total time</string>
    <string name="sleep_comparison_graph_type_blood_oxygen">Max. sleep SpO₂</string>
    <string name="sleep_comparison_graph_type_training">Training duration</string>
    <string name="sleep_comparison_graph_type_min_hr_during_sleep">Min. sleep HR</string>
    <string name="sleep_comparison_graph_type_avg_hr_during_sleep">Avg. sleep HR</string>
    <string name="sleep_comparison_graph_type_morning_resources">Wake-up resources</string>
    <string name="sleep_history_title">Sleep history</string>
    <string name="sleep_history_description_default">Please wear your watch during sleep to collect more relevant data.</string>

    <string name="heart_rate_stat_resting">Resting HR</string>
    <string name="heart_rate_stat_average">Avg. HR</string>
    <string name="heart_rate_stat_min_sleep">Min. sleep HR</string>
    <string name="heart_rate_stat_min_daytime">Min. daytime HR</string>
    <string name="chart_connect_commute">Commutes</string>
    <string name="about_commute">What is commute?</string>
    <string name="commute_instruction">Commute refers to traveling to a destination. Activities such as runs, rides, and walks with a straight-line distance of 500 meters or more from the starting point are automatically tagged as a commute. We encourage eco-friendly options like cycling and walking, as they reduce carbon emissions, improve air quality, and promote health. In addition to the automatically assigned commute tag, you can manually edit activity records to add a commute tag and document your environmental contributions.</string>
    <string name="exercises_counts">exercises</string>
    <string name="commute_tags_title">Tag commutes automatically</string>
    <string name="commute_tags_comment">Allow us to automatically tag all your runs, rides, and walks with a direct route of at least 500 meters between the start and endpoint as commutes, and start tracking the CO₂e emissions you save.</string>

    <!-- Resource States Instructions -->
    <string name="how_to_read_data">How to read the data?</string>
    <string name="recovering">1. Recovering: </string>
    <string name="resources_rise_quickly">Resources rise quickly.</string>
    <string name="scenario_prefix">a. Scenario: </string>
    <string name="recovering_scenario">Deep relaxation, especially quality sleep, indicating optimal recovery.</string>

    <string name="inactive">2. Inactive: </string>
    <string name="resources_change_slowly">Resources change slowly and unpredictably.</string>
    <string name="during_sleep_prefix">a. During Sleep: </string>
    <string name="inactive_sleep_desc">Remains stable or gradually increases (excluding awake periods), with slow recovery.</string>
    <string name="during_wakefulness_prefix">b. During Wakefulness: </string>
    <string name="inactive_wakefulness_desc">Can fluctuate slightly, depending on minor activities.</string>

    <string name="active">3. Active: </string>
    <string name="resources_decline">Resources decline, with the rate linked to exercise intensity and recovery.</string>
    <string name="active_scenario">Daily activities or exercise, where Resources change based on activity intensity and recovery time.</string>

    <string name="stressed">4. Stressed: </string>
    <string name="resources_drop_rapidly">Resources drop rapidly.</string>
    <string name="stressed_scenario">Stress during wakefulness causes a quick decline in Resources, signaling a high-pressure state.</string>

    <string name="how_resources_measured">How resources are measured?</string>
    <string name="resources_measured_based">Resources are measured based on physiological state</string>
    <string name="physiological_state">physiological state.</string>
    <string name="active_state">Active State: </string>
    <string name="active_state_desc">Activity Recovery Time reflects recovery needs; longer recovery times suggest faster Resource depletion.</string>
    <string name="inactive_state">Inactive State: </string>
    <string name="inactive_state_desc">Heart Rate Variability (HRV) gauges autonomic balance. High HRV indicates high Resources; low HRV signals low Resources. Key HRV metrics like RMSSD, Stress Index, and SDNN, alongside Heart Rate (HR), help assess stress levels.</string>

    <string name="resource_states_intro">The chart displays four states based on Resource changes:</string>

    <string name="chart_connect_training_load">Training Load</string>
    <string name="about_tss">What is training stress score (TSS)?</string>
    <string name="tss_instruction">Training stress score (TSS) quantifies training load, primarily for endurance sports, by factoring in intensity and duration. Higher scores indicate greater physiological stress. It is often used alongside heart rate and power output data, with intensity based on the athlete\'s aerobic threshold—defined in Suunto as heart rate, pace, and power limits in zones 4 or 5.</string>

    <string name="vo2_max_fitness_age">Fitness age</string>
    <string name="vo2_max_six_week_avg">6 week avg.</string>

    <!-- VO2MAX instructions -->
    <string name="about_vo2max_title">What is VO₂max?</string>
    <string name="about_vo2max_description">VO₂max (maximum oxygen uptake) measures how much oxygen your body can use during intense exercise. It is a key indicator of cardiovascular fitness and endurance—higher values generally mean better performance. VO₂max is influenced by factors such as age, gender, and training level. Improving it can enhance overall athletic ability.</string>
    <string name="how_to_get_vo2max_title">How to get VO₂max value?</string>
    <string name="how_to_get_vo2max_description">Your watch estimates VO₂max by analyzing data collected during workouts, including heart rate, pace, and exercise intensity. For example, outdoor running for at least 10 minutes can provide an estimate. Additionally, you can use the VO₂max test (Cooper Test) in SuuntoPlus to assess your VO₂max capacity.</string>

    <string name="how_to_get_vo2max_description_v2">Your watch estimates VO₂max by analyzing data from your running workouts, including heart rate and pace. You can also assess your VO₂max capacity using the \"VO₂max Test (Cooper Test)\" in SuuntoPlus.</string>

    <!-- How to use VO2MAX -->
    <string name="how_to_use_vo2max_title">How to use VO₂max?</string>
    <string name="how_to_use_vo2max_description">Health indicator A higher VO₂max typically indicates better cardiovascular function and oxygen delivery. Tracking this metric provides insights into heart and lung health, helping you determine if you need more aerobic training or lifestyle adjustments. Benchmark comparison Comparing your VO₂max with age/sex norms—or your past data—helps assess aerobic capacity. This insight guides adjustments to your exercise plan, whether it\'s time to push harder or maintain your current routine. Long-term gauge Regular VO₂max measurements over months or years indicate whether your fitness is improving, stabilizing, or declining. Identifying trends early allows you to fine-tune training volume and intensity to stay on track with your goals.</string>
    
    <!-- VO2MAX usage sections -->
    <string name="vo2max_health_indicator_title">Health indicator</string>
    <string name="vo2max_health_indicator_description">A higher VO₂max typically indicates better cardiovascular function and oxygen delivery. Tracking this metric provides insights into heart and lung health, helping you determine if you need more aerobic training or lifestyle adjustments.</string>
    
    <string name="vo2max_benchmark_comparison_title">Benchmark comparison</string>
    <string name="vo2max_benchmark_comparison_description">Compare your VO₂max with reference standards to evaluate your aerobic capacity and fitness level.</string>
    
    <string name="vo2max_long_term_gauge_title">Long-term gauge</string>
    <string name="vo2max_long_term_gauge_description">Regular VO₂max measurements over months or years indicate whether your fitness is improving, stabilizing, or declining. Identifying trends early allows you to fine-tune training volume and intensity to stay on track with your goals.</string>
    
    <string name="mil">mil.</string>

    <!-- Activity History -->
    <string name="activity_history_title">Activity history</string>
</resources>
