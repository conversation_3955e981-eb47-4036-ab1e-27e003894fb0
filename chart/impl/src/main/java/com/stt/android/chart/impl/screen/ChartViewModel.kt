package com.stt.android.chart.impl.screen

import android.os.SystemClock
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.chart.api.model.ChartComparison
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.SleepComparisonGraphType
import com.stt.android.chart.impl.model.WidgetInstruction
import com.stt.android.chart.impl.model.availableStyles
import com.stt.android.chart.impl.model.comparisonOffTitleRes
import com.stt.android.chart.impl.model.comparisonOnTitleRes
import com.stt.android.chart.impl.model.extraChartGranularity
import com.stt.android.chart.impl.model.leftComparisonColorRes
import com.stt.android.chart.impl.model.mainChartGranularity
import com.stt.android.chart.impl.model.rightComparisonColorRes
import com.stt.android.chart.impl.model.rightValueTypeRes
import com.stt.android.chart.impl.model.supportsComparison
import com.stt.android.chart.impl.model.targetChartComparison
import com.stt.android.chart.impl.model.titleRes
import com.stt.android.chart.impl.model.toAnalyticsMainGraphTypeValue
import com.stt.android.chart.impl.model.toAnalyticsSubGraphTypeValue
import com.stt.android.chart.impl.model.toAnalyticsTimeDim
import com.stt.android.chart.impl.model.toAnalyticsWidgetName
import com.stt.android.chart.impl.model.valueTypeRes
import com.stt.android.chart.impl.usecases.CalculateDateRangeUseCase
import com.stt.android.chart.impl.usecases.CommuteTagsDataUseCase
import com.stt.android.chart.impl.usecases.CreateChartDataUseCase
import com.stt.android.chart.impl.usecases.CreateChartTimeRangeStringUseCase
import com.stt.android.chart.impl.usecases.CreateGoalDataUseCase
import com.stt.android.chart.impl.usecases.CreateGoalEditorDataUseCase
import com.stt.android.chart.impl.usecases.CreateSleepViewDataUseCase
import com.stt.android.chart.impl.usecases.GetChartHighlightDataUseCase
import com.stt.android.chart.impl.usecases.GetHeartRateStatsViewDataUseCase
import com.stt.android.chart.impl.usecases.GetWidgetInstructionsUseCase
import com.stt.android.chart.impl.usecases.GetActivityTypesInTimeRangeUseCase
import com.stt.android.chart.impl.usecases.GetWorkoutListUseCase
import com.stt.android.chart.impl.usecases.IsWatchConnectedUseCase
import com.stt.android.chart.impl.usecases.UpdateGoalUseCase
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.combine
import com.stt.android.data.RELEASED_YEAR
import com.stt.android.domain.user.autoCommuteTaggingEnabled
import com.stt.android.eventtracking.EventTracker
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.Year
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject

@HiltViewModel
internal class ChartViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val createGoalDataUseCase: CreateGoalDataUseCase,
    private val createGoalEditorDataUseCase: CreateGoalEditorDataUseCase,
    private val updateGoalUseCase: UpdateGoalUseCase,
    private val createChartDataUseCase: CreateChartDataUseCase,
    private val calculateDateRangeUseCase: CalculateDateRangeUseCase,
    private val createChartTimeRangeStringUseCase: CreateChartTimeRangeStringUseCase,
    private val getChartHighlightDataUseCase: GetChartHighlightDataUseCase,
    private val isWatchConnectedUseCase: IsWatchConnectedUseCase,
    private val getWidgetInstructionsUseCase: GetWidgetInstructionsUseCase,
    private val createSleepViewDataUseCase: CreateSleepViewDataUseCase,
    private val getHeartRateStatsViewDataUseCase: GetHeartRateStatsViewDataUseCase,
    private val getWorkoutListUseCase: GetWorkoutListUseCase,
    private val getActivityTypesInTimeRangeUseCase: GetActivityTypesInTimeRangeUseCase,
    private val commuteTagsDataUseCase: CommuteTagsDataUseCase,
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val eventTracker: EventTracker,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    private val chartContent: Flow<ChartContent> = savedStateHandle
        .getStateFlow(ChartActivity.KEY_CHART_CONTENT, null)
        .filterNotNull()
    private val chartStyle: Flow<ChartStyle> = savedStateHandle
        .getStateFlow(ChartActivity.KEY_CHART_STYLE, null)
        .filterNotNull()
    private val chartGranularity: Flow<ChartGranularity> = savedStateHandle
        .getStateFlow(ChartActivity.KEY_CHART_GRANULARITY, null)
        .filterNotNull()
    private val chartComparison: Flow<ChartComparison> = savedStateHandle
        .getStateFlow(ChartActivity.KEY_CHART_COMPARISON, null)
        .filterNotNull()
    private val scrollToCurrentPage: MutableStateFlow<Boolean> = MutableStateFlow(false)
    private val chartPageIndex: MutableStateFlow<Int> = MutableStateFlow(
        calculateInitialPageIndex()
    )
    private val showExtraChartGranularitySelection: MutableStateFlow<Boolean> =
        MutableStateFlow(false)
    private val selectedHeartRateStatId = MutableStateFlow<String?>(null)
    private val selectedActivityTypeId = MutableStateFlow<Int?>(null)
    private val workoutCurrentPage = MutableStateFlow(1)
    private val _viewData: MutableStateFlow<ChartViewData> = MutableStateFlow(ChartViewData.Initial)
    val viewData: StateFlow<ChartViewData> = _viewData.asStateFlow()

    private var showHighlightJob: Job? = null

    private val fullyVisibleModuleNames: MutableSet<String> = linkedSetOf()
    private var viewDurationAccumulatedMs: Long = 0L
    private var viewDurationStartedAtMs: Long? = null

    // Sleep comparison graph type change reporting related variables
    private var lastPrimaryGraphTypeChange: SleepComparisonGraphType? = null
    private var lastSecondaryGraphTypeChange: SleepComparisonGraphType? = null
    private var primaryGraphTypeChanged = false
    private var secondaryGraphTypeChanged = false
    private var sleepComparisonGraphTypeChangeJob: Job? = null

    private val appLifecycleObserver = object : DefaultLifecycleObserver {
        override fun onStart(owner: LifecycleOwner) {
            startViewDuration()
        }

        override fun onStop(owner: LifecycleOwner) {
            stopAndReportViewDuration()
        }
    }

    init {
        ProcessLifecycleOwner.get().lifecycle.addObserver(appLifecycleObserver)
        if (ProcessLifecycleOwner.get().lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED)) {
            startViewDuration()
        }
        trackWidgetDetailPageExposure()

        combine(
            chartContent,
            chartStyle,
            chartGranularity,
            chartComparison,
            isWatchConnectedUseCase(),
            selectedHeartRateStatId,
            ::updateViewData
        )
            .flowOn(coroutinesDispatchers.io)
            .launchIn(viewModelScope)

        showExtraChartGranularitySelection
            .onEach { show ->
                _viewData.update { current ->
                    (current as? ChartViewData.Loaded)
                        ?.copy(showExtraChartGranularitySelection = show)
                        ?: current
                }
            }
            .launchIn(viewModelScope)
    }

    private fun calculateInitialPageIndex(
        chartGranularity: ChartGranularity? = savedStateHandle.get<ChartGranularity>(ChartActivity.KEY_CHART_GRANULARITY),
        targetDate: LocalDate? = savedStateHandle.get<LocalDate>(ChartActivity.KEY_TARGET_DATE),
    ): Int {
        chartGranularity ?: return UNKNOWN_CHART_PAGE_INDEX

        val today = LocalDate.now()
        return calculatePageIndexForDate(
            chartGranularity,
            targetDate ?: today,
            userSettingsController,
            today,
        )
    }

    private suspend fun updateViewData(
        chartContent: ChartContent = savedStateHandle.chartContent,
    ) {
        // We should use default arguments, but isWatchConnectedUseCase().first()
        // is a suspend function, so have to do this.
        updateViewData(
            chartContent = chartContent,
            chartStyle = savedStateHandle.chartStyle,
            chartGranularity = savedStateHandle.chartGranularity,
            chartComparison = savedStateHandle.chartComparison,
            isWatchConnected = isWatchConnectedUseCase().first(),
            selectedHeartRateStatId = selectedHeartRateStatId.value,
        )
    }

    private suspend fun updateViewData(
        chartContent: ChartContent,
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        chartComparison: ChartComparison,
        isWatchConnected: Boolean,
        selectedHeartRateStatId: String? = null,
    ) {
        val chartPageCount = calculateChartPageCount(chartGranularity)
        val sanitizedChartPageIndex = chartPageIndex
            .value
            .takeIf { it in 0..<chartPageCount }
            ?: (chartPageCount - 1)
            
        val style = ChartStyleViewData(
            currentChartStyle = chartStyle,
            availableChartStyles = chartContent.availableStyles.toImmutableList(),
        )

        val commuteTagsCreating = commuteTagsDataUseCase(chartContent = chartContent)

        val comparison = if (chartContent.supportsComparison(chartGranularity)) {
            when (chartComparison) {
                ChartComparison.NONE -> ChartComparisonViewData.Off(
                    titleRes = chartContent.comparisonOffTitleRes(chartGranularity),
                    target = chartContent.targetChartComparison(chartGranularity),
                )

                ChartComparison.LAST_PERIOD -> ChartComparisonViewData.LastPeriod(
                    titleRes = chartContent.comparisonOnTitleRes(chartGranularity),
                    chartValueType = chartContent.valueTypeRes(chartGranularity),
                    chartTimeRange = createChartTimeRangeStringUseCase(
                        chartGranularity = chartGranularity,
                        chartPageIndex = sanitizedChartPageIndex - 1,
                        chartPageCount = chartPageCount,
                    ),
                    leftColorRes = chartContent.leftComparisonColorRes,
                    rightColorRes = chartContent.rightComparisonColorRes,
                )

                ChartComparison.RIGHT_AXIS -> ChartComparisonViewData.RightChart(
                    titleRes = chartContent.comparisonOnTitleRes(chartGranularity),
                    chartValueType = chartContent.valueTypeRes(chartGranularity),
                    rightChartValueType = chartContent.rightValueTypeRes(chartGranularity),
                    leftColorRes = chartContent.leftComparisonColorRes,
                    rightColorRes = chartContent.rightComparisonColorRes,
                )
            }
        } else {
            ChartComparisonViewData.NotSupported
        }

        val dateRange = calculateDateRangeUseCase(
            chartGranularity = chartGranularity,
            chartPageIndex = sanitizedChartPageIndex,
            chartPageCount = chartPageCount,
        )

        val sleepViewData = createSleepViewDataUseCase(
            chartContent = chartContent,
            chartGranularity = chartGranularity,
            from = dateRange.start,
            to = dateRange.endInclusive,
        )

        val heartRateStatsViewData = getHeartRateStatsViewDataUseCase(
            chartContent = chartContent,
            chartGranularity = chartGranularity,
            from = dateRange.start,
            to = dateRange.endInclusive,
            selectedHeartRateStatId = selectedHeartRateStatId
        )
        
        val workoutHeaderViewData = getWorkoutListUseCase(
            username = currentUserController.username,
            chartContent = chartContent,
            fromDate = dateRange.start,
            toDate = dateRange.endInclusive,
            includeActivityTypeId = selectedActivityTypeId.value
        )
        
        val availableActivityTypesViewData = getActivityTypesInTimeRangeUseCase(
            chartContent = chartContent,
            username = currentUserController.username,
            fromDate = dateRange.start,
            toDate = dateRange.endInclusive,
            selectedActivityTypeId = selectedActivityTypeId.value
        )
        
        _viewData.value = ChartViewData.Loaded(
            chartStyle = style,
            chartGranularity = chartGranularity,
            chartComparison = comparison,
            mainChartGranularities = chartContent.mainChartGranularity.toImmutableList(),
            extraChartGranularities = chartContent.extraChartGranularity.toImmutableList(),
            showExtraChartGranularitySelection = showExtraChartGranularitySelection.value,
            chartContentTitle = chartContent.titleRes,
            chartValueType = chartContent.valueTypeRes(chartGranularity),
            chartTimeRange = createChartTimeRangeStringUseCase(
                chartGranularity = chartGranularity,
                chartPageIndex = sanitizedChartPageIndex,
                chartPageCount = chartPageCount,
            ),
            scrollToCurrentPage = scrollToCurrentPage.value,
            currentChartPage = sanitizedChartPageIndex,
            chartPageCount = chartPageCount,
            chartData = emptyMap(),
            chartHighlight = ChartHighlightViewData.None,
            goal = GoalViewData.None,
            commuterTags = commuteTagsCreating,
            goalEditor = GoalEditorViewData.None,
            isWatchConnected = isWatchConnected,
            instructions = getInstructions(chartContent),
            sleepViewData = sleepViewData,
            heartRateStatsData = heartRateStatsViewData,
            workoutHeaderViewData = workoutHeaderViewData,
            availableActivityTypesViewData = availableActivityTypesViewData,
        )

        val chartData = ((sanitizedChartPageIndex - 1)..(sanitizedChartPageIndex + 1))
            .associateWith { pageIndex ->
                createChartData(
                    chartContent = chartContent,
                    chartStyle = chartStyle,
                    chartGranularity = chartGranularity,
                    chartComparison = chartComparison,
                    chartPageIndex = pageIndex,
                    chartPageCount = chartPageCount,
                    selectedHeartRateStatId = selectedHeartRateStatId,
                )
            }
        val goalData = createGoalDataUseCase(
            chartContent = chartContent,
        )

        // Simple granularity check to prevent stale data updates
        if (savedStateHandle.chartGranularity != chartGranularity) {
            return
        }

        _viewData.update { current ->
            (current as? ChartViewData.Loaded)
                ?.copy(
                    chartData = chartData,
                    goal = goalData,
                )
                ?: current
        }
    }

    private fun getInstructions(chartContent: ChartContent): ImmutableList<WidgetInstruction> {
        return getWidgetInstructionsUseCase(chartContent)
    }

    private suspend fun createChartData(
        chartContent: ChartContent,
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        chartComparison: ChartComparison,
        chartPageIndex: Int,
        chartPageCount: Int,
        selectedHeartRateStatId: String?,
    ): StateFlow<ChartData> {
        if (chartPageIndex < 0 || chartPageIndex >= chartPageCount || savedStateHandle.chartGranularity != chartGranularity) {
            return MutableStateFlow(
                ChartData(
                    chartGranularity = chartGranularity,
                    series = persistentListOf(),
                    highlightEnabled = false,
                    goal = null,
                    highlightDecorationLines = persistentMapOf(),
                    currentValues = persistentListOf(),
                    chartContent = chartContent,
                    colorIndicator = null,
                )
            )
        }

        val sanitizedChartComparison = chartComparison
            .takeIf { chartContent.supportsComparison(chartGranularity) }
            ?: ChartComparison.NONE

        val dateRange = calculateDateRangeUseCase(
            chartGranularity = chartGranularity,
            chartPageIndex = chartPageIndex,
            chartPageCount = chartPageCount,
        )

        return createChartDataUseCase(
            coroutineScope = viewModelScope,
            chartContent = chartContent,
            chartStyle = chartStyle,
            chartGranularity = chartGranularity,
            chartComparison = sanitizedChartComparison,
            dateRange = dateRange,
            selectedHeartRateStatId = selectedHeartRateStatId
        )
    }

    fun onViewEvent(event: ChartViewEvent) {
        when (event) {
            is ChartViewEvent.Close,
            is ChartViewEvent.OpenWorkout -> throw IllegalStateException("Should be handled in ChartActivity")
            is ChartViewEvent.ChartPageUpdated -> viewModelScope.launch {
                onCurrentPageIndexUpdated(event.currentPageIndex)
            }

            is ChartViewEvent.ShowExtraChartGranularitySelection ->
                showExtraChartGranularitySelection.value = true

            is ChartViewEvent.HideExtraChartGranularitySelection ->
                showExtraChartGranularitySelection.value = false

            is ChartViewEvent.UpdateChartGranularity -> {
                stopAndReportViewDuration()
                fullyVisibleModuleNames.clear()
                startViewDuration()
                selectedActivityTypeId.value = null
                // Calculate correct initial page index for new granularity
                chartPageIndex.value = event.target?.let {
                    calculateInitialPageIndex(event.chartGranularity, it)
                } ?: calculatePageIndexForDate(
                    event.chartGranularity,
                    LocalDate.now(),
                    userSettingsController,
                )
                scrollToCurrentPage.value = true
                savedStateHandle[ChartActivity.KEY_CHART_GRANULARITY] = event.chartGranularity
            }

            is ChartViewEvent.UpdateChartStyle ->
                savedStateHandle[ChartActivity.KEY_CHART_STYLE] = event.chartStyle

            is ChartViewEvent.UpdateChartComparison ->
                savedStateHandle[ChartActivity.KEY_CHART_COMPARISON] = event.chartComparison

            is ChartViewEvent.ShowGoalEditor -> showGoalEditor()
            is ChartViewEvent.HideGoalEditor -> hideGoalEditor()
            is ChartViewEvent.UpdateGoal -> updateGoal(event)
            is ChartViewEvent.ShowHighlight -> showHighlight(event)
            is ChartViewEvent.HideHighlight -> hideHighlight()
            is ChartViewEvent.GoBackToCurrent -> viewModelScope.launch {
                val currentPageIndex = calculatePageIndexForDate(savedStateHandle.chartGranularity, LocalDate.now(), userSettingsController)
                chartPageIndex.value = currentPageIndex
                scrollToCurrentPage.value = true
                updateViewData()
            }

            is ChartViewEvent.ScrolledToCurrent -> {
                scrollToCurrentPage.value = false
            }

            is ChartViewEvent.ShowSleepComparisonHighlight -> {
                showSleepComparisonHighlight(event.entryX)
            }

            ChartViewEvent.HideSleepComparisonHighlight -> {
                hideSleepComparisonHighlight()
            }

            is ChartViewEvent.UpdateSleepComparisonPrimaryGraphType -> {
                updateSleepComparisonGraphType(primaryGraphType = event.graphType)
                handleSleepComparisonGraphTypeChange(
                    graphType = event.graphType,
                    isPrimary = true
                )
            }

            is ChartViewEvent.UpdateSleepComparisonSecondaryGraphType -> {
                updateSleepComparisonGraphType(secondaryGraphType = event.graphType)
                handleSleepComparisonGraphTypeChange(
                    graphType = event.graphType,
                    isPrimary = false
                )
            }

            is ChartViewEvent.UpdateCommuteTags -> {
                updateAutoTagCommute(enabled = event.tagsOn)
            }

            ChartViewEvent.ShowSleepComparisonPrimaryGraphSelection -> {
                updateSleepComparisonGraphSelection(primaryVisible = true)
            }

            ChartViewEvent.ShowSleepComparisonSecondaryGraphSelection -> {
                updateSleepComparisonGraphSelection(secondaryVisible = true)
            }

            ChartViewEvent.HideSleepComparisonPrimaryGraphSelection,
            ChartViewEvent.HideSleepComparisonSecondaryGraphSelection -> {
                updateSleepComparisonGraphSelection()
            }

            is ChartViewEvent.HeartRateStatSelected -> {
                updateSelectedHeartRateStat(event.statId)
            }

            is ChartViewEvent.ModuleFullyVisible -> {
                fullyVisibleModuleNames.addAll(event.moduleNames)
            }

            is ChartViewEvent.TrackButtonClick -> {
                trackButtonClick(event.buttonName)
            }

            is ChartViewEvent.ActivityTypeFilter -> {
                updateSelectedActivityType(event.activityTypeId)
            }
            
            is ChartViewEvent.LoadMoreWorkouts -> {
                loadMoreWorkouts()
            }
        }
    }

    private fun showGoalEditor() {
        viewModelScope.launch {
            _viewData.update { current ->
                (current as? ChartViewData.Loaded)
                    ?.copy(
                        goalEditor = createGoalEditorDataUseCase(
                            chartContent = savedStateHandle.chartContent,
                        ),
                    )
                    ?: current
            }
        }
    }

    private fun hideGoalEditor() {
        _viewData.update { current ->
            (current as? ChartViewData.Loaded)
                ?.copy(
                    goalEditor = GoalEditorViewData.None,
                )
                ?: current
        }
    }

    private fun updateGoal(goal: ChartViewEvent.UpdateGoal) {
        viewModelScope.launch {
            val chartContent = savedStateHandle.chartContent
            updateGoalUseCase(
                chartContent = chartContent,
                goal = goal.goal,
            )

            updateViewData(chartContent = chartContent)
        }
    }

    private fun showHighlight(event: ChartViewEvent.ShowHighlight) {
        showHighlightJob?.cancel()
        showHighlightJob = viewModelScope.launch {
            _viewData.update { current ->
                val chartContent = savedStateHandle.chartContent
                val chartComparison =
                    if (chartContent.supportsComparison(savedStateHandle.chartGranularity)) {
                        savedStateHandle.chartComparison
                    } else {
                        ChartComparison.NONE
                    }
                (current as? ChartViewData.Loaded)
                    ?.copy(
                        chartHighlight = getChartHighlightDataUseCase(
                            chartContent = chartContent,
                            chartComparison = chartComparison,
                            viewData = current,
                            entryX = event.entryX,
                            selectedHeartRateStatId = selectedHeartRateStatId.value
                        ),
                    )
                    ?: current
            }
        }
    }

    private fun hideHighlight() {
        showHighlightJob?.cancel()
        showHighlightJob = null

        _viewData.update { current ->
            (current as? ChartViewData.Loaded)
                ?.copy(
                    chartHighlight = ChartHighlightViewData.None,
                )
                ?: current
        }
    }

    private fun updateAutoTagCommute(enabled: Boolean) {
        viewModelScope.launch {
            val autoCommuteTaggingEnabled = userSettingsController.storeSettings(
                userSettingsController.settings.autoCommuteTaggingEnabled(enabled)
            ).autoCommuteTaggingEnabled
            _viewData.update { current ->
                (current as? ChartViewData.Loaded)?.let { loaded ->
                    loaded.copy(
                        commuterTags = when (loaded.commuterTags) {
                            is CommuteTagsViewData.CommuteTags -> {
                                CommuteTagsViewData.CommuteTags(
                                    title = loaded.commuterTags.title,
                                    tagsOn = autoCommuteTaggingEnabled,
                                )
                            }

                            CommuteTagsViewData.None -> CommuteTagsViewData.None
                        }
                    )
                } ?: current
            }
        }
    }

    private fun updateSleepComparisonGraphType(
        primaryGraphType: SleepComparisonGraphType? = null,
        secondaryGraphType: SleepComparisonGraphType? = null,
    ) {
        val chartGranularity = savedStateHandle.chartGranularity
        val currentPageIndex = (viewData.value as? ChartViewData.Loaded)?.currentChartPage ?: 0
        val chartPageCount = calculateChartPageCount(chartGranularity)

        val dateRange = calculateDateRangeUseCase(
            chartGranularity = chartGranularity,
            chartPageIndex = currentPageIndex,
            chartPageCount = chartPageCount,
        )

        val updatedSleepComparisonViewData = createSleepViewDataUseCase.updateComparisonViewData(
            chartGranularity = chartGranularity,
            from = dateRange.start,
            to = dateRange.endInclusive,
            primaryGraphType = primaryGraphType,
            secondaryGraphType = secondaryGraphType,
        )

        _viewData.update { current ->
            (current as? ChartViewData.Loaded)?.copy(
                sleepViewData = (current.sleepViewData as? SleepViewData.Loaded)?.copy(
                    comparisonViewData = updatedSleepComparisonViewData
                ) ?: SleepViewData.None
            ) ?: current
        }
    }

    private fun updateSleepComparisonGraphSelection(
        primaryVisible: Boolean = false,
        secondaryVisible: Boolean = false,
    ) = _viewData.update { current ->
        (current as? ChartViewData.Loaded)?.copy(
            sleepViewData = (current.sleepViewData as? SleepViewData.Loaded)?.copy(
                showPrimaryComparisonGraphSelection = primaryVisible,
                showSecondaryComparisonGraphSelection = secondaryVisible,
            ) ?: SleepViewData.None
        ) ?: current
    }

    private fun showSleepComparisonHighlight(entryX: Long) = _viewData.update { current ->
        (current as? ChartViewData.Loaded)?.copy(
            sleepViewData = (current.sleepViewData as? SleepViewData.Loaded)?.copy(
                comparisonHighlightViewData = createSleepViewDataUseCase.updateComparisonHighlightViewData(
                    current.sleepViewData.comparisonViewData,
                    entryX,
                )
            ) ?: SleepViewData.None
        ) ?: current
    }

    private fun hideSleepComparisonHighlight() = _viewData.update { current ->
        (current as? ChartViewData.Loaded)?.copy(
            sleepViewData = (current.sleepViewData as? SleepViewData.Loaded)?.copy(
                comparisonHighlightViewData = SleepComparisonHighlightViewData.None
            ) ?: SleepViewData.None
        ) ?: current
    }

    override fun onCleared() {
        ProcessLifecycleOwner.get().lifecycle.removeObserver(appLifecycleObserver)
        stopAndReportViewDuration()
        sleepComparisonGraphTypeChangeJob?.cancel()
        super.onCleared()
    }

    private fun startViewDuration() {
        if (viewDurationStartedAtMs == null) {
            viewDurationStartedAtMs = SystemClock.elapsedRealtime()
        }
    }

    private fun stopAndReportViewDuration() {
        val startedAt = viewDurationStartedAtMs ?: return
        val elapsed = SystemClock.elapsedRealtime() - startedAt
        viewDurationAccumulatedMs += elapsed
        viewDurationStartedAtMs = null

        val totalSeconds = (viewDurationAccumulatedMs / 1000).toInt()
        if (totalSeconds <= 0) return

        viewDurationAccumulatedMs = 0L

        val widgetName = savedStateHandle.chartContent.toAnalyticsWidgetName()
        val timeDim = savedStateHandle.chartGranularity.toAnalyticsTimeDim()
        val module = if (savedStateHandle.chartContent == ChartContent.SLEEP) {
            fullyVisibleModuleNames.toList()
        } else {
            listOf(AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.ALL)
        }
        eventTracker.trackEvent(
            AnalyticsEvent.LEAVE_WIDGETS_DETAIL_PAGE,
            mapOf(
                AnalyticsEventProperty.WIDGET_NAME to widgetName,
                AnalyticsEventProperty.TIME_DIM to timeDim,
                AnalyticsEventProperty.BROWSING_DURATION to totalSeconds,
                AnalyticsEventProperty.MODULE_NAME to module,
            )
        )
    }

    private suspend fun onCurrentPageIndexUpdated(updatedPageIndex: Int) {
        val chartComparison = savedStateHandle.chartComparison
        val chartContent = savedStateHandle.chartContent
        val chartGranularity = savedStateHandle.chartGranularity
        val chartStyle = savedStateHandle.chartStyle

        val chartPageCount = calculateChartPageCount(chartGranularity)
        val sanitizedChartPageIndex = updatedPageIndex
            .takeIf { it in 0..<chartPageCount }
            ?: (chartPageCount - 1)
            
        chartPageIndex.value = sanitizedChartPageIndex

        val dateRange = calculateDateRangeUseCase(
            chartGranularity = chartGranularity,
            chartPageIndex = sanitizedChartPageIndex,
            chartPageCount = chartPageCount,
        )

        val heartRateStatsViewData = getHeartRateStatsViewDataUseCase(
            chartContent = chartContent,
            chartGranularity = chartGranularity,
            from = dateRange.start,
            to = dateRange.endInclusive,
            selectedHeartRateStatId = selectedHeartRateStatId.value,
        )

        val comparison = if (chartContent.supportsComparison(chartGranularity)) {
            when (chartComparison) {
                ChartComparison.NONE -> ChartComparisonViewData.Off(
                    titleRes = chartContent.comparisonOffTitleRes(chartGranularity),
                    target = chartContent.targetChartComparison(chartGranularity),
                )

                ChartComparison.LAST_PERIOD -> ChartComparisonViewData.LastPeriod(
                    titleRes = chartContent.comparisonOnTitleRes(chartGranularity),
                    chartValueType = chartContent.valueTypeRes(chartGranularity),
                    chartTimeRange = createChartTimeRangeStringUseCase(
                        chartGranularity = chartGranularity,
                        chartPageIndex = sanitizedChartPageIndex - 1,
                        chartPageCount = chartPageCount,
                    ),
                    leftColorRes = chartContent.leftComparisonColorRes,
                    rightColorRes = chartContent.rightComparisonColorRes,
                )

                ChartComparison.RIGHT_AXIS -> ChartComparisonViewData.RightChart(
                    titleRes = chartContent.comparisonOnTitleRes(chartGranularity),
                    chartValueType = chartContent.valueTypeRes(chartGranularity),
                    rightChartValueType = chartContent.rightValueTypeRes(chartGranularity),
                    leftColorRes = chartContent.leftComparisonColorRes,
                    rightColorRes = chartContent.rightComparisonColorRes,
                )
            }
        } else {
            ChartComparisonViewData.NotSupported
        }

        val sleepViewData = createSleepViewDataUseCase(
            chartContent = chartContent,
            chartGranularity = chartGranularity,
            from = dateRange.start,
            to = dateRange.endInclusive,
        )

        val workoutHeaderViewData = getWorkoutListUseCase(
            username = currentUserController.username,
            chartContent = chartContent,
            fromDate = dateRange.start,
            toDate = dateRange.endInclusive,
            includeActivityTypeId = selectedActivityTypeId.value
        )
        
        val availableActivityTypesViewData = getActivityTypesInTimeRangeUseCase(
            chartContent = chartContent,
            username = currentUserController.username,
            fromDate = dateRange.start,
            toDate = dateRange.endInclusive,
            selectedActivityTypeId = selectedActivityTypeId.value
        )

        val chartTimeRange = createChartTimeRangeStringUseCase(
            chartGranularity = chartGranularity,
            chartPageIndex = sanitizedChartPageIndex,
            chartPageCount = chartPageCount,
        )

        _viewData.update { current ->
            (current as? ChartViewData.Loaded)?.let { loaded ->
                loaded.copy(
                    chartComparison = comparison,
                    chartTimeRange = chartTimeRange,
                    currentChartPage = sanitizedChartPageIndex,
                    heartRateStatsData = heartRateStatsViewData,
                    chartData = ((sanitizedChartPageIndex - 1)..(sanitizedChartPageIndex + 1))
                        .associateWith { pageIndex ->
                            loaded.chartData[pageIndex]
                                ?: createChartData(
                                    chartContent = chartContent,
                                    chartStyle = chartStyle,
                                    chartGranularity = chartGranularity,
                                    chartComparison = chartComparison,
                                    chartPageIndex = pageIndex,
                                    chartPageCount = chartPageCount,
                                    selectedHeartRateStatId = selectedHeartRateStatId.value,
                                )
                        },
                    sleepViewData = sleepViewData,
                    workoutHeaderViewData = workoutHeaderViewData,
                    availableActivityTypesViewData = availableActivityTypesViewData,
                )
            } ?: current
        }
    }

    private fun updateSelectedHeartRateStat(statId: String?) {
        selectedHeartRateStatId.value = statId
    }

    private fun updateSelectedActivityType(activityTypeId: Int?) {
        selectedActivityTypeId.value = activityTypeId
        
        _viewData.update { current ->
            (current as? ChartViewData.Loaded)?.copy(
                availableActivityTypesViewData = when (val activityTypesData = current.availableActivityTypesViewData) {
                    is AvailableActivityTypesViewData.Loaded -> activityTypesData.copy(
                        selectedActivityTypeId = activityTypeId
                    )
                    else -> activityTypesData
                }
            ) ?: current
        }
        
        workoutCurrentPage.value = 1
        viewModelScope.launch {
            refreshActivityFilteredData()
        }
    }
    
    private fun loadMoreWorkouts() {
        viewModelScope.launch {
            val current = _viewData.value as? ChartViewData.Loaded ?: return@launch
            val currentWorkoutData = current.workoutHeaderViewData as? WorkoutHeaderViewData.Loaded ?: return@launch
            
            if (!currentWorkoutData.canLoadMore || currentWorkoutData.isLoading) {
                return@launch
            }
            
            _viewData.update { currentViewData ->
                (currentViewData as? ChartViewData.Loaded)?.copy(
                    workoutHeaderViewData = currentWorkoutData.copy(isLoading = true)
                ) ?: currentViewData
            }
            
            val nextPage = workoutCurrentPage.value + 1
            val chartContent = savedStateHandle.chartContent
            val chartGranularity = savedStateHandle.chartGranularity
            val chartPageCount = current.chartPageCount
            val currentPageIndex = current.currentChartPage
            
            val dateRange = calculateDateRangeUseCase(
                chartGranularity = chartGranularity,
                chartPageIndex = currentPageIndex,
                chartPageCount = chartPageCount,
            )
            
            val newWorkoutData = getWorkoutListUseCase(
                username = currentUserController.username,
                chartContent = chartContent,
                fromDate = dateRange.start,
                toDate = dateRange.endInclusive,
                page = nextPage,
                includeActivityTypeId = selectedActivityTypeId.value
            )
            
            if (newWorkoutData is WorkoutHeaderViewData.Loaded) {
                workoutCurrentPage.value = nextPage
                
                val currentWorkouts = currentWorkoutData.workoutHeaders.first()
                val newWorkouts = newWorkoutData.workoutHeaders.first()
                val mergedWorkouts = currentWorkouts + newWorkouts
                
                _viewData.update { currentViewData ->
                    (currentViewData as? ChartViewData.Loaded)?.copy(
                        workoutHeaderViewData = WorkoutHeaderViewData.Loaded(
                            workoutHeaders = flowOf(mergedWorkouts).flowOn(coroutinesDispatchers.io),
                            canLoadMore = newWorkoutData.canLoadMore,
                            isLoading = false
                        )
                    ) ?: currentViewData
                }
            }
        }
    }

    private suspend fun refreshActivityFilteredData() {
        val current = _viewData.value as? ChartViewData.Loaded ?: return
        val chartContent = savedStateHandle.chartContent
        
        val chartGranularity = savedStateHandle.chartGranularity
        val chartPageCount = current.chartPageCount
        val currentPageIndex = current.currentChartPage
        
        val dateRange = calculateDateRangeUseCase(
            chartGranularity = chartGranularity,
            chartPageIndex = currentPageIndex,
            chartPageCount = chartPageCount,
        )
        
        val updatedWorkoutHeaderViewData = getWorkoutListUseCase(
            username = currentUserController.username,
            chartContent = chartContent,
            fromDate = dateRange.start,
            toDate = dateRange.endInclusive,
            page = 1,
            includeActivityTypeId = selectedActivityTypeId.value
        )
        
        _viewData.update { currentViewData ->
            (currentViewData as? ChartViewData.Loaded)?.copy(
                workoutHeaderViewData = updatedWorkoutHeaderViewData
            ) ?: currentViewData
        }
    }

    private fun trackWidgetDetailPageExposure() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            val chartContent = savedStateHandle.get<ChartContent>(ChartActivity.KEY_CHART_CONTENT)
            val chartGranularity =
                savedStateHandle.get<ChartGranularity>(ChartActivity.KEY_CHART_GRANULARITY)
            val source = savedStateHandle.get<String>(ChartActivity.KEY_SOURCE)
                ?: AnalyticsPropertyValue.WidgetDetailPageExposureSourceProperty.OTHER

            val widgetName = chartContent.toAnalyticsWidgetName()
            val timeDim = chartGranularity.toAnalyticsTimeDim()

            if (widgetName.isNotEmpty() && timeDim.isNotEmpty()) {
                eventTracker.trackEvent(
                    AnalyticsEvent.WIDGET_DETAIL_PAGE_EXPOSURE,
                    mapOf(
                        AnalyticsEventProperty.WIDGET_NAME to widgetName,
                        AnalyticsEventProperty.TIME_DIM to timeDim,
                        AnalyticsEventProperty.SOURCE to source,
                    )
                )
            }
        }
    }

    private fun trackButtonClick(buttonName: String) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            val widgetName = savedStateHandle.chartContent.toAnalyticsWidgetName()
            val timeDim = savedStateHandle.chartGranularity.toAnalyticsTimeDim()

            if (widgetName.isNotEmpty() && timeDim.isNotEmpty()) {
                eventTracker.trackEvent(
                    AnalyticsEvent.WIDGET_DETAIL_PAGE_BUTTON_CLICK,
                    mapOf(
                        AnalyticsEventProperty.PAGE_NAME to widgetName,
                        AnalyticsEventProperty.TIME_DIM to timeDim,
                        AnalyticsEventProperty.BUTTON_NAME to buttonName,
                    )
                )
            }
        }
    }

    private fun handleSleepComparisonGraphTypeChange(
        graphType: SleepComparisonGraphType,
        isPrimary: Boolean
    ) {
        if (isPrimary) {
            lastPrimaryGraphTypeChange = graphType
            primaryGraphTypeChanged = true
        } else {
            lastSecondaryGraphTypeChange = graphType
            secondaryGraphTypeChanged = true
        }
        
        if (sleepComparisonGraphTypeChangeJob?.isActive == true) {
            return
        }
        
        // Start 3-second delayed reporting task
        sleepComparisonGraphTypeChangeJob = viewModelScope.launch {
            delay(3000)
            reportSleepComparisonGraphTypeChange()
        }
    }

    private fun reportSleepComparisonGraphTypeChange() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            val widgetName = savedStateHandle.chartContent.toAnalyticsWidgetName()
            val timeDim = savedStateHandle.chartGranularity.toAnalyticsTimeDim()
            
            if (widgetName.isNotEmpty() && timeDim.isNotEmpty()) {
                // Get current primary and secondary graph type values
                val currentPrimaryGraphType = (viewData.value as? ChartViewData.Loaded)
                    ?.sleepViewData
                    ?.let { sleepViewData ->
                        if (sleepViewData is SleepViewData.Loaded) {
                            sleepViewData.comparisonViewData
                        } else null
                    }
                    ?.let { comparisonViewData ->
                        if (comparisonViewData is SleepComparisonViewData.Loaded) {
                            comparisonViewData.chartData.first()?.primaryGraphType
                        } else null
                    }
                
                val currentSecondaryGraphType = (viewData.value as? ChartViewData.Loaded)
                    ?.sleepViewData
                    ?.let { sleepViewData ->
                        if (sleepViewData is SleepViewData.Loaded) {
                            sleepViewData.comparisonViewData
                        } else null
                    }
                    ?.let { comparisonViewData ->
                        if (comparisonViewData is SleepComparisonViewData.Loaded) {
                            comparisonViewData.chartData.first()?.secondaryGraphType
                        } else null
                    }
                
                // Determine change content type
                val changedContent = when {
                    primaryGraphTypeChanged && secondaryGraphTypeChanged -> AnalyticsPropertyValue.ChangedContentProperty.ALL
                    primaryGraphTypeChanged -> AnalyticsPropertyValue.ChangedContentProperty.MAIN_GRAPH_TYPE
                    secondaryGraphTypeChanged -> AnalyticsPropertyValue.ChangedContentProperty.SUB_GRAPH_TYPE
                    else -> AnalyticsPropertyValue.ChangedContentProperty.ALL // Default case
                }
                
                val eventProperties = mutableMapOf<String, String>()
                eventProperties[AnalyticsEventProperty.PAGE_NAME] = AnalyticsPropertyValue.SleepChartChangedPageNameProperty.SLEEP_DETAILS_SCREEN
                eventProperties[AnalyticsEventProperty.TIME_DIM] = timeDim
                eventProperties[AnalyticsEventProperty.CHANGED_CONTENT] = changedContent
                
                // Only report chart type properties when both types exist
                val mainGraphTypeValue = currentPrimaryGraphType?.toAnalyticsMainGraphTypeValue()
                val subGraphTypeValue = currentSecondaryGraphType?.toAnalyticsSubGraphTypeValue()
                
                if (mainGraphTypeValue != null && subGraphTypeValue != null) {
                    eventProperties[AnalyticsEventProperty.MAIN_GRAPH_TYPE] = mainGraphTypeValue
                    eventProperties[AnalyticsEventProperty.SUB_GRAPH_TYPE] = subGraphTypeValue
                    
                    eventTracker.trackEvent(
                        AnalyticsEvent.SLEEP_CHART_CHANGED,
                        eventProperties
                    )
                }
                
                // Reset change status
                primaryGraphTypeChanged = false
                secondaryGraphTypeChanged = false
                lastPrimaryGraphTypeChange = null
                lastSecondaryGraphTypeChange = null
            }
        }
    }

    private companion object {
        const val UNKNOWN_CHART_PAGE_INDEX: Int = -1

        private val EPOCH_OF_SPORTS_TRACKER: LocalDate = LocalDate.of(RELEASED_YEAR, 1, 1)

        val SavedStateHandle.chartComparison: ChartComparison
            get() =
                requireNotNull(get<ChartComparison>(ChartActivity.KEY_CHART_COMPARISON))

        val SavedStateHandle.chartContent: ChartContent
            get() =
                requireNotNull(get<ChartContent>(ChartActivity.KEY_CHART_CONTENT))

        val SavedStateHandle.chartGranularity: ChartGranularity
            get() =
                requireNotNull(get<ChartGranularity>(ChartActivity.KEY_CHART_GRANULARITY))

        val SavedStateHandle.chartStyle: ChartStyle
            get() =
                requireNotNull(get<ChartStyle>(ChartActivity.KEY_CHART_STYLE))

        fun calculateChartPageCount(chartGranularity: ChartGranularity): Int =
            when (chartGranularity) {
                ChartGranularity.DAILY -> ChronoUnit.DAYS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    LocalDate.now()
                ).toInt() + 1

                ChartGranularity.WEEKLY -> ChronoUnit.WEEKS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    LocalDate.now()
                ).toInt() + 1

                ChartGranularity.SEVEN_DAYS -> (ChronoUnit.DAYS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    LocalDate.now()
                ).toInt() / 7) + 1

                ChartGranularity.MONTHLY -> ChronoUnit.MONTHS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    LocalDate.now()
                ).toInt() + 1

                ChartGranularity.THIRTY_DAYS -> (ChronoUnit.DAYS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    LocalDate.now()
                ).toInt() / 30) + 1

                ChartGranularity.SIXTY_DAYS -> (ChronoUnit.DAYS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    LocalDate.now()
                ).toInt() / 60) + 1

                ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS -> (ChronoUnit.DAYS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    LocalDate.now()
                ).toInt() / 180) + 1

                ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> (ChronoUnit.DAYS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    LocalDate.now()
                ).toInt() / 365) + 1

                ChartGranularity.SIX_WEEKS -> (ChronoUnit.WEEKS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    LocalDate.now()
                ).toInt() / 6) + 1

                ChartGranularity.SIX_MONTHS -> (ChronoUnit.MONTHS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    LocalDate.now()
                ).toInt() / 6) + 1

                ChartGranularity.YEARLY -> Year.now().value - EPOCH_OF_SPORTS_TRACKER.year + 1
                ChartGranularity.EIGHT_YEARS -> (Year.now().value - EPOCH_OF_SPORTS_TRACKER.year) / 8 + 1
            }

        fun calculatePageIndexForDate(
            chartGranularity: ChartGranularity,
            targetDate: LocalDate,
            userSettingsController: UserSettingsController,
            today: LocalDate = LocalDate.now()
        ): Int {
            val totalPageCount = calculateChartPageCount(chartGranularity)
            val targetPageIndex = when (chartGranularity) {
                ChartGranularity.DAILY -> ChronoUnit.DAYS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    targetDate
                ).toInt()

                ChartGranularity.WEEKLY -> {
                    val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                    val targetWeekStart =
                        targetDate.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                    val todayWeekStart =
                        today.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                    val weeksToGoBack =
                        ChronoUnit.WEEKS.between(targetWeekStart, todayWeekStart).toInt()
                    totalPageCount - 1 - weeksToGoBack
                }

                ChartGranularity.SEVEN_DAYS -> (ChronoUnit.DAYS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    targetDate
                ).toInt() / 7)

                ChartGranularity.MONTHLY -> ChronoUnit.MONTHS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    targetDate
                ).toInt()

                ChartGranularity.THIRTY_DAYS -> (ChronoUnit.DAYS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    targetDate
                ).toInt() / 30)

                ChartGranularity.SIXTY_DAYS -> (ChronoUnit.DAYS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    targetDate
                ).toInt() / 60)

                ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS -> (ChronoUnit.DAYS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    targetDate
                ).toInt() / 180)

                ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> (ChronoUnit.DAYS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    targetDate
                ).toInt() / 365)

                ChartGranularity.SIX_WEEKS -> (ChronoUnit.WEEKS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    targetDate
                ).toInt() / 6)

                ChartGranularity.SIX_MONTHS -> (ChronoUnit.MONTHS.between(
                    EPOCH_OF_SPORTS_TRACKER,
                    targetDate
                ).toInt() / 6)

                ChartGranularity.YEARLY -> targetDate.year - EPOCH_OF_SPORTS_TRACKER.year
                ChartGranularity.EIGHT_YEARS -> (targetDate.year - EPOCH_OF_SPORTS_TRACKER.year) / 8
            }
            return targetPageIndex.coerceIn(0, totalPageCount - 1)
        }
    }
}
