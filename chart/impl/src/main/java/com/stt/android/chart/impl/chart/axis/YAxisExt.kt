package com.stt.android.chart.impl.chart.axis

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberAxisGuidelineComponent
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberEnd
import com.patrykandpatrick.vico.compose.common.fill
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.axis.VerticalAxis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianValueFormatter
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.compose.theme.cloudyGrey
import kotlinx.collections.immutable.persistentListOf

@Composable
internal fun rememberYAxis(
    chartData: ChartData,
): VerticalAxis<Axis.Position.Vertical.End> {
    val context = LocalContext.current
    val itemPlacer = remember(chartData.goal, chartData.highlightDecorationLines) {
        YAxisItemPlacer(
            persistentListOf(
                chartData.goal
            ) + chartData.highlightDecorationLines.keys.toList(),
            colorIndicator = chartData.colorIndicator
        )
    }
    
    return VerticalAxis.rememberEnd(
        line = null,
        tick = null,
        guideline = rememberAxisGuidelineComponent(
            fill = fill(MaterialTheme.colorScheme.cloudyGrey),
        ),
        itemPlacer = itemPlacer,
        valueFormatter = remember(chartData.highlightDecorationLines, chartData.chartContent) {
            val highlightValues = chartData.highlightDecorationLines
            itemPlacer.createChartValueFormatter(
                highlightValues = highlightValues,
                defaultFormatter = CartesianValueFormatter.decimal(),
                chartContent = chartData.chartContent,
                context = context,
                chartData.needFormatYAxisLabel,
            )
        }
    )
}
