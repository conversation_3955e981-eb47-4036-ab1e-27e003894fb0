package com.stt.android.chart.impl.screen.components.sleep

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import com.stt.android.chart.impl.model.SleepPeriod
import com.stt.android.chart.impl.model.formatDuration
import com.stt.android.chart.impl.model.secondsToHourMinute
import com.stt.android.compose.theme.activityCycling
import com.stt.android.compose.theme.activitySleep
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.DualProgressBar
import java.util.Locale
import kotlin.math.abs
import kotlin.time.Duration
import com.stt.android.R as BR

@Composable
internal fun DailySleepDuration(
    longSleep: SleepPeriod?,
    naps: List<SleepPeriod>,
    longSleepDuration: Duration,
    napDuration: Duration,
    totalDuration: Duration,
    goalDuration: Duration,
    date: String,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                modifier = Modifier.weight(1f),
                text = totalDuration.inWholeSeconds.formatDuration(context),
                style = MaterialTheme.typography.bodyLargeBold,
                color = MaterialTheme.colorScheme.onSurface,
            )
            Text(
                text = stringResource(
                    BR.string.sleep_time_duration_of_target,
                    formatSleepTarget(totalDuration, goalDuration),
                ),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colorScheme.activitySleep,
            )
        }
        DualProgressBar(
            progress = (longSleepDuration / goalDuration).toFloat(),
            progressBarColor = MaterialTheme.colorScheme.activitySleep,
            secondaryProgress = (napDuration / goalDuration).toFloat(),
            secondaryProgressBarColor = MaterialTheme.colorScheme.activityCycling,
            backgroundColor = MaterialTheme.colorScheme.secondaryContainer,
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        ) {
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
            ) {
                val showDuration = listOfNotNull(longSleep, *naps.toTypedArray()).size > 1
                longSleep?.let {
                    SleepLegend(
                        color = MaterialTheme.colorScheme.activitySleep,
                        text = stringResource(
                            BR.string.sleep_time_duration_of_sleep,
                            "${it.fellAsleep} - ${it.wokeUp}",
                        ),
                        duration = it.totalDuration.inWholeSeconds.formatDuration(context),
                        showDuration = showDuration,
                    )
                }
                naps.forEach {
                    SleepLegend(
                        color = MaterialTheme.colorScheme.activityCycling,
                        text = stringResource(
                            BR.string.sleep_time_duration_of_nap,
                            "${it.fellAsleep} - ${it.wokeUp}",
                        ),
                        duration = it.totalDuration.inWholeSeconds.formatDuration(context),
                        showDuration = showDuration,
                    )
                }
            }
            Text(
                text = date,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface,
            )
        }
    }
}

private fun formatSleepTarget(totalDuration: Duration, goal: Duration): String {
    val diff = (totalDuration - goal).inWholeSeconds
    val sign = if (diff < 0L) "-" else "+"
    return abs(diff).secondsToHourMinute().let { (hours, minutes) ->
        String.format(Locale.US, "%s%d:%02d", sign, hours, minutes)
    }
}
