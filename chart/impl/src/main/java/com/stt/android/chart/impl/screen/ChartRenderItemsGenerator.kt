package com.stt.android.chart.impl.screen

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue

@Composable
internal fun generateRenderItems(
    viewData: ChartViewData.Loaded,
    onEvent: (ChartViewEvent) -> Unit
): List<ChartRenderItem> {
    val items = mutableListOf<ChartRenderItem>()
    
    items.add(ChartRenderItem.ChartSummaryAndPager(
        viewData = viewData,
        onEvent = onEvent
    ))
    
    val currentPageChartData = viewData.chartData[viewData.currentChartPage]?.value
    if (currentPageChartData?.currentValues?.isNotEmpty() == true) {
        items.add(ChartRenderItem.CurrentValues(viewData = viewData))
    }
    
    if (viewData.commuterTags !is CommuteTagsViewData.None) {
        items.add(ChartRenderItem.CommuterTags(
            viewData = viewData,
            onEvent = onEvent
        ))
    }
    
    if (viewData.sleepViewData !is SleepViewData.None) {
        val sleepViewData = viewData.sleepViewData as SleepViewData.Loaded
        
        items.add(ChartRenderItem.SleepStages(
            viewData = sleepViewData
        ))
        
        val metricsViewData = sleepViewData.dailyMetricsViewData
        if (metricsViewData is DailySleepMetricsViewData.Loaded) {
            items.add(ChartRenderItem.SleepQuality(
                viewData = sleepViewData,
                chartTimeRange = viewData.chartTimeRange
            ))
            
            items.add(ChartRenderItem.SleepDuration(
                viewData = sleepViewData,
                chartTimeRange = viewData.chartTimeRange
            ))
            
            items.add(ChartRenderItem.SleepResources(
                viewData = sleepViewData,
                chartTimeRange = viewData.chartTimeRange
            ))
        }
        
        items.add(ChartRenderItem.SleepComparisonChart(
            viewData = sleepViewData,
            chartTimeRange = viewData.chartTimeRange,
            onEvent = onEvent
        ))
    }
    
    if (viewData.goal !is GoalViewData.None) {
        items.add(ChartRenderItem.GoalSection(
            viewData = viewData,
            onEvent = onEvent
        ))
    }
    
    if (viewData.heartRateStatsData !is HeartRateStatsViewData.None) {
        items.add(ChartRenderItem.HeartRateStatsSelector(
            viewData = viewData,
            onEvent = onEvent
        ))
    }
    
    viewData.instructions.forEachIndexed { index, instruction ->
        items.add(ChartRenderItem.WidgetInstruction(
            instruction = instruction,
            index = index,
            onEvent = onEvent
        ))
    }

    if (viewData.availableActivityTypesViewData !is AvailableActivityTypesViewData.None) {
        val availableActivityTypesViewData = viewData.availableActivityTypesViewData as AvailableActivityTypesViewData.Loaded
        val availableActivityTypes by availableActivityTypesViewData.availableActivityTypes.collectAsState(initial = emptyList())
        if (availableActivityTypes.isNotEmpty()) {
            items.add(ChartRenderItem.ActivityHistoryHeader(
                availableActivityTypes = availableActivityTypes,
                selectedActivityTypeId = availableActivityTypesViewData.selectedActivityTypeId,
                onEvent = onEvent
            ))
        }
    }
    
    if (viewData.workoutHeaderViewData !is WorkoutHeaderViewData.None) {
        val workoutHeaderViewData = viewData.workoutHeaderViewData as WorkoutHeaderViewData.Loaded
        val workouts by workoutHeaderViewData.workoutHeaders.collectAsState(initial = emptyList())
        workouts.forEachIndexed { index, workout ->
            items.add(ChartRenderItem.WorkoutItem(
                workout = workout,
                index = index,
                isLastItem = index == workouts.size - 1,
                canLoadMore = workoutHeaderViewData.canLoadMore,
                isLoading = workoutHeaderViewData.isLoading,
                onEvent = onEvent
            ))
        }
    }
    
    return items
}
