package com.stt.android.chart.impl.screen.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.screen.ChartViewData
import com.stt.android.chart.impl.screen.CurrentValueData
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import com.stt.android.R as BR

@Composable
internal fun CurrentValues(
    viewData: ChartViewData.Loaded,
    modifier: Modifier = Modifier,
) {
    viewData.chartData[viewData.currentChartPage]
        ?.collectAsState(null)
        ?.value
        ?.currentValues
        ?.takeUnless(List<*>::isEmpty)
        ?.let { CurrentValues(it, modifier) }
}

@Composable
private fun CurrentValues(
    currentValues: ImmutableList<CurrentValueData>,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.padding(vertical = MaterialTheme.spacing.small),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
    ) {
        val rowCount = (currentValues.size + 1) / 2

        for (rowIndex in 0 until rowCount) {
            val startIndex = rowIndex * 2
            val itemsInThisRow = minOf(2, currentValues.size - startIndex)

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            ) {
                for (i in 0 until itemsInThisRow) {
                    val item = currentValues[startIndex + i]
                    CurrentValueItem(item, modifier = Modifier.weight(1f))
                }
            }
        }
    }
}

@Composable
private fun CurrentValueItem(
    currentValue: CurrentValueData,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        Text(
            text = currentValue.value,
            color = MaterialTheme.colorScheme.onSurface,
            style = MaterialTheme.typography.bodyXLargeBold,
        )
        Row(
            modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painterResource(currentValue.icon),
                tint = if (currentValue.iconColor == 0) {
                    Color.Unspecified
                } else {
                    colorResource(currentValue.iconColor)
                },
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.tiny),
            )
            Text(
                modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                text = currentValue.explanation,
                color = MaterialTheme.colorScheme.darkGrey,
                style = MaterialTheme.typography.bodySmall,
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewCurrentValues() {
    M3AppTheme {
        CurrentValues(
            currentValues = persistentListOf(
                CurrentValueData(
                    value = buildAnnotatedString {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                            append("1500")
                        }
                        append(" kcal")
                    },
                    explanation = "BMR",
                    icon = R.drawable.icon_bmr,
                    iconColor = 0
                ),
                CurrentValueData(
                    value = buildAnnotatedString {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                            append("2500")
                        }
                        append(" kcal")
                    },
                    explanation = "Total Calories",
                    icon = R.drawable.icon_bmr,
                    iconColor = 0
                ),
                CurrentValueData(
                    value = buildAnnotatedString {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                            append("2501")
                        }
                        append(" kcal")
                    },
                    explanation = "Total Calories",
                    icon = R.drawable.icon_bmr,
                    iconColor = 0
                ),
                CurrentValueData(
                    value = buildAnnotatedString {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                            append("2501")
                        }
                        append(" kcal")
                    },
                    explanation = "Total Calories",
                    icon = R.drawable.icon_bmr,
                    iconColor = 0
                )
            ),
            modifier = Modifier.padding(
                vertical = MaterialTheme.spacing.small,
                horizontal = MaterialTheme.spacing.medium,
            ),
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewCurrentValuesWithThreeItems() {
    M3AppTheme {
        CurrentValues(
            currentValues = persistentListOf(
                CurrentValueData(
                    value = buildAnnotatedString {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                            append("23")
                        }
                        append(" ms")
                    },
                    explanation = "Last night",
                    icon = BR.drawable.ic_hrv_12,
                    iconColor = 0
                ),
                CurrentValueData(
                    value = buildAnnotatedString {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                            append("32")
                        }
                        append(" ms")
                    },
                    explanation = "7-day avg.",
                    icon = BR.drawable.ic_hrv_12,
                    iconColor = 0
                ),
                CurrentValueData(
                    value = buildAnnotatedString {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                            append("18-36")
                        }
                        append(" ms")
                    },
                    explanation = "Recent normal range",
                    icon = BR.drawable.ic_hrv_12,
                    iconColor = 0
                )
            ),
            modifier = Modifier.padding(
                vertical = MaterialTheme.spacing.small,
                horizontal = MaterialTheme.spacing.medium,
            ),
        )
    }
}
