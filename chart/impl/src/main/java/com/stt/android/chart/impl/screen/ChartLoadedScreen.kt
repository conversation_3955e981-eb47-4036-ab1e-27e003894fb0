package com.stt.android.chart.impl.screen

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.stt.android.compose.util.LazyColumnVisibilityTracker
import com.stt.android.compose.util.TrackLazyColumnVisibility
import com.stt.android.chart.impl.screen.components.BackToCurrentButton
import com.stt.android.chart.impl.screen.components.ChartScreenTopBar
import com.stt.android.chart.impl.screen.components.ChartSelection
import com.stt.android.chart.impl.screen.components.ExtraChartGranularitySelection
import com.stt.android.chart.impl.screen.components.GoalEditor
import com.stt.android.chart.impl.screen.components.sleep.SleepComparisonGraphSelectionBottomSheet
import com.stt.android.chart.impl.screen.components.sleep.collectSleepHistoryItems
import com.stt.android.chart.impl.screen.components.sleep.sleepHistoryItems
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.spacing

@Composable
private fun rememberLazyListState(key1: Any, initial: Int = 0): LazyListState {
    return rememberSaveable(key1, saver = LazyListState.Saver) {
        LazyListState(firstVisibleItemIndex = 0, firstVisibleItemScrollOffset = initial)
    }
}

@Composable
internal fun ChartLoadedScreen(
    viewData: ChartViewData.Loaded,
    onEvent: (ChartViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    val lazyListState = rememberLazyListState(viewData.chartGranularity)
    var isReturnToCurrentButtonVisible by remember { mutableStateOf(false) }
    
    // Visibility tracking system
    val visibilityTracker = remember { LazyColumnVisibilityTracker() }

    val renderItems = generateRenderItems(
        viewData = viewData,
        onEvent = onEvent
    )

    val sleepHistoryItems = viewData.collectSleepHistoryItems()

    TrackLazyColumnVisibility(
        listState = lazyListState,
        tracker = visibilityTracker,
        onComponentVisible = { visibleComponents ->
            val analyticsComponents = visibleComponents
                .mapNotNull { key -> ChartItemKeys.mapToAnalyticsValue(key) }
                .toList()
            
            if (analyticsComponents.isNotEmpty()) {
                onEvent(ChartViewEvent.ModuleFullyVisible(analyticsComponents))
            }
        }
    )

    Scaffold(
        topBar = {
            ChartScreenTopBar(
                title = viewData.chartContentTitle,
                onEvent = onEvent,
            )
        },
        modifier = modifier,
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .narrowContent()
                .background(color = MaterialTheme.colorScheme.surface)
                .padding(paddingValues)
        ) {
            Column {
                ChartSelection(
                    viewData = viewData,
                    onEvent = onEvent,
                )

                LazyColumn(
                    state = lazyListState,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    items(
                        items = renderItems,
                        key = { item -> item.key }
                    ) { item ->
                        item.RenderContent()
                    }

                    sleepHistoryItems(items = sleepHistoryItems, onEvent = onEvent)

                    if (viewData.currentChartPage < viewData.chartPageCount - 1) {
                        item(key = "bottom_spacer") {
                            Spacer(
                                modifier = Modifier.height(112.dp)
                            )
                        }
                    }
                }
            }

            ExtraChartGranularitySelection(
                viewData = viewData,
                onEvent = onEvent,
            )

            SleepComparisonGraphSelectionBottomSheet(
                viewData = viewData.sleepViewData,
                onEvent = onEvent,
            )

            GoalEditor(
                viewData = viewData,
                onEvent = onEvent,
            )

            isReturnToCurrentButtonVisible = viewData.currentChartPage < viewData.chartPageCount - 1
            AnimatedVisibility(
                visible = isReturnToCurrentButtonVisible,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = MaterialTheme.spacing.xxxlarge),
                enter = slideInVertically(initialOffsetY = { it * 2 }),
                exit = slideOutVertically(targetOffsetY = { it * 2 }),
            ) {
                BackToCurrentButton(
                    onEvent = onEvent,
                )
            }
        }
    }
}
