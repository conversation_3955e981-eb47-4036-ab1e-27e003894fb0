package com.stt.android.chart.impl.data

import android.content.Context
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.PagingSource
import androidx.paging.PagingState
import androidx.paging.map
import com.stt.android.chart.api.model.ChartComparison
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartGranularity.DAILY
import com.stt.android.chart.api.model.ChartGranularity.EIGHT_YEARS
import com.stt.android.chart.api.model.ChartGranularity.MONTHLY
import com.stt.android.chart.api.model.ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.SEVEN_DAYS
import com.stt.android.chart.api.model.ChartGranularity.SIXTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.SIX_MONTHS
import com.stt.android.chart.api.model.ChartGranularity.SIX_WEEKS
import com.stt.android.chart.api.model.ChartGranularity.THIRTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS
import com.stt.android.chart.api.model.ChartGranularity.WEEKLY
import com.stt.android.chart.api.model.ChartGranularity.YEARLY
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.DailySleepDurationData
import com.stt.android.chart.impl.model.DailySleepMetricsData
import com.stt.android.chart.impl.model.DailySleepQualityData
import com.stt.android.chart.impl.model.DailySleepResourcesData
import com.stt.android.chart.impl.model.LineChartConfig
import com.stt.android.chart.impl.model.SleepComparisonChartData
import com.stt.android.chart.impl.model.SleepComparisonGraphType
import com.stt.android.chart.impl.model.SleepHistoryItem
import com.stt.android.chart.impl.model.SleepPeriod
import com.stt.android.chart.impl.model.SleepStageSummary
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.chart.impl.model.formatAverage
import com.stt.android.chart.impl.model.formatAverageEmpty
import com.stt.android.chart.impl.model.formatDuration
import com.stt.android.chart.impl.model.formatTime
import com.stt.android.chart.impl.model.secondsToHourMinute
import com.stt.android.chart.impl.screen.GoalEditorViewData
import com.stt.android.chart.impl.screen.GoalViewData
import com.stt.android.chart.impl.usecases.DailySleepHeartRateSeriesCreator
import com.stt.android.chart.impl.usecases.bloodOxygenYRangeConverter
import com.stt.android.chart.impl.usecases.bpmYRangeConverter
import com.stt.android.chart.impl.usecases.toNearest
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.activitydata.goals.FetchSleepGoalUseCase
import com.stt.android.domain.recovery.FetchRecoveryDataUseCase
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.sleep.SleepStage
import com.stt.android.domain.sleep.SleepStageInterval
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.domain.workouts.GetWorkoutHeadersForRangeUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dayviewv2.usecase.GenerateSleepQualityUseCase
import com.stt.android.ui.components.charts.model.ExtendedSleepStage
import com.stt.android.ui.components.charts.model.SleepRegion
import com.stt.android.ui.components.charts.model.SleepStageEntry
import com.stt.android.ui.components.charts.model.extended
import com.stt.android.ui.utils.DateFormatter
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.firstRecoveryDataForSleep
import com.stt.android.utils.lastRecoverDataForSleep
import com.stt.android.utils.toEpochMilli
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.TemporalAdjusters
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt
import kotlin.math.roundToLong
import kotlin.time.Duration
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import com.stt.android.R as BR
import com.stt.android.core.R as CR

internal class SleepDataLoader @Inject constructor(
    @param:ApplicationContext private val context: Context,
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val trendDataRepository: TrendDataRepository,
    private val fetchSleepUseCase: FetchSleepUseCase,
    private val fetchSleepGoalUseCase: FetchSleepGoalUseCase,
    private val generateSleepQualityUseCase: GenerateSleepQualityUseCase,
    private val fetchRecoveryDataUseCase: FetchRecoveryDataUseCase,
    private val getWorkoutHeadersForRangeUseCase: GetWorkoutHeadersForRangeUseCase,
    private val heartRateSeriesCreator: DailySleepHeartRateSeriesCreator,
    private val dateFormatter: DateFormatter,
) {
    fun formatDailyHighlightData(startSeconds: Long, endSeconds: Long): String {
        return (endSeconds - startSeconds).formatDuration(context)
    }

    fun formatHighlightData(hours: Float): String {
        return (hours * 3600L).roundToLong().formatDuration(context)
    }

    fun formatDailyHighlightDateTime(startSeconds: Long, endSeconds: Long) = buildString {
        val startDate = ZonedDateTime
            .ofInstant(Instant.ofEpochSecond(startSeconds), ZoneId.systemDefault())
            .toLocalDate()
        val endDate = ZonedDateTime
            .ofInstant(Instant.ofEpochSecond(endSeconds), ZoneId.systemDefault())
            .toLocalDate()
        dateFormatter.formatDate(startDate).let(::append)
        append(", ")
        TextFormatter.formatTime(context, startSeconds.seconds.inWholeMilliseconds).let(::append)
        append('-')
        if (startDate != endDate) {
            dateFormatter.formatDate(endDate).let(::append)
            append(", ")
        }
        TextFormatter.formatTime(context, endSeconds.seconds.inWholeMilliseconds).let(::append)
    }

    fun loadGoalData() = GoalViewData.Goal(
        icon = R.drawable.ic_sleep_goal,
        iconColor = BR.color.activity_data_sleep,
        title = R.string.chart_daily_sleep_target,
        goal = fetchSleepGoalUseCase.fetchSleepGoal()
            .map { it.inWholeSeconds.formatDuration(context) },
    )

    suspend fun loadGoalEditorData() = GoalEditorViewData.Editor(
        chartContent = ChartContent.SLEEP,
        requiresWatchConnection = true,
        currentGoal = fetchSleepGoalUseCase.fetchSleepGoal().first().inWholeSeconds.toInt(),
    )

    fun loadStagesData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
    ): Flow<List<SleepStageSummary>> {
        return sleepForDateRangeFlow(from, to).map { sleepList ->
            val totalAverage = Average()
            val awakeAverage = Average()
            val remAverage = Average()
            val lightAverage = Average()
            val deepAverage = Average()
            sleepList.forEach { sleep ->
                sleep.longSleep
                    ?.takeIf { it.sleepDuration > Duration.ZERO }
                    ?.let { longSleep ->
                        val awakeSeconds = longSleep.awakeDuration
                            ?.takeIf { it > Duration.ZERO }
                            ?.inWholeSeconds
                            ?.toFloat() ?: 0f
                        val remSeconds = longSleep.remSleepDuration
                            ?.takeIf { it > Duration.ZERO }
                            ?.inWholeSeconds
                            ?.toFloat() ?: 0f
                        val lightSeconds = longSleep.lightSleepDuration
                            ?.takeIf { it > Duration.ZERO }
                            ?.inWholeSeconds
                            ?.toFloat() ?: 0f
                        val deepSeconds = longSleep.deepSleepDuration
                            ?.takeIf { it > Duration.ZERO }
                            ?.inWholeSeconds
                            ?.toFloat() ?: 0f
                        val totalSeconds = awakeSeconds + remSeconds + lightSeconds + deepSeconds
                        totalAverage.feed(totalSeconds)
                        awakeAverage.feed(awakeSeconds)
                        remAverage.feed(remSeconds)
                        lightAverage.feed(lightSeconds)
                        deepAverage.feed(deepSeconds)
                    }
            }
            totalAverage.result.takeIf { it > 0f }?.let { total ->
                buildList {
                    remAverage.result.let { seconds ->
                        add(
                            SleepStageSummary(
                                SleepStage.REM,
                                if (chartGranularity == DAILY) BR.string.sleep_stages_rem else R.string.sleep_stages_avg_rem,
                                BR.color.sleep_rem,
                                seconds.roundToLong(),
                                (seconds / total).roundToTwoDecimals(),
                            )
                        )
                    }
                    lightAverage.result.let { seconds ->
                        add(
                            SleepStageSummary(
                                SleepStage.LIGHT,
                                if (chartGranularity == DAILY) BR.string.sleep_stages_light else R.string.sleep_stages_avg_light,
                                BR.color.sleep_core,
                                seconds.roundToLong(),
                                (seconds / total).roundToTwoDecimals(),
                            )
                        )
                    }
                    deepAverage.result.let { seconds ->
                        add(
                            SleepStageSummary(
                                SleepStage.DEEP,
                                if (chartGranularity == DAILY) BR.string.sleep_stages_deep else R.string.sleep_stages_avg_deep,
                                BR.color.sleep_deep,
                                seconds.roundToLong(),
                                (seconds / total).roundToTwoDecimals(),
                            )
                        )
                    }
                    add(
                        0,
                        SleepStageSummary(
                            SleepStage.AWAKE,
                            if (chartGranularity == DAILY) BR.string.sleep_stages_awake else R.string.sleep_stages_avg_awake,
                            BR.color.sleep_awake,
                            awakeAverage.result.roundToLong(),
                            (1f - this.map { it.percent }.sum()).roundToTwoDecimals(),
                        )
                    )
                }
            } ?: emptyList()
        }
    }

    fun loadDailyMetricsData(
        from: LocalDate,
        to: LocalDate,
    ): Flow<DailySleepMetricsData> {
        fun generateSleepQualityData(sleep: Sleep, sleepList: List<Sleep>): DailySleepQualityData? {
            val quality = generateSleepQualityUseCase(sleep, sleepList) ?: return null

            val longSleep = sleep.longSleep ?: return null

            val measurementUnit = userSettingsController.settings.measurementUnit

            return DailySleepQualityData(
                quality = quality.first,
                qualityDesc = quality.second?.let { context.getString(it) },
                avgHr = longSleep.avgHr,
                minHr = longSleep.minHr,
                avgHrv = longSleep.avgHrv?.roundToInt(),
                maxSpO2 = longSleep.maxSpO2,
                altitude = longSleep.altitude?.let { measurementUnit.toAltitudeUnit(it.toDouble()) },
                altitudeUnitRes = measurementUnit.altitudeUnit,
            )
        }

        fun generateSleepDurationData(sleep: Sleep): DailySleepDurationData? {
            if (sleep.totalSleepDuration <= Duration.ZERO) return null

            return DailySleepDurationData(
                longSleep = sleep.longSleep?.let {
                    SleepPeriod(
                        fellAsleep = it.fellAsleep.roundToNearestMinute().toZonedDateTime()
                            .formatTime(),
                        wokeUp = it.wokeUp.roundToNearestMinute().toZonedDateTime().formatTime(),
                        totalDuration = it.sleepDuration,
                    )
                },
                naps = sleep.naps.map {
                    SleepPeriod(
                        fellAsleep = it.fellAsleep.roundToNearestMinute().toZonedDateTime()
                            .formatTime(),
                        wokeUp = it.wokeUp.roundToNearestMinute().toZonedDateTime().formatTime(),
                        totalDuration = it.duration,
                    )
                },
                longSleepDuration = sleep.longSleep?.sleepDuration ?: Duration.ZERO,
                napDuration = sleep.getMergedNap()?.duration ?: Duration.ZERO,
                totalDuration = sleep.totalSleepDuration,
            )
        }

        fun generateResourcesData(
            sleep: Sleep,
            recoveryList: List<RecoveryData>,
        ): DailySleepResourcesData? {
            val longSleep = sleep.longSleep ?: return null
            val lastRecovery = recoveryList.lastRecoverDataForSleep(longSleep.wokeUp) ?: return null
            val firstRecovery = recoveryList.firstRecoveryDataForSleep(longSleep.fellAsleep)
            val gained = if (firstRecovery != null && firstRecovery != lastRecovery) {
                lastRecovery.balance - firstRecovery.balance
            } else null
            return DailySleepResourcesData(lastRecovery.balance, gained)
        }
        return combine(
            sleepForDateRangeFlow(from.minusDays(2), to),
            fetchSleepGoalUseCase.fetchSleepGoal(),
            recoveryForDateRangeFlow(from, to),
        ) { sleepList, sleepGoal, recoveryList ->
            val sleep = sleepList.firstOrNull { it.timestamp.toLocalDate() in from..to }
            if (sleep != null) {
                DailySleepMetricsData(
                    sleepQuality = generateSleepQualityData(sleep, sleepList),
                    sleepDuration = generateSleepDurationData(sleep),
                    resources = generateResourcesData(sleep, recoveryList),
                    sleepGoal = sleepGoal,
                )
            } else {
                DailySleepMetricsData(
                    sleepQuality = null,
                    sleepDuration = null,
                    resources = null,
                    sleepGoal = sleepGoal,
                )
            }
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    fun loadComparisonChartData(
        chartGranularity: ChartGranularity,
        primaryGraphType: SleepComparisonGraphType,
        secondaryGraphType: SleepComparisonGraphType,
        from: LocalDate,
        to: LocalDate,
    ): Flow<SleepComparisonChartData> {
        return sleepForDateRangeFlow(from, to).flatMapLatest { sleepList ->
            combine(
                createGraphData(sleepList, chartGranularity, primaryGraphType, from, to),
                createGraphData(sleepList, chartGranularity, secondaryGraphType, from, to),
            ) { primaryGraphData, secondaryGraphData ->
                SleepComparisonChartData(
                    chartGranularity = chartGranularity,
                    primaryGraphType = primaryGraphType,
                    primarySeries = primaryGraphData!!,
                    secondaryGraphType = secondaryGraphType,
                    secondarySeries = secondaryGraphData,
                )
            }
        }
    }

    private fun createGraphData(
        sleepList: List<Sleep>,
        chartGranularity: ChartGranularity,
        graphType: SleepComparisonGraphType,
        from: LocalDate,
        to: LocalDate,
    ): Flow<SleepComparisonChartData.Series?> {
        return when (graphType) {
            SleepComparisonGraphType.SLEEP_DURATION -> flowOf(
                if (sleepList.isNotEmpty()) {
                    createSleepDurationGraphData(chartGranularity, from, to, sleepList)
                } else {
                    createEmptyGraphData(
                        chartGranularity = chartGranularity,
                        from = from,
                        to = to,
                        chartType = ChartType.BAR,
                        yRange = 0.0 to 9.0,
                        formatedAverage = graphType.formatAverageEmpty(context),
                    )
                }
            )

            SleepComparisonGraphType.SLEEP_REGULARITY -> flowOf(
                if (sleepList.isNotEmpty()) {
                    createSleepRegularityGraphData(chartGranularity, from, to, sleepList)
                } else {
                    createEmptyGraphData(
                        chartGranularity = chartGranularity,
                        from = from,
                        to = to,
                        chartType = ChartType.CANDLESTICK,
                        yRange = (22 * 3600.0) to ((24 + 7) * 3600.0),
                        formatedAverage = graphType.formatAverageEmpty(context),
                    )
                }
            )

            SleepComparisonGraphType.SLEEP_NAP -> flowOf(
                if (sleepList.isNotEmpty()) {
                    createSleepNapGraphData(chartGranularity, from, to, sleepList)
                } else {
                    createEmptyGraphData(
                        chartGranularity = chartGranularity,
                        from = from,
                        to = to,
                        chartType = ChartType.BAR,
                        yRange = 0.0 to 9.0,
                        formatedAverage = graphType.formatAverageEmpty(context),
                    )
                }
            )

            SleepComparisonGraphType.SLEEP_TOTAL -> flowOf(
                if (sleepList.isNotEmpty()) {
                    createSleepTotalGraphData(chartGranularity, from, to, sleepList)
                } else {
                    createEmptyGraphData(
                        chartGranularity = chartGranularity,
                        from = from,
                        to = to,
                        chartType = ChartType.BAR,
                        yRange = 0.0 to 9.0,
                        formatedAverage = graphType.formatAverageEmpty(context),
                    )
                }
            )

            SleepComparisonGraphType.BLOOD_OXYGEN -> flowOf(
                if (sleepList.isNotEmpty()) {
                    createBloodOxygenGraphData(chartGranularity, from, to, sleepList)
                } else {
                    createEmptyGraphData(
                        chartGranularity = chartGranularity,
                        from = from,
                        to = to,
                        chartType = ChartType.LINE,
                        yRange = 85.0 to 100.0,
                        formatedAverage = graphType.formatAverageEmpty(context),
                    )
                }
            )

            SleepComparisonGraphType.TRAINING -> workoutForDateRangeFlow(from, to).map {
                if (it.isNotEmpty()) {
                    createTrainingGraphData(chartGranularity, from, to, it)
                } else {
                    createEmptyGraphData(
                        chartGranularity = chartGranularity,
                        from = from,
                        to = to,
                        chartType = ChartType.LINE,
                        yRange = 0.0 to 3.0,
                        formatedAverage = graphType.formatAverageEmpty(context),
                    )
                }
            }

            SleepComparisonGraphType.MIN_HR_DURING_SLEEP -> flowOf(
                if (sleepList.isNotEmpty()) {
                    createMinHrDuringSleepGraphData(chartGranularity, from, to, sleepList)
                } else {
                    createEmptyGraphData(
                        chartGranularity = chartGranularity,
                        from = from,
                        to = to,
                        chartType = ChartType.LINE,
                        yRange = 40.0 to 100.0,
                        formatedAverage = graphType.formatAverageEmpty(context),
                    )
                }
            )

            SleepComparisonGraphType.AVG_HR_DURING_SLEEP -> flowOf(
                if (sleepList.isNotEmpty()) {
                    createAvgHrDuringSleepGraphData(chartGranularity, from, to, sleepList)
                } else {
                    createEmptyGraphData(
                        chartGranularity = chartGranularity,
                        from = from,
                        to = to,
                        chartType = ChartType.LINE,
                        yRange = 40.0 to 100.0,
                        formatedAverage = graphType.formatAverageEmpty(context),
                    )
                }
            )

            SleepComparisonGraphType.MORNING_RESOURCES -> recoveryForDateRangeFlow(from, to).map {
                if (it.isNotEmpty()) {
                    createMorningResourcesGraphData(chartGranularity, from, to, sleepList, it)
                } else {
                    createEmptyGraphData(
                        chartGranularity = chartGranularity,
                        from = from,
                        to = to,
                        chartType = ChartType.LINE,
                        yRange = 0.0 to 120.0,
                        formatedAverage = graphType.formatAverageEmpty(context),
                    )
                }
            }

            SleepComparisonGraphType.NONE -> flowOf(null)
        }
    }

    private fun createEmptyGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        chartType: ChartType,
        yRange: Pair<Double, Double>,
        formatedAverage: String,
    ): SleepComparisonChartData.Series {
        val (minX, maxX) = when (chartGranularity) {
            WEEKLY,
            SEVEN_DAYS,
            MONTHLY,
            THIRTY_DAYS,
            SIX_WEEKS -> from.toEpochDay().toDouble() to to.toEpochDay().toDouble()

            SIX_MONTHS -> from.toEpochDay().toDouble() to to.toEpochDay().toDouble()

            YEARLY -> from.epochMonth.toDouble() to to.epochMonth.toDouble()

            DAILY,
            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS,
            EIGHT_YEARS -> throw Exception("Unsupported chart granularity: $chartGranularity")
        }
        return SleepComparisonChartData.Series(
            chartType = chartType,
            axisRange = SleepComparisonChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = yRange.first,
                maxY = yRange.second,
            ),
            entries = listOf(emptyList()),
            average = 0f,
            formatedAverage = formatedAverage,
        )
    }

    private fun createSleepDurationGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ) = createSleepGraphData(
        chartGranularity = chartGranularity,
        chartType = ChartType.BAR,
        from = from,
        to = to,
        sleepList = sleepList,
        dataExtractor = { it.longSleep?.sleepDuration?.toHours() },
        dataConverter = { it },
        yRangeConverter = { _, max -> Pair(0f, max.toNearest()) },
        averageFormatter = { SleepComparisonGraphType.SLEEP_DURATION.formatAverage(context, it) },
    )

    private fun createSleepRegularityGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ): SleepComparisonChartData.Series {
        var minX: Double
        var maxX: Double
        var minY = Float.MAX_VALUE
        var maxY = 0f
        val lowAverage = Average()
        val highAverage = Average()
        val validSleepList = sleepList.filter {
            val longSleep = it.longSleep
            longSleep != null && longSleep.fellAsleep > 0L && longSleep.wokeUp > 0L && longSleep.wokeUp > longSleep.fellAsleep
        }
        val secondsInDay = TimeUnit.HOURS.toSeconds(24)
        fun sleepRange(sleep: Sleep): Pair<Float, Float> {
            val startSeconds = sleep.longSleep!!.fellAsleep.secondsFromStartOfDay()
            val endSeconds = sleep.longSleep!!.wokeUp.secondsFromStartOfDay()
            val startAdjustment = if (endSeconds >= startSeconds) {
                secondsInDay
            } else {
                0L
            }
            return (startSeconds + startAdjustment).toFloat() to (endSeconds + secondsInDay).toFloat()
        }

        val entries = when (chartGranularity) {
            WEEKLY,
            SEVEN_DAYS,
            MONTHLY,
            THIRTY_DAYS,
            SIX_WEEKS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                validSleepList.map { sleep ->
                    val (low, high) = sleepRange(sleep)
                    lowAverage.feed(low)
                    highAverage.feed(high)
                    minY = min(minY, low)
                    maxY = max(maxY, high)
                    SleepComparisonChartData.Entry(
                        x = sleep.timestamp.toLocalDate().toEpochDay(),
                        high = high,
                        low = low,
                    )
                }
            }

            SIX_MONTHS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                validSleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate()
                            .with((TemporalAdjusters.previousOrSame(firstDayOfWeek)))
                    }
                    .map { (startOfWeek, weeklySleepList) ->
                        val weeklyLowAverage = Average()
                        val weeklyHighAverage = Average()
                        weeklySleepList.forEach { sleep ->
                            val (low, high) = sleepRange(sleep)
                            lowAverage.feed(low)
                            highAverage.feed(high)
                            weeklyLowAverage.feed(low)
                            weeklyHighAverage.feed(high)
                        }
                        minY = min(minY, weeklyLowAverage.result)
                        maxY = max(maxY, weeklyHighAverage.result)
                        SleepComparisonChartData.Entry(
                            x = startOfWeek.toEpochDay(),
                            high = weeklyHighAverage.result,
                            low = weeklyLowAverage.result,
                        )
                    }
            }

            YEARLY -> {
                minX = from.epochMonth.toDouble()
                maxX = to.epochMonth.toDouble()
                validSleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate().epochMonth
                    }
                    .map { (epochMonth, monthlySleepList) ->
                        val monthlyLowAverage = Average()
                        val monthlyHighAverage = Average()
                        monthlySleepList.forEach { sleep ->
                            val (low, high) = sleepRange(sleep)
                            lowAverage.feed(low)
                            highAverage.feed(high)
                            monthlyLowAverage.feed(low)
                            monthlyHighAverage.feed(high)
                        }
                        minY = min(minY, monthlyLowAverage.result)
                        maxY = max(maxY, monthlyHighAverage.result)
                        SleepComparisonChartData.Entry(
                            x = epochMonth.toLong(),
                            high = monthlyHighAverage.result,
                            low = monthlyLowAverage.result,
                        )
                    }
            }

            DAILY,
            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS,
            EIGHT_YEARS -> throw Exception("Unsupported chart granularity: $chartGranularity")
        }
        if (minY == Float.MAX_VALUE && maxY == 0f) {
            minY = 21 * 3600f
            maxY = (24 + 9) * 3600f
        }
        return SleepComparisonChartData.Series(
            chartType = ChartType.CANDLESTICK,
            axisRange = SleepComparisonChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY.toDouble(),
                maxY = maxY.toDouble(),
            ),
            entries = listOf(entries),
            average = 0f,
            formatedAverage = if (lowAverage.result > 0f && highAverage.result > 0f) {
                buildString {
                    append(context.getString(BR.string.avg))
                    append(' ')
                    append(lowAverage.result.toLong().formatTime())
                    append('-')
                    append(highAverage.result.toLong().formatTime())
                }
            } else "",
        )
    }

    private fun createSleepNapGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ) = createSleepGraphData(
        chartGranularity = chartGranularity,
        chartType = ChartType.BAR,
        from = from,
        to = to,
        sleepList = sleepList,
        dataExtractor = { it.getMergedNap()?.duration?.toHours() },
        dataConverter = { it },
        yRangeConverter = { _, max -> Pair(0f, max.toNearest()) },
        averageFormatter = { SleepComparisonGraphType.SLEEP_NAP.formatAverage(context, it) },
    )

    private fun createSleepTotalGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ): SleepComparisonChartData.Series {
        var minX: Double
        var maxX: Double
        var maxY = 0f
        val average = Average()
        val longSleepEntries = mutableListOf<SleepComparisonChartData.Entry>()
        val napEntries = mutableListOf<SleepComparisonChartData.Entry>()
        when (chartGranularity) {
            WEEKLY,
            SEVEN_DAYS,
            MONTHLY,
            THIRTY_DAYS,
            SIX_WEEKS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                sleepList.forEach { sleep ->
                    val x = sleep.timestamp.toLocalDate().toEpochDay()
                    sleep.longSleep
                        ?.sleepDuration
                        ?.takeIf { it > Duration.ZERO }
                        ?.let { value ->
                            longSleepEntries.add(
                                SleepComparisonChartData.Entry(
                                    x = x,
                                    high = value.toHours(),
                                )
                            )
                        }
                    sleep.getMergedNap()
                        ?.duration
                        ?.takeIf { it > Duration.ZERO }
                        ?.let { value ->
                            napEntries.add(
                                SleepComparisonChartData.Entry(
                                    x = x,
                                    high = value.toHours(),
                                )
                            )
                        }
                    sleep.totalSleepDuration
                        .takeIf { it > Duration.ZERO }
                        ?.inWholeSeconds
                        ?.toFloat()
                        ?.let { value ->
                            maxY = max(maxY, value)
                            average.feed(value)
                        }
                }
            }

            SIX_MONTHS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                sleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate()
                            .with((TemporalAdjusters.previousOrSame(firstDayOfWeek)))
                    }
                    .forEach { (startOfWeek, weeklySleepList) ->
                        val weeklyLongSleepAverage = Average()
                        val weeklyNapAverage = Average()
                        val weeklyTotalAverage = Average()
                        weeklySleepList.forEach { sleep ->
                            (sleep.longSleep?.sleepDuration ?: Duration.ZERO).let { value ->
                                weeklyLongSleepAverage.feed(value.inWholeSeconds.toFloat())
                            }
                            (sleep.getMergedNap()?.duration ?: Duration.ZERO).let { value ->
                                weeklyNapAverage.feed(value.inWholeSeconds.toFloat())
                            }
                            sleep.totalSleepDuration
                                .inWholeSeconds
                                .toFloat()
                                .let { value ->
                                    weeklyTotalAverage.feed(value)
                                    average.feed(value)
                                }
                        }
                        val x = startOfWeek.toEpochDay()
                        weeklyLongSleepAverage.result.takeIf { it > 0f }?.let { value ->
                            longSleepEntries.add(
                                SleepComparisonChartData.Entry(
                                    x = x,
                                    high = value.toHours(),
                                )
                            )
                        }
                        weeklyNapAverage.result.takeIf { it > 0f }?.let { value ->
                            napEntries.add(
                                SleepComparisonChartData.Entry(
                                    x = x,
                                    high = value.toHours(),
                                )
                            )
                        }
                        weeklyTotalAverage.result.takeIf { it > 0f }?.let { value ->
                            maxY = max(maxY, value)
                        }
                    }
            }

            YEARLY -> {
                minX = from.epochMonth.toDouble()
                maxX = to.epochMonth.toDouble()
                sleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate().epochMonth
                    }
                    .forEach { (epochMonth, monthlySleepList) ->
                        val monthlyLongSleepAverage = Average()
                        val monthlyNapAverage = Average()
                        val monthlyTotalAverage = Average()
                        monthlySleepList.forEach { sleep ->
                            (sleep.longSleep?.sleepDuration ?: Duration.ZERO).let { value ->
                                monthlyLongSleepAverage.feed(value.inWholeSeconds.toFloat())
                            }
                            (sleep.getMergedNap()?.duration ?: Duration.ZERO).let { value ->
                                monthlyNapAverage.feed(value.inWholeSeconds.toFloat())
                            }
                            sleep.totalSleepDuration
                                .inWholeSeconds
                                .toFloat()
                                .let { value ->
                                    monthlyTotalAverage.feed(value)
                                    average.feed(value)
                                }
                        }
                        val x = epochMonth.toLong()
                        monthlyLongSleepAverage.result.takeIf { it > 0f }?.let { value ->
                            longSleepEntries.add(
                                SleepComparisonChartData.Entry(
                                    x = x,
                                    high = value.toHours(),
                                )
                            )
                        }
                        monthlyNapAverage.result.takeIf { it > 0f }?.let { value ->
                            napEntries.add(
                                SleepComparisonChartData.Entry(
                                    x = x,
                                    high = value.toHours(),
                                )
                            )
                        }
                        monthlyTotalAverage.result.takeIf { it > 0f }?.let { value ->
                            maxY = max(maxY, value)
                        }
                    }
            }

            DAILY,
            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS,
            EIGHT_YEARS -> throw Exception("Unsupported chart granularity: $chartGranularity")
        }
        val convertedAverage = average.result.toHours()
        return SleepComparisonChartData.Series(
            chartType = ChartType.BAR,
            axisRange = SleepComparisonChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = 0.0,
                maxY = maxY.toHours().toNearest().toDouble(),
            ),
            entries = listOf(longSleepEntries.toList(), napEntries.toList()),
            average = convertedAverage,
            formatedAverage = SleepComparisonGraphType.SLEEP_TOTAL.formatAverage(
                context,
                convertedAverage,
            ),
        )
    }

    private fun createBloodOxygenGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ) = createSleepGraphData(
        chartGranularity = chartGranularity,
        chartType = ChartType.LINE,
        from = from,
        to = to,
        sleepList = sleepList,
        dataExtractor = { it.longSleep?.maxSpO2 },
        dataConverter = { it.toPercent() },
        yRangeConverter = ::bloodOxygenYRangeConverter,
        averageFormatter = { SleepComparisonGraphType.BLOOD_OXYGEN.formatAverage(context, it) },
    )

    private fun createTrainingGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        workoutList: List<WorkoutHeader>,
    ): SleepComparisonChartData.Series {
        var minX: Double
        var maxX: Double
        var maxY = 0.0
        val average = Average()
        val entries = when (chartGranularity) {
            WEEKLY,
            SEVEN_DAYS,
            MONTHLY,
            THIRTY_DAYS,
            SIX_WEEKS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                workoutList
                    .groupBy { workout ->
                        workout.startTime.toLocalDate()
                    }
                    .mapNotNull { (date, dailyWorkoutList) ->
                        dailyWorkoutList.sumOf { it.totalTime }.takeIf { it > 0f }?.let { value ->
                            maxY = max(maxY, value)
                            average.feed(value.toFloat())
                            SleepComparisonChartData.Entry(
                                x = date.toEpochDay(),
                                high = value.toFloat().toHours(),
                            )
                        }
                    }
            }

            SIX_MONTHS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                workoutList
                    .groupBy { workout ->
                        workout.startTime.toLocalDate()
                            .with((TemporalAdjusters.previousOrSame(firstDayOfWeek)))
                    }
                    .mapNotNull { (startOfWeek, weeklyWorkoutList) ->
                        val weeklyAverage = Average()
                        weeklyWorkoutList
                            .groupBy { workout ->
                                workout.startTime.toLocalDate()
                            }.forEach { (_, dailyWorkoutList) ->
                                dailyWorkoutList.sumOf { it.totalTime }.takeIf { it > 0f }
                                    ?.let { value ->
                                        weeklyAverage.feed(value.toFloat())
                                        average.feed(value.toFloat())
                                    }
                            }
                        weeklyAverage.result.takeIf { it > 0f }?.let { value ->
                            maxY = max(maxY, value.toDouble())
                            SleepComparisonChartData.Entry(
                                x = startOfWeek.toEpochDay(),
                                high = value.toHours(),
                            )
                        }
                    }
            }

            YEARLY -> {
                minX = from.epochMonth.toDouble()
                maxX = to.epochMonth.toDouble()
                workoutList
                    .groupBy { sleep ->
                        sleep.startTime.toLocalDate().epochMonth
                    }
                    .mapNotNull { (epochMonth, monthlyWorkoutList) ->
                        val monthlyAverage = Average()
                        monthlyWorkoutList
                            .groupBy { workout ->
                                workout.startTime.toLocalDate()
                            }.forEach { (_, dailyWorkoutList) ->
                                dailyWorkoutList.sumOf { it.totalTime }.takeIf { it > 0f }
                                    ?.let { value ->
                                        monthlyAverage.feed(value.toFloat())
                                        average.feed(value.toFloat())
                                    }
                            }
                        monthlyAverage.result.takeIf { it > 0f }?.let { value ->
                            maxY = max(maxY, value.toDouble())
                            SleepComparisonChartData.Entry(
                                x = epochMonth.toLong(),
                                high = value.toHours(),
                            )
                        }
                    }
            }

            DAILY,
            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS,
            EIGHT_YEARS -> throw Exception("Unsupported chart granularity: $chartGranularity")
        }
        val convertedAverage = average.result.toHours()
        return SleepComparisonChartData.Series(
            chartType = ChartType.LINE,
            axisRange = SleepComparisonChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = 0.0,
                maxY = maxY.toFloat().toHours().toNearest().toDouble(),
            ),
            entries = listOf(entries),
            average = convertedAverage,
            formatedAverage = SleepComparisonGraphType.TRAINING.formatAverage(
                context,
                convertedAverage,
            ),
        )
    }

    private fun createMinHrDuringSleepGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ) = createSleepGraphData(
        chartGranularity = chartGranularity,
        chartType = ChartType.LINE,
        from = from,
        to = to,
        sleepList = sleepList,
        dataExtractor = { it.longSleep?.minHr?.inBpm?.toFloat() },
        dataConverter = { it },
        yRangeConverter = ::bpmYRangeConverter,
        averageFormatter = {
            SleepComparisonGraphType.MIN_HR_DURING_SLEEP.formatAverage(context, it)
        },
    )

    private fun createAvgHrDuringSleepGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ) = createSleepGraphData(
        chartGranularity = chartGranularity,
        chartType = ChartType.LINE,
        from = from,
        to = to,
        sleepList = sleepList,
        dataExtractor = { it.longSleep?.avgHr?.inBpm?.toFloat() },
        dataConverter = { it },
        yRangeConverter = ::bpmYRangeConverter,
        averageFormatter = {
            SleepComparisonGraphType.AVG_HR_DURING_SLEEP.formatAverage(context, it)
        },
    )

    private fun createMorningResourcesGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
        recoveryList: List<RecoveryData>,
    ): SleepComparisonChartData.Series {
        var recoveryIteratorNextIndex = 0
        val allowedDelta = 90.minutes.inWholeMilliseconds
        fun dataExtractor(sleep: Sleep): Float {
            val wokeUp = sleep.longSleep?.wokeUp ?: return 0f
            val iterator = recoveryList.listIterator(recoveryIteratorNextIndex)
            while (iterator.hasNext()) {
                val recoveryData = iterator.next()
                if (recoveryData.timestamp <= wokeUp) {
                    if (recoveryData.timestamp >= wokeUp - allowedDelta) {
                        recoveryIteratorNextIndex = iterator.nextIndex()
                        return recoveryData.balance
                    }
                    return 0f
                }
            }
            return 0f
        }
        return createSleepGraphData(
            chartGranularity = chartGranularity,
            chartType = ChartType.LINE,
            from = from,
            to = to,
            sleepList = sleepList.reversed(),
            dataExtractor = ::dataExtractor,
            dataConverter = { it.toPercent() },
            yRangeConverter = { _, _ -> 0f to 120f },
            averageFormatter = {
                SleepComparisonGraphType.MORNING_RESOURCES.formatAverage(context, it)
            },
        )
    }

    private fun createSleepGraphData(
        chartGranularity: ChartGranularity,
        chartType: ChartType,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
        dataExtractor: (Sleep) -> Float?,
        dataConverter: (Float) -> Float,
        yRangeConverter: (Float, Float) -> Pair<Float, Float>,
        averageFormatter: (Float) -> String,
    ): SleepComparisonChartData.Series {
        var minX: Double
        var maxX: Double
        var minY = Float.MAX_VALUE
        var maxY = 0f
        val average = Average()
        val entries = when (chartGranularity) {
            WEEKLY,
            SEVEN_DAYS,
            MONTHLY,
            THIRTY_DAYS,
            SIX_WEEKS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                sleepList
                    .mapNotNull { sleep ->
                        dataExtractor(sleep)?.takeIf { it > 0f }?.let { value ->
                            minY = min(minY, value)
                            maxY = max(maxY, value)
                            average.feed(value)
                            SleepComparisonChartData.Entry(
                                x = sleep.timestamp.toLocalDate().toEpochDay(),
                                high = dataConverter(value),
                            )
                        }
                    }
            }

            SIX_MONTHS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                sleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate()
                            .with((TemporalAdjusters.previousOrSame(firstDayOfWeek)))
                    }
                    .mapNotNull { (startOfWeek, weeklySleepList) ->
                        val weeklyAverage = Average()
                        weeklySleepList.forEach { sleep ->
                            dataExtractor(sleep)?.takeIf { it > 0f }?.let { value ->
                                weeklyAverage.feed(value)
                                average.feed(value)
                            }
                        }
                        weeklyAverage.result.takeIf { it > 0f }?.let { value ->
                            minY = min(minY, value)
                            maxY = max(maxY, value)
                            SleepComparisonChartData.Entry(
                                x = startOfWeek.toEpochDay(),
                                high = dataConverter(value),
                            )
                        }
                    }
            }

            YEARLY -> {
                minX = from.epochMonth.toDouble()
                maxX = to.epochMonth.toDouble()
                sleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate().epochMonth
                    }
                    .mapNotNull { (epochMonth, monthlySleepList) ->
                        val monthlyAverage = Average()
                        monthlySleepList.forEach { sleep ->
                            dataExtractor(sleep)?.takeIf { it > 0f }?.let { value ->
                                monthlyAverage.feed(value)
                                average.feed(value)
                            }
                        }
                        monthlyAverage.result.takeIf { it > 0f }?.let { value ->
                            minY = min(minY, value)
                            maxY = max(maxY, value)
                            SleepComparisonChartData.Entry(
                                x = epochMonth.toLong(),
                                high = dataConverter(value),
                            )
                        }
                    }
            }

            DAILY,
            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS,
            EIGHT_YEARS -> throw Exception("Unsupported chart granularity: $chartGranularity")
        }
        if (minY == Float.MAX_VALUE || minY == maxY) {
            minY = 0f
        }
        val convertedMinY = dataConverter(minY)
        val convertedMaxY = dataConverter(maxY)
        val (adjustedMinY, adjustedMaxY) = yRangeConverter(convertedMinY, convertedMaxY)
        val convertedAverage = dataConverter(average.result)
        return SleepComparisonChartData.Series(
            chartType = chartType,
            axisRange = SleepComparisonChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = adjustedMinY.toDouble(),
                maxY = adjustedMaxY.toDouble(),
            ),
            entries = listOf(entries),
            average = convertedAverage,
            formatedAverage = averageFormatter(convertedAverage),
        )
    }

    fun loadChartData(
        chartGranularity: ChartGranularity,
        chartComparison: ChartComparison,
        from: LocalDate,
        to: LocalDate,
    ): Flow<ChartData> {
        return if (chartGranularity == DAILY) {
            combine(
                sleepForDateRangeFlow(from, to).map { it.firstOrNull() },
                sleepStagesForDateRangeFlow(from, to),
                trendDataForDateRangeFlow(from, to),
                fetchSleepGoalUseCase.fetchSleepGoal(),
            ) { sleep, sleepStages, trendData, sleepGoal ->
                val adjustedSleepStages = sleepStages.dropLastWhile { it.stage == SleepStage.AWAKE }
                val sleepRanges = calcDailySleepRegions(sleep, adjustedSleepStages)
                val sleepStagesSeries = createDailySleepDataSeries(
                    from = from,
                    to = to,
                    sleepRegions = sleepRanges,
                    sleep = sleep,
                    sleepStages = adjustedSleepStages,
                )
                val heartRateSeries = if (chartComparison != ChartComparison.NONE) {
                    with(heartRateSeriesCreator) {
                        val longSleepRanges = sleepRanges.filter { !it.nap }
                        if (longSleepRanges.isNotEmpty()) {
                            createDailyHeartRateSeries(
                                trendData,
                                sleep?.longSleep?.minHr?.inBpm?.roundToInt(),
                                longSleepRanges,
                            )
                        } else {
                            createDailyHeartRateEmptySeries(
                                sleepStagesSeries.axisRange.minX.toLong(),
                                sleepStagesSeries.axisRange.maxX.toLong(),
                            )
                        }
                    }
                } else null
                ChartData(
                    chartGranularity = DAILY,
                    series = buildList {
                        add(sleepStagesSeries)
                        heartRateSeries?.let { addAll(it) }
                    }.toImmutableList(),
                    highlightEnabled = true,
                    goal = sleepGoal.inWholeSeconds.toFloat().toHours(),
                    highlightDecorationLines = persistentMapOf(),
                    currentValues = persistentListOf(),
                    chartContent = ChartContent.SLEEP,
                    colorIndicator = null,
                )
            }
        } else {
            combine(
                sleepForDateRangeFlow(from, to),
                fetchSleepGoalUseCase.fetchSleepGoal(),
            ) { sleepList, sleepGoal ->
                ChartData(
                    chartGranularity = chartGranularity,
                    series = createSleepDataSeries(
                        chartGranularity = chartGranularity,
                        from = from,
                        to = to,
                        sleepList = sleepList,
                        sleepGoal = sleepGoal.inWholeSeconds.toFloat(),
                    ).toImmutableList(),
                    highlightEnabled = true,
                    goal = when (chartGranularity) {
                        SIX_MONTHS,
                        YEARLY -> null

                        else -> sleepGoal.inWholeSeconds.toFloat().toHours()
                    },
                    highlightDecorationLines = persistentMapOf(),
                    currentValues = persistentListOf(),
                    chartBarDisplayMode = ChartBarDisplayMode.STACKED,
                    chartContent = ChartContent.SLEEP,
                    colorIndicator = null,
                )
            }
        }
    }

    private fun calcDailySleepRegions(
        sleep: Sleep?,
        sleepStages: List<SleepStageInterval>,
    ): List<SleepRegion> {
        val longSleepFellAsleep = listOfNotNull(
            sleepStages.firstOrNull()?.timeISO8601?.toEpochSecond(),
            sleep?.longSleep?.fellAsleep?.toZonedDateTime()?.toEpochSecond(),
        ).minOrNull()
        val longSleepWokeUp = listOfNotNull(
            sleepStages.lastOrNull()?.let {
                it.timeISO8601.plusSeconds(it.duration.inWholeSeconds).toEpochSecond()
            },
            sleep?.longSleep?.wokeUp?.toZonedDateTime()?.toEpochSecond(),
        ).maxOrNull()
        return buildList {
            if (longSleepFellAsleep != null && longSleepWokeUp != null && longSleepFellAsleep < longSleepWokeUp) {
                add(SleepRegion(longSleepFellAsleep, longSleepWokeUp, false))
            }
            sleep?.naps?.forEach { nap ->
                val napFellAsleep = nap.fellAsleep.toZonedDateTime().toEpochSecond()
                val napWokeUp = nap.wokeUp.toZonedDateTime().toEpochSecond()
                if (napFellAsleep < napWokeUp) {
                    add(SleepRegion(napFellAsleep, napWokeUp, true))
                }
            }
        }.sortedBy { it.xStart }
    }

    private fun createDailySleepDataSeries(
        from: LocalDate,
        to: LocalDate,
        sleepRegions: List<SleepRegion>,
        sleep: Sleep?,
        sleepStages: List<SleepStageInterval>,
    ): ChartData.Series {
        val offset = ZonedDateTime.now().offset
        val (minX, maxX) = if (sleepRegions.any { !it.nap }) {
            sleepRegions.minOf { it.xStart } to sleepRegions.maxOf { it.xEnd }
        } else {
            from.atStartOfDay().toEpochSecond(offset) to to.atEndOfDay().toEpochSecond(offset)
        }
        // keep nap entries at the beginning of the list to draw them first
        val entries = listOf(
            *sleepRegions.filter { it.nap }.map {
                SleepStageEntry(
                    xStart = it.xStart,
                    xEnd = it.xEnd,
                    stage = ExtendedSleepStage.NAP,
                )
            }.toTypedArray(),
            *sleepStages.mapNotNull { interval ->
                // The stage minimum time is 30 seconds.
                // Since the precision is lost after long is converted to float, xStart equals xEnd will occur.
                // We just ignore such stages, or sleep stages chart may not be drawn correctly.
                interval.duration.inWholeSeconds.takeIf { it > 0L }?.let { duration ->
                    SleepStageEntry(
                        xStart = interval.timeISO8601.toEpochSecond(),
                        xEnd = interval.timeISO8601.plusSeconds(duration).toEpochSecond(),
                        stage = interval.stage.extended,
                    )
                }
            }.toTypedArray(),
        )

        return ChartData.Series(
            chartType = ChartType.SLEEP_STAGE,
            color = context.getColor(BR.color.activity_data_sleep),
            axisRange = ChartData.AxisRange(
                minX = minX.toDouble(),
                maxX = maxX.toDouble(),
                minY = 0.0,
                maxY = 4.0,
            ),
            entries = persistentListOf(),
            value = (sleep?.totalSleepDuration?.inWholeSeconds ?: 0L).formatSleepTime(context),
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(),
            sleepStageEntries = entries.toImmutableList(),
            sleepRegions = sleepRegions.toImmutableList(),
            backgroundRegion = null,
            groupStackBarStyle = null,
        )
    }

    private fun createSleepDataSeries(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
        sleepGoal: Float,
    ): List<ChartData.Series> {
        var minX: Double
        var maxX: Double
        var maxY = sleepGoal
        val average = Average()
        val longSleepEntries = mutableListOf<ChartData.Entry>()
        val napEntries = mutableListOf<ChartData.Entry>()
        when (chartGranularity) {
            WEEKLY,
            SEVEN_DAYS,
            MONTHLY,
            THIRTY_DAYS,
            SIX_WEEKS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                sleepList.forEach { sleep ->
                    val x = sleep.timestamp.toLocalDate().toEpochDay()
                    sleep.longSleep?.sleepDuration?.takeIf { it > Duration.ZERO }?.let { value ->
                        longSleepEntries.add(ChartData.Entry(x = x, y = value.toHours()))
                    }
                    sleep.getMergedNap()?.duration?.takeIf { it > Duration.ZERO }?.let { value ->
                        napEntries.add(ChartData.Entry(x = x, y = value.toHours()))
                    }
                    sleep.totalSleepDuration.takeIf { it > Duration.ZERO }
                        ?.inWholeSeconds
                        ?.toFloat()
                        ?.let { value ->
                            maxY = max(maxY, value)
                            average.feed(value)
                        }
                }
            }

            SIX_MONTHS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                sleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate()
                            .with((TemporalAdjusters.previousOrSame(firstDayOfWeek)))
                    }
                    .forEach { (startOfWeek, weeklySleepList) ->
                        val weeklyLongSleepAverage = Average()
                        val weeklyNapAverage = Average()
                        val weeklyTotalAverage = Average()
                        weeklySleepList.forEach { sleep ->
                            (sleep.longSleep?.sleepDuration ?: Duration.ZERO).let { value ->
                                weeklyLongSleepAverage.feed(value.inWholeSeconds.toFloat())
                            }
                            (sleep.getMergedNap()?.duration ?: Duration.ZERO).let { value ->
                                weeklyNapAverage.feed(value.inWholeSeconds.toFloat())
                            }
                            sleep.totalSleepDuration
                                .inWholeSeconds
                                .toFloat()
                                .let { value ->
                                    weeklyTotalAverage.feed(value)
                                    average.feed(value)
                                }
                        }
                        val x = startOfWeek.toEpochDay()
                        weeklyLongSleepAverage.result.takeIf { it > 0f }?.let { value ->
                            longSleepEntries.add(ChartData.Entry(x = x, y = value.toHours()))
                        }
                        weeklyNapAverage.result.takeIf { it > 0f }?.let { value ->
                            napEntries.add(ChartData.Entry(x = x, y = value.toHours()))
                        }
                        weeklyTotalAverage.result.takeIf { it > 0f }?.let { value ->
                            maxY = max(maxY, value)
                        }
                    }
            }

            YEARLY -> {
                minX = from.epochMonth.toDouble()
                maxX = to.epochMonth.toDouble()
                sleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate().epochMonth
                    }
                    .forEach { (epochMonth, monthlySleepList) ->
                        val monthlyLongSleepAverage = Average()
                        val monthlyNapAverage = Average()
                        val monthlyTotalAverage = Average()
                        monthlySleepList.forEach { sleep ->
                            (sleep.longSleep?.sleepDuration ?: Duration.ZERO).let { value ->
                                monthlyLongSleepAverage.feed(value.inWholeSeconds.toFloat())
                            }
                            (sleep.getMergedNap()?.duration ?: Duration.ZERO).let { value ->
                                monthlyNapAverage.feed(value.inWholeSeconds.toFloat())
                            }
                            sleep.totalSleepDuration
                                .inWholeSeconds
                                .toFloat()
                                .let { value ->
                                    monthlyTotalAverage.feed(value)
                                    average.feed(value)
                                }
                        }
                        val x = epochMonth.toLong()
                        monthlyLongSleepAverage.result.takeIf { it > 0f }?.let { value ->
                            longSleepEntries.add(ChartData.Entry(x = x, y = value.toHours()))
                        }
                        monthlyNapAverage.result.takeIf { it > 0f }?.let { value ->
                            napEntries.add(ChartData.Entry(x = x, y = value.toHours()))
                        }
                        monthlyTotalAverage.result.takeIf { it > 0f }?.let { value ->
                            maxY = max(maxY, value)
                        }
                    }
            }

            DAILY,
            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS,
            EIGHT_YEARS -> throw Exception("Unsupported chart granularity: $chartGranularity")
        }
        val value = average.result.roundToLong().formatSleepTime(context)
        val axisRange = ChartData.AxisRange(
            minX = minX,
            maxX = maxX,
            minY = 0.0,
            maxY = maxY.toHours().toNearest().toDouble(),
        )
        return listOf(
            ChartData.Series(
                chartType = ChartType.BAR,
                color = context.getColor(BR.color.activity_data_sleep),
                axisRange = axisRange,
                entries = longSleepEntries.toImmutableList(),
                value = value,
                candlestickEntries = persistentListOf(),
                lineConfig = LineChartConfig(),
                backgroundRegion = null,
                groupStackBarStyle = null,
            ),
            ChartData.Series(
                chartType = ChartType.BAR,
                color = context.getColor(CR.color.activity_data_nap),
                axisRange = axisRange,
                entries = napEntries.toImmutableList(),
                value = value,
                candlestickEntries = persistentListOf(),
                lineConfig = LineChartConfig(),
                backgroundRegion = null,
                groupStackBarStyle = null,
            ),
        )
    }

    private fun Long.formatSleepTime(context: Context) = buildAnnotatedString {
        val (hours, minutes) = secondsToHourMinute()
        if (hours > 0L || minutes == 0L) {
            append(hours.toString())
            withStyle(SpanStyle(fontSize = 12.sp)) {
                append(" ")
                append(context.getString(CR.string.hour))
            }
        }
        if (hours > 0L && minutes > 0L) {
            append(" ")
        }
        if (minutes > 0L) {
            append(minutes.toString())
            withStyle(SpanStyle(fontSize = 12.sp)) {
                append(" ")
                append(context.getString(CR.string.minute))
            }
        }
    }

    private fun sleepStagesForDateRangeFlow(
        from: LocalDate,
        to: LocalDate,
    ): Flow<List<SleepStageInterval>> = fetchSleepUseCase.fetchSleepStages(from, to)
        .map { it.values.flatten() }

    private fun trendDataForDateRangeFlow(
        from: LocalDate,
        to: LocalDate,
    ): Flow<List<TrendData>> {
        return trendDataRepository.fetchTrendDataForDateRange(
            from.minusDays(1).atStartOfDay().toEpochMilli(),
            to.atStartOfDay().toEpochMilli(),
            aggregated = false,
        )
    }

    private fun sleepForDateRangeFlow(
        from: LocalDate,
        to: LocalDate,
    ): Flow<List<Sleep>> {
        return fetchSleepUseCase.fetchSleeps(from, to)
    }

    private fun recoveryForDateRangeFlow(
        from: LocalDate,
        to: LocalDate,
    ): Flow<List<RecoveryData>> = fetchRecoveryDataUseCase.fetchRecoveryData(from, to)

    private fun workoutForDateRangeFlow(
        from: LocalDate,
        to: LocalDate,
    ): Flow<List<WorkoutHeader>> = flow {
        runSuspendCatching {
            getWorkoutHeadersForRangeUseCase(
                GetWorkoutHeadersForRangeUseCase.Params(
                    username = currentUserController.username,
                    activityTypeIds = emptyList(),
                    sinceMs = from.atStartOfDay().toEpochMilli(),
                    untilMs = to.atEndOfDay().toEpochMilli(),
                )
            )
        }.getOrNull().let {
            emit(it ?: emptyList())
        }
    }

    private fun Float.toPercent() = (this * 100).roundToInt().toFloat()

    private fun Float.roundToTwoDecimals(): Float {
        return (this * 100).roundToInt() / 100f
    }

    private fun Long.roundToNearestMinute(): Long {
        return this + 30.seconds.inWholeMilliseconds
    }

    private fun Long.toZonedDateTime(): ZonedDateTime {
        return Instant.ofEpochMilli(this).atZone(ZoneId.systemDefault())
    }

    private fun Long.toLocalDate() = toZonedDateTime().toLocalDate()

    private fun Long.secondsFromStartOfDay(): Long {
        return with(toZonedDateTime()) { hour * 3600L + minute * 60L + second }
    }

    // seconds to hours
    private fun Float.toHours() = this / 3600f

    private fun Duration.toHours(): Float = inWholeMilliseconds / 3600_000F

    private fun ZonedDateTime.formatTime(): String {
        return DateTimeFormatter.ofPattern("HH:mm").format(this)
    }

    class Average {
        var result = 0f
            private set
        private var total = 0

        fun feed(value: Float) {
            result += (value - result) / ++total
        }
    }

    fun createSleepHistoryPager(
        from: LocalDate,
        to: LocalDate,
    ): Flow<PagingData<SleepHistoryItem>> {
        return combine(
            Pager(
                config = PagingConfig(pageSize = 5, enablePlaceholders = false),
                pagingSourceFactory = { SleepHistoryPagingSource(from, to) },
            ).flow,
            fetchSleepGoalUseCase.fetchSleepGoal(),
        ) { pagingData, sleepGoal ->
            pagingData.map { sleep ->
                val date = sleep.timestamp.toLocalDate()
                SleepHistoryItem(
                    longSleep = sleep.longSleep?.let {
                        SleepPeriod(
                            fellAsleep = it.fellAsleep.roundToNearestMinute().toZonedDateTime()
                                .formatTime(),
                            wokeUp = it.wokeUp.roundToNearestMinute().toZonedDateTime()
                                .formatTime(),
                            totalDuration = it.sleepDuration,
                        )
                    },
                    naps = sleep.naps.map {
                        SleepPeriod(
                            fellAsleep = it.fellAsleep.roundToNearestMinute().toZonedDateTime()
                                .formatTime(),
                            wokeUp = it.wokeUp.roundToNearestMinute().toZonedDateTime()
                                .formatTime(),
                            totalDuration = it.duration,
                        )
                    },
                    longSleepDuration = sleep.longSleep?.sleepDuration ?: Duration.ZERO,
                    napDuration = sleep.getMergedNap()?.duration ?: Duration.ZERO,
                    totalDuration = sleep.totalSleepDuration,
                    goalDuration = sleepGoal,
                    dateStr = dateFormatter.formatDate(date),
                    date = date,
                )
            }
        }
    }

    inner class SleepHistoryPagingSource(
        private val from: LocalDate,
        private val to: LocalDate,
    ) : PagingSource<Int, Sleep>() {
        override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Sleep> {
            return runSuspendCatching {
                val nextPageNum = params.key ?: 1
                val loadSize = params.loadSize
                val pagedTo = to.minusDays(((nextPageNum - 1) * loadSize).toLong())
                var pagedFrom = pagedTo.minusDays((loadSize - 1).toLong())
                pagedFrom = if (pagedFrom > from) pagedFrom else from
                val list = sleepForDateRangeFlow(from = pagedFrom, to = pagedTo).first().reversed()
                val hasMore = pagedFrom != from
                LoadResult.Page(
                    data = list,
                    prevKey = null,
                    nextKey = if (hasMore) nextPageNum + 1 else null,
                )
            }.fold(
                onSuccess = { it },
                onFailure = { LoadResult.Error(it) },
            )
        }

        override fun getRefreshKey(state: PagingState<Int, Sleep>): Int? {
            return state.anchorPosition?.let { anchorPosition ->
                val anchorPage = state.closestPageToPosition(anchorPosition)
                anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
            }
        }
    }
}
