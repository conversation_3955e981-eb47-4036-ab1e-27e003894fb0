package com.stt.android.chart.impl.usecases

import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.SleepComparisonGraphType
import com.stt.android.chart.impl.screen.SleepComparisonViewData
import com.stt.android.chart.impl.screen.SleepViewData
import java.time.LocalDate
import javax.inject.Inject

internal class CreateSleepViewDataUseCase @Inject constructor(
    private val createSleepStagesDataUseCase: CreateSleepStagesDataUseCase,
    private val createDailySleepMetricsDataUseCase: CreateDailySleepMetricsDataUseCase,
    private val createSleepComparisonChartDataUseCase: CreateSleepComparisonChartDataUseCase,
    private val createSleepHistoryDataUseCase: CreateSleepHistoryDataUseCase,
) {
    operator fun invoke(
        chartContent: ChartContent,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
    ): SleepViewData {
        if (chartContent != ChartContent.SLEEP) return SleepViewData.None

        return SleepViewData.Loaded(
            stagesViewData = createSleepStagesDataUseCase(chartGranularity, from, to),
            dailyMetricsViewData = createDailySleepMetricsDataUseCase(chartGranularity, from, to),
            comparisonViewData = createSleepComparisonChartDataUseCase(chartGranularity, from, to),
            historyViewData = createSleepHistoryDataUseCase(chartGranularity, from, to),
        )
    }

    // called only when it's the sleep detail page
    fun updateComparisonViewData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        primaryGraphType: SleepComparisonGraphType?,
        secondaryGraphType: SleepComparisonGraphType?,
    ) = createSleepComparisonChartDataUseCase(
        chartGranularity = chartGranularity,
        from = from,
        to = to,
        primaryGraphType = primaryGraphType,
        secondaryGraphType = secondaryGraphType,
    )

    fun updateComparisonHighlightViewData(
        viewData: SleepComparisonViewData,
        entryX: Long,
    ) = createSleepComparisonChartDataUseCase.updateComparisonHighlightViewData(viewData, entryX)
}
