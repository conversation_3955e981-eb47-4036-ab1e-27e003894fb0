<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="app_name">Sample App</string>
    <string name="start_scan">Start scan</string>
    <string name="cancel_scan">Cancel scan</string>

    <string name="title_location_permission">Location permission</string>
    <string name="text_location_permission">Bluetooth LE scanning requires location permission</string>
    <string name="btn_ok">Ok</string>
    <string name="btn_get_gnss">Get GNSS</string>
    <string name="btn_logbook">Logbook</string>
    <string name="btn_settings">Settings</string>
    <string name="btn_summary">Summ</string>
    <string name="btn_data">Data</string>
    <string name="btn_send_notification">Notify</string>

    <string name="device_type">Type</string>
    <string name="device_serial">Serial</string>
    <string name="device_sw_version">SW Version</string>
    <string name="device_state">State</string>
    <string name="device_last_sync">Last sync</string>
    <string name="notification_hint">Notification text</string>
    <string name="btn_notifications">Notifications</string>

    <string name="setting_gender">Gender: <xliff:g example="FEMALE" id="gender">%1$s
    </xliff:g></string>
    <string name="setting_unit_system">Unit System: <xliff:g example="METRIC" id="unitSystem">
        %1$s</xliff:g></string>
    <string name="setting_birth_year">Birth year: <xliff:g example="1970" id="birthYear">%1$d</xliff:g></string>
    <string name="setting_weight">Weight:
        <xliff:g example="70.00" id="weight">%1$.2f</xliff:g> kg</string>
    <string name="notification_sync_finished">Watch synchronized</string>
    <string name="notification_successful_entries">%1$d new moves available</string>
    <string name="main_menu_kill_ui">Stop UI Process</string>
    <string name="main_menu_gather_logs">Gather logs</string>
    <string name="device_latest_sync">Last synced</string>
    <string name="btn_sync">Synchronize</string>
    <string name="btn_reset_connection">Reset connection</string>
    <string name="btn_get_service_stability">Get service stability</string>

    <string name="notification_channel_sync_result">Sync result</string>
    <string name="notification_channel_watch_service">Watch service</string>
    <string name="btn_disconnect">Disconnect</string>

</resources>
