package com.stt.android.di

import com.stt.android.STTBaseModule
import com.stt.android.di.moshiadapters.MoshiAdaptersModule
import com.stt.android.di.persistence.PersistenceModule
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@InstallIn(SingletonComponent::class)
@Module(
    includes = [
        AppModule::class,
        STTBaseModule::class,
        MoshiAdaptersModule::class,
        PersistenceModule::class,
        WaypointToolsModule::class,
    ]
)
object CommonAggregatorModule
