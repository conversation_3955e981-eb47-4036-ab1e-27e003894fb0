package com.stt.android.di

import com.stt.android.analytics.SuuntoAnalyticsModule
import com.stt.android.deleteaccount.datasource.DeleteAccountDataSourceModule
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusGuideRemoteSyncJobModule
import com.stt.android.di.connectivity.DataLayerModule
import com.stt.android.newemail.NewEmailModule
import com.stt.android.newphone.NewPhoneModule
import com.stt.android.resetpassword.ResetPasswordModule
import com.stt.android.session.di.UserCenterChinaModule
import com.stt.android.social.personalrecord.PersonalRecordModule
import com.stt.android.systemwidget.Suunto247SystemWidgetModule
import com.stt.android.watch.gearevent.GearEventModule
import com.stt.android.watch.sportmodes.SportModeActivityModule
import com.suunto.network.HeadsetModule
import dagger.Module

@Module(
    includes = [
        SportModeActivityModule::class,
        SuuntoAnalyticsModule::class,
        GearEventModule::class,
        DataLayerModule::class,
        Suunto247SystemWidgetModule::class,
        DeviceNavigatorModule::class,
        SuuntoPlusGuideRemoteSyncJobModule::class,
        HeadsetModule::class,
        UserCenterChinaModule::class,
        ResetPasswordModule::class,
        NewEmailModule::class,
        NewPhoneModule::class,
        PersonalRecordModule::class,
        DeleteAccountDataSourceModule::class,
    ]
)
abstract class BrandContributeAndroidInjectorModule : ContributeAndroidInjectorModule()
