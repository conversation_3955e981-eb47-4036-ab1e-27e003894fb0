package com.stt.android.home

import android.content.Context
import com.gojuno.koptional.Optional
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.data.sportmodes.component.SportModeComponent
import com.stt.android.watch.SuuntoWatchModel
import com.stt.android.watch.companionAssociation.CompanionAssociationHelper
import com.stt.android.watch.companionAssociation.CompanionLinkingUtils
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent
import dagger.hilt.android.qualifiers.ActivityContext
import io.reactivex.Maybe
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers

@Module
@InstallIn(ActivityComponent::class)
abstract class BrandHomeModule {
    companion object {
        @Provides
        fun provideSportModeComponentObservable(): Single<SportModeComponent> {
            // Dummy stub since we don't access sport mode component on home activity
            return Maybe.never<SportModeComponent>()
                .toSingle()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
        }

        @Provides
        fun providesCompanionAssociationHelper(
            @ActivityContext context: Context,
            suuntoWatchModel: SuuntoWatchModel,
            datahubAnalyticsTracker: DatahubAnalyticsTracker
        ): Optional<CompanionAssociationHelper> =
            CompanionLinkingUtils.createCompanionAssociationHelper(
                context,
                suuntoWatchModel,
                datahubAnalyticsTracker,
            )
    }
}
