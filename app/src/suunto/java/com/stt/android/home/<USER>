package com.stt.android.home

import android.Manifest
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.view.ContextThemeWrapper
import androidx.core.app.ActivityCompat
import androidx.core.content.edit
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.tracing.trace
import androidx.work.WorkManager
import com.gojuno.koptional.Optional
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.userDetailsAnalytics.UserDetailsAnalyticsJob
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.controllers.UserSettingsController
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.home.dashboard.bottomsheet.WeeklyGoalBottomSheetFragment
import com.stt.android.home.devicetype.DeviceTypeSelectActivity
import com.stt.android.home.diary.Diary
import com.stt.android.ui.activities.settings.countrysubdivision.CountrySubdivisionListActivity
import com.stt.android.usecases.startup.AppStatRepository
import com.stt.android.utils.BatteryOptimizationUtils
import com.stt.android.utils.BluetoothUtils
import com.stt.android.utils.STTConstants
import com.stt.android.watch.DeviceActivity
import com.stt.android.watch.companionAssociation.CompanionAssociationHelper
import com.stt.android.watch.getMissingNotificationsPermissions
import com.suunto.connectivity.Spartan
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.util.NotificationSettingsHelper
import com.suunto.connectivity.watch.WatchState
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.Locale
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import com.stt.android.home.diaryv2.DiaryFragment as DiaryV2Fragment

@AndroidEntryPoint
class HomeActivity : BaseHomeActivity(), WeeklyGoalBottomSheetFragment.BottomSheetUpdateListener {
    @Inject
    lateinit var batteryOptimizationUtils: BatteryOptimizationUtils

    @Inject
    lateinit var appStatRepository: AppStatRepository

    @Inject
    lateinit var notificationSettingsHelper: NotificationSettingsHelper

    @Inject
    lateinit var emarsysAnalytics: EmarsysAnalytics

    @Inject
    lateinit var datahubAnalyticsTracker: DatahubAnalyticsTracker

    @Inject
    @SuuntoSharedPrefs
    lateinit var suuntoSharedPrefs: SharedPreferences

    /**
     * The injection itself will self-configure and update the companion status
     */
    @Inject
    lateinit var companionAssociationHelper: Optional<CompanionAssociationHelper>

    @Inject
    lateinit var workManager: WorkManager

    @Inject
    lateinit var userSettingsController: UserSettingsController

    private var compositeDisposable = CompositeDisposable()

    override fun onCreate(savedInstanceState: Bundle?) = trace("HomeActivity.onCreate") {
        super.onCreate(savedInstanceState)

        viewModel.checkForBatteryOptimizationLiveData().observeK(this) { checkRequired ->
            if (checkRequired == true) {
                batteryOptimizationUtils.checkBatteryOptimizationState(this)
            }
        }

        viewModel.currentWatchAndState.observeNotNull(this) { (watch, state) ->
            checkReadCallLogPermission(
                currentDevice = watch,
                watchState = state
            )
        }

        viewModel.deviceFoundForSmoothPairing.observeK(this) {
            startActivity(DeviceActivity.newStartIntent(this))
        }

        viewModel.backgroundLocationPermissionForConnectedGpsNeeded
            .onEach { needed ->
                if (needed) {
                    startActivity(DeviceActivity.newStartIntent(this))
                }
            }.launchIn(lifecycleScope)

        viewModel.backgroundLocationPermissionForWeatherNeeded
            .onEach { needed ->
                if (needed) {
                    startActivity(BackgroundLocationPermissionActivity.newStartIntent(this))
                    suuntoSharedPrefs.edit {
                        putBoolean(
                            STTConstants.SuuntoPreferences.KEY_HAS_SHOWN_BACKGROUND_LOCATION_FOR_WEATHER_TUTORIAL,
                            true
                        )
                    }
                }
            }.launchIn(lifecycleScope)

        viewModel.navigationItemSelected.observeNotNull(this) {
            handleNavigationItemSelected(it)
        }

        viewModel.navigationItemReSelected.observeNotNull(this) {
            handleNavigationItemReSelected(it)
        }

        viewModel.needReloadDashboard.observe(this) {
            handleNavigationItemSelected(R.id.bottomBarDashboard)
        }

        // Analytics wants to set these user properties as soon as possible for new users so send
        // them here if not already sent
        if (!appStatRepository.initialUserDetailsSentToAnalytics) {
            UserDetailsAnalyticsJob.schedule(workManager)
        }

        val bundle = savedInstanceState ?: intent.extras
        bundle?.let { handleDeepLinkNavigation(it) }

        // If applicable after 5seconds we start one of the 2
        startDeviceActivityIfApplicable()
        startCountrySubdivisionSelectionIfApplicable()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        intent.extras?.let { handleDeepLinkNavigation(it) }
    }

    /**
     * Check if there are missing READ_CALL_LOG permission required by watch notifications.
     * Request READ_CALL_LOG permission if it is missing and user has enabled watch notifications.
     */
    private fun checkReadCallLogPermission(currentDevice: Spartan, watchState: WatchState) {
        val fwVersion = watchState.deviceInfo?.hw
        val capabilities =
            SuuntoDeviceCapabilityInfoProvider[currentDevice.suuntoBtDevice.deviceType]
        if (capabilities.supportsNotifications(fwVersion) &&
            notificationSettingsHelper.notificationsEnabled(this)
        ) {
            // Current device supports notifications and user has enabled notifications.
            val missingPermissions = getMissingNotificationsPermissions(
                notificationSettingsHelper,
                this,
                emarsysAnalytics,
                datahubAnalyticsTracker
            )

            if (missingPermissions.contains(Manifest.permission.READ_CALL_LOG)) {
                if (!suuntoSharedPrefs.getBoolean(
                        STTConstants.SuuntoPreferences.KEY_HOME_VIEW_HAS_REQUESTED_NOTIFICATION_PREFERENCES,
                        false
                    )
                ) {
                    showReadCallLogPermissionDialog()
                    suuntoSharedPrefs.edit {
                        putBoolean(
                            STTConstants.SuuntoPreferences.KEY_HOME_VIEW_HAS_REQUESTED_NOTIFICATION_PREFERENCES,
                            true
                        )
                    }
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        viewModel.stopSmoothPairingScanning()
    }

    override fun onResume() = trace("HomeActivity.onResume") {
        super.onResume()

        viewModel.initiateSmoothPairingIfNeeded()

        lifecycleScope.launch {
            viewModel.initiateBackgroundLocationRequestIfNeeded(
                context = this@HomeActivity,
            )
        }

        startCountrySubdivisionSelectionIfApplicable()
    }

    override fun onDestroy() {
        super.onDestroy()
        compositeDisposable.clear()
        companionAssociationHelper.toNullable()?.destroy()
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == BatteryOptimizationUtils.REQUEST_BATTERY_OPTIMIZATION_MODE) {
            datahubAnalyticsTracker.trackEvent(
                AnalyticsEvent.DOZE_MODE,
                AnalyticsProperties()
                    .putYesNo(
                        AnalyticsEvent.DOZE_MODE_DISABLED,
                        batteryOptimizationUtils.isIgnoringBatteryOptimizations()
                    )
            )
        }
    }

    override fun getDiaryFragment(
        showTabType: Diary.TabType?,
        primaryGraphType: GraphDataType?,
        secondaryGraphType: GraphDataType?,
        source: String?
    ): Fragment = DiaryV2Fragment.newInstance(showTabType)

    override fun onWeeklyTargetDurationChanged(weeklyTarget: Int) {
        // DashboardCardView will listen to changes itself
    }

    override fun onWeeklyTargetSettingDismissed() {
        // UI was already updated in onWeeklyTargetDurationChanged. No need to do anything here.
    }

    override fun onCoachEnabled(enabled: Boolean) {
        // DashboardCardView will listen to changes itself
    }

    override fun showWeeklyGoal() {
        val dialog = WeeklyGoalBottomSheetFragment()
        dialog.show(supportFragmentManager, WeeklyGoalBottomSheetFragment.FRAGMENT_TAG)
    }

    private fun showReadCallLogPermissionDialog() {
        val contextThemeWrapper = ContextThemeWrapper(this, R.style.WhiteTheme)
        val builder = AlertDialog.Builder(contextThemeWrapper)
            .setTitle(getString(R.string.phone_call_logs_permission_dialog_title))
            .setMessage(getString(R.string.phone_call_logs_permission_dialog_message))
            .setPositiveButton(getString(R.string.continue_str)) { _, _ -> requestReadCallLogPermission() }
            .setNegativeButton(getString(R.string.not_now)) { _, _ -> }
        builder.create().show()
    }

    private fun startDeviceActivityIfApplicable() {
        if (!BluetoothUtils.isBluetoothSupported(this) ||
            !suuntoSharedPrefs.getBoolean(
                STTConstants.SuuntoPreferences.KEY_SHOULD_START_DEVICE_ACTIVITY_AFTER_LOGIN,
                false
            )
        ) {
            return
        }
        suuntoSharedPrefs.edit {
            putBoolean(
                STTConstants.SuuntoPreferences.KEY_SHOULD_START_DEVICE_ACTIVITY_AFTER_LOGIN,
                false
            )
        }
        if (!appVersionViewModel.hasAppUpdateAvailable()) {
            startActivity(DeviceTypeSelectActivity.newStartIntent(this))
        }
    }

    private fun requestReadCallLogPermission() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.READ_CALL_LOG),
            REQUEST_NOTIFICATION_PERMISSIONS
        )
    }

    private fun startCountrySubdivisionSelectionIfApplicable() {
        if (userSettingsController.settings.country == Locale.US.country && userSettingsController.settings.countrySubdivision.isEmpty()) {
            // Clear all others
            compositeDisposable.clear()
            val startCountrySubdivisionDisposable = Single.just(this)
                .delay(5000, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .filter { !it.isFinishing }
                .subscribe({
                    startActivity(
                        CountrySubdivisionListActivity.newStartIntent(
                            applicationContext,
                            showCountrySelectionButton = true,
                            analyticsContext = AnalyticsPropertyValue.UsStateContext.EXISTING_USER_AFTER_UPDATE
                        )
                    )
                }) { throwable ->
                    Timber.w(
                        throwable,
                        "Could not start CountrySubdivisionListActivity"
                    )
                }
            compositeDisposable.add(startCountrySubdivisionDisposable)
        }
    }

    /**
     * Handle deep link related navigation
     * @param bundle Bundle containing intent parameters
     */
    private fun handleDeepLinkNavigation(bundle: Bundle) {
        if (bundle.getBoolean(KEY_SUBSCRIBE_MARKETING_CONSENT)) {
            intent.removeExtra(KEY_SUBSCRIBE_MARKETING_CONSENT)
            viewModel.acceptMarketingConsent()
        }
    }

    override fun hideExploreBadgeHighlight() {}

    companion object {
        private const val REQUEST_NOTIFICATION_PERMISSIONS = 1000
    }
}
