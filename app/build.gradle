plugins {
    id 'stt.android.plugin.application'
    id 'stt.android.plugin.application.flavors'
    id 'stt.android.plugin.application.firebase'
    id 'stt.android.plugin.application.publish'
    id "stt.android.plugin.compose"
    id "stt.android.plugin.hilt"
    alias libs.plugins.androidx.navigation.safeargs
}

apply from: "packaging.gradle"

android {
    lintOptions {
        disable("Aligned16KB")
    }

    defaultConfig {
        applicationId "com.stt.android"
        versionName rootProject.ext.versionName
        versionCode rootProject.ext.versionCode
        resourceConfigurations +=
            ['cs', 'da', 'de', 'es', 'fi', 'fr', 'it', 'iw', 'ja', 'ko', 'nl', 'nb', 'pl', 'pt', 'ru', 'sv', 'tr', 'th', 'zh-rCN', 'vi', 'zh-rTW']
    }

    testBuildType "debug"

    buildFeatures {
        dataBinding true
        buildConfig = true
    }

    buildTypes {
        debug {
            proguardFiles(*proguardDebugFilenames())
            // these settings are for com.stt.android.suunto.debug
            manifestPlaceholders = [
                XG_ACCESS_ID: "1500008623",
                XG_ACCESS_KEY : "AGJKCYN92KTI",
                HW_APPID : "100513481"
            ]
        }
        release {
            proguardFiles(*proguardFilenames())
        }
    }

    productFlavors {
        china {
            // these settings are for com.stt.android.suunto.china
            manifestPlaceholders = [
                XG_ACCESS_ID: "1500008624",
                XG_ACCESS_KEY : "AS06GOHUCOCI",
                HW_APPID : "100513481"
            ]
        }
    }

    lint {
        baseline file("lint-baseline.xml")
    }
    namespace 'com.stt.android.app'
    testNamespace 'com.stt.android'

    testVariants.configureEach { variant ->
        variant.getCompileConfiguration().exclude group: 'org.checkerframework', module: 'checker-qual'
        variant.getRuntimeConfiguration().exclude group: 'org.checkerframework', module: 'checker-qual'
    }
}

dependencies {
    implementation platform(libs.firebase.bom)
    implementation project(Deps.appBase)
    implementation project(Deps.explore)
    implementation project(Deps.diveCustomization)
    implementation project(Deps.session)
    implementation project(Deps.workoutdetails)
    implementation project(Deps.remote)
    implementation project(Deps.domain)
    implementation project(Deps.workoutsDomain)
    implementation project(Deps.datasource)
    implementation project(Deps.persistence)
    implementation project(Deps.exploreDatasource)
    implementation project(Deps.exploreRemote)
    implementation project(Deps.userDataSource)
    implementation project(Deps.diary)
    implementation project(Deps.diaryDomain)
    implementation project(Deps.exploreDomain)
    implementation project(Deps.maps)
    implementation project(Deps.device)
    implementation project(Deps.deviceonboarding)
    implementation project(Deps.questionnaire)
    implementation project(Deps.workoutplanner)
    implementation project(Deps.suuntoPlusStore)
    implementation project(Deps.elevationdata)
    implementation project(Deps.analytics)
    implementation project(Deps.core)
    implementation project(Deps.menstrualCycleOnboarding)
    implementation project(Deps.menstrualCycleDatasource)
    implementation project(Deps.timeline)
    suuntoImplementation project(Deps.headset)
    suuntoImplementation project(Deps.mapsProviderMapbox)
    suuntoImplementation project(Deps.connectivity)
    suuntoImplementation project(Deps.watchdebug)
    suuntoImplementation project(Deps.sportMode)
    suuntoImplementation project(Deps.watchOfflineMusic)

    implementation project(Deps.chartImpl)
    implementation project(Deps.tutorialImpl)
    implementation project(Deps.nfcImpl)
    implementation project(Deps.heartbelt)
    implementation project(Deps.tidesImpl)
    implementation project(Deps.remoteConfigImpl)

    implementation libs.soy.algorithms
    implementation libs.material
    implementation libs.androidx.work
    implementation libs.moshi
    implementation libs.play.auth
    implementation libs.simple.tooltip
    implementation libs.easypermissions
    implementation libs.facebook.sdk
    implementation libs.rxjava2.android
    implementation(libs.sim.formatter) {
        exclude group: 'org.javolution', module: 'javolution'
    }
    implementation libs.androidx.lifecycle.livedata.ktx
    implementation libs.okhttp
    implementation libs.play.install.referrer

    suuntoImplementation libs.gson

    sportstrackerWearApp project(Deps.wear)

    debugImplementation libs.leakcanary.android

    testImplementation project(Deps.testUtils)
}

static def proguardFilenames() {
    return ["../proguard/proguard-auto-value-gson.txt",
            "../proguard/proguard-gson.txt",
            "../proguard/proguard-jackson.txt",
            "../proguard/proguard-project.txt",
            "../proguard/proguard-dagger.txt",
            "../proguard/proguard-crashlytics.txt",
            "../proguard/proguard-okhttp.txt",
            "../proguard/proguard-mpandroidchart.txt",
            "../proguard/proguard-suunto.txt",
            "../proguard/proguard-ormlite.txt",
            "../proguard/proguard-amplitude.txt",
            "../proguard/proguard-jdeferred.txt",
            "../proguard/proguard-slf4j.txt",
            "../proguard/proguard-mdslib.txt",
            "../proguard/proguard-retrofit.txt",
            "../proguard/proguard-moshi.txt",
            "../proguard/proguard-rxjava2interop.txt",
            "../proguard/proguard-kotlin.txt",
            "../proguard/proguard-javatime.txt",
            "../proguard/proguard-emarsys.txt",
            "../proguard/proguard-formatter.txt",
            "../proguard/proguard-kotlinx-serialization.txt",
            "../proguard/proguard-jscience.txt",
            "../proguard/proguard-protobuf.txt",
            "../proguard/proguard-tencent.txt",
            "../proguard/proguard-wechat.txt",
            "../proguard/proguard-weibo.txt",
            "../proguard/proguard-amap.txt",
            "../proguard/proguard-weak-reference.txt"]
}

static def proguardDebugFilenames() {
    return proguardFilenames() + ["../proguard/proguard-debug.txt"]
}
