package com.stt.android.maps.mapbox

import android.animation.Animator
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.model.CameraPosition
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.mapbox.geojson.Point
import com.mapbox.maps.CameraOptions
import com.mapbox.maps.CoordinateBounds

/**
 * This constant represents the highest latitude value available to represent a geolocation.
 */
const val MAX_LATITUDE = 90.0

/**
 * This constant represents the highest longitude value available to represent a wrapped geolocation.
 */
const val MAX_WRAP_LONGITUDE = 180.0

/**
 * This constant represents the lowest latitude value available to represent a geolocation.
 */
const val MIN_LATITUDE = -90.0

/**
 * This constant represents the lowest longitude value available to represent a wrapped geolocation.
 */
const val MIN_WRAP_LONGITUDE = -180.0

fun googleZoomToMapbox(zoom: Float): Double {
    // Mapbox zoom level is equal to Google when the value is smaller by one
    return (zoom - 1).toDouble().coerceAtLeast(0.0)
}

fun CameraPosition.toMapbox(): CameraOptions {
    return CameraOptions.Builder()
        .bearing(bearing.toDouble())
        .center(target.toMapbox())
        .pitch(tilt.toDouble())
        .zoom(googleZoomToMapbox(zoom))
        .build()
}

fun LatLng.toMapbox(): Point = Point.fromLngLat(longitude, latitude)

fun LatLng.toMapbox(altitude: Double?): Point =
    if (altitude != null) Point.fromLngLat(longitude, latitude, altitude) else toMapbox()

fun List<LatLng>.toMapbox() = map { it.toMapbox() }

fun LatLngBounds.toMapbox(): CoordinateBounds =
    CoordinateBounds(
        Point.fromLngLat(southwest.longitude, southwest.latitude),
        Point.fromLngLat(northeast.longitude, northeast.latitude),
        false
    )

fun GoogleMap.CancelableCallback.toMapbox(): Animator.AnimatorListener {
    val that = this

    return object : Animator.AnimatorListener {
        override fun onAnimationStart(animation: Animator) {
            // Unused
        }

        override fun onAnimationEnd(animation: Animator) {
            that.onFinish()
        }

        override fun onAnimationCancel(animation: Animator) {
            that.onCancel()
        }

        override fun onAnimationRepeat(animation: Animator) {
            // Unused
        }
    }
}
